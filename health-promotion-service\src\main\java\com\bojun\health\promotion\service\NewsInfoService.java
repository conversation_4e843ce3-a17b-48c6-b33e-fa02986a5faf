package com.bojun.health.promotion.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.health.promotion.common.dto.NewsInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoParamDTO;
import com.bojun.health.promotion.common.entity.NewsInfo;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 18:19
 */
public interface NewsInfoService extends IService<NewsInfo> {

    int addNewsInfo(NewsInfoDTO record);

    NewsInfoDTO selectNewsInfoByNewsId(Integer newsId,boolean isLookOver);

    List<NewsInfoDTO> getNewsInfo(TopicInfoParamDTO topicInfo);

    int updateByPrimaryKeySelective(NewsInfoDTO record) throws Exception;

    int newsPutonOrOffShelf(NewsInfoDTO record) throws Exception;

    int deleteByPrimaryKey(NewsInfoDTO record);

}
