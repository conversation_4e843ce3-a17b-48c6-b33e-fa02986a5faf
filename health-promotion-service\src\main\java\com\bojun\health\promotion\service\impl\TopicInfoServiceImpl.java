package com.bojun.health.promotion.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;

import com.bojun.health.promotion.common.entity.NewsTopic;
import com.bojun.health.promotion.common.entity.TopicInfo;
import com.bojun.health.promotion.mapper.NewsInfoMapper;
import com.bojun.health.promotion.mapper.NewsTopicMapper;
import com.bojun.health.promotion.mapper.TopicInfoMapper;
import com.bojun.health.promotion.service.TopicInfoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 11:24
 */
@Service
public class TopicInfoServiceImpl extends ServiceImpl<TopicInfoMapper, TopicInfo> implements TopicInfoService {

    private static Logger logger = LoggerFactory.getLogger(TopicInfoServiceImpl.class);


    @Autowired
    private TopicInfoMapper topicInfoMapper;

    @Autowired
    private NewsTopicMapper newsTopicMapper;

    @Autowired
    private NewsInfoMapper newsInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(TopicInfoDTO topicInfo) throws Exception {
        // 更新栏目
        int topicResult = topicInfoMapper.updateByPrimaryKeySelective(topicInfo);
        if (topicResult <= 0) {
            throw new Exception("删除失败！");
        }
        // 查询栏目下所有的文章
        List<NewsTopic> newsTopics = newsTopicMapper.selectByTopicId(topicInfo.getTopicId());
        if(newsTopics.size() <= 0){
            return topicResult;
        }
        List<Integer> newsList = new ArrayList<>();
        for (NewsTopic newsTopic : newsTopics) {
            // 查询文章和其他的栏目是否有关联，对于有关联的，不做处理
            List<NewsTopic> newsTopicsList = newsTopicMapper.selectByNewsId(newsTopic.getNewsId());
            if (newsTopicsList.size() == 1) {
                newsList.add(newsTopic.getNewsId());
            }
        }
        // 更新文章状态为下架
        if (newsList.size() > 0) {
            int newsResult = newsInfoMapper.updateByNewsId(newsList);
            if (newsResult <= 0) {
                throw new Exception("删除失败！");
            }
        }
        // 删除文章和栏目的关联关系
        int newsTopicResult = newsTopicMapper.deleteByPrimaryKey(topicInfo.getTopicId());
        if (newsTopicResult <= 0) {
            throw new Exception("删除失败！");
        }
        return newsTopicResult;
    }

    @Override
    public int insert(TopicInfoDTO topicInfo) {
        return topicInfoMapper.insert(topicInfo);
    }

    @Override
    public int insertSelective(TopicInfoDTO topicInfo) {
        return 0;
    }

    @Override
    public TopicInfoDTO selectByPrimaryKey(Integer topicId) {
        return topicInfoMapper.selectByPrimaryKey(topicId);
    }

    @Override
    public int updateByPrimaryKeySelective(TopicInfoDTO record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(TopicInfoDTO topicInfo) {
        return topicInfoMapper.updateByPrimaryKeySelective(topicInfo);
    }

    @Override
    public List<TopicInfoDTO> getTopicInfo(TopicInfoDTO topicInfoDTO) {
        List<TopicInfoDTO> parentList = new ArrayList<>();
        // 查询栏目
        List<TopicInfoDTO> topicInfoList = topicInfoMapper.getTopicInfo(topicInfoDTO);
        // 遍历所有的栏目，查找出父集的栏目
        Integer topicId = topicInfoDTO.getTopicId();
        if (null != topicId) {
            topicInfoList.forEach(org -> {
                if (org.getTopicId() == topicId) {
                    parentList.add(org);
                    return;
                }
            });
        } else {
            topicInfoList.forEach(org -> {
                if (org.getParentTopicId() != null && org.getParentTopicId() == 0) {
                    parentList.add(org);
                }
            });
        }
        // 遍历父集的，找出子集
        parentList.forEach(parentOrg -> {
            parentOrg.setChildren(buildOrgTree(topicInfoList, parentOrg.getTopicId()));
        });
        return parentList;
    }

    /**
     * 遍历父集的，找出子集
     * @param orgList
     * @param parentId
     * @return
     */
    private List<TopicInfoDTO> buildOrgTree(List<TopicInfoDTO> orgList, Integer parentId) {
        List<TopicInfoDTO> treeList = new ArrayList<>();
        orgList.forEach(org -> {
            if (org.getParentTopicId().equals(parentId) && !org.getParentTopicId().equals(0)) {
                treeList.add(org);
            }
        });
        return treeList;
    }
}
