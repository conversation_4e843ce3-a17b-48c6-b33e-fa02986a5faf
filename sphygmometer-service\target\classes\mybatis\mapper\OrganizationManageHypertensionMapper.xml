<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.OrganizationManageHypertensionMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.OrganizationManageHypertensionDTO" id="OrganizationManageHypertensionDTOResult">
        <result property="organizationId"    column="organization_id"    />
        <result property="manageHypertensionCount"    column="manage_hypertension_count"    />
    </resultMap>

    <sql id="selectOrganizationManageHypertension">
    	select
	        organization_id,
	        manage_hypertension_count
		from 
        	t_organization_manage_hypertension
    </sql>

    <select id="selectOrganizationManageHypertensionById" parameterType="int" resultMap="OrganizationManageHypertensionDTOResult">
		<include refid="selectOrganizationManageHypertension"/>
		where 
        	organization_id = #{organizationId}
    </select>

    <select id="sumByIds" resultType="java.lang.Integer">
        select
        sum(manage_hypertension_count) manage_hypertension_count
        from
        t_organization_manage_hypertension
        <where>
            <if test="ids != null and ids.size() > 0">
                AND organization_id in
                <foreach item="item" index="index" collection="ids"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectOrganizationManageHypertensionList" parameterType="com.bojun.sphygmometer.dto.OrganizationManageHypertensionDTO" resultMap="OrganizationManageHypertensionDTOResult">
        <include refid="selectOrganizationManageHypertension"/>
        <where>  
		<if test="organizationId != null "> and organization_id = #{organizationId}</if>
		<if test="manageHypertensionCount != null "> and manage_hypertension_count = #{manageHypertensionCount}</if>
        </where>
    </select>

</mapper>