<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.AppMessageReadMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.AppMessageReadDTO" id="AppMessageReadDTOResult">
        <result property="id"    column="id"    />
        <result property="createDate"    column="create_date"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="isRead"    column="is_read"    />
        <result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectAppMessageRead">
    	select
	        id,
	        create_date,
	        notice_id,
	        is_read,
	        user_id
		from 
        	t_app_message_read
    </sql>

    <select id="selectAppMessageReadById" parameterType="int" resultMap="AppMessageReadDTOResult">
		<include refid="selectAppMessageRead"/>
		where 
        	id = #{id}
    </select>

    <select id="selectAppMessageReadList" parameterType="com.bojun.sphygmometer.dto.AppMessageReadDTO" resultMap="AppMessageReadDTOResult">
        <include refid="selectAppMessageRead"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="createDate != null "> and create_date = #{createDate}</if>
		<if test="noticeId != null  and noticeId != ''"> and notice_id = #{noticeId}</if>
		<if test="isRead != null "> and is_read = #{isRead}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
        </where>
    </select>


    <update id="updateMessageHaveRead" parameterType="com.bojun.sphygmometer.dto.AppMessageReadDTO">
        update t_app_message_read set is_read = 1 where notice_id in
            <foreach item="item" index="index" collection="noticeIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
    </update>

</mapper>