/**
 * 
 */
package com.bojun.base.manage.controller.homePage.vo;

import java.io.Serializable;
import java.util.List;

import com.bojun.system.dto.MenuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：主页菜单
 * Description：主页菜单列表返回信息
 * Author：lj
 * created： 2020年4月27日
 */
@ApiModel(value = "主页菜单", description = "主页菜单")
public class HomeMenuInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5508381353925610437L;

	@ApiModelProperty(value="菜单ID")
	private String menuId;
		
	@ApiModelProperty(value="菜单名称")
	private String menuName;
	
	@ApiModelProperty(value="上级菜单ID（一级菜单为空）")
	private String parentId;
		
	@ApiModelProperty(value="关联的系统id")
	private String systemId;

	@ApiModelProperty(value="菜单类型 1：菜单 2：按钮", required = true, example = "1")
	private Integer menuType;
	
	@ApiModelProperty(value=" 菜单级别 1：一级 2：二级 ............类推", required = true, example = "1")
	private Integer level;
	
	@ApiModelProperty(value="菜单URL", required = true, example = "xxx/index")
	private String menuUrl;
		
	@ApiModelProperty(value="权限菜单URL", required = true, example = "xxx/index")
	private String authorityUrl;
	
	@ApiModelProperty(value="显示顺序")
	private Integer showIndex;
	
	@ApiModelProperty(value="菜单图标 ")
	private String icon;
	
	
	@ApiModelProperty(value="子菜单")
	private List<MenuDTO> children;

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public Integer getMenuType() {
		return menuType;
	}

	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public String getMenuUrl() {
		return menuUrl;
	}

	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}

	public String getAuthorityUrl() {
		return authorityUrl;
	}

	public void setAuthorityUrl(String authorityUrl) {
		this.authorityUrl = authorityUrl;
	}

	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}

	

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	

	public List<MenuDTO> getChildren() {
		return children;
	}

	public void setChildren(List<MenuDTO> children) {
		this.children = children;
	}
	
}
