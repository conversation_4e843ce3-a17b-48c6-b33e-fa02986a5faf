<?xml version="1.0" encoding="UTF-8" ?>
<testsuite tests="1" failures="0" name="com.bojun.ip.test.AppTest" time="0.002" errors="0" skipped="0">
  <properties>
    <property name="idea.version" value="2020.3.3"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="sun.boot.library.path" value="F:\sofeWare\Java\jdk1.8.0_281\jre\bin"/>
    <property name="java.vm.version" value="25.281-b09"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="maven.multiModuleProjectDirectory" value="D:\code\云his\common\后端\commons_ip"/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="path.separator" value=";"/>
    <property name="guice.disable.misplaced.annotation.check" value="true"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="user.script" value=""/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="user.dir" value="D:\code\云his\common\后端\commons_ip"/>
    <property name="java.runtime.version" value="1.8.0_281-b09"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="java.endorsed.dirs" value="F:\sofeWare\Java\jdk1.8.0_281\jre\lib\endorsed"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="line.separator" value="
"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="user.variant" value=""/>
    <property name="os.name" value="Windows 10"/>
    <property name="maven.ext.class.path" value="C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\plugins\maven\lib\maven-event-listener.jar"/>
    <property name="classworlds.conf" value="C:\apache-maven-3.5.0\bin\m2.conf"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.library.path" value="F:\sofeWare\Java\jdk1.8.0_281\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;F:\sofeWare\TortoiseSVN\bin;C:\apache-maven-3.5.0\bin;F:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;F:\Users\DELL-PC\AppData\Local\Programs\Microsoft VS Code\bin;."/>
    <property name="maven.conf" value="C:\apache-maven-3.5.0/conf"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.class.version" value="52.0"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="maven.repo.local" value="D:\project\m2"/>
    <property name="os.version" value="10.0"/>
    <property name="library.jansi.path" value="C:\apache-maven-3.5.0\lib\jansi-native\windows64"/>
    <property name="user.home" value="C:\Users\<USER>\apache-maven-3.5.0\boot\plexus-classworlds-2.5.2.jar;C:\Program Files\JetBrains\IntelliJ IDEA 2020.3.3\lib\idea_rt.jar"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.home" value="F:\sofeWare\Java\jdk1.8.0_281\jre"/>
    <property name="sun.java.command" value="org.codehaus.classworlds.Launcher -Didea.version=2020.3.3 -s C:\apache-maven-3.5.0\conf\settings.xml -Dmaven.repo.local=D:\project\m2 install"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="user.language" value="zh"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.version" value="1.8.0_281"/>
    <property name="java.ext.dirs" value="F:\sofeWare\Java\jdk1.8.0_281\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.boot.class.path" value="F:\sofeWare\Java\jdk1.8.0_281\jre\lib\resources.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\lib\rt.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\lib\sunrsasign.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\lib\jsse.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\lib\jce.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\lib\charsets.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\lib\jfr.jar;F:\sofeWare\Java\jdk1.8.0_281\jre\classes"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="maven.home" value="C:\apache-maven-3.5.0"/>
    <property name="file.separator" value="\"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="sun.desktop" value="windows"/>
    <property name="sun.cpu.isalist" value="amd64"/>
  </properties>
  <testcase classname="com.bojun.ip.test.AppTest" name="testApp" time="0.002"/>
</testsuite>