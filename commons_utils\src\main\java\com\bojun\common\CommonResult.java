package com.bojun.common;


/**
 * @description
 * @author:
 * @create: 2020-10-22
 **/
public class CommonResult {

    public static final String FAIL_CODE = "FAIL";
    public static final String SUCCESS_CODE = "SUCCESS";

    private String code;
    private String msg;

    private Object object;

    public CommonResult() {
    }

    public CommonResult(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public CommonResult(String code, String msg,Object object) {
        this.code = code;
        this.msg = msg;
        this.object = object;
    }

    public static Boolean checkResult(CommonResult comm){
        if(CommonResult.FAIL_CODE.equals(comm.getCode())){
            return false;
        }else{
            return true;
        }
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }
}
