package com.bojun.base.system.mapper;

import com.bojun.system.dto.OperationLogDTO;
import com.bojun.system.entity.OperationLog;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * Model： 日志管理DAO
 * Description：日志管理DAO
 * Author：黄卫平
 * created： 2020年4月27日
 */
@Mapper
public interface SystemOperationLogMapper {
    /**
     * @Description 查询日志列表
     * <AUTHOR>
     * @param operationLogDTO
     * @return
     * List<SystemDictDTO>
     * 2020年4月27日
     */
    Page<List<OperationLogDTO>> getSystemLogList(OperationLogDTO operationLogDTO);
    
    /**
     * @Description 新增系统日志
     * <AUTHOR>
     * @param operationLog
     * @return
     * @return int
     * created：2020年6月4日
     */
	int addSystemLog(OperationLog operationLog);
}
