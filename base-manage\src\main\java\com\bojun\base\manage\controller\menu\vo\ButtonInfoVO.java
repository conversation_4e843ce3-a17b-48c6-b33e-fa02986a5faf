/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：菜单管理 
 * Description：查询产品列表返回信息
 * Author：赖水秀 
 * created： 2020年5月12日
 */
@ApiModel(value = "按钮信息", description = "按钮信息")
public class ButtonInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5508381353925610437L;

	@ApiModelProperty(value="按钮ID")
	private String menuId;
		
	@ApiModelProperty(value="按钮名称", required = true, example = "人员管理")
	private String menuName;
	
	@ApiModelProperty(value="上级菜单ID（一级菜单为空）")
	private String parentId;	
		
	@ApiModelProperty(value="权限URL", required = true, example = "xxx/index")
	private String authorityUrl;

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getAuthorityUrl() {
		return authorityUrl;
	}

	public void setAuthorityUrl(String authorityUrl) {
		this.authorityUrl = authorityUrl;
	}
	
}
