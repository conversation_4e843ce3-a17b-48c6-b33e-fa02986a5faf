
package com.bojun.system.dto;

import java.util.List;
import java.util.Map;

import com.bojun.system.entity.ManageRole;
import com.bojun.system.entity.ManageRoleOrganMenu;
/**
 * 
*Model：角色管理
*Description：角色管理DTO
*Author:刘俊
*created：2020年4月30日
 */
public class ManageRoleDTO  extends ManageRole {

   private static final long serialVersionUID = -1344026328907379635L;
   
   private Integer everyPage; 	//每页显示数
	
 	private Integer pageNum;   //页面数
 	
 	private List<ManageRoleSystemDTO> roleSystemList;//角色产品关联
 	
	private List<ManageRoleOrganDTO> roleOrganList;//角色机构关联
	
	private  List<SystemDictDTO> systemList; //角色下的产品
	
	private  List<Map> getRoleOrganDet;//角色关联机构
	
	private List<ManageRoleOrganMenu> organMenu;//接收的结构树
	
	private Integer roleCount;//角色下用户总数
	
	private Integer  organizationId;//机构Id
	
	
	private List<Map<String, Object>> roleOrganTreeList;//返回机构树ID
	
    private String organizationName;//机构名称
    private String deptName;//部门名称
    private String wardName;//病区名称
	
	
	
 	


	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getWardName() {
		return wardName;
	}

	public void setWardName(String wardName) {
		this.wardName = wardName;
	}

	public List<Map> getGetRoleOrganDet() {
		return getRoleOrganDet;
	}

	public List<Map<String, Object>> getRoleOrganTreeList() {
		return roleOrganTreeList;
	}

	public void setRoleOrganTreeList(List<Map<String, Object>> roleOrganTreeList) {
		this.roleOrganTreeList = roleOrganTreeList;
	}

	public List<ManageRoleOrganMenu> getOrganMenu() {
		return organMenu;
	}

	public void setOrganMenu(List<ManageRoleOrganMenu> organMenu) {
		this.organMenu = organMenu;
	}

	public void setGetRoleOrganDet(List<Map> getRoleOrganDet) {
		this.getRoleOrganDet = getRoleOrganDet;
	}

	public List<SystemDictDTO> getSystemList() {
		return systemList;
	}

	public void setSystemList(List<SystemDictDTO> systemList) {
		this.systemList = systemList;
	}


	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public Integer getRoleCount() {
		return roleCount;
	}

	public void setRoleCount(Integer roleCount) {
		this.roleCount = roleCount;
	}

	public List<ManageRoleOrganDTO> getRoleOrganList() {
		return roleOrganList;
	}

	public void setRoleOrganList(List<ManageRoleOrganDTO> roleOrganList) {
		this.roleOrganList = roleOrganList;
	}

	public List<ManageRoleSystemDTO> getRoleSystemList() {
		return roleSystemList;
	}

	public void setRoleSystemList(List<ManageRoleSystemDTO> roleSystemList) {
		this.roleSystemList = roleSystemList;
	}

	public Integer getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(Integer everyPage) {
		this.everyPage = everyPage;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}


  

}
