package com.bojun.organization.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2021年2月20日
*/
@Component
public class CommonConfig{ 
	
	/**
	 * 通用文件显示地址
	 */
	@Value("${base.http.url}")
	private String baseHttpUrl;
	
	/**
	 * 通用文件上传根目录
	 */
	@Value("${base.upload.path}")
	private String baseUploadPath;
	
	public String getBaseHttpUrl() {
		return baseHttpUrl;
	}

	public void setBaseHttpUrl(String baseHttpUrl) {
		this.baseHttpUrl = baseHttpUrl;
	}

	public String getBaseUploadPath() {
		return baseUploadPath;
	}

	public void setBaseUploadPath(String baseUploadPath) {
		this.baseUploadPath = baseUploadPath;
	}

}

