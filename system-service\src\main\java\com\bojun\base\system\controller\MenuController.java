/**
 * 
 */
package com.bojun.base.system.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.IMenuService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.MenuDTO;
import com.bojun.system.enums.SystemDictEnums;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

/** 
 * Model： 菜单管理模块
 * Description： 菜单管理模块
 * Author：赖水秀
 * created： 2020年4月28日
 */
@RestController
public class MenuController extends BoJunBaseController {
	
	
	private static Log log = LogFactory.getLog(MenuController.class);
	
	@Autowired
	private IMenuService menuService;
	
	/**
	 * @Description 保存菜单信息
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/saveMenu", method = RequestMethod.POST)
	public void saveMenu(@RequestBody MenuDTO menuDTO) {
		try {			
			int addNumber = menuService.saveMenu(menuDTO);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("saveMenu:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 修改菜单信息
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/updateMenu", method = RequestMethod.POST)
	public void updateMenu(@RequestBody MenuDTO menuDTO) {
		try {
			int addNumber = menuService.updateMenu(menuDTO);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateSystemDict:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
		
	
	/**
	 * @Description 查询单个菜单信息
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/getMenuById", method = RequestMethod.POST)
	public void getMenuById(@RequestParam(value="menuId") String menuId) {
		try {
			MenuDTO menuDTO = menuService.getMenuById(menuId);
			if (menuDTO == null) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(menuDTO));
		} catch (Exception e) {
			log.error("getMenuById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 分页查询菜单列表
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/getMenuPageList", method = RequestMethod.POST)
	public void getMenuPageList(@RequestBody MenuDTO menuDTO) {
		try {
			int pageNum = (null == menuDTO.getPageNum() ? 1:menuDTO.getPageNum());
			int everyPage = (null == menuDTO.getEveryPage() ? 10:menuDTO.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<MenuDTO> page = menuService.getMenuPageList(menuDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}			
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {
			log.error("getMenuPageList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 查询菜单列表
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/getMenuTreeList", method = RequestMethod.POST)
	public void getMenuTreeList(@RequestBody MenuDTO menuDTO) {
		try {			
			if(0==menuDTO.getAuthType().intValue()) {
				menuDTO.setRoleId(null);
			}
			List<MenuDTO> list = menuService.getMenuList(menuDTO);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			List<MenuDTO> treeList = toTree(list);
			if(null == treeList || treeList.isEmpty()) {
				treeList = list;
			}		
			outJson(successInfo(treeList));
		} catch (Exception e) {
			log.error("getMenuTreeList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * @Description 查询菜单按钮树
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/getMenuButtonTree", method = RequestMethod.POST)
	public void getMenuButtonTree(@RequestBody MenuDTO menuDTO) {
		try {			
			if(0==menuDTO.getAuthType().intValue()) {
				menuDTO.setRoleId(null);
			}
			List<MenuDTO> list = menuService.getMenuButtonList(menuDTO);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			List<MenuDTO> treeList = toTree(list);
			if(null == treeList || treeList.isEmpty()) {
				treeList = list;
			}		
			outJson(successInfo(treeList));
		} catch (Exception e) {
			log.error("getMenuButtonTree:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 列表转换成树形菜单
	 * <AUTHOR>
	 * @param sourceList
	 * @return
	 * List
	 * 2020年4月28日
	 */
	private static List<MenuDTO> toTree(List<MenuDTO> sourceList) {
		List<MenuDTO> rest = new ArrayList<MenuDTO>();
		Map map = (Map) sourceList.stream().collect(Collectors.toMap(MenuDTO::getMenuId, Function.identity()));

		sourceList.forEach(data -> {
			MenuDTO item = (MenuDTO) data;

			if (item.getParentId() == null || "".equals(item.getParentId())) {
				rest.add(item);
				item.setChildren(new ArrayList<>());
			}
			
			if(item.getBtnChildren()!=null && item.getBtnChildren().size()>0) {
				List<MenuDTO> btnChildren = item.getBtnChildren();
				StringBuffer menuBtnName = new StringBuffer();
				for (MenuDTO btn : btnChildren) {
					menuBtnName.append(menuBtnName.length()==0 ? btn.getMenuName() : ("," + btn.getMenuName()));
				}
				item.setMenuBtnName(menuBtnName.toString());
			}
		});
		sourceList.forEach(data -> {
			MenuDTO item = (MenuDTO) data;
			/*
			if (item.getParentId() == null || "".equals(item.getParentId())) {

			} else {
				MenuDTO parent = (MenuDTO) map.get(item.getParentId());
				if (parent.getChildren() == null) {
					parent.setChildren(new ArrayList<>());
				}
				parent.getChildren().add(item);
			}*/
			
			if (item.getParentId() != null && !"".equals(item.getParentId())) {
				MenuDTO parent = (MenuDTO) map.get(item.getParentId());
				if(null != parent) {
					if (parent.getChildren() == null) {
						parent.setChildren(new ArrayList<>());
					}
					parent.getChildren().add(item);
				}
			}
		});

		return rest;
	}
	
	
	/**
	 * @Description 单个删除信息
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/deleteMenuById", method = RequestMethod.POST)
	public void deleteMenuById(@RequestParam(value="menuId") String menuId) {
		try {			
			int deleteNumber = menuService.deleteMenuById(menuId);
			if (deleteNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("deleteMenuById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 批量删除信息
	 * <AUTHOR>
	 * void
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/batchDeleteMenu", method = RequestMethod.POST)
	public void batchDeleteMenu(@RequestBody List<String> menuIds) {
		try {
			for (String menuId : menuIds) {
				deletebyid(menuId);
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("batchDeleteMenu:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 递归删除菜单
	 * <AUTHOR>
	 * void
	 * 2020年4月29日
	 */
	private void deletebyid(String menuId) {
		List<MenuDTO> subMenuList = menuService.getSubMenuList(menuId);		
		if( null != subMenuList && !subMenuList.isEmpty() && subMenuList.size() > 0) {
			for (MenuDTO menuDTO : subMenuList) {
				deletebyid(menuDTO.getMenuId()); 
			}
		}
		int deleteNumber = menuService.deleteMenuById(menuId);
		if (deleteNumber <= 0) {
			outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
			return;
		}
	}
	
	
	/**
	 * @Description 获取菜单下的所有按钮
	 * <AUTHOR>
	 * @param menuId
	 * void
	 * 2020年5月12日
	 */
	@RequestMapping(value = "/getButtonListByMenu", method = RequestMethod.POST)
	public void getButtonListByMenu(@RequestParam(value="menuId") String menuId) {
		try {			
			List<MenuDTO> list = menuService.getButtonListByMenu(menuId);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
							
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getButtonListByMenu:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 保存/删除按钮信息
	 * <AUTHOR>
	 * @param menuDTO
	 * void
	 * 2020年5月12日
	 */
	@RequestMapping(value = "/saveButtonInfo", method = RequestMethod.POST)
	public void saveButtonInfo(@RequestBody MenuDTO menuDTO) {
		try {
			menuService.saveButtonInfo(menuDTO);			
			outJson(successInfo());
		} catch (Exception e) {
			log.error("saveButtonInfo:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description TODO
	 * <AUTHOR>
	 * @param menuDTO
	 * void
	 * 2020年6月4日
	 */
	@RequestMapping(value = "/updateMenuStatus", method = RequestMethod.POST)
	public void updateMenuStatus(@RequestBody MenuDTO menuDTO) {
		try {
			updateSubListStatus(menuDTO);			
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateMenuStatus:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 递归修改菜单状态
	 * <AUTHOR>
	 * @param menuDTO
	 * void
	 * 2020年6月4日
	 */
	private void updateSubListStatus(MenuDTO menuDTO) {
		List<MenuDTO> subMenuList = menuService.getSubMenuList(menuDTO.getMenuId());		
		if( null != subMenuList && !subMenuList.isEmpty() && subMenuList.size() > 0) {
			for (MenuDTO menu : subMenuList) {
				MenuDTO temp = new MenuDTO();
				temp.setMenuId(menu.getMenuId());
				temp.setIsDisplay(menuDTO.getIsDisplay());
				updateSubListStatus(temp); 
			}
		}
		int updateNum = menuService.updateMenu(menuDTO);
		if (updateNum <= 0) {
			outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
			return;
		}
	}

}
