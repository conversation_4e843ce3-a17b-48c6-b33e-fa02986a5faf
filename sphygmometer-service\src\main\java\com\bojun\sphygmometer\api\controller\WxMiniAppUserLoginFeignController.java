package com.bojun.sphygmometer.api.controller;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaUserInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.page.Results;
import com.bojun.sphygmometer.api.WxMiniAppUserLoginFeignClient;
import com.bojun.sphygmometer.common.WxMiniAppConstants;
import com.bojun.sphygmometer.dto.SphygmometerUserDTO;
import com.bojun.sphygmometer.entity.SphygmometerDevice;
import com.bojun.sphygmometer.entity.SphygmometerUser;
import com.bojun.sphygmometer.service.SphygmometerDeviceService;
import com.bojun.sphygmometer.service.SphygmometerUserService;
import com.bojun.utils.BeanUtil;
import com.bojun.utils.MD5Util;
import com.bojun.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Optional;

/**
 * 高血压用户信息表FeignController
 * 
 * <AUTHOR>
 * @date 2021-03-19 14:15:25
 */
@Slf4j
@RestController
public class WxMiniAppUserLoginFeignController extends BaseFeignController implements WxMiniAppUserLoginFeignClient {

    @Autowired
    private WxMaService wxService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SphygmometerUserService sphygmometerUserService;

    @Autowired
    private SphygmometerDeviceService sphygmometerDeviceService;

    @PostMapping(PREFIX + "/loginByAuth")
    @Override
    public Results<SphygmometerUserDTO> loginByAuth(@RequestBody SphygmometerUserDTO sphygmometerUserDTO) throws Exception {
        SphygmometerUserDTO result = new SphygmometerUserDTO();
        log.info("loginByAuth0:" + sphygmometerUserDTO);
        WxMaJscode2SessionResult wxResult = getOpenId(sphygmometerUserDTO.getCode(), wxService);
        log.info("loginByAuth1:" + wxResult);
        //获取微信信息
        WxMaUserInfo wxMaUserInfo = wxService.getUserService().getUserInfo(wxResult.getSessionKey(),
                sphygmometerUserDTO.getUserInfoEncryptedData(), sphygmometerUserDTO.getUserInfoIv());
        log.info("loginByAuth2:" + wxMaUserInfo);
        if(!redisUtil.hasKey(WxMiniAppConstants.WX_OPENID_KEY + wxResult.getOpenid())){
            WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxService.getUserService().getPhoneNoInfo(wxResult.getSessionKey(),
                    sphygmometerUserDTO.getEncryptedData(), sphygmometerUserDTO.getIv());
            //根据手机号查询是否有该用户
            SphygmometerUser sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery().
                    eq(SphygmometerUser::getMobile,wxMaPhoneNumberInfo.getPhoneNumber()));
            //根据UnionId查询是否有该用户
            SphygmometerUser sphygmometerUser1 = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery().
                    eq(SphygmometerUser::getUnionId,wxResult.getUnionid()));
            //验证用户手机号是否正确
            if(Optional.ofNullable(sphygmometerUser1).isPresent() &&
                    null != sphygmometerUser1.getMobile() &&
                    !wxMaPhoneNumberInfo.getPhoneNumber().equals(sphygmometerUser1.getMobile())){
                return Results.fail("该微信已绑定手机号，请使用绑定过的手机号");
            }
            result = initUserInfo(wxResult, sphygmometerUserDTO,sphygmometerUser,wxMaUserInfo,wxMaPhoneNumberInfo);
            if(null != sphygmometerUser){
                sphygmometerUser.setHeadPortrait(wxMaUserInfo.getAvatarUrl());
                sphygmometerUserService.updateById(sphygmometerUser);
            }
        }else{
            SphygmometerUser sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery().
                    eq(SphygmometerUser::getMobile,wxResult.getOpenid()));
            if(null != sphygmometerUser){
                sphygmometerUser.setHeadPortrait(wxMaUserInfo.getAvatarUrl());
                sphygmometerUserService.updateById(sphygmometerUser);
            }
            String string = redisUtil.get(WxMiniAppConstants.WX_OPENID_KEY + wxResult.getOpenid()).toString();
            result = JSON.parseObject(string, SphygmometerUserDTO.class);
        }
        return Results.data(result);
    }

    @PostMapping(PREFIX + "/loginBySilent")
    @Override
    public SphygmometerUserDTO loginBySilent(@RequestBody SphygmometerUserDTO sphygmometerUserDTO) {
        SphygmometerUserDTO result = new SphygmometerUserDTO();
        try{
            WxMaJscode2SessionResult wxResult = getOpenId(sphygmometerUserDTO.getCode(), wxService);
            if (!redisUtil.hasKey(WxMiniAppConstants.WX_OPENID_KEY + wxResult.getOpenid())) {
                SphygmometerUser sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery().
                        eq(SphygmometerUser::getWxappOpenId,wxResult.getOpenid()));
                result =  BeanUtil.deepCopyProperties(sphygmometerUser, SphygmometerUserDTO.class);
            } else {
                String string = redisUtil.get(WxMiniAppConstants.WX_OPENID_KEY + wxResult.getOpenid()).toString();
                result = JSON.parseObject(string, SphygmometerUserDTO.class);
            }
            return result;
        }catch (Exception exception){
            exception.printStackTrace();
        }
       return null;
    }


    /**
     * 初始化用户信息
     * @param wxResult
     * @param miniAppLoginVo
     */
    private SphygmometerUserDTO initUserInfo(WxMaJscode2SessionResult wxResult, SphygmometerUserDTO miniAppLoginVo,
                                             SphygmometerUser sphygmometerUser,WxMaUserInfo wxMaUserInfo,WxMaPhoneNumberInfo wxMaPhoneNumberInfo) {
        if(Optional.ofNullable(sphygmometerUser).isPresent() && StringUtils.isBlank(sphygmometerUser.getWxappOpenId())){
            sphygmometerUser.setWxappOpenId(wxResult.getOpenid());
            sphygmometerUser.setMobile(wxMaPhoneNumberInfo.getPhoneNumber());
            sphygmometerUser.setWsToken(MD5Util.getMD5code(sphygmometerUser.getMobile()+ System.currentTimeMillis()));
            sphygmometerUserService.updateById(sphygmometerUser);
            return BeanUtil.deepCopyProperties(sphygmometerUser, SphygmometerUserDTO.class);
        }
        log.info("-----------sphygmometerUser-----"+JSONObject.toJSONString(sphygmometerUser));
        if (!Optional.ofNullable(sphygmometerUser).isPresent()) { //新增用户
            sphygmometerUser = new SphygmometerUser();
            sphygmometerUser.setMobile(wxMaPhoneNumberInfo.getPhoneNumber());
            sphygmometerUser.setNickName(wxMaUserInfo.getNickName());
            sphygmometerUser.setCity(wxMaUserInfo.getCity());
            sphygmometerUser.setCountry(wxMaUserInfo.getCountry());
            sphygmometerUser.setProvince(wxMaUserInfo.getProvince());
            sphygmometerUser.setWxappOpenId(wxResult.getOpenid());
            if(wxMaUserInfo.getGender()!= null ){
                sphygmometerUser.setGender(Integer.valueOf(wxMaUserInfo.getGender()));
            }
            sphygmometerUser.setHeadPortrait(wxMaUserInfo.getAvatarUrl());
            sphygmometerUser.setUnionId(wxResult.getUnionid());
            if(null != miniAppLoginVo.getBindDeviceId()){
                SphygmometerDevice sphygmometerDevice = sphygmometerDeviceService.getById(miniAppLoginVo.getBindDeviceId());
                if(Optional.ofNullable(sphygmometerDevice).isPresent()){
                    //organization_id是投放地点，relation_org_id关联社康（设备的管理机构）
                    sphygmometerUser.setManageOrganizationId(sphygmometerDevice.getRelationOrgId());
                    sphygmometerUser.setRegisterOrganizationId(sphygmometerDevice.getRelationOrgId());
                }
            }
            sphygmometerUser.setRegisterSource(3);
            sphygmometerUser.setRegisterTime(new Date());
            sphygmometerUser.setWsToken(MD5Util.getMD5code(sphygmometerUser.getMobile()+ System.currentTimeMillis()));
            log.info("-----------saveSphygmometerUser-----"+JSONObject.toJSONString(sphygmometerUser));
            sphygmometerUserService.save(sphygmometerUser);
            return BeanUtil.deepCopyProperties(sphygmometerUser, SphygmometerUserDTO.class);
        }
        return BeanUtil.deepCopyProperties(sphygmometerUser, SphygmometerUserDTO.class);
    }

    /**
     * 获取openID
     *
     * @param code
     * @param wxService
     * @return
     */
    private WxMaJscode2SessionResult getOpenId(String code, WxMaService wxService) throws WxErrorException {
        WxMaJscode2SessionResult wxResult = wxService.getUserService().getSessionInfo(code);
        redisUtil.set(WxMiniAppConstants.WX_SESSION_KEY + wxResult.getOpenid(), wxResult.getSessionKey());
        return wxResult;
    }
}
