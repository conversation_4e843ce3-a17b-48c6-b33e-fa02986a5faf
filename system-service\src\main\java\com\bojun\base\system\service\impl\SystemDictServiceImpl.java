/**
 * 
 */
package com.bojun.base.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.MenuMapper;
import com.bojun.base.system.mapper.SystemDictMapper;
import com.bojun.base.system.service.ISystemDictService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.MenuDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.dto.SystemDictTypeDTO;
import com.github.pagehelper.Page;


/**
 * Model： 产品管理模块服务实现类
 * Description：产品管理模块服务实现类
 * Author：赖水秀
 * created： 2020年4月27日
 */
@Service
public class SystemDictServiceImpl implements ISystemDictService {

	@Autowired
	private SystemDictMapper systemDictMapper;
	@Autowired
	private MenuMapper menuMapper;
	
	/**
	 * @Description 查询系统类型下拉数据列表
	 * <AUTHOR>
	 * @return
	 * List<SystemDictTypeDTO>
	 * 2020年4月27日
	 */
	@Override
	public List<SystemDictTypeDTO> getSystemDictTypeList() {		
		return systemDictMapper.getSystemDictTypeList();
	}
	
	/**
	 * @Description 查询系统类型下拉数据列表
	 * <AUTHOR>
	 * @return
	 * List<SystemDictTypeDTO>
	 * 2020年4月27日
	 */
	@Override
	public int saveSystemDict(SystemDictDTO systemDictDto) {
		return systemDictMapper.saveSystemDict(systemDictDto);
	}
	
	/**
	 * @Description 根据单个产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * SystemDictDTO
	 * 2020年4月27日
	 */
	@Override
	public SystemDictDTO getSystemDictById(String systemId) {		
		return systemDictMapper.getSystemDictById(systemId);
	}
	
	/**
	 * @Description 查询产品信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月27日
	 */
	@Override
	public Page<SystemDictDTO> getSystemDictList(SystemDictDTO systemDictDto) {		
		return systemDictMapper.getSystemDictList(systemDictDto);
	}
	
	/**
	 * @description: 查询产品名称
	 * @author: 赖允翔
	 * @date: 2020/4/27
	 * @Param:
	 * @return:
	 */
	@Override
	public List<SystemDictDTO> getSystemDicts(SystemDictDTO systemDictDTO) {

		return systemDictMapper.getSystemDicts(systemDictDTO);
	}
	

	@Override
	public int updateSystemDict(SystemDictDTO systemDictDto) {		
		return systemDictMapper.updateSystemDict(systemDictDto);
	}
	/**
	 * @Description 根据角色ID查询产品列表树
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月28日
	 */
	@Override
	public List<SystemDictDTO> getSystemTreeByRoleId(SystemDictDTO systemDictDto) {
		//查询所有有权限产品
		
		List<SystemDictDTO> systemList=new ArrayList<SystemDictDTO>();
		//超级管理员
		if(systemDictDto.getAuthType().equals(0)||0==systemDictDto.getAuthType()) {
			systemDictDto.setIsEnabled(1);
			systemList = systemDictMapper.getSystemDictList(systemDictDto);
		}else {//普通管理员
			systemList = systemDictMapper.getSystemDictByRoleId(systemDictDto);
		}
		
		if (null==systemList  || systemList.isEmpty()) {
			return null;
		}
		//查询产品下有权限的菜单
		for (SystemDictDTO systemDictDTO2 : systemList) {
			MenuDTO menuDTO=new MenuDTO();
			List<MenuDTO> menuList=new ArrayList<MenuDTO>();
			//超级管理员
			if(systemDictDto.getAuthType().equals(0)||0==systemDictDto.getAuthType()) {
				 menuDTO.setSystemId(systemDictDTO2.getSystemId());
				 menuDTO.setIsDisplay(systemDictDto.getIsDisplay());
				 menuList = menuMapper.getMenuButtonList(menuDTO);
			}else {//普通管理员
				menuDTO.setRoleId(systemDictDTO2.getRoleId());
				menuDTO.setSystemId(systemDictDTO2.getSystemId());
				 menuList = menuMapper.getMenuRoleList(menuDTO);
			}
			//菜单树
			if(null!=menuList&&!menuList.isEmpty()) {
				List<MenuDTO> treeList = systemToTree(menuList);
				systemDictDTO2.setTreeList(treeList);
			}
			
		}
		return systemList;
	}
	/**
	 * @Description 根据角色ID查询产品列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月28日
	 */
	@Override
	public List<SystemDictDTO> getSystemDictByRoleId(SystemDictDTO systemDictDto) {
		//查询所有有权限产品
		List<SystemDictDTO> systemList = systemDictMapper.getSystemDictByRoleId(systemDictDto);
		if (systemList == null || systemList.isEmpty()) {
			return null;
		}
		//查询产品下有权限的菜单
		for (SystemDictDTO systemDictDTO2 : systemList) {
			MenuDTO menuDTO=new MenuDTO();
			menuDTO.setRoleId(systemDictDTO2.getRoleId());
			menuDTO.setSystemId(systemDictDTO2.getSystemId());
			List<MenuDTO> menuList = menuMapper.getMenuRoleList(menuDTO);
			//List<MenuDTO> treeList = systemToTree(menuList);
			systemDictDTO2.setTreeList(menuList);
		}
		return systemList;
	}


	/**
	 * @Description 查询所有产品信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * 2020年4月30日
	 */
	@Override
	public List<SystemDictDTO> getAllSystemDictList() {		
		return systemDictMapper.getAllSystemDictList();
	}
	/**
	 * @Description 列表转换成树形菜单
	 * <AUTHOR>
	 * @param sourceList
	 * @return
	 * List
	 * 2020年4月28日
	 */
	private static List<MenuDTO> systemToTree(List<MenuDTO> sourceList) {
		List<MenuDTO> rest = new ArrayList<MenuDTO>();
		Map map = (Map) sourceList.stream().collect(Collectors.toMap(MenuDTO::getMenuId, Function.identity()));

		sourceList.forEach(data -> {
			MenuDTO item = (MenuDTO) data;

			if (item.getParentId() == null || "".equals(item.getParentId())) {
				rest.add(item);
				item.setChildren(new ArrayList<>());
			}
		});
		sourceList.forEach(data -> {
			MenuDTO item = (MenuDTO) data;
			/*
			if (item.getParentId() == null || "".equals(item.getParentId())) {

			} else {
				MenuDTO parent = (MenuDTO) map.get(item.getParentId());
				if (parent.getChildren() == null) {
					parent.setChildren(new ArrayList<>());
				}
				parent.getChildren().add(item);
			}*/
			
			if (item.getParentId() != null && !"".equals(item.getParentId())) {
				System.out.println("ParentId------------------------------"+item.getParentId());
				MenuDTO parent = (MenuDTO) map.get(item.getParentId());
				if (null != parent) {
					if (null == parent.getChildren()) {
						parent.setChildren(new ArrayList<>());
					}
					parent.getChildren().add(item);
				}
			}
		});

		return rest;
	}

}
