
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.FormQuestionItemDTO;



/**
 * 
*Model：满意度问卷题目子项目信息表
*Description：满意度问卷题目子项目信息表
*Author:李欣颖
*created：2020年5月13日
 */
@Mapper
public interface FormQuestionItemMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	int queryFormQuestionItemCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询满意度问卷题目子项目信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionItemDTO>
     * created：2020年5月13日
	 */
	List<FormQuestionItemDTO> getFormQuestionItem(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	Integer addFormQuestionItem(FormQuestionItemDTO formQuestionItemDTO);

	/**
	 * 
	 * @Description 删除满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	Integer deleteFormQuestionItem(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	Integer updateFormQuestionItem(FormQuestionItemDTO formQuestionItemDTO);

	/**
	 * 
	 * @Description 查询单个满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionItemDTO
	 * created：2020年5月13日
	 */
	FormQuestionItemDTO getFormQuestionItemById(Map<String, Object> mapPara);


	
}
