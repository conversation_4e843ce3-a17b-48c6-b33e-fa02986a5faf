package com.bojun.health.promotion.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/7 14:42
 */
@ApiModel(value = "查询资讯-1", description = "查询资讯-1")
public class SelectNewsVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "ID")
    private int newsId;
    @ApiModelProperty(value = "话题")
    private String topicLabel;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "科室ID")
    private int deptId;
    @ApiModelProperty(value = "排序下标")
    private int showIndex;
    @ApiModelProperty(value = "首页排序")
    private int homeIndex;
    @ApiModelProperty(value = "是否在健康首页  0： 否  1：是")
    private int isHome;
    @ApiModelProperty(value = "审核状态  1：审核通过  2：待审核  3：审核未通过")
    private int authStatus;
    @ApiModelProperty(value = "状态  1:已发布  2:待发布  3：已下架")
    private int status;
    @ApiModelProperty(value = "发布方式  1:审核通过后立即发布   2:手动发布")
    private int publishType;
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date publishTime;
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    @ApiModelProperty(value = "阅读量")
    private Integer readNumber;
    @ApiModelProperty(value = "话题")
    private String topicName;
    @ApiModelProperty(value = "封面图")
    private String url;
    @ApiModelProperty(value = "1患者app2医生app3全部")
    private Integer showType;
    @ApiModelProperty(value = "资讯类型1文章2视频3其他")
    private Integer newsType;
    @ApiModelProperty(value = "收藏人数")
    private Integer collectionNumber;
    @ApiModelProperty(value = "1健康资讯2医学资讯")
    private Integer sort;
    
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public Integer getReadNumber() {
        return readNumber;
    }

    public void setReadNumber(Integer readNumber) {
        this.readNumber = readNumber;
    }

    public int getNewsId() {
        return newsId;
    }

    public void setNewsId(int newsId) {
        this.newsId = newsId;
    }

    public String getTopicLabel() {
        return topicLabel;
    }

    public void setTopicLabel(String topicLabel) {
        this.topicLabel = topicLabel;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getDeptId() {
        return deptId;
    }

    public void setDeptId(int deptId) {
        this.deptId = deptId;
    }

    public int getShowIndex() {
        return showIndex;
    }

    public void setShowIndex(int showIndex) {
        this.showIndex = showIndex;
    }

    public int getHomeIndex() {
        return homeIndex;
    }

    public void setHomeIndex(int homeIndex) {
        this.homeIndex = homeIndex;
    }

    public int getIsHome() {
        return isHome;
    }

    public void setIsHome(int isHome) {
        this.isHome = isHome;
    }

    public int getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(int authStatus) {
        this.authStatus = authStatus;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getPublishType() {
        return publishType;
    }

    public void setPublishType(int publishType) {
        this.publishType = publishType;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

	public Integer getShowType() {
		return showType;
	}

	public void setShowType(Integer showType) {
		this.showType = showType;
	}

	public Integer getNewsType() {
		return newsType;
	}

	public void setNewsType(Integer newsType) {
		this.newsType = newsType;
	}

	public Integer getCollectionNumber() {
		return collectionNumber;
	}

	public void setCollectionNumber(Integer collectionNumber) {
		this.collectionNumber = collectionNumber;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

    
}
