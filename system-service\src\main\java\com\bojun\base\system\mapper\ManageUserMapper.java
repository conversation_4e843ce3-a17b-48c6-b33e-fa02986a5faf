
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bojun.healthcare.dto.DoctorInfoDTO;
import com.bojun.healthcare.entity.dto.AppUserDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.entity.ManageUser;
import com.github.pagehelper.Page;

/**
*Model：系统管理员用户信息DAO
*Description：系统管理员用户信息DAO
*Author:段德鹏
*created：2020年4月24日
 */
@Mapper
public interface ManageUserMapper {

	
	/**
	 * @Description 根据系统管理员账号查询管理员信息
	 * <AUTHOR>
	 * @param paramsMap 账号
	 * @return 返回管理员信息
	 * ManageUser
	 */
	ManageUserDTO getManageUserByParams(ManageUserDTO manageUserDTO);
	
	/**
	 * 
	 * @Description 更新融云token
	 * <AUTHOR>
	 * @param manageUser
	 * @return
	 * Integer
	 * 2021年1月20日
	 */
	Integer updateRongToken(ManageUserDTO manageUser);
	
	/**
	 * @Description 更新管理员用户信息
	 * <AUTHOR>
	 * @param manageUser
	 * @return
	 * @return int
	 * created：2020年4月24日
	 */
	int updateManageUserByUserId(ManageUser manageUser);
	
	/**
	 * @Description 修改互联网医院移动端用户信息是否拥有互联网医院权限
	 * <AUTHOR>
	 * @param Map
	 * @return
	 * @return int
	 * created：2020年11月26日
	 */
	int updateAppUserForInternetHis(Map<String,Object> paramMap);
	
	/**
	 * 
	 * @Description 获取医生信息
	 * <AUTHOR>
	 * @param paramMap
	 * @return
	 * DoctorInfoDTO
	 * 2021年2月25日
	 */
	public DoctorInfoDTO getDoctorById(Map<String,Object> paramMap);
	
	/**
	 * 
	 * @Description 获取医生app信息
	 * <AUTHOR>
	 * @param paramMap
	 * @return
	 * DoctorInfoDTO
	 * 2021年2月25日
	 */
	public AppUserDTO getAppUser(Map<String,Object> paramMap);
	
	/**
	 * 
	 * @Description 新增医护app账户
	 * <AUTHOR>
	 * @param AppUserDTO
	 * @return
	 * 2021年2月25日
	 */
	public Integer addAppUser(AppUserDTO appUser);
	
	/**
	 * @Description 新增用户
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int addManageUser(ManageUser manageUser);
	
	/**
	 * @Description 修改用户
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int updateManageUser(ManageUser manageUser);
	
	/**
	 * 
	 * @Description TODO
	 * <AUTHOR>
	 * @param manageUser
	 * @return
	 * int
	 * 2021年3月3日
	 */
	int updateAppUser(ManageUser manageUser);

	/**
	 * @Description 重置用户密码
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int resetManageUserPassword(ManageUser manageUser);
	/**
	 * @Description 查询用户列表
	 * <AUTHOR>
	 * @param ManageUserDTO
	 * @return
	 * List<ManageUserDTO>
	 * 2020年4月27日
	 */
	Page<ManageUserDTO> getManageUserList(ManageUserDTO manageUserDTO);
	
	/**
	 * @Description 启用禁用用户状态
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int enableDisableUser(ManageUser manageUser);
	
	/**
	 * @Description 修改用户密码
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int updateUserPasswords(ManageUser manageUser);

	List<ManageUserDTO> getManageUserListByOrgId(@Param(value = "orgId") Integer orgId);
	/**
	 * @Description 通过身份证获取用户信息
	 * <AUTHOR>
	 * @param [manageUserDTO]
	 * @return com.bojun.system.dto.ManageUserDTO
	 * @date：2020-07-23日
	 */
	ManageUserDTO getManageUserByIdNo(ManageUserDTO manageUserDTO);


	/**
	 * @Description 通过身份证获取用户信息
	 * <AUTHOR>
	 * @param [manageUserDTO]
	 * @return com.bojun.system.dto.ManageUserDTO
	 * @date：2020-07-23日
	 */
	ManageUserDTO getManageUserByMobile(String mobile);
	
	/**
	 * 
	 * @Description 查询机构基本信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年9月23日
	 */
	List<OrganizationInfoDTO> getOrganizationList(OrganizationInfoDTO organizationInfoDTO);
	
	/**
	 * 
	 * @Description 确认用户是否存在于人事系统
	 * <AUTHOR>
	 * @param condition
	 * @return
	 * Integer
	 * 2020年9月27日
	 */
	Integer checkUserExistEmployee(Map<String,Object> condition);

	/**
	 * 查询运维权限和角色权限
	 * @return
	 */
	Page<ManageUserDTO> getManageRoleUserList();


	Page<ManageUserDTO> getManageRoleUserListBySystemId(ManageUserDTO manageUser);


}
