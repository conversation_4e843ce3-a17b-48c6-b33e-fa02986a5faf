package com.bojun.base.system.api.controller;

import com.bojun.base.system.api.MessageNotificationReceiverV2FeignClient;
import com.bojun.base.system.service.MessageNotificationReceiverV2Service;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.system.dto.MessageNotificationReceiverV2DTO;
import com.bojun.system.entity.MessageNotificationReceiverV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息通知接收人信息表FeignController
 * 
 * <AUTHOR>
 * @date 2021-06-07 15:53:16
 */
@RestController
public class MessageNotificationReceiverV2FeignController extends BaseFeignController implements MessageNotificationReceiverV2FeignClient
{
    @Autowired
    private MessageNotificationReceiverV2Service messageNotificationReceiverService;

	/**
     * 查询消息通知接收人信息表分页列表
     */
    @PostMapping(PREFIX + "/page")
    public Results<PageData<MessageNotificationReceiverV2DTO>> page(@RequestBody MessageNotificationReceiverV2DTO messageNotificationReceiverV2DTO){
        startPage(messageNotificationReceiverV2DTO.getPageNum(), messageNotificationReceiverV2DTO.getEveryPage());
        List<MessageNotificationReceiverV2DTO> list = messageNotificationReceiverService.selectMessageNotificationReceiverList(messageNotificationReceiverV2DTO);
        return Results.list(getPageData(list));
    }

    /**
     * 查询消息通知接收人信息表列表
     */
    @PostMapping(PREFIX + "/list")
    public Results<List<MessageNotificationReceiverV2DTO>> list(@RequestBody MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO){
        List<MessageNotificationReceiverV2DTO> list = messageNotificationReceiverService.selectMessageNotificationReceiverList(MessageNotificationReceiverV2DTO);
        return Results.list(list);
    }

    /**
     * 获取消息通知接收人信息表详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<MessageNotificationReceiverV2DTO> getInfo(@RequestParam("id") Integer id){
        MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO = messageNotificationReceiverService.selectMessageNotificationReceiverById(id);
        return Results.data(MessageNotificationReceiverV2DTO);
    }

    /**
     * 新增消息通知接收人信息表DTO
     */
    @PostMapping(PREFIX + "/addDTO")
    public Results addDTO(@RequestBody MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO){
    	Integer num = messageNotificationReceiverService.insertMessageNotificationReceiver(MessageNotificationReceiverV2DTO);
        return Results.opResult(num);
    }

    /**
     * 修改消息通知接收人信息表DTO
     */
    @PostMapping(PREFIX + "/updateSystemMessageHaveRead")
    public Results updateSystemMessageHaveRead(@RequestBody MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO)  {
        Integer num = messageNotificationReceiverService.updateMessageNotificationReceiver(MessageNotificationReceiverV2DTO);
        return Results.opResult(num);
    }
    
    /**
     * 新增消息通知接收人信息表
     */
    @PostMapping(PREFIX + "/add")
    public Results add(@RequestBody MessageNotificationReceiverV2 messageNotificationReceiver){
        Integer num = messageNotificationReceiverService.insertMessageNotificationReceiver(messageNotificationReceiver);
        return Results.opResult(num);
    }

    /**
     * 修改消息通知接收人信息表
     */
    @PostMapping(PREFIX + "/edit")
    public Results edit(@RequestBody MessageNotificationReceiverV2 messageNotificationReceiver){
        Integer num = messageNotificationReceiverService.updateMessageNotificationReceiver(messageNotificationReceiver);
        return Results.opResult(num);
    }

    /**
     * 删除消息通知接收人信息表，多个以逗号分隔
     */
    @GetMapping(PREFIX + "/removeByIds")
    public Results removeByIds(@RequestParam("ids") String ids) {
        Integer num = messageNotificationReceiverService.deleteMessageNotificationReceiverByIds(ids);
        return Results.opResult(num);
    }
}
