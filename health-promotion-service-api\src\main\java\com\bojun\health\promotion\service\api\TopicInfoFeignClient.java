package com.bojun.health.promotion.service.api;

import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.service.api.hystrix.TopicInfoFeignClientHystrix;
import com.bojun.page.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 13:23
 */
@FeignClient(name = "health-promotion-service", fallback = TopicInfoFeignClientHystrix.class)
public interface TopicInfoFeignClient {

    @PostMapping(value = "/getNewsTopicList")
    Results<List<TopicInfoDTO>> getNewsTopicList(TopicInfoDTO topicInfoDTO);


    @PostMapping(value = "/addNewsTopic")
    Integer addNewsTopic(TopicInfoDTO record);


    @PostMapping(value = "/updateNewsTopic")
    Integer updateNewsTopic(TopicInfoDTO record);

    @PostMapping(value = "/deleteNewsTopic")
    Integer deleteNewsTopic(TopicInfoDTO record) throws Exception;

    @GetMapping(value = "/getById")
    public TopicInfoDTO getById(@RequestParam("topicId") Integer topicId);
}
