package com.bojun.system.entity;

import java.io.Serializable;

import com.sun.star.util.Date;

/**
 * 
*Model：角色管理
*Description：角色管理实体类
*Author:刘俊
*created：2020年4月30日
 */
public class ManageRole implements Serializable {

   private static final long serialVersionUID = -1344026328907379635L;


      private String roleId; // 角色ID（uuid）
      private String roleName; // 角色名称
      private Integer isEnabled; // 启用状态： 0：否  1：是
      private String roleDescribe; // 角色说明
      private Integer createUserId; // 添加角色的用户ID
      private Date createTime; // 创建时间
      private Integer roleType;//角色类型  1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维
      private Integer dataPermissions;//数据权限  1: 角色机构  2：账号机构
      
    
      
     
      
    
    public Integer getDataPermissions() {
		return dataPermissions;
	}

	public void setDataPermissions(Integer dataPermissions) {
		this.dataPermissions = dataPermissions;
	}

	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getRoleId()
    {
        return roleId;
    }
    
    public void setRoleId(String roleId)
    {
        this.roleId = roleId;
    }   	
      
    
    public String getRoleName()
    {
        return roleName;
    }
    
    public void setRoleName(String roleName)
    {
        this.roleName = roleName;
    }   	
      
    
    public Integer getIsEnabled()
    {
        return isEnabled;
    }
    
    public void setIsEnabled(Integer isEnabled)
    {
        this.isEnabled = isEnabled;
    }   	
      
    
    public String getRoleDescribe()
    {
        return roleDescribe;
    }
    
    public void setRoleDescribe(String roleDescribe)
    {
        this.roleDescribe = roleDescribe;
    }   	
      
    
    public Integer getCreateUserId()
    {
        return createUserId;
    }
    
    public void setCreateUserId(Integer createUserId)
    {
        this.createUserId = createUserId;
    }   	
      
    
   
    

}
