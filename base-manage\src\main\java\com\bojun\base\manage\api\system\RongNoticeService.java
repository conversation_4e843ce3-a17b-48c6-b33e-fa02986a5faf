package com.bojun.base.manage.api.system;

import com.bojun.base.manage.api.system.hystrix.RongNoticeServiceHystrix;
import com.bojun.system.dto.RongNoticeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知服务类
 * @Description: 基础控台融云消息通知服务类
 * @since 2020-12-04
 */
@FeignClient(name = "system-service", fallback = RongNoticeServiceHystrix.class)
public interface RongNoticeService {
    @PostMapping(value = "/saveRongNotice")
    String saveRongNotice(RongNoticeDTO rongnoticeDTO);

    @PostMapping(value = "/deleteRongNotice")
    String deleteRongNotice(@RequestBody List<String> noticeId);

    @PostMapping(value = "/getRongNotice")
    String getRongNotice(RongNoticeDTO rongnoticeDTO);

    @PostMapping(value = "/getRongNoticeById")
    String getRongNoticeById(RongNoticeDTO rongnoticeDTO);

    @PostMapping(value = "/updateRongNoticeById")
    String updateRongNoticeById(RongNoticeDTO rongnoticeDTO);
}
