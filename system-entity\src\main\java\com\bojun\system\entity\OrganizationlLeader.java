package com.bojun.system.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;

/**
 * 
 * Model：医院领导信息 Description：医院领导信息实体类 Author:肖泽权 created：2020年1月12日
 */
@ApiModel(value = "医院领导信息", description = "医院领导信息")
public class OrganizationlLeader implements Serializable {

	private static final long serialVersionUID = -1344026328907379635L;

	private Integer id; //	
	private String employeeId; // 员工id	
	private String   realName ;
	private Integer   general ;
	private String     deptId;
	private String     isRehire;
	private String     nationName;
	private String     isNumber;
	private String     postName;
	private String     postId;
	private String     postLevel;
	private String     age;
	private String     entryDateString;
	private String     educationName;
	private String     graduateSchool;
	
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(String employeeId) {
		this.employeeId = employeeId;
	}
	public String getRealName() {
		return realName;
	}
	public void setRealName(String realName) {
		this.realName = realName;
	}
	public Integer getGeneral() {
		return general;
	}
	public void setGeneral(Integer general) {
		this.general = general;
	}
	public String getDeptId() {
		return deptId;
	}
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}
	public String getIsRehire() {
		return isRehire;
	}
	public void setIsRehire(String isRehire) {
		this.isRehire = isRehire;
	}
	public String getNationName() {
		return nationName;
	}
	public void setNationName(String nationName) {
		this.nationName = nationName;
	}
	public String getIsNumber() {
		return isNumber;
	}
	public void setIsNumber(String isNumber) {
		this.isNumber = isNumber;
	}
	public String getPostName() {
		return postName;
	}
	public void setPostName(String postName) {
		this.postName = postName;
	}
	public String getPostId() {
		return postId;
	}
	public void setPostId(String postId) {
		this.postId = postId;
	}
	public String getPostLevel() {
		return postLevel;
	}
	public void setPostLevel(String postLevel) {
		this.postLevel = postLevel;
	}
	public String getAge() {
		return age;
	}
	public void setAge(String age) {
		this.age = age;
	}
	public String getEntryDateString() {
		return entryDateString;
	}
	public void setEntryDateString(String entryDateString) {
		this.entryDateString = entryDateString;
	}
	public String getEducationName() {
		return educationName;
	}
	public void setEducationName(String educationName) {
		this.educationName = educationName;
	}
	public String getGraduateSchool() {
		return graduateSchool;
	}
	public void setGraduateSchool(String graduateSchool) {
		this.graduateSchool = graduateSchool;
	}
	
	
	
}
