/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：省份信息
 * Author：赖水秀
 * created： 2020年5月7日
 */
@ApiModel(value = "省份信息", description = "查询省份返回信息")
public class ProvinceDictVO implements Serializable {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = 9091385817087116458L;

	@ApiModelProperty(value="省份code")
	private String provinceCode;

	@ApiModelProperty(value="省份名称")
	private String provinceName;

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

}
