package com.bojun.system.dto;

import com.bojun.system.entity.RongNotice;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知拓展
 * @Description: 基础控台融云消息通知拓展
 * @since 2020-12-04
 */
@Data
public class RongNoticeDTO extends RongNotice {
    @ApiModelProperty(value = "开始时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private java.sql.Date startTime; //定时发布时间

    @ApiModelProperty(value = "结束时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private java.sql.Date endTime; //定时发布时间

    @ApiModelProperty(value = "机构ID")
    private Integer organizationId;
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    @ApiModelProperty(value = "条数")
    private Integer everyPage;

    private Integer id;

    @ApiModelProperty(value = "关键字", example = " ")
    private String keyWord; //消息标题

    private String noticeTypeName;
    
    private Integer noticeFlag;//通知标识
    
    private String orderId;//订单id
    
    private String doctorId;//医生id
}
