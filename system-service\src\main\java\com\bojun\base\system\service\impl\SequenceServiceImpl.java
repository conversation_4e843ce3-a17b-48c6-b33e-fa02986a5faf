package com.bojun.base.system.service.impl;

import com.bojun.base.system.mapper.SequenceMapper;
import com.bojun.base.system.service.ISequenceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SequenceServiceImpl implements ISequenceService {
    @Autowired
    private SequenceMapper sequenceMapper;

    @Override
    public int getNextVal(String seqName) {
        return sequenceMapper.getNextVal(seqName);
    }
}
