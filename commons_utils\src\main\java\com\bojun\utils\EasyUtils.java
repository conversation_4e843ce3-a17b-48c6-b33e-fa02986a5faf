package com.bojun.utils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONArray;


/**
 * 
 *Model：EasyUtils
 *Description：
 *Author:  佘亚运
 *created：2019年4月12日
 */
public class EasyUtils {
	

	
	/**
	 * 
	 * @Description: 反向计分
	 * @author: 佘亚运
	 * @create: 2019年4月13日
	 *
	 */
	public static Short scoreRever(Short score){
		switch(score){
	     case 1:
	    	 score=5;
	    	 break;
	     case 2:
	    	 score=4;
	    	 break;
	     case 3:
	    	 score=3;
	    	 break;
	     case 4:
	    	 score=2;
	    	 break;
	     case 5:
	    	 score=1;
	    	 break;
	     default:
	    	 score=0;
	       break;
	   }	
		return score;
	}
	
	/**
	 * 
	 * @Description: 根据分数判断体质类型: 1:是    2:倾向是
	 * @author: 佘亚运
	 * @create: 2019年4月13日
	 *
	 */
	public static Short scoreToBody(Short score){
		Short flag = 0;
		if(score>=11){
			flag=1;
		}
		if(score>=9 && score<=10){
			flag=2;
		}
		return flag;	
	}
	
	/**
	 * 
	 *@Description: json字符串转list
	 * @author: 佘亚运
	 * @create: 2019年4月18日
	 *
	 */
	public static List<String> jsonToList(String jsonStr) {
        JSONArray json = JSONArray.parseArray(jsonStr);
        List<String> strs = new ArrayList<String>();
        for(Object obj:json){
        	strs.add((String)obj);   	
        }
        return strs;
    }
	
	/**
	 * 
	 * @Description: 判断是否包含某个值
	 * @author: 佘亚运
	 * @create: 2019年4月19日
	 * @param: param比较参数    
	 * @param: values目标数组
	 *
	 */
	public static Boolean isContain(int param,int...values){
		Boolean falg = false;
		for(int i=1;i<values.length;i++){
			if(param==values[i]){
				falg =  true;
				break;
			}
		}
		return falg;
	}
	
	/**
	 * 
	 * @Description: 判断是否不包含某个值
	 * @author: 佘亚运
	 * @create: 2019年4月19日
	 * @param: param比较参数    
	 * @param: values目标数组
	 * 
	 *
	 */
	public static Boolean isNoContain(int param,int...values){
		for(int i=1;i<values.length;i++){
			if(param==values[i]){
			  return true;
			}
		}
		return false;
	}
}
