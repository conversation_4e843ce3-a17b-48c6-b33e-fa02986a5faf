package com.bojun.author;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 权限注解接口
 * <AUTHOR>
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD}) //方法声明
public @interface AuthAnnotation {
    /**
     * 请求action
     * @return
     */
	String action();
	/**
	 * 参数，默认空
	 * @return
	 */
	String[] param() default {};
	/**
	 * 是否使用参数 默认false
	 * @return
	 */
	boolean useParam() default false;
}
