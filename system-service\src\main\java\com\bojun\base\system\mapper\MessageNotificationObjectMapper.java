package com.bojun.base.system.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import com.bojun.system.entity.MessageNotificationObject;
import com.bojun.system.dto.MessageNotificationObjectDTO;

/**
 * MessageNotificationObjectMapper接口
 * 
 * <AUTHOR>
 * @date 2021-09-01 09:41:11
 */
@Mapper
public interface MessageNotificationObjectMapper extends BaseMapper<MessageNotificationObject> {

    /**
     * 查询消息通知关联推送对象表
     *
     * @param id 消息通知关联推送对象表ID
     * @return 消息通知关联推送对象表
     */
    public MessageNotificationObjectDTO selectMessageNotificationObjectById(Integer id);

    /**
     * 查询消息通知关联推送对象表列表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 消息通知关联推送对象表集合
     */
    public List<MessageNotificationObjectDTO> selectMessageNotificationObjectList(MessageNotificationObjectDTO messageNotificationObjectDTO);
}
