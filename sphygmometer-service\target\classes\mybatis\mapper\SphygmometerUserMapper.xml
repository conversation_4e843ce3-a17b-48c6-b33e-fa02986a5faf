<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.SphygmometerUserMapper">

    <resultMap type="com.bojun.sphygmometer.dto.SphygmometerUserDTO" id="SphygmometerUserDTOResult">
        <result property="userId" column="user_id"/>
        <result property="nickName" column="nick_name"/>
        <result property="gender" column="gender"/>
        <result property="headPortrait" column="head_portrait"/>
        <result property="wxOpenId" column="wx_open_id"/>
        <result property="appOpenId" column="app_open_id"/>
        <result property="wxappOpenId" column="wxapp_open_id"/>
        <result property="rongToken" column="rong_token"/>
        <result property="unionId" column="union_id"/>
        <result property="mobile" column="mobile"/>
        <result property="country" column="country"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="bindDeviceId" column="bind_device_id"/>
        <result property="bindDeviceUserType" column="bind_device_user_type"/>
        <result property="bindTime" column="bind_time"/>
        <result property="manageOrganizationId" column="manage_organization_id"/>
        <result property="registerOrganizationId" column="register_organization_id"/>
        <result property="registerSource" column="register_source"/>
        <result property="registerTime" column="register_time"/>
        <result property="geTuiClientId" column="ge_tui_client_id"/>
        <result property="regionUser" column="region_user"/>
        <result property="password" column="password"/>
        <result property="salt" column="salt"/>
        <result property="realName" column="real_name"/>
        <result property="idNo" column="id_no"/>
        <result property="isCertification" column="is_certification"/>
        <result property="age" column="age"/>
        <result property="birthday" column="birthday"/>
        <result property="nationName" column="nation_name"/>
        <result property="hasDocument" column="has_document"/>
        <result property="wsToken" column="ws_token"/>
        <result property="residentId" column="resident_id"/>
        <result property="isSign" column="is_sign"/>
        <result property="signTime" column="sign_time"/>
    </resultMap>

    <sql id="selectSphygmometerUser">
    	select
            user_id,
            nick_name,
            gender,
            head_portrait,
            wx_open_id,
            app_open_id,
            wxapp_open_id,
            union_id,
            rong_token,
            mobile,
            country,
            province,
            city,
            bind_device_id,
            bind_time,
            manage_organization_id,
            register_organization_id,
            register_source,
            register_time,
            ge_tui_client_id,
            region_user,
            password,
            salt,
            resident_id,
            bind_device_user_type,
            is_sign,
            sign_time
		from
        	t_sphygmometer_user tsu
    </sql>

    <select id="selectSphygmometerUserById" parameterType="int" resultMap="SphygmometerUserDTOResult">
        select
	        tsu.user_id,
	        tsu.nick_name,
	        trbi.real_name,
	        tsu.gender,
	        tsu.head_portrait,
	        tsu.wx_open_id,
	        tsu.app_open_id,
	        tsu.wxapp_open_id,
	        tsu.union_id,
	        tsu.mobile,
	        tsu.country,
	        tsu.province,
	        tsu.city,
	        tsu.bind_device_id,
	        tsu.bind_time,
            trbi.id_no,
            trbi.birthday,
            tsu.ws_token,
            ROUND( DATEDIFF( CURDATE( ), trbi.birthday ) / 365.2422 ) age,
            (select nation_name from system.t_nation_dict where trbi.nation_code = nation_code) nation_name,
	        tsu.manage_organization_id,
	        tsu.register_organization_id,
	        (SELECT tsu1.mobile FROM t_sphygmometer_user tsu1 WHERE tsu1.user_id = (SELECT tsur.relative_user_id FROM t_sphygmometer_user_relative tsur where tsur.user_id = tsu.user_id order by create_time desc limit 1)) as relative_mobile,
	        (SELECT count(1) FROM `t_sphygmometer_record` tsr where tsr.user_id = tsu.user_id) testingCount,
			(SELECT count(1) FROM `t_sphygmometer_record` tsr where tsr.user_id = tsu.user_id AND DATE_FORMAT(tsr.measure_time,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') AND
			(tsr.heartbeat_result != 1 OR tsr.pressure_result != 1)) nowTestingCount,
	        (select organization_name  from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
			(select organization_name  from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
	        tsu.register_source,
	        tsu.register_time,
	        tsu.ge_tui_client_id,
	        tsu.resident_id,
            tsu.bind_device_user_type,
            tsu.is_sign,
            tsu.sign_time,
	        (CASE WHEN
	            tsu.union_id is not null THEN 1
	            ELSE 0
	        END) bind_wechat,
	        (CASE
                WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                    (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.id_no)) != 0) THEN  1
                ELSE 0
            END) is_certification
		from
        	t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsu.user_id = #{userId}
    </select>

    <select id="getAppUserInfo" parameterType="int" resultMap="SphygmometerUserDTOResult">
        select
	        tsu.user_id,
	        tsu.nick_name,
	        trbi.real_name,
	        tsu.gender,
	        tsu.head_portrait,
	        tsu.wx_open_id,
	        tsu.app_open_id,
	        tsu.wxapp_open_id,
	        tsu.union_id,
	        tsu.mobile,
	        tsu.country,
	        tsu.province,
	        tsu.city,
	        tsu.bind_device_id,
	        tsu.bind_time,
	        tsu.manage_organization_id,
	        tsu.register_organization_id,
	        (SELECT count(1) FROM `t_sphygmometer_record` tsr where tsr.user_id = tsu.user_id and tsr.device_type = 2) testingCount,
			(
			SELECT
			    count(1)
			FROM
			    `t_sphygmometer_record` tsr
			where
			    tsr.user_id = tsu.user_id and
			    tsr.device_type = 2 and
			    DATE_FORMAT(tsr.measure_time,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') AND
			    (tsr.heartbeat_result != 1 OR tsr.pressure_result not in (1, 4))
			) nowTestingCount,
	        (select organization_name  from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
			(select organization_name  from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
	        tsu.register_source,
	        tsu.register_time,
	        tsu.ge_tui_client_id,
	        tsu.resident_id,
            tsu.bind_device_user_type,
            tsu.is_sign,
            tsu.sign_time,
            (CASE WHEN
	            tsu.union_id is not null THEN 1
	            ELSE 0
	        END) bind_wechat,
	        (CASE
                WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                    (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.id_no)) != 0) THEN  1
                ELSE 0
            END) is_certification
		from
        	t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsu.user_id = #{userId}
    </select>



    <select id="getAppUserInfoByWxApp" parameterType="int" resultMap="SphygmometerUserDTOResult">
        select
            tsu.user_id,
            tsu.nick_name,
            trbi.real_name,
            tsu.gender,
            tsu.head_portrait,
            tsu.wx_open_id,
            tsu.app_open_id,
            tsu.wxapp_open_id,
            tsu.union_id,
            tsu.mobile,
            tsu.country,
            tsu.province,
            tsu.city,
            tsu.bind_device_id,
            tsu.bind_time,
            tsu.manage_organization_id,
            tsu.register_organization_id,
            (SELECT count(1) FROM `t_sphygmometer_record` tsr where tsr.user_id = tsu.user_id and tsr.device_type = 1) testingCount,
            (
                SELECT
                    count(1)
                FROM
                    `t_sphygmometer_record` tsr
                where
                    tsr.user_id = tsu.user_id and
                    tsr.device_type = 1 and
                    DATE_FORMAT(tsr.measure_time,'%Y-%m-%d') = DATE_FORMAT(now(),'%Y-%m-%d') AND
                    (tsr.heartbeat_result != 1 OR tsr.pressure_result not in (1, 4))
            ) nowTestingCount,
            (select organization_name  from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
            (select organization_name  from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
            tsu.register_source,
            tsu.register_time,
            tsu.ge_tui_client_id,
            tsu.resident_id,
            tsu.bind_device_user_type,
            tsu.is_sign,
            tsu.sign_time,
            (CASE
                 WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                    (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.id_no)) != 0) THEN  1
                 ELSE 0
                END) is_certification
        from
            t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsu.user_id = #{userId}
    </select>


    <select id="selectSphygmometerUserList" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO"
            resultMap="SphygmometerUserDTOResult">
        SELECT
            tsu.user_id,
            tsu.nick_name,
            tsu.mobile,
            tsu.wx_open_id,
            tsu.head_portrait,
            (select organization_name from organization.t_organization_info where tsu.manage_organization_id =
            organization_id) organization_name,
            DATE_FORMAT(tsu.register_time, '%Y-%m-%d %H:%i:%s' )registerTimeStr,
            DATE_FORMAT( tsr.measure_time, '%Y-%m-%d %H:%i:%s' ) measureTimeStr,
            tsr.measure_place,
            tsr.systolic_pressure,
            tsr.diastolic_pressure,
            tsr.heartbeat,
            tsr.pressure_result,
            tsr.heartbeat_result,
            thhi.hypertension_type,
            tsr.device_type,
            tsu.resident_id,
            tsu.bind_device_user_type,
            tsu.is_sign,
            tsu.sign_time
        FROM
            t_sphygmometer_user tsu
        left join t_hypertension_health_info thhi on tsu.user_id = thhi.user_id
        LEFT JOIN t_sphygmometer_record tsr ON tsu.user_id = tsr.user_id
        WHERE
        tsr.id in (SELECT MAX(tsr2.id) FROM t_sphygmometer_record tsr2 GROUP BY tsr2.user_id)
        <if test="searchContent != null  and searchContent != ''">
            AND (
            mobile LIKE concat( '%', '${searchContent}', '%' ) OR
            nick_name LIKE concat( '%', '${searchContent}', '%' ) OR
            tsr.measure_place LIKE concat( '%', '${searchContent}', '%' )
            )
        </if>
        <if test="registerStartTime != null  and registerStartTime != ''">
            AND register_time >= #{registerStartTime}
            AND register_time <![CDATA[<=]]> #{registerEndTime}
        </if>
        <if test="measureStartTime != null  and measureStartTime != ''">
            AND tsr.measure_time >= #{measureStartTime}
            AND tsr.measure_time <![CDATA[<=]]> #{measureEndTime}
        </if>
        <if test="pressureResult != null ">
            AND tsr.pressure_result = #{pressureResult}
        </if>
        <if test="heartbeatResult != null  ">
            AND tsr.heartbeat_result = #{heartbeatResult}
        </if>
        <if test="hypertensionType != null  and hypertensionType != ''">
            AND thhi.hypertension_type = #{hypertensionType}
        </if>
        <if test="manageOrganizationLists != null  and manageOrganizationLists.size() > 0">
            and tsu.manage_organization_id in
            <foreach item="item" index="index"
                     collection="manageOrganizationLists" open="(" separator=","
                     close=")">
                #{item.organizationId}
            </foreach>
        </if>
        <if test="deviceType != null  and deviceType != ''">
            AND tsr.device_type = #{deviceType}
        </if>
		<if test="isRegisterMobile != null  and isRegisterMobile == 0 ">
			AND tsu.mobile IS NULL
		</if>
		<if test="isRegisterMobile != null  and isRegisterMobile == 1 ">
			AND tsu.mobile IS NOT NULL
		</if>
        order by tsr.measure_time desc
    </select>

    <select id="getTotalCount" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO"
            resultType="java.lang.Integer">
        select
        count(1)
        from
        t_sphygmometer_user
        <where>
            <if test="registerSource != null and registerSource != 0">
                register_source = #{registerSource}
            </if>
            <if test="registerStartTime != null  and registerEndTime != ''">
                AND register_time >= STR_TO_DATE(#{registerStartTime},'%Y-%m-%d %H:%i:%s')
                AND register_time <![CDATA[<=]]> STR_TO_DATE(#{registerEndTime},'%Y-%m-%d %H:%i:%s')
            </if>
        </where>
    </select>

    <select id="getDayAvgCount" resultType="java.lang.Double">
        SELECT
        AVG( t1.date_count ) AS avg_count
        FROM
        (
        SELECT
        count( 1 ) AS date_count,
        DATE_FORMAT( tsu.register_time, '%Y%m%d' )
        FROM
        t_sphygmometer_user tsu
        <where>
            <if test="registerSource != null and registerSource != 0">
                AND register_source = #{registerSource}
            </if>
            <if test="registerEndTime != null">
                AND register_time <![CDATA[<=]]> #{registerEndTime}
            </if>
        </where>
        GROUP BY DATE_FORMAT( tsu.register_time, '%Y%m%d' )
        ) t1
    </select>

    <!-- 绑定用户手机号 -->
    <update id="bindUserMobile">
		UPDATE t_sphygmometer_user SET mobile = #{mobileNum} WHERE user_id = ${userId}
	</update>

    <!-- 获取手机绑定用户数量 -->
    <select id="getBindMobileUserCount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(user_id) FROM t_sphygmometer_user WHERE mobile = #{mobileNum}
	</select>

    <update id="doUnbindDevice" parameterType="java.lang.Integer">
		update
			t_sphygmometer_user
		set
			bind_device_id = null,
			bind_device_user_type = null,
			bind_time = null
		where
			user_id = #{userId}
	</update>

    <select id="checkDeviceIsBind" resultType="java.lang.Integer">
        select
        count(1)
        from
        t_sphygmometer_user tsu
        left join t_sphygmometer_device tsd on tsd.device_id = tsu.bind_device_id
        where
        tsd.device_no = #{deviceNo}
        <if test="nqUserId != null">
            and tsu.user_id != #{nqUserId}
        </if>
        <if test="deviceUserType != null">
            and tsu.bind_device_user_type = #{deviceUserType}
        </if>
    </select>

    <select id="getUserInfoByUniqueKey" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO" resultMap="SphygmometerUserDTOResult">
        select
	        tsu.user_id,
	        tsu.nick_name,
	        trbi.real_name,
	        tsu.gender,
	        tsu.head_portrait,
	        tsu.wx_open_id,
	        tsu.app_open_id,
	        tsu.wxapp_open_id,
	        tsu.union_id,
	        tsu.mobile,
	        tsu.country,
	        tsu.province,
	        tsu.city,
	        tsu.bind_device_id,
	        tsu.bind_time,
	        tsu.manage_organization_id,
	        tsu.register_organization_id,
	        tsu.register_source,
	        tsu.register_time,
	        tsu.password,
	        tsu.salt,
            trbi.id_no,
            tsu.resident_id,
            tsu.bind_device_user_type,
            tsu.is_sign,
            tsu.sign_time,
	        (CASE WHEN
	            tsu.union_id is not null THEN 1
	            ELSE 0
	        END) bind_wechat,
	        (CASE
                WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                    (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.id_no)) != 0) THEN  1
                ELSE 0
            END) is_certification
		from
        	t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        <where>
            <if test="userId != null">
                AND tsu.user_id = #{userId}
            </if>
            <if test="idNo != null and idNo != ''">
                AND trbi.id_no = #{idNo}
            </if>
            <if test="mobile != null and mobile != ''">
                AND tsu.mobile = #{mobile}
            </if>
            <if test="unionId != null and unionId != ''">
                AND tsu.union_id = #{unionId}
            </if>
            <if test="residentId != null and residentId != ''">
                AND tsu.resident_id = #{residentId}
            </if>
            <if test="wxOpenId != null and wxOpenId != ''">
                AND tsu.wx_open_id = #{wxOpenId}
            </if>
            <if test="wxappOpenId != null and wxappOpenId != ''">
                AND tsu.wxapp_open_id = #{wxappOpenId}
            </if>
            <if test="appOpenId != null and appOpenId != ''">
                AND tsu.app_open_id = #{appOpenId}
            </if>
            <if test="bindDeviceId != null and bindDeviceUserType != null">
                AND tsu.bind_device_id = #{bindDeviceId}
                AND tsu.bind_device_user_type = #{bindDeviceUserType}
            </if>
        </where>
    </select>

    <select id="getUserListByUnoinIdNull" resultMap="SphygmometerUserDTOResult">
		select
	        user_id,
	        nick_name,
	        gender,
	        head_portrait,
	        wx_open_id,
	        app_open_id,
	        wxapp_open_id,
	        union_id,
	        mobile,
	        country,
	        province,
	        city,
	        bind_device_id,
	        bind_time,
	        manage_organization_id,
	        register_organization_id,
	        register_source,
	        register_time,
	        resident_id,
	        bind_device_user_type,
            is_sign,
            sign_time
		from
        	t_sphygmometer_user
        where
        	wx_open_id is not null and union_id is null
	</select>

    <select id="getScreenUserList" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO" resultMap="SphygmometerUserDTOResult">
        SELECT * FROM (
            SELECT
                tsu.user_id,
                tsu.nick_name,
                tsu.mobile,
                tsu.manage_organization_id,
                tsu.register_organization_id,
                tsr.organization_id as measure_organization_id,
                (select organization_name from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
                (select organization_name from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
                (select organization_name from organization.t_organization_info where tsr.organization_id = organization_id) measure_organization_name,
                tsr.systolic_pressure,
                tsr.diastolic_pressure,
                tsr.pressure_result,
                tsr.heartbeat,
                tsr.heartbeat_result,
                tsr.measure_time as last_measure_time,
                tsr.measure_place,
                tsr.organization_id,
                tsu.bind_device_id,
                tsr.device_type,
                (select count(1) from t_sphygmometer_record tsr3 where tsr3.user_id = tsu.user_id and tsr3.device_type = 1) as testing_count,
                (select count(1) from t_sphygmometer_record tsr4 where tsr4.user_id = tsu.user_id and tsr4.device_type = 1 and pressure_result in (2,3)) as abnormal_pressure_result_count,
                (
                CASE WHEN trbi.has_document = 1 THEN 1
                WHEN trbi.has_document = 0 THEN 0
                ELSE 0 END
                ) has_document,
                tsu.register_time,
                trbi.id_no,
                trbi.real_name,
                tsu.region_user,
                tsu.resident_id,
                tsu.bind_device_user_type,
                tsu.is_sign,
                tsu.sign_time,
                (CASE
                WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                (LENGTH(trim(trbi.id_no)) != 0) THEN  1
                ELSE 0
                END) is_certification
            FROM
                t_sphygmometer_user tsu
            INNER JOIN t_sphygmometer_record tsr ON tsu.user_id = tsr.user_id
            AND tsr.id in (SELECT MAX(tsr2.id) FROM t_sphygmometer_record tsr2 WHERE (tsr2.device_type = 1 or tsr2.device_type = 3) GROUP BY tsr2.user_id)
            left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        ) temp
        <where>
            <if test="pressureResult != null ">
                AND temp.pressure_result = #{pressureResult}
            </if>
            <if test="heartbeatResult != null  ">
                AND temp.heartbeat_result = #{heartbeatResult}
            </if>
            <if test="measureStartTime != null  and measureStartTime != ''">
                AND temp.last_measure_time >= #{measureStartTime}
                AND temp.last_measure_time <![CDATA[<=]]> #{measureEndTime}
            </if>
            <if test="registerOrganizationList != null  and registerOrganizationList.size() > 0">
                and temp.register_organization_id in
                <foreach item="item" index="index"
                         collection="registerOrganizationList" open="(" separator=","
                         close=")">
                    #{item.organizationId}
                </foreach>
            </if>
            <if test="hasDocument != null and hasDocument == 1">
                AND temp.has_document = 1
            </if>
            <if test="hasDocument != null and hasDocument == 0">
                AND (temp.has_document = 0 OR temp.has_document is null)
            </if>
            <if test="abnormalPressureResultCount != null and abnormalPressureResultCount == 1">
                AND temp.abnormal_pressure_result_count = 0
            </if>
            <if test="abnormalPressureResultCount != null and abnormalPressureResultCount == 2">
                AND temp.abnormal_pressure_result_count <![CDATA[>]]> 0 AND temp.abnormal_pressure_result_count <![CDATA[<]]> 3
            </if>
            <if test="abnormalPressureResultCount != null and abnormalPressureResultCount == 3">
                AND temp.abnormal_pressure_result_count <![CDATA[>=]]> 3
            </if>
            <if test="searchContent != null  and searchContent != ''">
                AND (
                temp.mobile LIKE concat( '%', '${searchContent}', '%' ) OR
                temp.nick_name LIKE concat( '%', '${searchContent}', '%' )
                )
            </if>
            <if test="regionUser != null and regionUser == 1">
                AND (temp.region_user = #{regionUser} or temp.device_type = 1)
            </if>
            <if test="regionUser != null and regionUser == 2">
                AND (temp.region_user = #{regionUser} or temp.device_type = 3)
            </if>
            <if test="isSigned != null">
                and temp.is_sign = #{isSigned}
            </if>
        </where>
        order by
        temp.last_measure_time desc
    </select>

    <select id="getScreenUserInfo" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO" resultMap="SphygmometerUserDTOResult">
        select
            tsu.user_id,
            tsu.nick_name,
            tsu.gender,
            tsu.head_portrait,
            tsu.mobile,
            tsu.manage_organization_id,
            tsu.register_organization_id,
            (select tsr.organization_id from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 1 ORDER BY tsr.measure_time DESC LIMIT 1) as measure_organization_id,
            (select organization_name from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
            (select organization_name from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
            (select o.organization_name from t_sphygmometer_record tsr left join organization.t_organization_info o on o.organization_id = tsr.organization_id where tsu.user_id = tsr.user_id AND tsr.device_type = 1 ORDER BY tsr.measure_time DESC LIMIT 1) measure_organization_name,
            tsu.country,
            tsu.province,
            tsu.city,
            tsu.bind_device_id,
            tsu.bind_time,
            tsu.register_source,
            tsu.register_time,
            trbi.has_document,
            trbi.real_name,
            ROUND(DATEDIFF(CURDATE(), trbi.birthday)/365.2422) as age,
            trbi.id_no,
            trbi.birthday,
            tsu.resident_id,
            tsu.is_sign,
            tsu.sign_time,
            (select nation_name from system.t_nation_dict where trbi.nation_code = nation_code) nation_name,
            (CASE
                 WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                    (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                    (LENGTH(trim(trbi.id_no)) != 0) THEN  1
                 ELSE 0
                END) is_certification,
            (select count(1) from t_sphygmometer_record tsr1 where tsr1.user_id = tsu.user_id and tsr1.device_type = 1) as testing_count,
            (select count(1) from t_sphygmometer_record tsr2 where tsr2.user_id = tsu.user_id and tsr2.device_type = 1 and pressure_result in (2,3) ) as abnormal_pressure_result_count
        from
            t_sphygmometer_user tsu
                left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsu.user_id = #{userId}
    </select>

    <select id="getSignContractUserList" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO" resultMap="SphygmometerUserDTOResult">
        SELECT * FROM (
            SELECT
                tsu.user_id,
                trbi.real_name,
                tsu.mobile,
                trbi.id_no,
                (case when (thhi.hypertension_type is null or thhi.hypertension_type = '') then 0 else thhi.hypertension_type end) hypertension_type,
                tsu.manage_organization_id,
                tsu.register_organization_id,
                (select tsr.organization_id from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as measure_organization_id,
                (select organization_name from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
                (select organization_name from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
                (select o.organization_name from t_sphygmometer_record tsr left join organization.t_organization_info o on o.organization_id = tsr.organization_id where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) measure_organization_name,
                (select tsr.systolic_pressure from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as systolic_pressure,
                (select tsr.diastolic_pressure from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as diastolic_pressure,
                (select tsr.pressure_result from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as pressure_result,
                (select tsr.heartbeat from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as heartbeat,
                (select tsr.heartbeat_result from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as heartbeat_result,
                (select tsr.measure_time from t_sphygmometer_record tsr where tsu.user_id = tsr.user_id AND tsr.device_type = 2 ORDER BY tsr.measure_time DESC LIMIT 1) as last_measure_time,
                tsu.register_time,
                tsu.bind_device_id,
                tsu.resident_id,
                tsu.bind_device_user_type,
                tsu.is_sign,
                tsu.sign_time,
                (select count(1) from t_sphygmometer_record tsr3 where tsr3.user_id = tsu.user_id and tsr3.device_type = 2) as testing_count,
                (select count(1) from t_sphygmometer_record tsr4 where tsr4.user_id = tsu.user_id and tsr4.device_type = 2 and pressure_result in (2,3) ) as abnormal_pressure_result_count,
                (case when (select count(1) from t_user_test_plan tutp where tutp.user_id = tsu.user_id and plan_status = 1) > 0 then 1 else 0 end) as has_user_plan
            FROM
                t_sphygmometer_user tsu
            left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
            left join t_hypertension_health_info thhi on tsu.user_id = thhi.user_id
        ) temp
        WHERE
            temp.is_sign = 1
        <if test="pressureResult != null ">
            AND temp.pressure_result = #{pressureResult}
        </if>
        <if test="heartbeatResult != null  ">
            AND temp.heartbeat_result = #{heartbeatResult}
        </if>
        <if test="dateType == 1 and measureStartTime != null and measureStartTime != ''">
            AND temp.register_time >= #{measureStartTime}
            AND temp.register_time <![CDATA[<=]]> #{measureEndTime}
        </if>
        <if test="dateType == 2 and measureStartTime != null and measureStartTime != ''">
            AND temp.last_measure_time >= #{measureStartTime}
            AND temp.last_measure_time <![CDATA[<=]]> #{measureEndTime}
        </if>
        <if test="manageOrganizationList != null  and manageOrganizationList.size() > 0">
            and temp.manage_organization_id in
            <foreach item="item" index="index"
                     collection="manageOrganizationList" open="(" separator=","
                     close=")">
                #{item.organizationId}
            </foreach>
        </if>
        <if test="hypertensionType != null and hypertensionType != '0' and hypertensionType != ''">
            AND temp.hypertension_type = #{hypertensionType}
        </if>
        <if test="hypertensionType != null and (hypertensionType == '0' or hypertensionType == '')">
            AND (temp.hypertension_type is null or temp.hypertension_type = '')
        </if>
        <if test="hasUserPlan != null">
            AND temp.has_user_plan = #{hasUserPlan}
        </if>
        <if test="searchContent != null  and searchContent != ''">
            AND (
            temp.mobile LIKE concat( '%', '${searchContent}', '%' ) OR
            temp.real_name LIKE concat( '%', '${searchContent}', '%' )
            )
        </if>
        order by
            temp.last_measure_time desc
    </select>

    <select id="getNoBindDeviceUserList" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserDTO" resultMap="SphygmometerUserDTOResult">
        SELECT * FROM (
            SELECT
                tsu.user_id,
                trbi.real_name,
                tsu.gender,
                trbi.birthday,
                tsu.mobile,
                trbi.id_no,
                tsu.register_time,
                trbi.create_time,
                ROUND(DATEDIFF(CURDATE(), trbi.birthday)/365.2422) as age,
                tsu.resident_id,
                tsu.manage_organization_id,
                tsu.is_sign,
                (CASE
                WHEN (ISNULL(trbi.real_name) != 1) <![CDATA[&&]]>
                (LENGTH(trim(trbi.real_name)) != 0) <![CDATA[&&]]>
                (ISNULL(trbi.id_no) != 1) <![CDATA[&&]]>
                (LENGTH(trim(trbi.id_no)) != 0) THEN 1
                ELSE 0
                END) is_certification
            FROM
                t_sphygmometer_user tsu
            left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        ) temp
        WHERE
        temp.is_sign = 0 AND temp.is_certification = 1
        <if test="registerStartTime != null  and registerEndTime != ''">
            AND temp.create_time >= #{registerStartTime}
            AND temp.create_time <![CDATA[<=]]> #{registerEndTime}
        </if>
        <if test="searchContent != null  and searchContent != ''">
            AND (
            temp.real_name LIKE concat( '%', '${searchContent}', '%' ) OR
            temp.mobile LIKE concat( '%', '${searchContent}', '%' ) OR
            temp.id_no LIKE concat( '%', '${searchContent}', '%' )
            )
        </if>
        <if test="manageOrganizationList != null  and manageOrganizationList.size() > 0">
            and temp.manage_organization_id in
            <foreach item="item" index="index"
                     collection="manageOrganizationList" open="(" separator=","
                     close=")">
                #{item.organizationId}
            </foreach>
        </if>
        order by
            temp.create_time desc
    </select>

    <select id="getSignContractUserCount" parameterType="com.bojun.sphygmometer.dto.SignContractUserCountParamDTO" resultType="Integer">
        SELECT
            count(1)
        FROM
            t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        WHERE
            tsu.is_sign = 1
        <if test="authOrgIdList != null  and authOrgIdList.size() > 0">
            and tsu.manage_organization_id in
            <foreach item="item" index="index"
                     collection="authOrgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="bindStartTime != null  and bindEndTime != null">
            AND tsu.bind_time IS NOT NULL
            AND tsu.bind_time BETWEEN #{bindStartTime} AND #{bindEndTime}
        </if>
    </select>


    <select id="getTestPlanCount" parameterType="com.bojun.sphygmometer.dto.TestPlanCountParamDTO" resultType="Integer">
        select
        count(1)
        from
        t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        left join t_user_test_plan tutp on tutp.user_id = tsu.user_id
        <where>
            <if test="isSignContract == 1">
                AND tsu.is_sign = #{isSignContract}
            </if>
            <if test="hasTestPlan == 0 ">
                and tutp.user_id is null
            </if>
            <if test="hasTestPlan == 1 ">
                and tutp.user_id is not null
            </if>
            <if test="authOrgIdList != null and authOrgIdList.size() > 0">
                AND tsu.manage_organization_id in
                <foreach item="item" index="index" collection="authOrgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getSignedUserCount" resultType="com.bojun.sphygmometer.dto.SignedUserIndexDTO">
        SELECT
        SUM( CASE WHEN thhi.hypertension_type = 1 THEN 1 ELSE 0 END) AS hypertensionGrade1,
        SUM( CASE WHEN thhi.hypertension_type = 2 THEN 1 ELSE 0 END) AS hypertensionGrade2,
        SUM( CASE WHEN thhi.hypertension_type = 3 THEN 1 ELSE 0 END) AS hypertensionGrade3,
        SUM( CASE WHEN thhi.hypertension_type is null THEN 1 ELSE 0 END) AS hypertensionUnknownCount,
        SUM( CASE WHEN thhi.compliance = 1 THEN 1 ELSE 0 END) AS complianceGoodCount,
        SUM( CASE WHEN thhi.compliance = 2 THEN 1 ELSE 0 END) AS complianceGeneralCount,
        SUM( CASE WHEN thhi.compliance = 3 THEN 1 ELSE 0 END) AS complianceBadCount,
        SUM( CASE WHEN thhi.compliance is null THEN 1 ELSE 0 END) AS complianceUnknownCount
        FROM
        t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        left join t_hypertension_health_info thhi on thhi.user_id = tsu.user_id
        WHERE
            tsu.is_sign = 1
        <if test="orgIdList != null  and orgIdList.size() > 0">
            and tsu.manage_organization_id in
            <foreach item="item" index="index"
                     collection="orgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="startTime != null  and endTime != null">
            AND tsu.bind_time IS NOT NULL
            and tsu.bind_time between #{startTime} and #{endTime}
        </if>
    </select>

    <select id="getHypertensionTypeCount" parameterType="com.bojun.sphygmometer.dto.HypertensionTypeCountParamDTO" resultType="Integer">
        select
            count(1)
        from
            t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        left join t_hypertension_health_info thhi on tsu.user_id = thhi.user_id
        <where>
            <if test="isSignContract == 1">
                and tsu.is_sign = #{isSignContract}
            </if>
            <if test=' hypertensionType != null and hypertensionType != "0" and hypertensionType != "" '> and hypertension_type = #{hypertensionType}</if>
            <if test=' hypertensionType != null and (hypertensionType == "0" or hypertensionType == "") '>and
                (hypertension_type is null or hypertension_type = '' OR hypertension_type = '0')
            </if>
            <if test="authOrgIdList != null  and authOrgIdList.size() > 0">
                and tsu.manage_organization_id in
                <foreach item="item" index="index"
                         collection="authOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="unBindWechat" parameterType="java.lang.Integer">
        update t_sphygmometer_user set app_open_id = null, union_id = null where user_id = #{userId}
    </update>

</mapper>