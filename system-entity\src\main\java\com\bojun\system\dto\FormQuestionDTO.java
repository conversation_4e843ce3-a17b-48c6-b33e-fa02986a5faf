package com.bojun.system.dto;

import com.bojun.system.entity.FormQuestion;

import java.util.List;

/**
 * Model：满意度问卷题目信息表
 * Description：满意度问卷题目信息表DTO
 * Author:李欣颖
 * created：2020年5月7日
 */
public class FormQuestionDTO extends FormQuestion {

    private static final long serialVersionUID = -1344026328907379635L;
    private List<FormQuestionOptionDTO> formQuestionOptionDTOS;
    private String deleteTimeStr; // 删除时间
 
   	private List<FormQuestionOptionDTO> formQuestionOptionDTOList;//题目选项
   	private List<FormQuestionItemDTO>  formQuestionItemDTOList;//满意度问卷题目子项目集合


   
	public List<FormQuestionItemDTO> getFormQuestionItemDTOList() {
		return formQuestionItemDTOList;
	}

	public void setFormQuestionItemDTOList(List<FormQuestionItemDTO> formQuestionItemDTOList) {
		this.formQuestionItemDTOList = formQuestionItemDTOList;
	}

	public List<FormQuestionOptionDTO> getFormQuestionOptionDTOS() {
        return formQuestionOptionDTOS;
    }

    public void setFormQuestionOptionDTOS(List<FormQuestionOptionDTO> formQuestionOptionDTOS) {
        this.formQuestionOptionDTOS = formQuestionOptionDTOS;
    }

    public List<FormQuestionOptionDTO> getFormQuestionOptionDTOList() {
        return formQuestionOptionDTOList;
    }

    public void setFormQuestionOptionDTOList(List<FormQuestionOptionDTO> formQuestionOptionDTOList) {
        this.formQuestionOptionDTOList = formQuestionOptionDTOList;
    }

    public String getDeleteTimeStr() {
        return deleteTimeStr;
    }

    public void setDeleteTimeStr(String deleteTimeStr) {
        this.deleteTimeStr = deleteTimeStr;
    }
}
