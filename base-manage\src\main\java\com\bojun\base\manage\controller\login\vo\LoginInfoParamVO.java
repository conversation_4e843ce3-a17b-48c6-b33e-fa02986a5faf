/**
 * 
 */
package com.bojun.base.manage.controller.login.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：登陆信息VO
*Description：登陆信息VO
*Author:段德鹏
*created：2020年4月12日
**/
@ApiModel(value = "登录信息", description = "登录信息")
public class LoginInfoParamVO implements Serializable {

	private static final long serialVersionUID = -9120830022257472830L;
	
	@NotEmpty(message = "账号不能为空")
	@ApiModelProperty(value="账号", required = true, example = "100000")
	private String accountNo;
	
	@ApiModelProperty(value="机构ID", required = false, example = "0")
	private Integer organizationId; 
	
	@NotEmpty(message = "密码不能为空")
	@ApiModelProperty(value="密码", required = true, example = "123456")
	private String passwords;
	
	@NotNull(message = "登陆类型不能为空 ")
	@ApiModelProperty(value="登陆类型 0 工号 1 账户", required = true, example = "1")
	private Integer loginType;

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public String getPasswords() {
		return passwords;
	}

	public void setPasswords(String passwords) {
		this.passwords = passwords;
	}

	public Integer getLoginType() {
		return loginType;
	}

	public void setLoginType(Integer loginType) {
		this.loginType = loginType;
	}

	
	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}
	
}
