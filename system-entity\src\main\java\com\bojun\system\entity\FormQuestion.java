package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：呼叫记录
 * Description：呼叫记录实体类
 * Author:刘修纬
 * created：2020年5月12日
 */
public class FormQuestion implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;


    private Integer questionId; // 问题id
    private Integer questionnaireId; // 问卷id
    private Integer questionNumber; // 问题序号
    private String title; // 问题标题
    private Integer questionType; // 问题类型  1：单选题  2：多选题  3：单选填空题  4：多选填空题  5：矩阵单选  6：矩阵多选
    private Integer isRequired; // 是否必填  0:否   1：是
    private Integer score; // 分值
    private Integer minLength; // 最小输入长度
    private Integer maxLength; // 最大输入长度
    private Integer limitType; // 内容限制：-1：不限制， 1：数字，2：字母，3：中文，4：Email,5:手机号码
    private Integer isDelete; // 删除标记  0:否  1：是
    private Integer deleteUserId; // 删除人用户id
    private java.sql.Date deleteTime; // 删除时间


    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }


    public Integer getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(Integer questionnaireId) {
        this.questionnaireId = questionnaireId;
    }


    public Integer getQuestionNumber() {
        return questionNumber;
    }

    public void setQuestionNumber(Integer questionNumber) {
        this.questionNumber = questionNumber;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


    public Integer getQuestionType() {
        return questionType;
    }

    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
    }


    public Integer getIsRequired() {
        return isRequired;
    }

    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }


    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }


    public Integer getMinLength() {
        return minLength;
    }

    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }


    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }


    public Integer getLimitType() {
        return limitType;
    }

    public void setLimitType(Integer limitType) {
        this.limitType = limitType;
    }


    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }


    public Integer getDeleteUserId() {
        return deleteUserId;
    }

    public void setDeleteUserId(Integer deleteUserId) {
        this.deleteUserId = deleteUserId;
    }


    public java.sql.Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(java.sql.Date deleteTime) {
        this.deleteTime = deleteTime;
    }


}
