/**
 * 
 */
package com.bojun.base.manage.controller.manageRole.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：角色管理 
 * Description：启用禁用角色状态 
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "启用禁用角色状态 ", description = "启用禁用角色状态 传入参数")
public class EnableDisableRoleVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;


	
	@NotEmpty(message = "角色ID不能为空")
	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;
	
	
	 @NotNull(message = "启用状态不能为空")
	 @ApiModelProperty(value = "启用状态： 0：否  1：是", required = true, example = "0")
     private Integer isEnabled;


	public String getRoleId() {
		return roleId;
	}


	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}


	public Integer getIsEnabled() {
		return isEnabled;
	}


	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	} 
	 
	
	
	
	
	
}
