spring.application.name=sphygmometer-service
spring.profiles.active=dev
feign.okhttp.enabled=true
feign.hystrix.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000

ribbon.ReadTimeout=50000
ribbon.ConnectTimeout=50000

#mybatis-plus
mybatis-plus.config-location=classpath:mybatis/mybatis-config.xml
mybatis-plus.mapper-locations=classpath:mybatis/mapper/*.xml

spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=jdbc:mysql://${db.ip-port}/sphygmometer?useUnicode=true&characterEncoding=utf-8&useSSL=false&zeroDateTimeBehavior=convertToNull
spring.datasource.username=${db.username}
spring.datasource.password=${db.password}

spring.datasource.maximum-pool-size=100
spring.datasource.max-idle=10
spring.datasource.max-wait=10000
spring.datasource.min-idle=5
spring.datasource.initial-size=5
spring.datasource.validation-query=SELECT 1
spring.datasource.test-on-borrow=false
spring.datasource.test-while-idle=true
spring.datasource.time-between-eviction-runs-millis=18800

#reids#
spring.redis.database=0
spring.redis.timeout=10000
  


