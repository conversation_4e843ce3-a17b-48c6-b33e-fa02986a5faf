<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mq.mapper.SphygmometerUserRelativeMapper">
    
    <resultMap type="com.bojun.sphygmometer.mq.dto.SphygmometerUserRelativeDTO" id="SphygmometerUserRelativeDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="relativeUserId"    column="relative_user_id"    />
        <result property="relationship"    column="relationship"    />
        <result property="mobile"    column="mobile"    />
		<result property="wxOpenId" column="wx_open_id"/>
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSphygmometerUserRelative">
    	select
			tsur.id,
			tsur.user_id,
			tsur.relative_user_id,
			tsur.relationship,
			tsur.mobile,
			tsur.create_time
		from 
        	t_sphygmometer_user_relative tsur
    </sql>

    <select id="selectSphygmometerUserRelativeById" parameterType="int" resultMap="SphygmometerUserRelativeDTOResult">
		<include refid="selectSphygmometerUserRelative"/>
		where
		tsur.id = #{id}
    </select>

    <select id="selectSphygmometerUserRelativeList" parameterType="com.bojun.sphygmometer.mq.dto.SphygmometerUserRelativeDTO" resultMap="SphygmometerUserRelativeDTOResult">
        <include refid="selectSphygmometerUserRelative"/>
        <where>  
		<if test="id != null "> and tsur.id = #{id}</if>
		<if test="userId != null "> and tsur.user_id = #{userId}</if>
		<if test="wxOpenId != null and wxOpenId != ''"> and tsur.wx_open_id = #{wxOpenId}</if>
		<if test="relativeUserId != null "> and tsur.relative_user_id = #{relativeUserId}</if>
		<if test="relationship != null  and relationship != ''"> and tsur.relationship = #{relationship}</if>
		<if test="mobile != null  and mobile != ''"> and tsur.mobile = #{mobile}</if>
		<if test="createTime != null "> and tsur.create_time = #{createTime}</if>
        </where>
        order by tsur.create_time desc
    </select>

</mapper>