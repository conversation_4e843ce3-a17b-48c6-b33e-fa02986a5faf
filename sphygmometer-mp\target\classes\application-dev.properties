server.port=8706

# 数据库相关
db.ip-port=127.0.0.1:3306
db.username=sphygmometer
db.password=sphygmometer123

#redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379

wxmp.appId=
wxmp.appSecret=
wx.verify.token=
wx.open.ma.appId=

#微信模板消息ID
#测量第一步，开始检测提示模板ID
wxmp.test.step1.start.tpl.msg.id=PTE5voV3ps6mxciiN_jZSSjA4kG9nev73Gb3LNJKRCY
#跳转URL
wxmp.test.step1.start.tpl.msg.url=https://vip.bj-ihealthcare.com/dev/pages/warmPrompt/warmPrompt
#测量第一步排队提示模板ID
wxmp.test.step1.tip.tpl.msg.id=7a2ITgsN7lcxDMad_CI7Fq8ElkBTRJXag2F5e8Zdsuc
#测量提示模板ID
wxmp.test.tip.tpl.msg.id=PTE5voV3ps6mxciiN_jZSSjA4kG9nev73Gb3LNJKRCY
#测量结果模板ID
wxmp.test.result.tpl.msg.id=ucm0clp1x9vjTw_vbCHzzWJRZGsEDF2dfp1KHAIt_D0


#臂筒式设备socket配置
device.socket.host=
device.socket.port=