package com.bojun.system.dto;

import com.bojun.system.entity.Role;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色表对象 t_v2_role
 *
 * <AUTHOR>
 * @date 2021-06-15 11:01:35
 */
@ApiModel(value = "RoleDTO对象")
@Data
public class RoleDTO extends Role {
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
}
