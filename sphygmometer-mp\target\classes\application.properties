spring.application.name=sphygmometer-mp
spring.profiles.active=dev
server.servlet.context-path=/sphygmometer-mp

##mybatis-plus##
mybatis-plus.config-location=classpath:mybatis/mybatis-config.xml
mybatis-plus.mapper-locations=classpath:mybatis/mapper/*.xml

spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=jdbc:mysql://${db.ip-port}/sphygmometer?useUnicode=true&characterEncoding=utf-8&useSSL=false&zeroDateTimeBehavior=convertToNull
spring.datasource.username=${db.username}
spring.datasource.password=${db.password}

spring.datasource.maximum-pool-size=100
spring.datasource.max-idle=10
spring.datasource.max-wait=10000
spring.datasource.min-idle=5
spring.datasource.initial-size=5
spring.datasource.validation-query=SELECT 1
spring.datasource.test-on-borrow=false
spring.datasource.test-while-idle=true
spring.datasource.time-between-eviction-runs-millis=18800

#reids#
spring.redis.database=0
spring.redis.lettuce.pool.max-active=100
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=0
spring.redis.timeout=10000
#netty socket#
netty.server.port=15002

#wx config#
wx.accesstoken.url=https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential
wx.messsage.send.url=https://api.weixin.qq.com/cgi-bin/message/custom/send
wx.user.getopenid.url=https://api.weixin.qq.com/sns/oauth2/access_token
wx.user.getbaseinfo.url=https://api.weixin.qq.com/cgi-bin/user/info

#swagger2 config#
swagger.project.title=\u9ad8\u8840\u538b\u516c\u4f17\u53f7\u7cfb\u7edf\u63a5\u53e3\u6587\u6863
swagger.project.description=\u9ad8\u8840\u538b\u516c\u4f17\u53f7\u7cfb\u7edf\u63a5\u53e3\u6587\u6863
swagger.project.groupname=1.0
swagger.project.version=1.0
swagger.project.base.package=com.bojun.sphygmometer.mq.controller
swagger.project.base.url=http\://localhost\:${server.port}/sphygmometer-mp
