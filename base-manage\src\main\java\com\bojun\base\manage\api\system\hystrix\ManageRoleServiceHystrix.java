/**
 * 
 */
package com.bojun.base.manage.api.system.hystrix;

import org.springframework.stereotype.Component;

import com.bojun.base.manage.api.system.IManageRoleService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.dto.ManageRoleDTO;
import com.bojun.system.entity.ManageRole;

/**
*Model：角色信息接口熔断器
*Description：角色信息接口熔断器
*Author: lj
*created：2020年4月23日
*/
@Component
public class ManageRoleServiceHystrix implements IManageRoleService {

	@Override
	public String addManageRole(ManageRoleDTO manageRole) {
		throw new BaseRuntimeException("addManageRole接口服务已断开");
	}

	@Override
	public String updateManageRole(ManageRoleDTO manageRole) {
		throw new BaseRuntimeException("updateManageRole接口服务已断开");
	}

	@Override
	public String getManageRoleList(ManageRoleDTO manageRole) {
		throw new BaseRuntimeException("getManageRoleList接口服务已断开");
	}

	@Override
	public String enableDisableManageRole(ManageRole manageRole) {
		throw new BaseRuntimeException("enableDisableManageRole接口服务已断开");
	}

	@Override
	public String getManageRoleOne(ManageRoleDTO manageRole) {
		throw new BaseRuntimeException("getManageRoleOne接口服务已断开");
	}

	
	
}
