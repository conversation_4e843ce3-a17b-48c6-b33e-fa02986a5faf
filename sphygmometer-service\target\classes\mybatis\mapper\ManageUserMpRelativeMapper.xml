<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.ManageUserMpRelativeMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.ManageUserMpRelativeDTO" id="ManageUserMpRelativeDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="openId"    column="open_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectManageUserMpRelative">
    	select
	        id,
	        user_id,
	        open_id,
	        create_time
		from 
        	t_manage_user_mp_relative
    </sql>

    <select id="selectManageUserMpRelativeById" parameterType="int" resultMap="ManageUserMpRelativeDTOResult">
		<include refid="selectManageUserMpRelative"/>
		where 
        	id = #{id}
    </select>

    <select id="selectManageUserMpRelativeList" parameterType="com.bojun.sphygmometer.dto.ManageUserMpRelativeDTO" resultMap="ManageUserMpRelativeDTOResult">
        <include refid="selectManageUserMpRelative"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>

</mapper>