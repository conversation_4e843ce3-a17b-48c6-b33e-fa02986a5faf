package com.bojun.push;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.alibaba.fastjson.JSONObject;
import com.bojun.utils.HttpUtil;
import com.bojun.utils.PropertiesUtils;

/**
 * <AUTHOR>
 * @Description 微信公众号消息推送工具类
 * @Date 2021/3/30
 * @Param
 * @return
 **/
public class WXPublicPlatformPushUtils {

	Log log = LogFactory.getLog(WXPublicPlatformPushUtils.class);

	// 公众号--appid
	private static final String PUBLIC_APP_ID = PropertiesUtils.getProperty("config.properties", "wx.public.appid");

	// 公众号--秘钥
	private static final String PAUBLIC_SECRET = PropertiesUtils.getProperty("config.properties", "wx.public.secret");

	//映射地址
	private static String weiapiUrl = PropertiesUtils.getProperty("config.properties", "wx.api");
	//映射地址
	private static String weiopenUrl = PropertiesUtils.getProperty("config.properties", "wx.open");
	
	public static void main(String[] args) {

	}

	/**
	 * <AUTHOR>
	 * @Description 发送公众号消息
	 * @Date 2021/3/30
	 * @Param
	 * @return
	 **/
	public static boolean pushMsg(String templateId) {
		String accessToken = getAccessToken();
		String url = weiapiUrl+"cgi-bin/message/template/send?access_token=" + accessToken;
		//参数
		Map<String,Object> params = new HashMap<String,Object>();
		params.put("templateId", templateId);
		HttpUtil.doPost(url, params);
		return true;
	}
	
	
	
	/**
	 * <AUTHOR>
	 * @Description 获取accessToken
	 * @Date 2021/3/30
	 * @Param
	 * @return
	 **/
	private static String getAccessToken() {
		String url = weiapiUrl+"cgi-bin/token?grant_type=client_credential&appid=" + PUBLIC_APP_ID + "&secret=" + PAUBLIC_SECRET;
		String result = HttpUtil.doGet(url);
		Map<String, Object> map = (Map<String, Object>) JSONObject.parse(result);
		String accessToken = String.valueOf(map.get("access_token"));
		return accessToken;
	}
	
	
	/**
	 * <AUTHOR>
	 * @Description 获取消息模板
	 * @Date 2021/3/30
	 * @Param
	 * @return
	 **/
	private void getMsgTemplate() {
		String accessToken = getAccessToken();
		String url = weiapiUrl+"cgi-bin/template/get_all_private_template?access_token=" + accessToken;
		String result = HttpUtil.doGet(url);
		Map<String, Object> map = (Map<String, Object>) JSONObject.parse(result);
	}
	
	
}
