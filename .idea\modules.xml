<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/base-manage/base-manage.iml" filepath="$PROJECT_DIR$/base-manage/base-manage.iml" />
      <module fileurl="file://$PROJECT_DIR$/commons_redis/commons_redis.iml" filepath="$PROJECT_DIR$/commons_redis/commons_redis.iml" />
      <module fileurl="file://$PROJECT_DIR$/commons_sms/commons_sms.iml" filepath="$PROJECT_DIR$/commons_sms/commons_sms.iml" />
      <module fileurl="file://$PROJECT_DIR$/commons_utils/commons_utils.iml" filepath="$PROJECT_DIR$/commons_utils/commons_utils.iml" />
      <module fileurl="file://$PROJECT_DIR$/eureka-server/eureka-server.iml" filepath="$PROJECT_DIR$/eureka-server/eureka-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/health-promotion-service/health-promotion-service.iml" filepath="$PROJECT_DIR$/health-promotion-service/health-promotion-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/health-promotion-service-api/health-promotion-service-api.iml" filepath="$PROJECT_DIR$/health-promotion-service-api/health-promotion-service-api.iml" />
      <module fileurl="file://$PROJECT_DIR$/organization-entity/organization-entity.iml" filepath="$PROJECT_DIR$/organization-entity/organization-entity.iml" />
      <module fileurl="file://$PROJECT_DIR$/organization-service/organization-service.iml" filepath="$PROJECT_DIR$/organization-service/organization-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/organization-service-api/organization-service-api.iml" filepath="$PROJECT_DIR$/organization-service-api/organization-service-api.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-app/sphygmometer-app.iml" filepath="$PROJECT_DIR$/sphygmometer-app/sphygmometer-app.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-entity/sphygmometer-entity.iml" filepath="$PROJECT_DIR$/sphygmometer-entity/sphygmometer-entity.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-manage/sphygmometer-manage.iml" filepath="$PROJECT_DIR$/sphygmometer-manage/sphygmometer-manage.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-mp/sphygmometer-mp.iml" filepath="$PROJECT_DIR$/sphygmometer-mp/sphygmometer-mp.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-service/sphygmometer-service.iml" filepath="$PROJECT_DIR$/sphygmometer-service/sphygmometer-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-service-api/sphygmometer-service-api.iml" filepath="$PROJECT_DIR$/sphygmometer-service-api/sphygmometer-service-api.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-socket/sphygmometer-socket.iml" filepath="$PROJECT_DIR$/sphygmometer-socket/sphygmometer-socket.iml" />
      <module fileurl="file://$PROJECT_DIR$/sphygmometer-wxapp/sphygmometer-wxapp.iml" filepath="$PROJECT_DIR$/sphygmometer-wxapp/sphygmometer-wxapp.iml" />
      <module fileurl="file://$PROJECT_DIR$/system-entity/system-entity.iml" filepath="$PROJECT_DIR$/system-entity/system-entity.iml" />
      <module fileurl="file://$PROJECT_DIR$/system-service/system-service.iml" filepath="$PROJECT_DIR$/system-service/system-service.iml" />
      <module fileurl="file://$PROJECT_DIR$/system-service-api/system-service-api.iml" filepath="$PROJECT_DIR$/system-service-api/system-service-api.iml" />
    </modules>
  </component>
</project>