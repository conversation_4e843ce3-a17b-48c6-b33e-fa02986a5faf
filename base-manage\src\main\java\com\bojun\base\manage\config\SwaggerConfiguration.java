/**
 * 
 */
package com.bojun.base.manage.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.github.xiaoymin.swaggerbootstrapui.annotations.EnableSwaggerBootstrapUI;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
*Model：swagger2接口文档配置类
*Description：swagger2接口文档配置类
*Author:段德鹏
*created：2020年3月27日
*/
@Configuration
@EnableSwagger2
@EnableSwaggerBootstrapUI
public class SwaggerConfiguration {

	@Bean
	public Docket createRestApi() {
	     return new Docket(DocumentationType.SWAGGER_2)
	     .useDefaultResponseMessages(false)
	     .apiInfo(apiInfo())
	     .groupName("1.0版本")
	     .select()
	     .apis(RequestHandlerSelectors.basePackage("com.bojun.base.manage.controller"))
	     .paths(PathSelectors.any())
	     .build();
	}

	private ApiInfo apiInfo() {
	     return new ApiInfoBuilder()
	     .title("配置管理系统")
	     .description("配置管理系统接口文档")
	     .termsOfServiceUrl("http://localhost:8012/baseManage")
	     .version("1.0")
	     .build();
	}

}
