package com.bojun.base.manage.controller.notice.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 15:17
 */
@ApiModel(value = "删除通知通告", description = "删除通知通告")
public class DelNoticeVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;

    @ApiModelProperty(value = "多个Id")
    private List<String> noticeIds;
    @ApiModelProperty(value = "是否删除，删除时传1", example = "1")
    private int isDelete;

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public List<String> getNoticeIds() {
        return noticeIds;
    }

    public void setNoticeIds(List<String> noticeIds) {
        this.noticeIds = noticeIds;
    }


}
