package com.bojun.system.dto;

import com.bojun.system.entity.ManageUserMemorandum;
/**
 * 
*Model：管理员备忘录信息表
*Description：管理员备忘录信息表DTO
*Author:李欣颖
*created：2020年3月21日
 */
public class ManageUserMemorandumDTO  extends ManageUserMemorandum {

   private static final long serialVersionUID = -1344026328907379635L;

   private String deleteTimeStr; // 删除时间
	private String createTimeStr; // 创建时间
	 private String memoDateStr;

	public String getMemoDateStr() {
		return memoDateStr;
	}

	public void setMemoDateStr(String memoDateStr) {
		this.memoDateStr = memoDateStr;
	}

	public String getDeleteTimeStr() {
		return deleteTimeStr;
	}

	public void setDeleteTimeStr(String deleteTimeStr) {
		this.deleteTimeStr = deleteTimeStr;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

  

}
