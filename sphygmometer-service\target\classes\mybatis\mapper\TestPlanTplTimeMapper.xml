<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.TestPlanTplTimeMapper">

    <resultMap type="com.bojun.sphygmometer.dto.TestPlanTplTimeDTO" id="TestPlanTplTimeDTOResult">
        <result property="id" column="id"/>
        <result property="testPlanTplId" column="test_plan_tpl_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

    <sql id="selectTestPlanTplTime">
        select id,
               test_plan_tpl_id,
               start_time,
               end_time,
               create_time,
               create_user_id,
               create_user_name,
               update_time,
               update_user_id,
               update_user_name,
               is_delete
        from t_test_plan_tpl_time
    </sql>

    <select id="selectTestPlanTplTimeById" parameterType="int" resultMap="TestPlanTplTimeDTOResult">
        <include refid="selectTestPlanTplTime"/>
        where
        id = #{id}
    </select>

    <select id="selectTestPlanTplTimeList" parameterType="com.bojun.sphygmometer.dto.TestPlanTplTimeDTO"
            resultMap="TestPlanTplTimeDTOResult">
        <include refid="selectTestPlanTplTime"/>
        where is_delete = 0
        <if test="id != null ">and id = #{id}</if>
        <if test="testPlanTplId != null ">and test_plan_tpl_id = #{testPlanTplId}</if>
        <if test="startTime != null  and startTime != ''">and start_time = #{startTime}</if>
        <if test="endTime != null  and endTime != ''">and end_time = #{endTime}</if>
        <if test="createTime != null ">and create_time = #{createTime}</if>
        <if test="createUserId != null ">and create_user_id = #{createUserId}</if>
        <if test="createUserName != null  and createUserName != ''">and create_user_name = #{createUserName}</if>
        <if test="updateTime != null ">and update_time = #{updateTime}</if>
        <if test="updateUserId != null ">and update_user_id = #{updateUserId}</if>
        <if test="updateUserName != null  and updateUserName != ''">and update_user_name = #{updateUserName}</if>
    </select>

</mapper>