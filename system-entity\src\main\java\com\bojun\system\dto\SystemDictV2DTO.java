package com.bojun.system.dto;

import com.bojun.system.entity.SystemDictV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统（项目）字典信息表对象 t_system_dict_v2
 * 
 * <AUTHOR>
 * @date 2021-06-21 11:37:26
 */
@ApiModel(value = "SystemDictV2DTO对象")
@Data
public class SystemDictV2DTO extends SystemDictV2 {
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
}
