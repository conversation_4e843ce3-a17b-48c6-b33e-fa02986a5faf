/**
 * 
 */
package com.bojun.base.manage.controller.homePage.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：主页菜单
 * Description：主页菜单
 * Author：lj
 * created： 2020年4月28日
 */
@ApiModel(value = "主页菜单入参", description = "主页菜单入参")
public class HomeTreeMenuParamVO implements Serializable {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = -2861615641502296513L;

	
	@ApiModelProperty(value="系统id", required = true, example = "1")
	private String systemId;	
	
	@NotEmpty(message = "角色ID不能为空")
	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;
	
	@NotNull(message = "权限类型不能为空 ")
	@ApiModelProperty(value = "（0：超级管理员，1：普通管理员）", required = true, example = "1")
	private Integer authType;

	
	@ApiModelProperty(value="产品名称", required = true, example = "1")
	private String searchKey;
	
	
	
	
	public String getSearchKey() {
		return searchKey;
	}

	public void setSearchKey(String searchKey) {
		this.searchKey = searchKey;
	}

	public Integer getAuthType() {
		return authType;
	}

	public void setAuthType(Integer authType) {
		this.authType = authType;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	
	
}
