package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：产品管理
 * Description：系统类型实体
 * Author：赖水秀
 * created： 2020年4月27日
 */
public class SystemDictType implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7192147948131559959L;
	
	private Integer systemTypeId; //系统类型id
	
	private String systemTypeName;	//系统类型名称
	
	private Integer isEnabled;	//启用标记   0：否   1：是
	
	private Integer showIndex;	//排序下标
	
	private String remark;	//备注

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public String getSystemTypeName() {
		return systemTypeName;
	}

	public void setSystemTypeName(String systemTypeName) {
		this.systemTypeName = systemTypeName;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}
