package com.bojun.base.system.controller;


import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.ISystemOperationLogService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.OperationLogDTO;
import com.bojun.system.entity.OperationLog;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

/**
 * Model： 模块名称
 * Description：文件描述
 * Author：黄卫平
 * created： 2020年4月27日
 */
@RestController
public class SystemLogsController extends BoJunBaseController {

    private static Log logger = LogFactory.getLog(SystemLogsController.class);

    @Autowired
    private ISystemOperationLogService systemOperationLogService;


    /**
     * @Description 模糊搜索日志接口
     * <AUTHOR>
     * @param operationLogDTO
     * void
     * 2020年4月27日
     */
    @RequestMapping(value = "/getSystemLogList", method = RequestMethod.POST)
    public void getSystemLogList(@RequestBody OperationLogDTO operationLogDTO) {
        try {

            if(!StringUtils.isBlank(operationLogDTO.getSearchKey())) {

                String key = operationLogDTO.getSearchKey();
                // 同一个查询条件匹配多个字段模糊查找，转换为多个字段模糊查询
                // 效率不高  ！！！！！！！
                operationLogDTO.setIpAddress(key);
                operationLogDTO.setExceptionMsg(key);
                operationLogDTO.setOperationContent(key);
                operationLogDTO.setRequestAction(key);
                operationLogDTO.setRequestParams(key);
                operationLogDTO.setWorkNumber(key);
                operationLogDTO.setRealName(key);
                operationLogDTO.setAccountNo(key);
            }

            int pageNum = (null == operationLogDTO.getPageNum()?1:operationLogDTO.getPageNum());
            int everyPage = (null == operationLogDTO.getEveryPage()?10:operationLogDTO.getEveryPage());
            PageHelper.startPage(pageNum, everyPage);

            Page<List<OperationLogDTO>> page = systemOperationLogService.getSystemLogList(operationLogDTO);
            if (page == null || page.getTotal() == 0) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }

            outJson(successPageInfo(page.getResult(), page.getTotal()));
        } catch (Exception e) {
            logger.error("getSystemLogList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }
    
    /**
     * @Description  新增系统日志
     * <AUTHOR>
     * @param operationLog
     * @return void
     * created：2020年6月4日
     */
    @RequestMapping(value = "/addSystemLog", method = RequestMethod.POST)
    public void addSystemLog(@RequestBody OperationLog operationLog) {
    	try {
    		systemOperationLogService.addSystemLog(operationLog);
    		outJson(successInfo());
    	} catch (Exception e) {
    		logger.error("addSystemLog:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
    	}
    	
    }
}
