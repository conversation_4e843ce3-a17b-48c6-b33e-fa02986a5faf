
package com.bojun.base.system.mapper;

import com.bojun.system.dto.ManageUserRoleDTO;
import com.bojun.system.entity.ManageUserRole;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;


/**
 * 
*Model：菜单表
*Description：菜单表
*Author:李欣颖
*created：2020年3月3日
 */
@Mapper
public interface ManageUserRoleMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年3月3日
	 */
	Integer queryManageUserRoleCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询菜单表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<ManageUserRoleDTO>
     * created：2020年3月3日
	 */
	List<ManageUserRoleDTO> getManageUserRole(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增菜单表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年3月3日
	 */
	Integer addManageUserRole(ManageUserRoleDTO manageUserRoleDTO);

	/**
	 * 
	 * @Description 删除菜单表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年3月3日
	 */
	Integer deleteManageUserRole(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改菜单表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年3月3日
	 */
	Integer updateManageUserRole(ManageUserRoleDTO manageUserRoleDTO);

	/**
	 * 
	 * @Description 查询单个菜单表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return ManageUserRoleDTO
	 * created：2020年3月3日
	 */
	ManageUserRoleDTO getManageUserRoleById(Map<String, Object> mapPara);

	ManageUserRole getRoleByProductAndUserId(Map<String, Object> resultMap);


	
}
