package com.bojun.base.system.service.impl;

import com.bojun.base.system.mapper.SystemOperationLogMapper;
import com.bojun.base.system.service.ISystemOperationLogService;
import com.bojun.system.dto.OperationLogDTO;
import com.bojun.system.entity.OperationLog;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * Model： 日志管理模块服务实现类
 * Description：日志管理模块服务实现类
 * Author：黄卫平
 * created： 2020年4月27日
 */
@Service
public class SystemOperationLogServiceImpl implements ISystemOperationLogService {

    @Autowired
    private SystemOperationLogMapper systemOperationLogMapper;

    @Override
    public Page<List<OperationLogDTO>> getSystemLogList(OperationLogDTO operationLogDTO) {

        return systemOperationLogMapper.getSystemLogList(operationLogDTO);
    }

	@Override
	public int addSystemLog(OperationLog operationLog) {
		return systemOperationLogMapper.addSystemLog(operationLog);
	}
    
    
}
