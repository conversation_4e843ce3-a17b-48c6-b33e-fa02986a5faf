package com.bojun.base.manage.controller.version;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.VersionManagerService;
import com.bojun.base.manage.controller.version.vo.AddVersionManagerVo;
import com.bojun.base.manage.controller.version.vo.SelectVersionManagerVo;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.entity.AppVersion;
import com.bojun.system.entity.ManageUser;
import com.bojun.utils.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 11:24
 */
@RestController
@RequestMapping("/versionManager")
@Api(tags = {"版本管理"})
@ApiSort(value = 1)
public class VersionManagerController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(VersionManagerController.class);


    @Autowired
    private VersionManagerService versionManagerService;


    @ApiOperation(value = "添加版本", notes = "添加版本管理（赖允翔）")
    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/addVersion")
    @SystemLog(action = "addVersion", description = "添加版本", operationType = Contants.ADD_REQUEST)
    @AuthAnnotation(action = "addVersion")
    public Results addVersion(@RequestBody AddVersionManagerVo addVersionManagerVo, HttpServletRequest request) {
        try {
            AppVersion appVersion = new AppVersion();
            BeanUtils.copyProperties(addVersionManagerVo, appVersion);
            ManageUser manageUser = (ManageUser) request.getAttribute("manageUser");
            appVersion.setCreateUserId(manageUser.getUserId());
            String result = versionManagerService.addVersion(appVersion);
            return returnResults(result);
        } catch (Exception e) {
            logger.error("addVersion:", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    @ApiOperation(value = "版本管理（全查）", notes = "版本管理（赖允翔）")
    @ApiOperationSupport(order = 2)
    @PostMapping(value = "/getVersionManager")
    @AuthAnnotation(action = "/getVersionManager")
    public Results<SelectVersionManagerVo> getVersionManager(@RequestBody SelectVersionManagerVo versionManagerVo) {
        try {
            AppVersion appVersion = new AppVersion();
            BeanUtils.copyProperties(versionManagerVo, appVersion);
            appVersion.setStartTime(DateUtil.toMin(DateUtil.toDate(versionManagerVo.getStartTime(),"yyyy-MM-dd")));
            appVersion.setEndTime(DateUtil.toMax(DateUtil.toDate(versionManagerVo.getEndTime(),"yyyy-MM-dd")));
            String result = versionManagerService.getVersionManager(appVersion);
            return returnResultsPage(result, SelectVersionManagerVo.class);
        } catch (BeansException e) {
            logger.error("/getVersionManager", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    @ApiOperation(value = "查询产品名称", notes = "查询产品名称（赖允翔）")
    @ApiOperationSupport(order = 2)
    @PostMapping(value = "/getSystemDict")
    @AuthAnnotation(action = "/getSystemDict")
    public Results<List<SystemDictDTO>> getSystemDict() {
        try {
            String result = versionManagerService.getSystemDict();
            return returnResultsPage(result, SystemDictDTO.class);
        } catch (BeansException e) {
            logger.error("/getSystemDict", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }
}
