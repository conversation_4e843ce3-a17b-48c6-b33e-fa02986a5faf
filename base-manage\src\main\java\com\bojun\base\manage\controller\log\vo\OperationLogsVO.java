package com.bojun.base.manage.controller.log.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

/**
 *Model：
 *Description：操作日志
 *Author: 黄卫平
 *created：2020年4月23日
 */
@ApiModel("系统操作日志")
public class OperationLogsVO {

    @ApiModelProperty(value = "日志主键ID")
    private Integer id;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "系统id")
    private String systemId;

    @ApiModelProperty(value = "操作类型  1：新增  2：修改  3：删除   4：登陆登出")
    private Integer operationType;

    @ApiModelProperty(value = "操作接口")
    private String requestAction;

    @ApiModelProperty(value = "请求参数")
    private String requestParams;

    @ApiModelProperty(value = "操作内容")
    private String operationContent;

    @ApiModelProperty(value = "是否是异常请求  0：否  1：是")
    private Integer isException;

    @ApiModelProperty(value = "异常信息")
    private String exceptionMsg;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @ApiModelProperty(value = "IP地址")
    private String ipAddress;

    @ApiModelProperty(value = "操作时间")
    private String operationTimeStr;

    @ApiModelProperty(value = "登录账号")
    private String accountNo; // 登录账号

    @ApiModelProperty(value = "操作人工号")
    private String workNumber; //工号

    @ApiModelProperty(value = "操作人姓名")
    private String realName; // 姓名

    @ApiModelProperty(value = "系统名称")
    private String systemName; //系统名称

    public Integer getId() {
        return id;
    }

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId){
        this.userId = userId;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId){
        this.systemId = systemId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType){
        this.operationType = operationType;
    }

    public String getRequestAction() {
        return requestAction;
    }

    public void setRequestAction(String requestAction){
        this.requestAction = requestAction;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams){
        this.requestParams = requestParams;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent){
        this.operationContent = operationContent;
    }

    public Integer getIsException() {
        return isException;
    }

    public void setIsException(Integer isException){
        this.isException = isException;
    }

    public String getExceptionMsg() {
        return exceptionMsg;
    }

    public void setExceptionMsg(String exceptionMsg){
        this.exceptionMsg = exceptionMsg;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime){
        this.operationTime = operationTime;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getOperationTimeStr() {
        return operationTimeStr;
    }

    public void setOperationTimeStr(String operationTimeStr) {
        this.operationTimeStr = operationTimeStr;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
}