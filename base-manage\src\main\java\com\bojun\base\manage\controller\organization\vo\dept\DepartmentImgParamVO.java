package com.bojun.base.manage.controller.organization.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Model：模块名称
 * Description：文件描述
 * Author: 肖泽权
 * created：2020年5月7日
 */
@ApiModel(value = "科室图片信息", description = "科室图片信息")
public class DepartmentImgParamVO implements Serializable {
	
	private static final long serialVersionUID = 109958065299895997L;

	@NotEmpty(message = "图片名称")
	@ApiModelProperty(value = "图片名称", required = true, example = "hdu8h.jpg")
	private String deptImage;

	@NotEmpty(message = "图片地址")
    @ApiModelProperty(value = "图片地址", example = "http://************:8095/bojunFile/dept/hdu8h.jpg")
    private String deptImageUrl;

	@NotNull(message = "是否封面")
	@ApiModelProperty(value = "是否封面", required = true, example = "1")
	private Integer isCover;

    @NotNull(message = "显示下标")
    @ApiModelProperty(value = "显示下标", example = "1")
    private Integer showIndex;

	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}

	public String getDeptImageUrl() {
		return deptImageUrl;
	}

	public void setDeptImageUrl(String deptImageUrl) {
		this.deptImageUrl = deptImageUrl;
	}

	public String getDeptImage() {
		return deptImage;
	}

	public void setDeptImage(String deptImage) {
		this.deptImage = deptImage;
	}

	public Integer getIsCover() {
		return isCover;
	}

	public void setIsCover(Integer isCover) {
		this.isCover = isCover;
	}
	
}

