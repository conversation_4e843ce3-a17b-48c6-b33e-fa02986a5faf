package com.bojun.base.system.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestBody;

import com.bojun.system.dto.MessageNotificationDTO;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月30日
*/
public interface MessageNotificationService {
	
	/**
	 * 
	 * @Description 查询消息通知（站内）信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	public List<MessageNotificationDTO> getMessageNotification(@RequestBody Map<String, Object> mapPara);
	
	
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer queryMessageNotificationCount(@RequestBody Map<String, Object> paramsMap);
	
	
	/**
	 * 
	 * @Description 新增消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer addMessageNotification(@RequestBody MessageNotificationDTO messageNotificationDTO);
	
	
	/**
	 * 
	 * @Description 删除消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	public int deleteMessageNotification(@RequestBody Map<String, Object> paramsMap);
	
	/**
	 * 
	 * @Description 修改消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	public int updateMessageNotification(@RequestBody MessageNotificationDTO messageNotificationDTO);
	
	
	/**
	 * 
	 * @Description 查询单个消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return MessageNotificationDTO
	 */
	public MessageNotificationDTO getMessageNotificationById(@RequestBody Map<String, Object> paramsMap);

	
	/**
	 * 
	 * @Description 查询推送人
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	public List<MessageNotificationDTO> findListUser(@RequestBody Map<String, Object> paramsMap);
	
	
	
	/**
	 * 
	 * @Description 查询推送科室
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	public List<MessageNotificationDTO> findListDept(@RequestBody Map<String, Object> paramsMap);
	
	
	
	
	public List<MessageNotificationDTO> getMessageNotificationType();

}

