package com.bojun.base.system.api.controller;

import com.bojun.base.system.api.SystemDictV2FeignClient;
import com.bojun.base.system.service.SystemDictV2Service;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.system.dto.SystemDictV2DTO;
import com.bojun.system.entity.SystemDictV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统（项目）字典信息表FeignController
 * 
 * <AUTHOR>
 * @date 2021-06-21 11:37:26
 */
@RestController
public class SystemDictV2FeignController extends BaseFeignController implements SystemDictV2FeignClient {
    @Autowired
    private SystemDictV2Service systemDictV2Service;

	/**
     * 查询系统（项目）字典信息表分页列表
     */
    @PostMapping(PREFIX + "/page")
    public Results<PageData<SystemDictV2DTO>> page(@RequestBody SystemDictV2DTO systemDictV2DTO){
        startPage(systemDictV2DTO.getPageNum(), systemDictV2DTO.getEveryPage());
        List<SystemDictV2DTO> list = systemDictV2Service.selectSystemDictV2List(systemDictV2DTO);
        return Results.list(getPageData(list));
    }

    /**
     * 查询系统（项目）字典信息表列表
     */
    @PostMapping(PREFIX + "/list")
    public Results<List<SystemDictV2DTO>> list(@RequestBody SystemDictV2DTO systemDictV2DTO){
        List<SystemDictV2DTO> list = systemDictV2Service.selectSystemDictV2List(systemDictV2DTO);
        return Results.list(list);
    }

    /**
     * 获取系统（项目）字典信息表详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<SystemDictV2DTO> getInfo(@RequestParam("systemId") String systemId){
        SystemDictV2DTO systemDictV2DTO = systemDictV2Service.selectSystemDictV2ById(systemId);
        return Results.data(systemDictV2DTO);
    }

    /**
     * 新增系统（项目）字典信息表DTO
     */
    @PostMapping(PREFIX + "/addDTO")
    public Results addDTO(@RequestBody SystemDictV2DTO systemDictV2DTO){
    	Integer num = systemDictV2Service.insertSystemDictV2(systemDictV2DTO);
        return Results.opResult(num);
    }

    /**
     * 修改系统（项目）字典信息表DTO
     */
    @PostMapping(PREFIX + "/editDTO")
    public Results editDTO(@RequestBody SystemDictV2DTO systemDictV2DTO){
        Integer num = systemDictV2Service.updateSystemDictV2(systemDictV2DTO);
        return Results.opResult(num);
    }
    
    /**
     * 新增系统（项目）字典信息表
     */
    @PostMapping(PREFIX + "/add")
    public Results add(@RequestBody SystemDictV2 systemDictV2){
        Integer num = systemDictV2Service.insertSystemDictV2(systemDictV2);
        return Results.opResult(num);
    }

    /**
     * 修改系统（项目）字典信息表
     */
    @PostMapping(PREFIX + "/edit")
    public Results edit(@RequestBody SystemDictV2 systemDictV2){
        Integer num = systemDictV2Service.updateSystemDictV2(systemDictV2);
        return Results.opResult(num);
    }

    /**
     * 删除系统（项目）字典信息表，多个以逗号分隔
     */
    @GetMapping(PREFIX + "/removeByIds")
    public Results removeByIds(@RequestParam("ids") String ids) {
        Integer num = systemDictV2Service.deleteSystemDictV2ByIds(ids);
        return Results.opResult(num);
    }
}
