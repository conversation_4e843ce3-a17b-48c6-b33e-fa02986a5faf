package com.bojun.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 * <AUTHOR>
 *
 */
public class StringUtil {
	public static final char[] NUMBER = new char[] { '1', '2', '3', '4', '5', '6', '7', '8', '9', '0' };
	public static final char[] ALPHEBIC = new char[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M',
			'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };
	public static final char[] ALPHEBIC_AND_NUMBER_UPPER = new char[] { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I',
			'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '1', '2', '3', '4',
			'5', '6', '7', '8', '9', '0' };
	public static final char[] ALPHEBIC_AND_NUMBER_LOWER = new char[] { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i',
			'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '1', '2', '3', '4',
			'5', '6', '7', '8', '9', '0' };

	public static final char[] ALPHEBIC_LOWER = new char[] { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l',
			'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' };

	public static String getRandomUpperStrNum(int strLength) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < strLength; i++) {
			int j = (int) (Math.random() * ALPHEBIC_AND_NUMBER_UPPER.length);
			sb.append(j);
		}
		return sb.toString();
	}

	public static String getAlphaOrder(int order) {
		StringBuffer sb = new StringBuffer();
		int size = ALPHEBIC.length;
		int i = order - 1;
		if (i < 0)
			throw new IllegalArgumentException();
		while (i >= size) {
			int j = i % size;
			i = i / size - 1;
			sb.insert(0, ALPHEBIC[j]);
		}
		sb.insert(0, ALPHEBIC[i]);
		return sb.toString();
	}

	public static String getRandomLowerStr(int strLength) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < strLength; i++) {
			int j = (int) (Math.random() * ALPHEBIC_AND_NUMBER_LOWER.length);
			sb.append(ALPHEBIC_AND_NUMBER_LOWER[j]);
		}
		return sb.toString();
	}

	public static String getRandomLower(int strLength) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < strLength; i++) {
			int j = (int) (Math.random() * ALPHEBIC_LOWER.length);
			sb.append(ALPHEBIC_LOWER[j]);
		}
		return sb.toString();
	}

	public static String getRandomUpperStr(int strLength) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < strLength; i++) {
			int j = (int) (Math.random() * ALPHEBIC_AND_NUMBER_UPPER.length);
			sb.append(ALPHEBIC_AND_NUMBER_UPPER[j]);
		}
		return sb.toString();
	}

	public static String getRandomStrNum(int strLength) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < strLength; i++) {
			int j = (int) (Math.random() * NUMBER.length);
			sb.append(NUMBER[j]);
		}
		return sb.toString();
	}

	public static String getRandomString(int strLength) {
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < strLength; i++) {
			int j = (int) (Math.random() * ALPHEBIC.length);
			sb.append(ALPHEBIC[j]);
		}
		return sb.toString();
	}



	public static long objectToLong(Object object) {
		if (null == object)
			return 0;
		try {
			return Long.parseLong(object.toString());
		} catch (Exception e) {
			return 0;
		}
	}

	public static int objectToInt(Object object) {
		if (null == object)
			return 0;
		try {
			return Integer.parseInt(object.toString());
		} catch (Exception e) {
			return 0;
		}
	}
	
	/**
	 * 
	 * @Description 生成专项档案编号
	 * <AUTHOR>
	 * @return
	 * String
	 */
	public static String generaterRecordId(String s) {
		//return s+OnlyIdUtils.getOnlyId();
		return generaterRecordIdByCurrentTime(s);
	}

	/**
	 * 
	 * @Description 生成专项档案编号
	 * <AUTHOR>
	 * @return
	 * String
	 */
	public static String generaterRecordIdByCurrentTime(String s) {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String createdateStr = sdf.format(date);
		return s + createdateStr+getRandom(4);
	}
	

	/**
	 * 
	 * @Description 获取指定位数随机数
	 * <AUTHOR>
	 * @param length
	 * @return
	 * String
	 */
	public static String getRandom(int length) {
		String val = "";
		Random random = new Random();
		for (int i = 0; i < length; i++) {
			val += String.valueOf(random.nextInt(10));
		}
		return val;
	}

	public static String generateUUID() {
		return UUID.randomUUID().toString().replaceAll("-", "").toUpperCase();
	}

	/**
	 * 验证手机号码
	 * @Description 验证手机号码
	 * <AUTHOR>
	 * @param mobile
	 * @return
	 * boolean
	 */
	public static boolean isMobile(String mobile) {
		if (mobile == null)
			return false;
		boolean flag = false;
		// flag = java.util.regex.Pattern.matches("( *13[0-9]\\d{8} *)|(
		// *15\\d{9} *)|( *18\\d{9} *)|( *14[57]\\d{8} *)|( *170[059]\\d{7} *)|(
		// *17[768]\\d{8} *)" ,sMobile) ;
		//!/^((0\d{2,3}-?\d{7,8})|(1[356789]\d{9})
		flag = java.util.regex.Pattern.matches("^((0\\d{2,3}-?\\d{7,8})|(1[356789]\\d{9}))$", mobile);
		return flag;
	}

	public static boolean isEmpty(String str) {
		return str == null || str.length() == 0;
	}

	/**
	 * 根据身份证计算年龄
	 * @Description TODO
	 * <AUTHOR>
	 * @param card
	 * @return
	 * int
	 */
	public static int getAgeFromIdNo(String card) {
		String year = "1990";
		String month = "12";
		// String sex = "1";
		if (card.length() == 18) {
			year = card.substring(6).substring(0, 4);// 年份
			month = card.substring(10).substring(0, 2);// 月份
			// sex = card.substring(16).substring(0, 1);
		}
		if (card.length() == 15) {
			year = "19" + card.substring(6, 8);// 年份
			month = card.substring(8, 10);// 月份
			// sex = card.substring(14, 15);
		}

		// if (Integer.parseInt(sex) % 2 == 0) {
		// sex = "女";
		// } else {
		// sex = "男";
		// }
		Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		String currYear = format.format(date).substring(0, 4);// 当前年份
		String currMonth = format.format(date).substring(5, 7);// 月份
		int age = 0;
		if (Integer.parseInt(month) <= Integer.parseInt(currMonth)) { // 当前月份大于用户出身的月份表示已过生
			age = Integer.parseInt(currYear) - Integer.parseInt(year) ;
		} else {// 当前用户还没过生
			age = Integer.parseInt(currYear) - Integer.parseInt(year)-1;
		}
		return age;
	}

	/**
	 * 根据身份证计算性别
	 * @Description TODO
	 * <AUTHOR>
	 * @param card
	 * @return
	 * String
	 */
	public static String getSexFromIdNo(String card) {
		String sex = "1";
		if (card.length() == 18) {
			sex = card.substring(16).substring(0, 1);
		}
		if (card.length() == 15) {
			sex = card.substring(14, 15);
		}

		if (Integer.parseInt(sex) % 2 == 0) {
			sex = "2"; // 女
		} else {
			sex = "1";// 男
		}

		return sex;
	}
	/**
	 * 根据身份证计算性别,健康码专用
	 * @Description TODO
	 * <AUTHOR>
	 * @param card
	 * @return
	 * String
	 */
	public static String getSexByIdNo(String card) {
		String sex = "1";
		if (card.length() == 18) {
			sex = card.substring(16).substring(0, 1);
		}
		if (card.length() == 15) {
			sex = card.substring(14, 15);
		}
       
			if (Integer.parseInt(sex) % 2 == 0) {
				sex = "0"; // 女
			} else {
				sex = "1";// 男
			}
       
		return sex;
	}

	/**
	 * 
	 * @Description: 
	 * @author: 佘亚运
	 * @create: 2019年4月22日
	 *
	 */
	public static boolean isIdCard(String idCard) {
		if (idCard == null)
			return false;
		boolean flag = false;
		String regex = "^\\d{15}|^\\d{17}([0-9]|X|x)$";
		
		flag = match(regex, idCard);
		return flag;
	}

	/**
	 * 
	 * @Description: 通过身份证号码获取生日
	 * @author: 佘亚运
	 * @create: 2019年4月22日
	 *
	 */
	public static String cardToBirthdady(String idCard) {
		if (idCard == null) {
			return "";
		}
		if (isIdCard(idCard) == false) {
			return "";
		}
		if (idCard.length() == 18) {
			idCard= idCard.substring(6, 14);
		}
		if (idCard.length() == 15) {
			idCard=  "19"+idCard.substring(6, 12);
		}
		
		return idCard.substring(0, 4)+"-"+idCard.substring(4, 6)+"-"+idCard.substring(6, 8);
	}

	/**
	* @param regex
	* 正则表达式字符串
	* @param str
	* 要匹配的字符串
	* @return 如果str 符合 regex的正则表达式格式,返回true, 否则返回 false;
	*/
	private static boolean match(String regex, String str) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(str);
		return matcher.matches();
	}
}
