<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.DeviceBindRecordMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.DeviceBindRecordDTO" id="DeviceBindRecordDTOResult">
        <result property="id"    column="id"    />
        <result property="createDate"    column="create_date"    />
        <result property="userId"    column="user_id"    />
        <result property="deviceId"    column="device_id"    />
        <result property="bindStatus"    column="bind_status"    />
        <result property="bindDeviceUserType"    column="bind_device_user_type"    />
    </resultMap>

    <sql id="selectDeviceBindRecord">
    	select
	        id,
	        create_date,
	        user_id,
	        device_id,
	        bind_status,
	        bind_device_user_type
		from 
        	t_device_bind_record
    </sql>

    <select id="selectDeviceBindRecordById" parameterType="int" resultMap="DeviceBindRecordDTOResult">
		<include refid="selectDeviceBindRecord"/>
		where 
        	id = #{id}
    </select>

    <select id="selectDeviceBindRecordList" parameterType="com.bojun.sphygmometer.dto.DeviceBindRecordDTO" resultMap="DeviceBindRecordDTOResult">
        <include refid="selectDeviceBindRecord"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="createDate != null "> and create_date = #{createDate}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="deviceId != null "> and device_id = #{deviceId}</if>
		<if test="bindStatus != null  and bindStatus != ''"> and bind_status = #{bindStatus}</if>
        </where>
    </select>

</mapper>