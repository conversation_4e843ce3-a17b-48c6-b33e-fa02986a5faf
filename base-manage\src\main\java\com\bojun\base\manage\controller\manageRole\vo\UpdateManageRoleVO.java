/**
 * 
 */
package com.bojun.base.manage.controller.manageRole.vo;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.bojun.system.dto.ManageRoleSystemDTO;
import com.bojun.system.entity.ManageRoleOrganMenu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：角色管理 
 * Description：修改角色信息 
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "修改角色信息", description = "修改角色信息传入")
public class UpdateManageRoleVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;



	//@NotEmpty(message = "角色ID不能为空")
	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;

	@NotEmpty(message = "角色名称不能为空")
	@ApiModelProperty(value = "角色名称", required = true, example = "医生")
	private String roleName;

	@NotNull(message = "启用状态不能为空")
	@ApiModelProperty(value = "启用状态： 0：否  1：是", required = true, example = "1")
	private Integer isEnabled; //

	@ApiModelProperty(value = "角色说明", required = true, example = "医生")
	private String roleDescribe;
	
	@NotNull(message = "数据权限不能为空")
	@ApiModelProperty(value = "1: 角色机构  2：账号机构", required = true, example = "2")
	private Integer dataPermissions;//数据权限  
	
	

	@ApiModelProperty(value = "角色关联产品", required = true, example = "")
	private List<ManageRoleSystemDTO> roleSystemList;


	
	@ApiModelProperty(value = "角色关联的结构树", required = true, example = "")
	private List<ManageRoleOrganMenu> organMenu;
	
	@NotNull(message = "角色类型不能为空")
	@ApiModelProperty(value = "1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维", required = true, example = "1")
	private Integer roleType;
	
	//@NotNull(message = "添加角色的用户ID不能为空")
	@ApiModelProperty(value = "添加角色的用户IDId", required = true, example = "1")
	private Integer createUserId;

	
	
	
	
	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public Integer getDataPermissions() {
		return dataPermissions;
	}

	public void setDataPermissions(Integer dataPermissions) {
		this.dataPermissions = dataPermissions;
	}

	public List<ManageRoleOrganMenu> getOrganMenu() {
		return organMenu;
	}

	public void setOrganMenu(List<ManageRoleOrganMenu> organMenu) {
		this.organMenu = organMenu;
	}

	public List<ManageRoleSystemDTO> getRoleSystemList() {
		return roleSystemList;
	}

	public void setRoleSystemList(List<ManageRoleSystemDTO> roleSystemList) {
		this.roleSystemList = roleSystemList;
	}

	

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getRoleDescribe() {
		return roleDescribe;
	}

	public void setRoleDescribe(String roleDescribe) {
		this.roleDescribe = roleDescribe;
	}



}
