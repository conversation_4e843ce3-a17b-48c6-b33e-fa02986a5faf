package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.SystemDictV2Mapper;
import com.bojun.base.system.service.SystemDictV2Service;
import com.bojun.system.dto.SystemDictV2DTO;
import com.bojun.system.entity.SystemDictV2;
import com.bojun.utils.Convert;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * SystemDictV2Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-06-21 11:37:26
 */
@Service
public class SystemDictV2ServiceImpl extends ServiceImpl<SystemDictV2Mapper, SystemDictV2> implements SystemDictV2Service
{

    /**
     * 查询系统（项目）字典信息表
     * 
     * @param systemId 系统（项目）字典信息表ID
     * @return 系统（项目）字典信息表
     */
    @Override
    public SystemDictV2DTO selectSystemDictV2ById(String systemId)
    {
        return this.baseMapper.selectSystemDictV2ById(systemId);
    }

    /**
     * 查询系统（项目）字典信息表列表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 系统（项目）字典信息表集合
     */
    @Override
    public List<SystemDictV2DTO> selectSystemDictV2List(SystemDictV2DTO systemDictV2DTO)
    {
        return this.baseMapper.selectSystemDictV2List(systemDictV2DTO);
    }

    /**
     * 新增系统（项目）字典信息表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 结果
     */
    @Override
    public int insertSystemDictV2(SystemDictV2DTO systemDictV2DTO)
    {
        return this.baseMapper.insert(systemDictV2DTO);
    }

    /**
     * 修改系统（项目）字典信息表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 结果
     */
    @Override
    public int updateSystemDictV2(SystemDictV2DTO systemDictV2DTO)
    {
        return this.baseMapper.updateById(systemDictV2DTO);
    }
    
    /**
     * 新增系统（项目）字典信息表
     * 
     * @param systemDictV2 系统（项目）字典信息表
     * @return 结果
     */
    @Override
    public int insertSystemDictV2(SystemDictV2 systemDictV2)
    {
        return this.baseMapper.insert(systemDictV2);
    }

    /**
     * 修改系统（项目）字典信息表
     * 
     * @param systemDictV2 系统（项目）字典信息表
     * @return 结果
     */
    @Override
    public int updateSystemDictV2(SystemDictV2 systemDictV2)
    {
        return this.baseMapper.updateById(systemDictV2);
    }

    /**
     * 删除系统（项目）字典信息表对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteSystemDictV2ByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除系统（项目）字典信息表信息
     * 
     * @param systemId 系统（项目）字典信息表ID
     * @return 结果
     */
    @Override
    public int deleteSystemDictV2ById(String systemId)
    {
        return this.removeById(systemId) ? 1 : 0;
    }
}
