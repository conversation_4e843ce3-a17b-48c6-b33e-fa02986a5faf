package com.bojun.base.manage.api.system;

import com.bojun.base.manage.api.system.hystrix.VersionManagerServiceHystrix;
import com.bojun.system.entity.AppVersion;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 8:44
 */
@FeignClient(name="system-service", fallback = VersionManagerServiceHystrix.class)
public interface VersionManagerService {
    /**
     * @description: 添加版本号
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @PostMapping(value = "/addVersion")
    String addVersion(@RequestBody AppVersion appVersion);

    /**
     * @description: 添加版本号
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getVersionManager")
    String getVersionManager(@RequestBody AppVersion appVersion);

    /**
     * @description: 查询产品名称
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getSystemDict")
    String getSystemDict();
}
