/**
 * 
 */
package com.bojun.base.manage.controller.login;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.IManageUserService;
import com.bojun.base.manage.controller.login.vo.LoginInfoParamVO;
import com.bojun.base.manage.controller.login.vo.ManageUserLoginVO;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.ip.IPUtils;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.enums.SystemDictEnums;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
*Model：登录，退出
*Description：登录，退出
*Author: 段德鹏
*created：2020年4月23日
 */
@SuppressWarnings({ "unchecked", "rawtypes" })
@RestController
@RequestMapping("login")
@Api(tags = {"登录,登出接口"})
@ApiSort(value = 1)
public class LoginController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(LoginController.class);
	
	@Autowired
	private IManageUserService manageUserService;
	
	@Autowired
	private RedisUtil redisUtil; 
	
	/**
	 * @Description 密码登录
	 * <AUTHOR>
	 * @param request
	 * @param paramsMap
	 * void
	 * created：2020年4月23日
	 */
	@ApiOperation(value = "用户登录", notes = "账号密码登录（段德鹏）")
	@ApiOperationSupport(order = 1)
	@RequestMapping(value="/userLoginByPwd", method = RequestMethod.POST)
	public Results<ManageUserLoginVO> userLoginByPwd(HttpServletRequest request, @RequestBody @Valid LoginInfoParamVO loginInfoVO) {
		try {
			ManageUserDTO manageUserDTO = new ManageUserDTO();
			BeanUtils.copyProperties(loginInfoVO, manageUserDTO);
			//客户端ip地址
			manageUserDTO.setIpAddress(IPUtils.getClientIP(request));
			//产品编码
			manageUserDTO.setSystemId(SystemDictEnums.BASE_CONFIG_MANAGE.getSystemId());
			//请求系统用户登录接口
			String result = manageUserService.userLoginByPwd(manageUserDTO);
			return returnResults(result, ManageUserLoginVO.class);
		} catch (RuntimeException e) {
			logger.error("userLoginByPwd", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("userLoginByPwd:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}	
	
	
	/**
	 * @Description 登出
	 * <AUTHOR>
	 * @param token
	 * @return
	 * @return Results<Object>
	 * created：2020年4月23日
	 */
	@ApiOperation(value = "用户登出", notes = "用户登出（段德鹏）")
	@ApiImplicitParams({
        @ApiImplicitParam(name = "token",value = "token身份令牌", dataType = "String", paramType = "header")
	})
	@ApiOperationSupport(order = 2)
	@RequestMapping(value="/loginOut", method = RequestMethod.POST)
	@SystemLog(action = "loginOut", description = "用户登出", operationType = Contants.LOGIN_OUT_REQUEST)
	public Results loginOut(HttpServletRequest request, @RequestHeader(value = "token", required = true) String token) {
		try {
			//判断缓存是否存在
			if (!redisUtil.hasKey(token)) {
				return failResults(ResponseCodeEnum.UNLOGIN_REQUEST.getCode());
			}
			//清除旧token信息
			redisUtil.del(token);
			return sucessResults();
		} catch (Exception e) {
			logger.error("loginOut:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

}
