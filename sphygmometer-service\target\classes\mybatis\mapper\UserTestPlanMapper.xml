<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserTestPlanMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.UserTestPlanDTO" id="UserTestPlanDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="testFrequency"    column="test_frequency"    />
        <result property="remark"    column="remark"    />
        <result property="planStatus"    column="plan_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectUserTestPlan">
    	select
	        id,
	        user_id,
	        name,
	        test_frequency,
	        remark,
	        plan_status,
	        create_time,
	        create_user_id,
	        create_user_name,
	        update_time,
	        update_user_id,
	        update_user_name,
	        is_delete
		from 
        	t_user_test_plan
    </sql>

    <select id="selectUserTestPlanById" parameterType="int" resultMap="UserTestPlanDTOResult">
		<include refid="selectUserTestPlan"/>
		where 
        	id = #{id}
    </select>

    <select id="selectUserTestPlanList" parameterType="com.bojun.sphygmometer.dto.UserTestPlanDTO" resultMap="UserTestPlanDTOResult">
        <include refid="selectUserTestPlan"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="name != null  and name != ''"> and name = #{name}</if>
		<if test="testFrequency != null  and testFrequency != ''"> and test_frequency LIKE concat( '%', '${testFrequency}', '%' )</if>
		<if test="remark != null  and remark != ''"> and remark = #{remark}</if>
		<if test="planStatus != null "> and plan_status = #{planStatus}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createUserName != null  and createUserName != ''"> and create_user_name = #{createUserName}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateUserName != null  and updateUserName != ''"> and update_user_name = #{updateUserName}</if>
		<if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
        order by update_time desc
    </select>

	<select id="getManageDTOCount" resultType="java.lang.Integer">
		SELECT
		count(distinct plan.user_id)
		FROM
		t_sphygmometer_user tsu
		left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
		left join t_user_test_plan plan on plan.user_id = tsu.user_id and plan.is_delete = 0 and plan.plan_status = 1
		WHERE
			tsu.is_sign = 1
		<if test="orgIdList != null  and orgIdList.size() > 0">
			and tsu.manage_organization_id in
			<foreach item="item" index="index"
					 collection="orgIdList" open="(" separator=","
					 close=")">
				#{item}
			</foreach>
		</if>
		<if test="startTime != null  and endTime != null">
			and tsu.bind_time between #{startTime} and #{endTime}
		</if>
	</select>

</mapper>