<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.TipsTemplateMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.TipsTemplateDTO" id="TipsTemplateDTOResult">
        <result property="id"    column="id"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="productType"    column="product_type"    />
        <result property="promptContent"    column="prompt_content"    />
        <result property="pressureResults"    column="pressure_results"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTipsTemplate">
    	select
	        id,
	        organization_id,
	        product_type,
	        prompt_content,
	        pressure_results,
	        create_time,
	        update_time
		from 
        	t_tips_template
    </sql>

    <select id="selectTipsTemplateById" parameterType="int" resultMap="TipsTemplateDTOResult">
		<include refid="selectTipsTemplate"/>
		where 
        	id = #{id}
    </select>

    <select id="selectTipsTemplateList" parameterType="com.bojun.sphygmometer.dto.TipsTemplateDTO" resultMap="TipsTemplateDTOResult">
        <include refid="selectTipsTemplate"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="organizationId != null "> and organization_id = #{organizationId}</if>
		<if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
		<if test="promptContent != null  and promptContent != ''"> and prompt_content = #{promptContent}</if>
		<if test="pressureResults != null  and pressureResults != ''"> and pressure_results = #{pressureResults}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="endTime != null and statrTime ">and update_time BETWEEN #{statrTime}and #{endTime}</if>
        </where>
        order by update_time desc
    </select>

</mapper>