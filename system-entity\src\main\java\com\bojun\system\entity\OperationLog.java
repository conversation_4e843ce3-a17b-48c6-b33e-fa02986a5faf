package com.bojun.system.entity;

import java.util.Date;

/**
 *
 *Model：消息通知（站内）
 *Description：系统日志
 *Author:黄卫平
 *created：2020年4月27日
 */
public class OperationLog {

    private Integer id;

    // 用户id
    private Integer userId;

    // 系统id
    private String systemId;

    // 操作类型  1：新增  2：修改  3：删除   4：登陆登出
    private Integer operationType;

    // 操作接口
    private String requestAction;

    // 请求参数
    private String requestParams;

    // 操作内容
    private String operationContent;

    // 是否是异常请求  0：否  1：是
    private Integer isException;

    // 异常信息
    private String exceptionMsg;

    // 操作时间
    private Date operationTime;

    //IP地址 ip_address
    private String ipAddress;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id){
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId){
        this.userId = userId;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId){
        this.systemId = systemId;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType){
        this.operationType = operationType;
    }

    public String getRequestAction() {
        return requestAction;
    }

    public void setRequestAction(String requestAction){
        this.requestAction = requestAction;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams){
        this.requestParams = requestParams;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent){
        this.operationContent = operationContent;
    }

    public Integer getIsException() {
        return isException;
    }

    public void setIsException(Integer isException){
        this.isException = isException;
    }

    public String getExceptionMsg() {
        return exceptionMsg;
    }

    public void setExceptionMsg(String exceptionMsg){
        this.exceptionMsg = exceptionMsg;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime){
        this.operationTime = operationTime;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
}