package com.bojun.base.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.MessageNotificationReceiverService;
import com.bojun.base.system.service.MessageNotificationService;
import com.bojun.employee.enums.UploadImgFilePathEnum;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.MessageNotificationReceiverDTO;
import com.bojun.utils.PropertiesUtils;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月29日
*/
@RestController
public class MessageNotificationReceiverController extends BoJunBaseController{
	
private final static Log log = LogFactory.getLog(MessageNotificationReceiverController.class);
	
	@Autowired
	private MessageNotificationReceiverService  messageNotificationReceiverService;
	
	// 文件访问路径
	private String url = PropertiesUtils.getProperty("config.properties", "personnel.path")
					+ UploadImgFilePathEnum.MESSAGE.getModuleImgPath();
	
	/**
	 * 
	 * @Description 查询消息通知接收人信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/getMessageNotificationReceiver", method = RequestMethod.POST)
	@ResponseBody
	public void getMessageNotificationReceiver(@RequestBody Map<String, Object> paramsMap, HttpServletRequest request) {
		try {
			// 查询
			if (null == paramsMap || null == paramsMap.get("pageNum") || null == paramsMap.get("everyPage")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			long totalCount = messageNotificationReceiverService.queryMessageNotificationReceiverCount(paramsMap);
			if (totalCount <= 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			// 分页查询
			List<MessageNotificationReceiverDTO> resList = messageNotificationReceiverService
					.getMessageNotificationReceiverHome(paramsMap);
			long UnreadCount = messageNotificationReceiverService.queryMessageNotificationReceiverUnreadCount(paramsMap);
			Map<String, Object> resultMap = new HashMap<String, Object>();
			resultMap.put("totalCount", totalCount);
			resultMap.put("messageNotificationReceiverList", resList);
			resultMap.put("unreadCount", UnreadCount);
			outJson(successInfo(resultMap));
		} catch (Exception e) {

			log.error("getMessageNotificationReceiver:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 查询单个消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/getMessageNotificationReceiverById", method = RequestMethod.POST)
	@ResponseBody
	public void getMessageNotificationReceiverById(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap || null == paramsMap.get("id")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			MessageNotificationReceiverDTO resMessageNotificationReceiverDTO = messageNotificationReceiverService
					.getMessageNotificationReceiverById(paramsMap);
			if (null == resMessageNotificationReceiverDTO) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			if(StringUtils.isNotBlank(resMessageNotificationReceiverDTO.getFileName())){
				resMessageNotificationReceiverDTO.setFileUrl(url + resMessageNotificationReceiverDTO.getFileName());
			}
			outJson(successInfo(resMessageNotificationReceiverDTO));
		} catch (Exception e) {
			log.error("getMessageNotificationReceiverById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * 
	 * @Description 查询单个消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/getContractMessageById", method = RequestMethod.POST)
	@ResponseBody
	public void getContractMessageById(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap || null == paramsMap.get("id")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			MessageNotificationReceiverDTO resMessageNotificationReceiverDTO = messageNotificationReceiverService
					.getContractMessageById(paramsMap);
			if (null == resMessageNotificationReceiverDTO) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}

			outJson(successInfo(resMessageNotificationReceiverDTO));
		} catch (Exception e) {
			log.error("getMessageNotificationReceiverById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * 
	 * @Description 更新消息已读状态
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/updateMessageNotificationReceiver", method = RequestMethod.POST)
	@ResponseBody
	public void updateMessageNotificationReceiver(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap.get("id")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}

			MessageNotificationReceiverDTO messageNotificationReceiverDTO = new MessageNotificationReceiverDTO();

			messageNotificationReceiverDTO.setId((Integer) paramsMap.get("id"));
			messageNotificationReceiverDTO.setIsRead(1);
			Integer nNum = messageNotificationReceiverService.updateMessageNotificationReceiver(messageNotificationReceiverDTO);
			if (null == nNum || nNum.intValue() < 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "更新已读状态失败"));
				return;
			}
			outJson(successInfo());

		} catch (Exception e) {

			log.error("deleteMessageNotification:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * 
	 * @Description 更新合同消息已读状态
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	//@RequestMapping(value = "/updateContractRemindMessage", method = RequestMethod.POST)
	/*public void updateContractMessage(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap.get("id")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}

			ContractRemindMessageDTO contractRemindMessageDTO = new ContractRemindMessageDTO();

			contractRemindMessageDTO.setId((Integer) paramsMap.get("id"));
			contractRemindMessageDTO.setIsRead(1);
			Integer nNum = messageNotificationReceiverService.updateContractRemindMessage(contractRemindMessageDTO);
			if (null == nNum || nNum.intValue() < 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "更新已读状态失败"));
				return;
			}
			outJson(successInfo());

		} catch (Exception e) {

			log.error("updateContractRemindMessage:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}*/

}

