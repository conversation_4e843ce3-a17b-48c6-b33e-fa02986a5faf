package com.bojun.base.manage.controller.notice.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 15:17
 */
@ApiModel(value = "修改通知通告", description = "修改通知通告")
public class UpdateNoticeVO implements Serializable {
	
    private static final long serialVersionUID = -9120830022257472830L;
    
    @ApiModelProperty(value = "消息Id", example = "fa8b5b6b-fce0-4bce-b221-ee720d01db88")
    private String noticeId;
    
    @ApiModelProperty(value = "是否立即发送 1是  0否", example = "1")
    private int isImmediately;
    
    @ApiModelProperty(value = "定时发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timingTime;
    
    @ApiModelProperty(value = "推送内容", example = "test")
    private String noticeContent;
    
    @ApiModelProperty(value = "通知类型", example = "1")
    private String noticeTypeId;
    
    @ApiModelProperty(value = "通知标题")
    private String title;
    
    @ApiModelProperty(value = "推送产品id", example = "1,3,5")
    private String synchronizationPlatform;
    
    @ApiModelProperty(value = "接收部门")
    private List<ObjectVO> objectVO;

    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    public int getIsImmediately() {
        return isImmediately;
    }

    public void setIsImmediately(int isImmediately) {
        this.isImmediately = isImmediately;
    }

    public Date getTimingTime() {
        return timingTime;
    }

    public void setTimingTime(Date timingTime) {
        this.timingTime = timingTime;
    }


    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getNoticeTypeId() {
        return noticeTypeId;
    }

    public void setNoticeTypeId(String noticeTypeId) {
        this.noticeTypeId = noticeTypeId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

	public String getSynchronizationPlatform() {
		return synchronizationPlatform;
	}

	public void setSynchronizationPlatform(String synchronizationPlatform) {
		this.synchronizationPlatform = synchronizationPlatform;
	}

	public List<ObjectVO> getObjectVO() {
        return objectVO;
    }

    public void setObjectVO(List<ObjectVO> objectVO) {
        this.objectVO = objectVO;
    }
}
