/**
 * 
 */
package com.bojun.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：通用查询信息对象
*Description：通用查询信息对象
*Author:段德鹏
*created：2020年4月12日
**/
@ApiModel(description = "通用查询信息对象")
public class BaseQueryInfoVO implements Serializable {

	private static final long serialVersionUID = -4686347593044553320L;
	@ApiModelProperty(value = "页码", example = "1")
	private Integer pageNum;
	
	@ApiModelProperty(value = "页容量", example = "10")
	private Integer everyPage;

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(Integer everyPage) {
		this.everyPage = everyPage;
	}

}
