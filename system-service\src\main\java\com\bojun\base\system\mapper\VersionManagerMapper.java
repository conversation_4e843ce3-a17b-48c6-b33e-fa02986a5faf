package com.bojun.base.system.mapper;

import com.bojun.system.dto.AppVersionDTO;
import com.bojun.system.entity.AppVersion;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 9:24
 */
@Mapper
public interface VersionManagerMapper {

    int addVersion(AppVersion appVersion);

    int deleteVersion(AppVersion appVersion);

    Page<AppVersion> getVersionManager(AppVersion appVersion);

    AppVersionDTO getAppVersion(AppVersionDTO appVersionDTO);

    AppVersionDTO getAppVersionbyCreateTime(AppVersionDTO appVersion1);
}
