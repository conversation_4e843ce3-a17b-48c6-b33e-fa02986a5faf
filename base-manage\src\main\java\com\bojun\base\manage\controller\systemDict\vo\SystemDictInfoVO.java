/**
 * 
 */
package com.bojun.base.manage.controller.systemDict.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：产品管理 
 * Description：查询产品列表返回信息
 * Author：赖水秀 
 * created： 2020年4月27日
 */
@ApiModel(value = "产品列表", description = "查询产品列表返回信息")
public class SystemDictInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;

	@ApiModelProperty(value = "系统id")
	private String systemId;

	@ApiModelProperty(value = "系统名称")
	private String systemName;

	@ApiModelProperty(value = "系统类型id")
	private Integer systemTypeId;

	@ApiModelProperty(value = "是否是移动端(0:移动端，1：web端)")
	private Integer isMobile;

	@ApiModelProperty(value = "主页URL")
	private String homeUrl;

	@ApiModelProperty(value = "启用标记   0：否   1：是")
	private Integer isEnabled;

	@ApiModelProperty(value = "图标样式")
	private String iconCass;

	@ApiModelProperty(value = "备注")
	private String remark;

	@ApiModelProperty(value = "系统类型名称")
	private String systemTypeName;
	

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public Integer getIsMobile() {
		return isMobile;
	}

	public void setIsMobile(Integer isMobile) {
		this.isMobile = isMobile;
	}

	public String getHomeUrl() {
		return homeUrl;
	}

	public void setHomeUrl(String homeUrl) {
		this.homeUrl = homeUrl;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getIconCass() {
		return iconCass;
	}

	public void setIconCass(String iconCass) {
		this.iconCass = iconCass;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getSystemTypeName() {
		return systemTypeName;
	}

	public void setSystemTypeName(String systemTypeName) {
		this.systemTypeName = systemTypeName;
	}

	
	
}
