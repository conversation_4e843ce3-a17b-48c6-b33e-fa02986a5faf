package com.bojun.sphygmometer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.push.GeTuiPushUtils;
import com.bojun.sphygmometer.common.PushTypeEnum;
import com.bojun.sphygmometer.dto.AppMessageNotificationDTO;
import com.bojun.sphygmometer.dto.NewsPersonTagDTO;
import com.bojun.sphygmometer.dto.NewsPushRecordDTO;
import com.bojun.sphygmometer.dto.PersonTagDTO;
import com.bojun.sphygmometer.entity.NewsPushRecord;
import com.bojun.sphygmometer.mapper.NewsPushRecordMapper;
import com.bojun.sphygmometer.service.AppMessageNotificationService;
import com.bojun.sphygmometer.service.NewsPersonTagService;
import com.bojun.sphygmometer.service.NewsPushRecordService;
import com.bojun.sphygmometer.service.PersonTagService;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import com.bojun.utils.SpringUtils;
import com.gexin.rp.sdk.base.IPushResult;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.Minutes;
import org.joda.time.Seconds;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * NewsPushRecordService业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-01 15:00:38
 */
@Service
public class NewsPushRecordServiceImpl extends ServiceImpl<NewsPushRecordMapper, NewsPushRecord> implements NewsPushRecordService {

    private static Logger logger = LoggerFactory.getLogger(NewsPushRecordServiceImpl.class);
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 推送消息
     *
     * @param newsId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doPushNews(Integer newsId) {
        NewsPushRecordDTO newsPushRecordDTO = null;
        newsPushRecordDTO = this.baseMapper.selectNewsPushRecordById(newsId);
        // 如果没有记录，说明这个文章不需要推送
        if (newsPushRecordDTO == null) {
            return;
        }
        if (newsPushRecordDTO.getPushType() != null && newsPushRecordDTO.getPushType().equals(0)) {
            // 推送文章信息
            pushNews(newsId);
            return;
        } else {
            String key = "pushNews_" + newsId;
            DateTime startTime = new DateTime(newsPushRecordDTO.getPushTime());
            // 间隔的毫秒数
            int secondsBetween = Seconds.secondsBetween(new DateTime(), startTime).getSeconds();
            if (secondsBetween > 0) {
                // 推到redis
                redisUtil.set(key, null, secondsBetween);
            }
            logger.info("间隔的毫秒数：" + secondsBetween);
        }
    }

    /**
     * 推送文章信息
     *
     * @param newsId
     */
    @Override
    public void pushNews(Integer newsId) {
        try {
            List<Integer> userIdList = new ArrayList<>();
            Map<String, String> stringStringMap = this.baseMapper.selectNewsInfoByNewsId(newsId);
            String title = stringStringMap.get("title");
            String coverImage = stringStringMap.get("coverImage");
            NewsPersonTagService newsPersonTagService = SpringUtils.getBean(NewsPersonTagService.class);
            NewsPersonTagDTO newsPersonTagDTO = newsPersonTagService.selectNewsPersonTagById(newsId);
            // 根据人员标签，获取那些人需要推送
            StringBuffer stringBuffer = new StringBuffer();
            PersonTagService personTagService = SpringUtils.getBean(PersonTagService.class);
            // 通过code查询有哪些记录
            List<PersonTagDTO> personTagList = personTagService.getPersonTagListByCode(String.valueOf(newsPersonTagDTO.getPersonTagCode()));
            if (null == personTagList && personTagList.size() <= 0) {
                return;
            }
            for (PersonTagDTO personTag : personTagList) {
                // 通过记录查询记录对应的人
                List<Map<String, Object>> userByPersonTagList = personTagService.getUserByPersonTag(personTag.getColumnName(), personTag.getTableName(), personTag.getValue());
                for (Map<String, Object> stringMap : userByPersonTagList) {
                    if (stringMap.get("geTuiClientId") == null) {
                        continue;
                    }
                    stringBuffer.append(stringMap.get("geTuiClientId")).append(",");
                    userIdList.add((Integer) stringMap.get("userId"));
                }
            }
            // 如果app的token为空，直接返回
            if (StringUtils.isBlank(stringBuffer.toString())) {
                return;
            }
            // 组装参数信息
            String content = stringStringMap.get("content").replaceAll("\\&[a-zA-Z]{1,10}", "").replaceAll("<[^>]*>", "").replaceAll("[(/>)<]", "");
            // 推送
            boolean iPushResult = GeTuiPushUtils.pushMsgToMany(Arrays.asList(stringBuffer.toString().split(",").clone()), title,
                    getContent(content), "{'msgType':'" + PushTypeEnum.POPULAR_SCIENCE_RECOMMENDATION.getMgType() + "'}");
            logger.info("推送文章id:" + newsId + "推送结果：" + iPushResult);
            // 保存app消息推送记录
            saveAppMessagePushRecord(content, coverImage, title, userIdList, 1, new Date());
        } catch (Exception e) {
            logger.error("消息推送失败！，文章id:" + newsId, e);
        } finally {
            // 因没有重试机制，不管推送成功还是失败，都更新推送状态,防止后面推送失败
            NewsPushRecordDTO newsPushRecordDTO = this.baseMapper.selectNewsPushRecordById(newsId);
            if (null == newsPushRecordDTO) {
                return;
            }
            newsPushRecordDTO.setStatus(1);
            int results = this.baseMapper.updateById(newsPushRecordDTO);
            logger.info("更新推送状态结果" + results + ("文章id:" + newsId));
        }
    }

    /**
     * 内容大于3072字节会报错，这里截取部分。
     *
     * @param content
     * @return
     */
    private String getContent(String content) {
        return content.length() > 500 ? content.substring(0, 500) : content;
    }

    private void saveAppMessagePushRecord(String content, String coverImage,
                                          String title, List<Integer> userIdList,
                                          Integer status, Date pushTime) {
        // 保存推送记录
        if (userIdList.size() > 0) {
            AppMessageNotificationService appMessageNotificationService = SpringUtils.getBean(AppMessageNotificationService.class);
            for (Integer userId : userIdList) {
                AppMessageNotificationDTO appMessageNotificationDTO = new AppMessageNotificationDTO();
                appMessageNotificationDTO.setUserId(Integer.valueOf(userId));
                appMessageNotificationDTO.setNoticeContent(content);
                appMessageNotificationDTO.setCreateTime(new Date());
                appMessageNotificationDTO.setPublishTime(pushTime);
                appMessageNotificationDTO.setIsRead(0);
                appMessageNotificationDTO.setTitle(title);
                appMessageNotificationDTO.setIsDelete(0);
                appMessageNotificationDTO.setStatus(status);
                appMessageNotificationDTO.setMsgType(7);
                appMessageNotificationDTO.setNoticePicture(coverImage);
                appMessageNotificationService.insertAppMessageNotification(appMessageNotificationDTO);
            }
        }
    }

    @Override
    public NewsPushRecordDTO selectNewsPushRecordById(Integer newsId) {
        return this.baseMapper.selectNewsPushRecordById(newsId);
    }

    /**
     * 查询文章推送记录列表
     *
     * @param newsPushRecordDTO 文章推送记录
     * @return 文章推送记录集合
     */
    @Override
    public List<NewsPushRecordDTO> selectNewsPushRecordList(NewsPushRecordDTO newsPushRecordDTO) {
        return this.baseMapper.selectNewsPushRecordList(newsPushRecordDTO);
    }

    /**
     * 新增文章推送记录
     *
     * @param newsPushRecordDTO 文章推送记录
     * @return 结果
     */
    @Override
    public int insertNewsPushRecord(NewsPushRecordDTO newsPushRecordDTO) {
        newsPushRecordDTO.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(newsPushRecordDTO);
    }

    /**
     * 修改文章推送记录
     *
     * @param newsPushRecordDTO 文章推送记录
     * @return 结果
     */
    @Override
    public int updateNewsPushRecord(NewsPushRecordDTO newsPushRecordDTO) {
        return this.baseMapper.updateById(newsPushRecordDTO);
    }

    /**
     * 新增文章推送记录
     *
     * @param newsPushRecord 文章推送记录
     * @return 结果
     */
    @Override
    public int insertNewsPushRecord(NewsPushRecord newsPushRecord) {
        newsPushRecord.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(newsPushRecord);
    }

    /**
     * 修改文章推送记录
     *
     * @param newsPushRecord 文章推送记录
     * @return 结果
     */
    @Override
    public int updateNewsPushRecord(NewsPushRecord newsPushRecord) {
        return this.baseMapper.updateById(newsPushRecord);
    }

    /**
     * 删除文章推送记录对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteNewsPushRecordByIds(String ids) {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除文章推送记录信息
     *
     * @param id 文章推送记录ID
     * @return 结果
     */
    @Override
    public int deleteNewsPushRecordById(Integer id) {
        return this.removeById(id) ? 1 : 0;
    }
}
