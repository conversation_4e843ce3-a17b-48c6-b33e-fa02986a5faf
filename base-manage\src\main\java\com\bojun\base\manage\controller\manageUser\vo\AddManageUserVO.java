/**
 * 
 */
package com.bojun.base.manage.controller.manageUser.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：用户管理 
 * Description：新增用户信息 
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "新增用户信息", description = "新增用户传入和返回信息")
public class AddManageUserVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;

//	@NotEmpty(message = "系统ID不能为空")
//	@ApiModelProperty(value = "系统id", required = true, example = "S001")
//	private String systemId;
//
//	@NotNull(message = "系统类型ID不能为空")
//	@ApiModelProperty(value = "系统类型id", required = true, example = "1")
//	private Integer systemTypeId;

	@ApiModelProperty(value = "用户Id", required = false, example = "1")
	private Integer userId;

	@NotEmpty(message = "登录账号不能为空")
	@ApiModelProperty(value = "登录账号", required = true, example = "1")
	private String accountNo;

	@NotNull(message = "机构id不能为空")
	@ApiModelProperty(value = "机构id", required = true, example = "1")
	private Integer organizationId;

	@NotEmpty(message = "系统名称不能为空")
	@ApiModelProperty(value = "系统名称", required = true, example = "测试")
	private String organizationName;

	@NotEmpty(message = "角色ID不能为空")
	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;

//	@NotEmpty(message = "工号不能为空")
	@ApiModelProperty(value = "工号", required = true, example = "1")
	private String workNumber;

	@NotNull(message = "科室部门id不能为空")
	@ApiModelProperty(value = "部门id", required = true, example = "1")
	private Integer deptId;
	
	@ApiModelProperty(value = "身份证号码", example = "360721111111111111")
	private String idNo; //
	
	@NotEmpty(message = "姓名不能为空")
	@ApiModelProperty(value = "姓名", required = true, example = "1")
	private String realName; //

//	@ApiModelProperty(value = "权限类型（0：超级管理员，1：普通管理员）", required = true, example = "1")
//	private Integer authType; //

	@NotNull(message = "用户类型不能为空")
	@ApiModelProperty(value = "用户类型 1：医疗机构人员 2：养老机构人员 3：监管人员 4：其他", required = true, example = "1")
	private Integer userType; //

	@NotEmpty(message = "密码不能为空")
	@ApiModelProperty(value = "密码", required = true, example = "1")
	private String passwords; //

	@NotNull(message = "状态类型不能为空")
	@ApiModelProperty(value = "状态0:停用 1:启用", required = true, example = "1")
	private Integer status;

	@NotEmpty(message = "创建来源（初始创建的系统code）不能为空")
	@ApiModelProperty(value = "创建来源（初始创建的系统code）", required = true, example = "1")
	private String sourceSystemId; //

//	@NotEmpty(message = "备注不能为空")
	@ApiModelProperty(value = "备注", required = false, example = "1")
	private String remark; //

//	@NotNull(message = "区域（病区）id不能为空")
	@ApiModelProperty(value = "区域（病区）id", required = false, example = "1")
	private Integer wardId;

	@NotEmpty(message = "手机号不能为空")
	@ApiModelProperty(value = "手机号", required = true, example = "1")
	private String mobile;
	
	@NotNull(message = "上级用户id不能为空")
	@ApiModelProperty(value = "上级用户id", required = true, example = "1")
	private Integer parentId; // 

	
	
	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}

	

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getWorkNumber() {
		return workNumber;
	}

	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}

	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getPasswords() {
		return passwords;
	}

	public void setPasswords(String passwords) {
		this.passwords = passwords;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getSourceSystemId() {
		return sourceSystemId;
	}

	public void setSourceSystemId(String sourceSystemId) {
		this.sourceSystemId = sourceSystemId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getWardId() {
		return wardId;
	}

	public void setWardId(Integer wardId) {
		this.wardId = wardId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}
	
	
}
