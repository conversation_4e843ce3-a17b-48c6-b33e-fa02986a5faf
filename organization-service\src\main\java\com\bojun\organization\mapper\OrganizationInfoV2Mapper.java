package com.bojun.organization.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OrganizationInfoMapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@Mapper
public interface OrganizationInfoV2Mapper extends BaseMapper<OrganizationInfoV2>
{

    /**
     * 查询机构信息表
     *
     * @param organizationId 机构信息表ID
     * @return 机构信息表
     */
    public OrganizationInfoV2DTO selectOrganizationInfoById(Integer organizationId);

    /**
     * 查询机构信息表列表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 机构信息表集合
     */
    public List<OrganizationInfoV2DTO> selectOrganizationInfoList(OrganizationInfoV2DTO organizationInfoDTO);

    /**
     * 1. 传入roleId, 将获得相关角色的机构数据
     * 2. 什么都不传, 查全部机构
     * 3. organizationId/organizationIds有值,则会查其下级机构数据
     * @param organizationInfoDTO
     * @return 返回平级机构列表
     */
    List<OrganizationInfoV2DTO> getChildrenOrgListByRoleId(OrganizationInfoV2DTO organizationInfoDTO);
}
