package com.bojun.health.promotion.common.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 新闻话题关系表对象 t_news_topic
 * 
 * <AUTHOR>
 * @date 2021-08-26 10:07:05
 */
@ApiModel(value = "NewsTopic对象" , description = "新闻话题关系表")
@Data
@TableName("t_news_topic")
public class NewsTopic implements Serializable {
    private static final long serialVersionUID = 1L;


    /** id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 资讯id */
    @ApiModelProperty(value = "资讯id", example = "")
	@TableField("news_id")
    private Integer newsId;

    /** 话题id */
    @ApiModelProperty(value = "话题id", example = "")
	@TableField("topic_id")
    private String topicId;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("create_time")
    private Date createTime;

    /** 创建用户ID */
    @ApiModelProperty(value = "创建用户ID", example = "")
	@TableField("create_user_id")
    private Integer createUserId;

    /** 创建用户姓名 */
    @ApiModelProperty(value = "创建用户姓名", example = "")
	@TableField("create_user_name")
    private String createUserName;

    /** 是否置顶 */
    @ApiModelProperty(value = "是否置顶", example = "")
	@TableField("is_top")
    private Integer isTop;

    /** 文章在栏目中排序下标 */
    @ApiModelProperty(value = "文章在栏目中排序下标", example = "")
	@TableField("news_show_index")
    private Integer newsShowIndex;
}
