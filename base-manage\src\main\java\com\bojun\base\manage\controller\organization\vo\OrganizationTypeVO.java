/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：机构类型信息
 * Author：赖水秀
 * created： 2020年4月28日
 */
@ApiModel(value = "机构类型信息", description = "查询机构类型返回信息")
public class OrganizationTypeVO implements Serializable {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1405426874750589257L;
		
	@ApiModelProperty(value="类型code")
	private String typeCode;	//类型code
	
	@ApiModelProperty(value="类型名称")
	private String typeName;	//类型name
	
	@ApiModelProperty(value="分类code")
	private String classCode;	//分类code


	public String getTypeCode() {
		return typeCode;
	}

	public void setTypeCode(String typeCode) {
		this.typeCode = typeCode;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getClassCode() {
		return classCode;
	}

	public void setClassCode(String classCode) {
		this.classCode = classCode;
	}

}
