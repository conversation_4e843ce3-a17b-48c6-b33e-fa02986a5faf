package com.bojun.health.promotion.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/6/1 19:15
 */
@Data
public class UpdateNewsDTO {

    @ApiModelProperty(value = "栏目id", example = "")
    private Integer topicId;
    @ApiModelProperty(value = "是否置顶： 1：否 0：是", example = "")
    private Integer isTop;
    @ApiModelProperty(value = "文章在栏目的显示下标", example = "")
    private Integer newsShowIndex;
}
