package com.bojun.utils;

import java.util.List;
import java.util.Random;

/**
 * RandomUtil
 * 随机数生成类
 * <AUTHOR>
 * @version 1.0
 * @date 2016-03-31
 */

public class RandomUtil {
	
	/**
     * 获取指定长度的随机数字
     * @param length 指定数字长度
     * @return 指定长度的字符串
     */
	public static String getRandomNumber(int length) {
		char[] randoms = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' };
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			sb.append(randoms[random.nextInt(randoms.length)]);
		}     
		return sb.toString();  
	}
	
	/**
     * 获取指定长度的随机字符串
     * @param length 指定字符串长度
     * @return 指定长度的字符串
     */
    public static String getRandomStringByLength(int length) {
        String randoms = "abcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(randoms.length());
            sb.append(randoms.charAt(number));
        }
        return sb.toString();
    }

	/**
	 * 获取指定长度的随机纯字母符串
	 * @param length 指定字符串长度
	 * @return 指定长度的字符串
	 */
	public static String getRandomPureLettersByLength(int length) {
		String randoms = "abcdefghijklmnopqrstuvwxyz";
		Random random = new Random();
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < length; i++) {
			int number = random.nextInt(randoms.length());
			sb.append(randoms.charAt(number));
		}
		return sb.toString();
	}

	/**
     * 获取指定长度的随机字符串
     * @param length 指定字符串长度
     * @return 指定长度的字符串
     */
    public static String getRandomString(int length) {
        String randoms = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(randoms.length());
            sb.append(randoms.charAt(number));
        }
        return sb.toString();
    }
    
    /**
     * 随机获取List中的内容
     */
    public static int getRandomList(List<?> list) {
    	Random random = new Random();
    	int index = random.nextInt(list.size());
    	return index;
    }
    
    /**
     * 随机获取Array中的Integer内容
     */
    public static Integer getRandomArray(Integer[] array) {
    	Random random = new Random();
    	int index = random.nextInt(array.length);
    	return array[index];
    }
    
    /**
     * 随机按比率权重取数 
     */
	public static int percentageDoubleRandom() {
		double rate0 = 0.80;
		double rate1 = 0.10;
		double rate2 = 0.05;
		double rate3 = 0.03;
		double rate4 = 0.02;
		double randomNumber = Math.random();
		if (randomNumber >= 0 && randomNumber <= rate0) {
			return 200;
		} else if (randomNumber >= rate0 && randomNumber <= rate0 + rate1) {
			return 1000;
		} else if (randomNumber >= rate0 + rate1 && randomNumber <= rate0 + rate1 + rate2) {
			return 5000;
		} else if (randomNumber >= rate0 + rate1 + rate2 && randomNumber <= rate0 + rate1 + rate2 + rate3) {
			return 10000;
		} else if (randomNumber >= rate0 + rate1 + rate2 + rate3 && randomNumber <= rate0 + rate1 + rate2 + rate3 + rate4) {
			return 50000;
		}
		return 0;
	}
	
	/**
     * 随机按比率权重取数 
     */
	public static int percentageIntRandom() {
		int rate0 = 5;
		int rate1 = 3;
		int rate2 = 2;
		Random random = new Random();
    	int randomNumber = random.nextInt(10);
		if (randomNumber >= 0 && randomNumber <= rate0) {
			return 200;
		} else if (randomNumber >= rate0 && randomNumber <= rate0 + rate1) {
			return 1000;
		} else if (randomNumber >= rate0 + rate1 && randomNumber <= rate0 + rate1 + rate2) {
			return 5000;
		}
		return 0;
	}
    
    /**
     * 主函数测试
     */
    public static void main(String[] args) {
    	/*
    	System.out.println(getRandomNumber(6));
    	System.out.println(getRandomString(10));
    	for (int i = 0; i < 10; i++) {
    		String randomString = getRandomString(8);
    		System.out.println(randomString);
    	}
		for (int i = 0; i < 5; i++) {
			System.out.println("第" + (i + 1) + "个数是：" + percentageDoubleRandom() + "\n");
		}
		for (int i = 0; i < 5; i++) {
			System.out.println("第" + (i + 1) + "个数是：" + percentageIntRandom() + "\n");
		}
    	Integer[] array = {1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 6, 8};
		Integer redCoin = RandomUtil.getRandomArray(array);
		System.out.println(redCoin);
		*/
    	System.out.println(getRandomStringByLength(5));
    }
}
