package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知接收人信息表对象 t_message_notification_receiver
 * 
 * <AUTHOR>
 * @date 2021-06-07 15:53:16
 */
@ApiModel(value = "MessageNotificationReceiver对象" , description = "消息通知接收人信息表")
@Data
@TableName("t_message_notification_receiver")
public class MessageNotificationReceiverV2 implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 消息通知id */
    @ApiModelProperty(value = "消息通知id", example = "")
	@TableField("notice_id")
    private String noticeId;

    /** 接收人用户id */
    @ApiModelProperty(value = "接收人用户id", example = "")
	@TableField("receive_user_id")
    private Integer receiveUserId;
    
    /** 通知类型id */
    @ApiModelProperty(value = "通知类型id", example = "")
	@TableField("notice_type_id")
    private String noticeTypeId;

    /** 接收时间 */
    @ApiModelProperty(value = "接收时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("receive_time")
    private Date receiveTime;

    /** 是否已读  0：未读  1：已读 */
    @ApiModelProperty(value = "是否已读  0：未读  1：已读", example = "")
	@TableField("is_read")
    private Integer isRead;

    /** 读取时间 */
    @ApiModelProperty(value = "读取时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("read_time")
    private Date readTime;

    /** 是否删除  0：否  1：是 */
    @ApiModelProperty(value = "是否删除  0：否  1：是", example = "")
	@TableField("is_delete")
    private Integer isDelete;

    /** 删除时间 */
    @ApiModelProperty(value = "删除时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("delete_time")
    private Date deleteTime;
}
