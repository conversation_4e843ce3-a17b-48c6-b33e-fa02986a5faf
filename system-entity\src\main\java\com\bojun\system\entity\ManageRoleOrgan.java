package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：角色机构关联
 * Description：角色机构关联实体类
 * Author:刘俊
 * created：2020年4月30日
 */
public class ManageRoleOrgan implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;

    private Integer roId; // 角色科室关联id

    private String roleId; // 角色ID（uuid）

    private Integer organizationId; // 机构id

    //是否有权限(全选半选之分)0无(半选)1有(全选)
    private Integer hasAuth;

	public Integer getHasAuth() {
		return hasAuth;
	}

	public void setHasAuth(Integer hasAuth) {
		this.hasAuth = hasAuth;
	}

	public Integer getRoId() {
        return roId;
    }

    public void setRoId(Integer roId) {
        this.roId = roId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }


}
