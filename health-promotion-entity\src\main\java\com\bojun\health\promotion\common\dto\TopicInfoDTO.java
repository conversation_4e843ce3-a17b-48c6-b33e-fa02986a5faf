package com.bojun.health.promotion.common.dto;

import com.bojun.health.promotion.common.entity.TopicInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 话题信息表对象 t_topic_info
 * 
 * <AUTHOR>
 * @date 2021-06-01 16:08:00
 */
@ApiModel(value = "TTopicInfoDTO对象")
@Data
public class TopicInfoDTO extends TopicInfo
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private List<TopicInfoDTO> children;
}
