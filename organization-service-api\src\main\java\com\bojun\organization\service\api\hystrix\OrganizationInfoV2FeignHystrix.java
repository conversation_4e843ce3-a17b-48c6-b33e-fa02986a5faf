package com.bojun.organization.service.api.hystrix;

import com.bojun.exception.BaseRuntimeException;
import com.bojun.organization.dto.GetAuthOrganizationV2DTO;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;
import com.bojun.organization.service.api.OrganizationInfoV2FeignClient;
import com.bojun.page.Results;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 机构信息表FeignHystrix
 *
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@Component
public class OrganizationInfoV2FeignHystrix implements OrganizationInfoV2FeignClient
{

//	/**
//     * 查询机构信息表分页列表
//     */
//    @Override
//    public Results<PageData<OrganizationInfoV2DTO>> page(OrganizationInfoV2DTO organizationInfoDTO){
//        throw new BaseRuntimeException(PREFIX + "/page 接口服务已断开");
//    }

    /**
     * 查询机构信息表列表
     */
    @Override
    public Results<List<OrganizationInfoV2DTO>> list(OrganizationInfoV2DTO organizationInfoDTO){
        throw new BaseRuntimeException(PREFIX + "/list 接口服务已断开");
    }

    /**
     * 获取机构信息表详细信息
     */
    @Override
    public Results<OrganizationInfoV2DTO> getInfo(Integer organizationId){
        throw new BaseRuntimeException(PREFIX + "/getInfo 接口服务已断开");
    }

    @Override
    public Results<OrganizationInfoV2> getInfoByName(String organizationName) {
        throw new BaseRuntimeException(PREFIX + "/getInfoByName 接口服务已断开");
    }

//    /**
//     * 新增机构信息表DTO
//     */
//    @Override
//    public Results addDTO(OrganizationInfoV2DTO organizationInfoDTO){
//        throw new BaseRuntimeException(PREFIX + "/addDTO 接口服务已断开");
//    }
//
//    /**
//     * 修改机构信息表DTO
//     */
//    @Override
//    public Results editDTO(OrganizationInfoV2DTO organizationInfoDTO){
//        throw new BaseRuntimeException(PREFIX + "/editDTO 接口服务已断开");
//    }
//
//    /**
//     * 新增机构信息表
//     */
//    @Override
//    public Results add(OrganizationInfoV2 organizationInfo){
//        throw new BaseRuntimeException(PREFIX + "/add 接口服务已断开");
//    }
//
//    /**
//     * 修改机构信息表
//     */
//    @Override
//    public Results edit(OrganizationInfoV2 organizationInfo){
//        throw new BaseRuntimeException(PREFIX + "/edit 接口服务已断开");
//    }
//
//    /**
//     * 删除机构信息表，多个以逗号分隔
//     */
//    @Override
//    public Results removeByIds(String ids) {
//        throw new BaseRuntimeException(PREFIX + "/removeByIds 接口服务已断开");
//    }

    @Override
    public Results<List<OrganizationInfoV2DTO>> getFullAuthOrgList(GetAuthOrganizationV2DTO getAuthOrganizationV2DTO) {
        throw new BaseRuntimeException(PREFIX + "/getFullAuthOrgList 接口服务已断开");
    }

    @Override
    public Results<List<OrganizationInfoV2DTO>> getAuthOrgTreeList(GetAuthOrganizationV2DTO getAuthOrganizationV2DTO) {
        throw new BaseRuntimeException(PREFIX + "/getAuthOrgTreeList 接口服务已断开");
    }
}
