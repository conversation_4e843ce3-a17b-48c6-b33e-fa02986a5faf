package com.bojun.base.system.mapper;

import com.bojun.system.dto.FormQuestionAnswerDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireResultDTO;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/6 17:28
 */
@Mapper
public interface SatisfactionMapper {

    Page<List<SatisfactionQuestionnaireDTO>> getSatisfactionSurvey(SatisfactionQuestionnaireDTO questionnaireDTO);

    SatisfactionQuestionnaireDTO getSatisfactionSurveyById(@Param("questionnaireId") Integer questionnaireId);

    int addQuestionnaireResult(SatisfactionQuestionnaireResultDTO questionnaireResult);

    int addFormQuestionAnswer(@Param("list") List<FormQuestionAnswerDTO> formQuestionAnswerDTOS);
}
