package com.bojun.base.system.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.INoticeService;
import com.bojun.base.system.service.IRongNoticeService;
import com.bojun.commons.rong.RongUtils;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.healthcare.dto.UserNoticeDTO;
import com.bojun.healthcare.enums.NoticeTemplateEnums;
import com.bojun.healthcare.enums.NoticeTypeEnums;
import com.bojun.system.dto.MessageNotificationDTO;
import com.bojun.system.dto.MessageNotificationObjectDTO;
import com.bojun.system.dto.QuartzJobRecordDTO;
import com.bojun.system.entity.MessageNotificaionType;
import com.bojun.system.entity.MessageNotificationObject;
import com.bojun.system.enums.SystemDictEnums;
import com.bojun.utils.PropertiesUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Model： 通知公告 Description： Author: 赖允翔 created：2020/4/24 17:15
 */
@RestController
public class NoticeController extends BoJunBaseController {
	private static Logger logger = LoggerFactory.getLogger(NoticeController.class);

	@Autowired
	INoticeService noticeService;
	
	@Autowired
	private IRongNoticeService rongNoticeService;
	
	private String internetHospital = PropertiesUtils.getProperty("application.properties", "internetHospital");

	/**
	 * @description: 通知公告查询（全查）
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/getNotices")
	public void getNotices(@RequestBody MessageNotificationDTO messageNotificationDTO) {
		try {
			int pageNum = (0 == messageNotificationDTO.getPageNum() ? 1 : messageNotificationDTO.getPageNum());
			int everyPage = (0 == messageNotificationDTO.getEveryPage() ? 10 : messageNotificationDTO.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<MessageNotificationDTO> page = noticeService.getNotices(messageNotificationDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			List<String> publishObject = new ArrayList<>();
			for (MessageNotificationDTO notificationDTO : page.getResult()) {
				List<MessageNotificationObjectDTO> objectOrg = noticeService.getObjectOrg(notificationDTO);
				for (MessageNotificationObjectDTO notificationObjectDTO : objectOrg) {
					StringBuffer sb = new StringBuffer();
					// 追加机构
					sb.append(notificationObjectDTO.getOrganizationName());
					List<MessageNotificationObjectDTO> objectDept = noticeService.getObjectDept(notificationObjectDTO);
					if (null != objectDept && !objectDept.isEmpty()) {
						for (MessageNotificationObjectDTO messageNotificationObjectDTO : objectDept) {
							// 追加部门
							sb.append("-" + messageNotificationObjectDTO.getDeptName());
							List<MessageNotificationObjectDTO> objectWard = noticeService
									.getObjectWard(messageNotificationDTO);
							if (null != objectWard && !objectWard.isEmpty()) {
								for (MessageNotificationObjectDTO ward : objectWard) {
									sb.append("-" + ward);
								}
							}
						}
					}
					publishObject.add(sb.toString());
				}
				String join = String.join(",", publishObject);
				notificationDTO.setPublishObject(join);
			}

			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {
			logger.error("getNotices:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}

	}

	/**
	 * @description: 通知公告查询（单查）
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/getNoticeById")
	void getNoticeById(@RequestParam(value = "noticeId") String noticeId) {
		try {
			MessageNotificationDTO messageNotificationDTO = noticeService.getNoticeById(noticeId);
			if (messageNotificationDTO == null) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(messageNotificationDTO));
		} catch (Exception e) {
			logger.error("getSystemDictById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @description: 添加通知
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/addNotice")
//	@Transactional(rollbackFor = Exception.class)
	void addNotice(@RequestBody MessageNotificationDTO messageNotificationDTO) {
		try {
			int addNumber = noticeService.addNotice(messageNotificationDTO);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			List<String> systemIds = new ArrayList<>();
			String systemId = messageNotificationDTO.getSynchronizationPlatform();
			if (systemId.contains(",")) {
				systemIds = Arrays.asList(systemId.split(","));
			} else {
				systemIds.add(systemId);
			}
			int add = noticeService.addMessageNotificationSystem(messageNotificationDTO.getNoticeId(), systemIds);
			if (messageNotificationDTO.getObjectVO() != null && !messageNotificationDTO.getObjectVO().isEmpty()) {
				for (MessageNotificationObject Object : messageNotificationDTO.getObjectVO()) {
					Object.setNoticeId(messageNotificationDTO.getNoticeId());
					addNumber = noticeService.addMessageNotificationObject(Object);
					if (addNumber < 0) {
						outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
						return;
					}
				}
			}
			outJson(successInfo());
		} catch (Exception e) {
//			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			logger.error("/addNotice:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @description: 删除通知
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/deleteNotices")
	void deleteNotices(@RequestBody MessageNotificationDTO messageNotificationDTO) {
		try {
			int addNumber = 0;
			List<String> ids = messageNotificationDTO.getNoticeIds();
			for (String id : ids) {
				messageNotificationDTO.setNoticeId(id);
				addNumber = noticeService.updateNotice(messageNotificationDTO);
			}

			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			logger.error("/deleteNotices:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @description: 修改通知
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/updateNotice")
	@Transactional(rollbackFor = Exception.class)
	void updateNotice(@RequestBody MessageNotificationDTO messageNotificationDTO) {
		try {
			// 删除推送对象
			int addNumber;
			String systemId = messageNotificationDTO.getSynchronizationPlatform();
			if (messageNotificationDTO.getIsImmediately() == 0) {
				noticeService.deleteMessageNotificationObject(messageNotificationDTO);
				// 添加推送对象
				for (MessageNotificationObject Object : messageNotificationDTO.getObjectVO()) {
					Object.setNoticeId(messageNotificationDTO.getNoticeId());
					addNumber = noticeService.addMessageNotificationObject(Object);
					if (addNumber < 0) {
						outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
						return;
					}
				}
				// 删除推送产品
				int systemCount = noticeService.deleteMessageNotificationSystem(messageNotificationDTO);
				if (systemCount <= 0) {
					outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
					return;
				}
				List<String> systemIds = new ArrayList<>();
				if (systemId.contains(",")) {
					systemIds = Arrays.asList(systemId.split(","));
				} else {
					systemIds.add(systemId);
				}
				// 添加推送产品
				addNumber = noticeService.addMessageNotificationSystem(messageNotificationDTO.getNoticeId(), systemIds);
				if (addNumber <= 0) {
					outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
					return;
				}
			}
			addNumber = noticeService.updateNotice(messageNotificationDTO);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			logger.error("/updateNotice:", e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @param
	 * @description: 获取通知类型
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/getNoticeType")
	void getNoticeType() {
		try {
			List<MessageNotificaionType> messageNotificaionTypes = noticeService.getNoticeType();
			outJson(successInfo(messageNotificaionTypes));
		} catch (Exception e) {
			logger.error("getNoticeType:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	@RequestMapping(value = "/getMessageNotification", method = RequestMethod.POST)
	public void getMessageNotification(@RequestBody MessageNotificationDTO messageNotification) {
		try {
			int pageNum = (0 == messageNotification.getPageNum() ? 1 : messageNotification.getPageNum());
			int everyPage = (0 == messageNotification.getEveryPage() ? 10 : messageNotification.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<MessageNotificationDTO> messageNotificaions = noticeService
					.getMessageNotification(messageNotification);
			outJson(successPageInfo(messageNotificaions.getResult(), messageNotificaions.getTotal()));
		} catch (Exception e) {
			logger.error("getNoticeType:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	@RequestMapping(value = "/getMessageNotificationById", method = RequestMethod.POST)
	public void getMessageNotificationById(@RequestParam("noticeId") String noticeId) {
		try {
			MessageNotificationDTO messageNotificaion = noticeService.getMessageNotificationById(noticeId);
			outJson(successInfo(messageNotificaion));
		} catch (Exception e) {
			logger.error("getMessageNotificationById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * @description: 获取执行记录
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@PostMapping(value = "/getQuartzJobRecordList")
	public void getQuartzJobRecordList(@RequestBody QuartzJobRecordDTO quartzJobRecordDTO) {
		try {
			int pageNum = (null == quartzJobRecordDTO.getPageNum() ? 1 : quartzJobRecordDTO.getPageNum());
			int everyPage = (null == quartzJobRecordDTO.getEveryPage() ? 10 : quartzJobRecordDTO.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<QuartzJobRecordDTO> page = rongNoticeService.getQuartzJobRecordList(quartzJobRecordDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {
			logger.error("getQuartzJobRecordList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}

	}
}
