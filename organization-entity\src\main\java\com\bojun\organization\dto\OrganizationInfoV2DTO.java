package com.bojun.organization.dto;

import com.bojun.organization.entity.OrganizationInfoV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 机构信息表对象 t_organization_info
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@ApiModel(value = "OrganizationInfoV2DTO对象")
@Data
public class OrganizationInfoV2DTO extends OrganizationInfoV2
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;

    @ApiModelProperty(value = "角色ID", example = "")
    private String roleId;

    @ApiModelProperty(value = "是否有权限(全选半选之分)0无(半选)1有(全选)", example = "")
    private Integer hasAuth;

    @ApiModelProperty(value = "父级机构ID集合", example = "")
    private List<Integer> parentIds;

    @ApiModelProperty(value = "子集", example = "")
    private List<OrganizationInfoV2DTO> children;

    @ApiModelProperty(value = "区/县编号")
    private String countyCode;

    @ApiModelProperty(value = "层级", example = "")
    private Integer hierarchy;

}
