
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.bojun.system.dto.MessageNotificationReceiverDTO;



/**
 * 
*Model：消息通知接收人信息表
*Description：消息通知接收人信息表
*Author:李欣颖
*created：2020年1月7日
 */
@Mapper
public interface MessageNotificationReceiverMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	long queryMessageNotificationReceiverCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询消息通知接收人信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationReceiverDTO>
	 */
	List<MessageNotificationReceiverDTO> getMessageNotificationReceiver(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	Integer addMessageNotificationReceiver(MessageNotificationReceiverDTO messageNotificationReceiverDTO);

	/**
	 * 
	 * @Description 删除消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	Integer deleteMessageNotificationReceiver(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param messageNotificationReceiverDTO
	 * @return
	 * @return int
	 */
	Integer updateMessageNotificationReceiver(MessageNotificationReceiverDTO messageNotificationReceiverDTO);

	/**
	 * 
	 * @Description 查询单个消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return MessageNotificationReceiverDTO
	 */
	MessageNotificationReceiverDTO getMessageNotificationReceiverById(Map<String, Object> mapPara);

	Map<String, Object> getStatisticsContract();

	Map<String, Object> getStatisticsEmployees();

	Map<String, Object> getStatisticsIncompleteData();

	long queryMessageNotificationReceiverUnreadCount(Map<String, Object> paramsMap);
	Integer addMessageNotificationReceiverList(@Param("list")List<MessageNotificationReceiverDTO> list);

	List<MessageNotificationReceiverDTO> getMessageNotificationReceiverHome(Map<String, Object> mapPara);

	MessageNotificationReceiverDTO getContractMessageById(Map<String, Object> paramsMap);


	
}
