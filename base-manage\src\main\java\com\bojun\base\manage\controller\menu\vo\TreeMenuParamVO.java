/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：菜单管理
 * Description：查询树形下拉菜单信息
 * Author：赖水秀
 * created： 2020年4月28日
 */
@ApiModel(value = "查询树形下拉菜单条件", description = "查询树形下拉菜单列表传入的查询条件")
public class TreeMenuParamVO implements Serializable {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = -2861615641502296513L;

	@NotEmpty(message = "系统id不能为空")
	@ApiModelProperty(value="系统id", required = true, example = "1")
	private String systemId;	
	
	@NotNull(message = "菜单类型不能为空")
	@ApiModelProperty(value="菜单类型（传入1） ", required = true, example = "1")
	private Integer menuType;

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public Integer getMenuType() {
		return menuType;
	}

	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}
	
}
