/**
 * 
 */
package com.bojun.base.system.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.ISystemDictService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.dto.SystemDictTypeDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

/** 
 * Model： 系统管理模块
 * Description： 系统管理
 * Author：赖水秀
 * created： 2020年4月27日
 */
@RestController
public class SystemDictController extends BoJunBaseController {
	
	
	private static Log log = LogFactory.getLog(SystemDictController.class);
	
	@Autowired
	private ISystemDictService systemDictService;
	
	
	
	/**
	 * @Description 
	 * <AUTHOR>
	 * @param systemDictDto
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/saveSystemDict", method = RequestMethod.POST)
	public void saveSystemDict(@RequestBody SystemDictDTO systemDictDto) {
		try {
			SystemDictDTO systemDictDTO = systemDictService.getSystemDictById(systemDictDto.getSystemId());
			if(null != systemDictDTO) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "产品编码已经存在，请重新输入！"));
				return;
			}
			int addNumber = systemDictService.saveSystemDict(systemDictDto);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("saveSystemDict:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 修改产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/updateSystemDict", method = RequestMethod.POST)
	public void updateSystemDict(@RequestBody SystemDictDTO systemDictDto) {
		try {			
			int addNumber = systemDictService.updateSystemDict(systemDictDto);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateSystemDict:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 查询产品分类列表
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemDictTypeList", method = RequestMethod.POST)
	public void getSystemDictTypeList() {
		try {
			List<SystemDictTypeDTO> list = systemDictService.getSystemDictTypeList();
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getSystemDictTypeList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 查询单个产品信息
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemDictById", method = RequestMethod.POST)
	public void getSystemDictById(@RequestParam(value="systemId") String systemId) {
		try {
			SystemDictDTO systemDictDto = systemDictService.getSystemDictById(systemId);
			if (systemDictDto == null) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(systemDictDto));
		} catch (Exception e) {
			log.error("getSystemDictById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 分页查询产品列表
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemDictList", method = RequestMethod.POST)
	public void getSystemDictList(@RequestBody SystemDictDTO systemDictDTO) {
		try {
			int pageNum = (null == systemDictDTO.getPageNum()?1:systemDictDTO.getPageNum());
			int everyPage = (null == systemDictDTO.getEveryPage()?10:systemDictDTO.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<SystemDictDTO> page = systemDictService.getSystemDictList(systemDictDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}			
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {
			log.error("getSystemDictList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 根据角色ID查询产品列表
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemDictByRoleId", method = RequestMethod.POST)
	public void getSystemDictByRoleId(@RequestBody SystemDictDTO systemDictDto) {
		try {
			List<SystemDictDTO> list = systemDictService.getSystemTreeByRoleId(systemDictDto);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getSystemDictByRoleId:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	
	/**
	 * @Description 查询树形产品下拉列表
	 * <AUTHOR>
	 * void
	 * 2020年4月30日
	 */
	@RequestMapping(value = "/getSystemDictTreeList", method = RequestMethod.POST)
	public void getSystemDictTreeList() {
		try {			
			List<SystemDictDTO> list = systemDictService.getAllSystemDictList();
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			
			List<SystemDictDTO> treeList = toTree(list);			
			outJson(successInfo(treeList));
		} catch (Exception e) {
			log.error("getMenuTreeList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * @Description 根据角色ID查询所有产品下的菜单树
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemMenuTreeByRoleId", method = RequestMethod.POST)
	public void getSystemMenuTreeByRoleId(@RequestBody SystemDictDTO systemDictDto) {
		try {
			//查询所有有权限产品
			List<SystemDictDTO> systemList = systemDictService.getSystemTreeByRoleId(systemDictDto);
			if (systemList == null || systemList.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(systemList));
		} catch (Exception e) {
			log.error("getSystemDictByRoleId:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 列表转换成树形菜单
	 * <AUTHOR>
	 * @param sourceList
	 * @return
	 * List
	 * 2020年4月30日
	 */
	private static List<SystemDictDTO> toTree(List<SystemDictDTO> sourceList) {
		List<SystemDictDTO> rest = new ArrayList<SystemDictDTO>();
		Map map = (Map) sourceList.stream().collect(Collectors.toMap(SystemDictDTO::getSystemId, Function.identity()));

		sourceList.forEach(data -> {
			SystemDictDTO item = (SystemDictDTO) data;			
			if (item.getParentId() == null || "".equals(item.getParentId())) {
				rest.add(item);
				item.setChildren(new ArrayList<>());
			}
		});
		sourceList.forEach(data -> {
			SystemDictDTO item = (SystemDictDTO) data;			
			if (item.getParentId() != null && !"".equals(item.getParentId())) {
				SystemDictDTO parent = (SystemDictDTO) map.get(item.getParentId());
				if (parent.getChildren() == null) {
					parent.setChildren(new ArrayList<>());
				}
				parent.getChildren().add(item);
				
			}
		});		
		return rest;
	}
	
}
