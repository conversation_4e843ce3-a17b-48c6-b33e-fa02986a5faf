/**
 * 
 */
package com.bojun.system.enums;

/**
*Model：系统字典枚举类
 *Description：系统字典枚举类
 *Author:李欣颖
 *created：2020年3月3日
 */
public enum SystemDictEnums {


    BASE_CONFIG_MANAGE("S003", "基础配置管理系统"),
	SPHYGMOMETER_MANAGE("SPM001","网格化高血压筛查系统"),
	SPHYGMOMETER_APP("SPM002","慢病智能管家")
	;
	
    /**
     * 系统ID
     */
    private String systemId;

    /**
     * 系统名称
     */
    private String systemName;
	
	private SystemDictEnums(String systemId, String systemName) {
		this.systemId = systemId;
		this.systemName = systemName;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

}
