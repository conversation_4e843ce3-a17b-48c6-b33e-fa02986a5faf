package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：机构管理
*Description：机构图片信息
*Author: 赖水秀
*created：2020年5月8日
*/
@ApiModel(value = "机构图片信息", description = "机构图片信息")
public class OrganizationImgVO implements Serializable{
	
	private static final long serialVersionUID = 109958065299895997L;
	
	
	@ApiModelProperty(value="机构id", required = true, example = "10003")
	private Integer organizationId;
	
	
	@ApiModelProperty(value="图片名称", required = true, example = "aaa.jpg")
	private String organizationImage;
	
	
	@ApiModelProperty(value="图片地址", required = true, example = "http://************:8095/bojunFile/org/aaa.jpg")
	private String organizationImageUrl;	//图片地址
		
	@ApiModelProperty(value="图片id")
	private Integer id;
	
	@ApiModelProperty(value="是否封面 1 是 0 否")
	private Integer isCover;

	public Integer getIsCover() {
		return isCover;
	}

	public void setIsCover(Integer isCover) {
		this.isCover = isCover;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationImage() {
		return organizationImage;
	}

	public void setOrganizationImage(String organizationImage) {
		this.organizationImage = organizationImage;
	}

	public String getOrganizationImageUrl() {
		return organizationImageUrl;
	}

	public void setOrganizationImageUrl(String organizationImageUrl) {
		this.organizationImageUrl = organizationImageUrl;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}
	
}

