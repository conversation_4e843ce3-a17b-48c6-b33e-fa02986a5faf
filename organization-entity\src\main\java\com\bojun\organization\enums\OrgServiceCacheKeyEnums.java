package com.bojun.organization.enums;
/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年9月25日
*/
public enum OrgServiceCacheKeyEnums {
	
	ORG_AND_DEPT_LAYERED("ORG_AND_DEPT_LAYERED","机构和科室层级关系"),
	Y<PERSON>IAO_ORG_AND_DEPT_LAYERED("YILIAO_ORG_AND_DEPT_LAYERED","医疗机构和科室层级关系"),
	YANGLAO_ORG_AND_DEPT_LAYERED("YANGLAO_ORG_AND_DEPT_LAYERED","养老机构和科室层级关系");
	
	/**
     * key
     */
    private String key;

    /**
     * 说明
     */
    private String desc;
    
    private OrgServiceCacheKeyEnums(String key, String desc) {
		this.key = key;
		this.desc = desc;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}
    
}

