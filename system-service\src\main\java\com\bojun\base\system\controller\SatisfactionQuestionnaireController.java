package com.bojun.base.system.controller;


import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.config.CommonConfig;
import com.bojun.base.system.service.IFormQuestionItemService;
import com.bojun.base.system.service.IFormQuestionOptionService;
import com.bojun.base.system.service.IFormQuestionService;
import com.bojun.base.system.service.ISatisfactionQuestionnaireService;
import com.bojun.contants.FilePathConstants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.FormQuestionDTO;
import com.bojun.system.dto.FormQuestionItemDTO;
import com.bojun.system.dto.FormQuestionOptionDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 
*Model：满意度问卷表
*Description：满意度问卷表
*Author:李欣颖
*created：2020年5月7日
 */
@RestController
//@RequestMapping("satisfactionQuestionnaire")
public class SatisfactionQuestionnaireController extends BoJunBaseController {

	private static Log log = LogFactory.getLog(SatisfactionQuestionnaireController.class);


	@Autowired
	ISatisfactionQuestionnaireService satisfactionQuestionnaireService;
	
	@Autowired
	IFormQuestionService formQuestionService;
	
	@Autowired
	IFormQuestionOptionService formQuestionOptionService;
	
	@Autowired
	IFormQuestionItemService formQuestionItemService;
	
	@Autowired
	private CommonConfig commonConfig;
	
	/**
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 * @Description 查询满意度问卷表信息列表
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/getSatisfactionQuestionnaire", method = RequestMethod.POST)
	public void getSatisfactionQuestionnaire(@RequestBody SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
		try {
			int pageNum = (null == satisfactionQuestionnaireDTO.getPageNum() ? 1:satisfactionQuestionnaireDTO.getPageNum());
			int everyPage = (null == satisfactionQuestionnaireDTO.getEveryPage() ? 10:satisfactionQuestionnaireDTO.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<SatisfactionQuestionnaireDTO> page = satisfactionQuestionnaireService.getSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			List<SatisfactionQuestionnaireDTO> result = page.getResult();
			for (SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO2 : result) {
				if(StringUtils.isNotBlank(satisfactionQuestionnaireDTO2.getCoverImg())) {
					satisfactionQuestionnaireDTO2.setCoverImgStr(commonConfig.getBaseHttpUrl() + FilePathConstants.STATISFACTION_PATH + satisfactionQuestionnaireDTO2.getCoverImg());
				}
			}
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {

			log.error("getSatisfactionQuestionnaire:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	

	/**
	 * 
	 * @Description 新增满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/addSatisfactionQuestionnaire", method = RequestMethod.POST)
	public void addSatisfactionQuestionnaire(@RequestBody SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO ) {
		try {
			if (null == satisfactionQuestionnaireDTO || (null!=satisfactionQuestionnaireDTO.getIsRelaDept()&& 1==satisfactionQuestionnaireDTO.getIsRelaDept()&&StringUtils.isBlank(satisfactionQuestionnaireDTO.getDeptIds()) )
					|| StringUtils.isBlank(satisfactionQuestionnaireDTO.getTitle())) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			List<FormQuestionDTO> questionList = satisfactionQuestionnaireDTO.getFormQuestionDTOList();
			if (null != questionList && questionList.size() > 0) {
				for (FormQuestionDTO formQuestionDTO : questionList) {

					if (null == formQuestionDTO.getQuestionType() || null == formQuestionDTO.getQuestionNumber()
							|| StringUtils.isBlank(formQuestionDTO.getTitle())) {
						outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
						return;
					}
					List<FormQuestionOptionDTO> QuestionOptionList = formQuestionDTO.getFormQuestionOptionDTOList();
					if (null != QuestionOptionList && QuestionOptionList.size() > 0) {
						for (FormQuestionOptionDTO formQuestionOptionDTO : QuestionOptionList) {
							if (StringUtils.isBlank(formQuestionOptionDTO.getOptionValue())
									|| null == formQuestionOptionDTO.getScore()) {
								outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
								return;
							}
						}

					}
				}
			}
//			paramsMap.put("id",UuidGenerator.getUuidGenerator());
			Integer nNum = satisfactionQuestionnaireService.addSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
			if (null == nNum || nNum.intValue() != 1) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());

		} catch (Exception e) {
			log.error("addSatisfactionQuestionnaire:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 删除满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/deleteSatisfactionQuestionnaire", method = RequestMethod.POST)
	public void deleteSatisfactionQuestionnaire(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap || paramsMap.size() == 0 || null == paramsMap.get("ids")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			Integer deleteNum = satisfactionQuestionnaireService.deleteSatisfactionQuestionnaire(paramsMap);

			outJson(successInfo("成功删除" + deleteNum + "记录"));

		} catch (Exception e) {

			log.error("deleteSatisfactionQuestionnaire:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 查询单个满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/getSatisfactionQuestionnaireById", method = RequestMethod.POST)
	public void getSatisfactionQuestionnaireById(@RequestParam(value = "questionnaireId") Integer questionnaireId) {
		try {
			if (null == questionnaireId) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			SatisfactionQuestionnaireDTO resSatisfactionQuestionnaireDTO = satisfactionQuestionnaireService.getSatisfactionQuestionnaireById(questionnaireId);
			if (null == resSatisfactionQuestionnaireDTO) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			if(StringUtils.isNotBlank(resSatisfactionQuestionnaireDTO.getCoverImg())) {
				resSatisfactionQuestionnaireDTO.setCoverImgStr(commonConfig.getBaseHttpUrl() + FilePathConstants.STATISFACTION_PATH + resSatisfactionQuestionnaireDTO.getCoverImg());
			}
			Map<String, Object> mapPara =new HashMap<String, Object>();
			mapPara.put("questionnaireId", questionnaireId);
            List<FormQuestionDTO> formQuestionList = formQuestionService.getFormQuestion(mapPara);
            if (null!=formQuestionList&&formQuestionList.size()>0) {
            	resSatisfactionQuestionnaireDTO.setFormQuestionDTOList(formQuestionList);
				for (FormQuestionDTO formQuestionDTO : formQuestionList) {
					mapPara.put("questionId", formQuestionDTO.getQuestionId());
					List<FormQuestionOptionDTO> formQuestionOptionDTOList = formQuestionOptionService.getFormQuestionOption(mapPara);
					if(3==formQuestionDTO.getQuestionType() || 4==formQuestionDTO.getQuestionType())
					{
						List<FormQuestionItemDTO> formQuestionItemDTOLsit = formQuestionItemService.getFormQuestionItem(mapPara);
						if (null!=formQuestionItemDTOLsit&&formQuestionItemDTOLsit.size()>0) {
							formQuestionDTO.setFormQuestionItemDTOList(formQuestionItemDTOLsit);
						}
					}
					if (null!=formQuestionOptionDTOList&&formQuestionOptionDTOList.size()>0) {
						formQuestionDTO.setFormQuestionOptionDTOList(formQuestionOptionDTOList);
					}
				}
			}
			outJson(successInfo(resSatisfactionQuestionnaireDTO));
		} catch (Exception e) {
			log.error("getSatisfactionQuestionnaireById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}


	/**
	 * 
	 * @Description 修改满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/updateSatisfactionQuestionnaire", method = RequestMethod.POST)
	public void updateSatisfactionQuestionnaire(@RequestBody SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
		try {
			if (null == satisfactionQuestionnaireDTO || StringUtils.isBlank(satisfactionQuestionnaireDTO.getTitle()) ||
					null == satisfactionQuestionnaireDTO.getQuestionnaireId()) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			if (null != satisfactionQuestionnaireDTO.getIsRelaDept() &&
					1 == satisfactionQuestionnaireDTO.getIsRelaDept() &&
					StringUtils.isEmpty(satisfactionQuestionnaireDTO.getDeptIds())) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			List<FormQuestionDTO> questionList = satisfactionQuestionnaireDTO.getFormQuestionDTOList();
			if (null != questionList && questionList.size() > 0) {
				for (FormQuestionDTO formQuestionDTO : questionList) {

					if (null == formQuestionDTO.getQuestionType() || null == formQuestionDTO.getQuestionNumber()
							|| StringUtils.isBlank(formQuestionDTO.getTitle())) {
						outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
						return;
					}
					List<FormQuestionOptionDTO> QuestionOptionList = formQuestionDTO.getFormQuestionOptionDTOList();
					if (null != QuestionOptionList && QuestionOptionList.size() > 0) {
						for (FormQuestionOptionDTO formQuestionOptionDTO : QuestionOptionList) {
							if (StringUtils.isBlank(formQuestionOptionDTO.getOptionValue())
									|| null == formQuestionOptionDTO.getScore()) {
								outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
								return;
							}
						}

					}
				}
			}
			Integer nNum=satisfactionQuestionnaireService.updateSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
			if (null == nNum || nNum.intValue()!= 1) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateSatisfactionQuestionnaire:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * 
	 * @Description 修改满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/updateSatisfactionQuestionnaireStatus", method = RequestMethod.POST)
	public void updateSatisfactionQuestionnaireStatus(@RequestBody SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {

		try {
			if (null == satisfactionQuestionnaireDTO ||null==satisfactionQuestionnaireDTO.getStatus() || null==satisfactionQuestionnaireDTO.getQuestionnaireId() ) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			
			Integer nNum=satisfactionQuestionnaireService.updateSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
			if (null == nNum || nNum.intValue()<= 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateSatisfactionQuestionnaire:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
		
		
	
	}
}
