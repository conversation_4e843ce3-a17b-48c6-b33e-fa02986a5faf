package com.bojun.base.manage.controller.notice.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/30 8:47
 */
public class NoticeTypeVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "消息类型Id")
    private String noticeTypeId;
    @ApiModelProperty(value = "消息类型名字")
    private String noticeTypeName;

    public String getNoticeTypeId() {
        return noticeTypeId;
    }

    public void setNoticeTypeId(String noticeTypeId) {
        this.noticeTypeId = noticeTypeId;
    }

    public String getNoticeTypeName() {
        return noticeTypeName;
    }

    public void setNoticeTypeName(String noticeTypeName) {
        this.noticeTypeName = noticeTypeName;
    }
}
