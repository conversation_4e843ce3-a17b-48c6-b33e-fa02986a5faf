/**
 * 
 */
package com.bojun.base.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.page.PageData;
import com.bojun.response.Results;
import com.bojun.utils.BeanUtil;
import com.bojun.utils.DateUtils;
import com.bojun.vo.Page;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;

import java.beans.PropertyEditorSupport;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
*Model：公共控制器
*Description：公共控制器
*Author:段德鹏
*created：2020年4月9日
 * @param <T>
**/
public class BaseController<T> {
	
	/**
	 * @Description 失败请求
	 * <AUTHOR>
	 * @param code
	 * @return
	 * @return Results
	 * created：2020年4月12日
	 */
	@SuppressWarnings("rawtypes")
	public Results failResults() {
		Results results = new Results<>(ResponseCodeEnum.FAIL_REQUEST.getCode(), 
				ResponseCodeEnum.FAIL_REQUEST.getErrorDescr(), null);
		return results;
	}
	
	
	/**
	 * @Description  失败请求
	 * <AUTHOR>
	 * @param code  结果状态码
	 * @return
	 * @return Results<T>
	 * created：2020年4月9日
	 */
	@SuppressWarnings("rawtypes")
	public Results failResults(Integer code) {
		Results results = new Results<>(code, ResponseCodeEnum.getErrorCodeDescr(code), null);
		return results;
	}
	
	/**
	 * @Description 失败请求
	 * <AUTHOR> 
	 * @param code 结果状态码
	 * @param msg 返回消息
	 * @return
	 * @return Results<Object>
	 * created：2020年4月9日
	 */
	@SuppressWarnings("rawtypes")
	public Results failResults(Integer code, String msg) {
		Results results = new Results<>(code, msg, null);
		return results;
	}
	
	/**
	 * @Description  成功请求
	 * <AUTHOR>
	 * @param obj
	 * @return
	 * @return Results<Object>
	 * created：2020年4月9日
	 */
	@SuppressWarnings("rawtypes")
	public Results sucessResults(Object obj) {
		Results results = new Results<>(ResponseCodeEnum.SUCCESS_REQUEST.getCode(), 
				ResponseCodeEnum.SUCCESS_REQUEST.getErrorDescr(), obj);
		return results;
	}
	
	/**
	 * @Description 成功请求
	 * <AUTHOR>
	 * @return
	 * @return Results
	 * created：2020年4月11日
	 */
	@SuppressWarnings("rawtypes")
	public Results sucessResults() {
		Results results = new Results<>(ResponseCodeEnum.SUCCESS_REQUEST.getCode(), 
				ResponseCodeEnum.SUCCESS_REQUEST.getErrorDescr(), null);
		return results;
	}
	
	/**
	 * @Description  异常请求
	 * <AUTHOR>
	 * @return
	 * @return Results
	 * created：2020年6月5日
	 */
	@SuppressWarnings("rawtypes")
	public Results errorResults() {
		Results results = new Results<>(ResponseCodeEnum.EXCEPTION_REQUEST.getCode(), 
				ResponseCodeEnum.EXCEPTION_REQUEST.getErrorDescr(), null);
		return results;
	}
	
	/**
	 * @Description  异常请求
	 * <AUTHOR>
	 * @return
	 * @return Results
	 * created：2020年6月5日
	 */
	@SuppressWarnings("rawtypes")
	public Results errorResults(Exception e) {
		Results results = new Results<>(ResponseCodeEnum.EXCEPTION_REQUEST.getCode(), 
				"请求异常：" + e.getMessage(), null);
		return results;
	}
	
	/**
	 * @Description  熔断请求
	 * <AUTHOR>
	 * @return
	 * @return Results
	 * created：2020年6月5日
	 */
	@SuppressWarnings("rawtypes")
	public Results hystrixResults() {
		Results results = new Results<>(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode(), 
				ResponseCodeEnum.UNREASONABLE_REQUEST.getErrorDescr(), null);
		return results;
	}
	
	/**
	 * @Description  熔断请求
	 * <AUTHOR>
	 * @return
	 * @return Results
	 * created：2020年6月5日
	 */
	@SuppressWarnings("rawtypes")
	public Results hystrixResults(String msg) {
		Results results = new Results<>(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode(), 
				msg, null);
		return results;
	}
	
	/**
	 * @Description  返回成功，失败结果
	 * <AUTHOR>
	 * @param result
	 * @return
	 * @return Results
	 * created：2020年4月28日
	 */
	@SuppressWarnings("rawtypes")
	public Results returnResults(String result) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		if (200 != resultOjb.getInteger("code")) {
			return failResults(resultOjb.getInteger("code"), resultOjb.getString("msg"));
		}
		Results results = new Results<>(resultOjb.getInteger("code"), 
				resultOjb.getString("msg"), resultOjb.get("data"));
		return results;
	}
	
	/**
	 * @Description  json结果转换为Results
	 * <AUTHOR>
	 * @param result
	 * @return
	 * @return Results
	 * created：2020年6月9日
	 */
	@SuppressWarnings("rawtypes")
	public Results getResults(String result) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		Results results = new Results<>(resultOjb.getInteger("code"), 
				resultOjb.getString("msg"), resultOjb.get("data"));
		return results;
	}
	
	/**
	 * @Description 返回带VO结果（多结果）
	 * <AUTHOR>
	 * @param result
	 * @param clazz
	 * @return
	 * @return Results
	 * created：2020年4月28日
	 */
	@SuppressWarnings("rawtypes")
	public Results returnResults(String result, Class<T> clazz,String obj) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		if (200 != resultOjb.getInteger("code")) {
			return failResults(resultOjb.getInteger("code"), resultOjb.getString("msg"));
		}
		String jsonStr = resultOjb.get("data").toString();
		JSONObject  jsonObj = JSONObject.parseObject(jsonStr);
		JSONObject jsonObj2 = jsonObj.getJSONObject(obj);
		jsonObj.remove(obj);
		JSONObject fluentPutAll = jsonObj.fluentPutAll(jsonObj2);
		T vo = JSON.toJavaObject(fluentPutAll, clazz);
		return sucessResults(vo);
	} 
	/**
	 * @Description 返回带VO结果
	 * <AUTHOR>
	 * @param result
	 * @param clazz
	 * @return
	 * @return Results
	 * created：2020年4月28日
	 */
	@SuppressWarnings("rawtypes")
	public Results returnResults(String result, Class<T> clazz) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		if (200 != resultOjb.getInteger("code")) {
			return failResults(resultOjb.getInteger("code"), resultOjb.getString("msg"));
		}
		String jsonStr = resultOjb.get("data").toString();
		JSONObject  jsonObj = JSONObject.parseObject(jsonStr);
		T vo = JSON.toJavaObject(jsonObj, clazz);
		return sucessResults(vo);
	} 
	/**
	 * @Description 返回带page结果
	 * <AUTHOR>
	 * @param result
	 * @param clazz
	 * @return
	 * @return Results
	 * created：2020年4月28日
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Results returnResultsPage(String result, Class<T> clazz) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		if (200 != resultOjb.getInteger("code")) {
			return failResults(resultOjb.getInteger("code"), resultOjb.getString("msg"));
		}
		String jsonStr = resultOjb.get("data").toString();
		Integer totalCount = resultOjb.getInteger("totalCount");
		List<T> list = JSONArray.parseArray(jsonStr, clazz);
		Page page = new Page();
		page.setTotalCount(totalCount);
		page.setDataList(list);
		return sucessResults(page);
	}
	/**
	 * @Description 返回带page结果
	 * <AUTHOR>
	 * @param result
	 * @param clazz
	 * @return
	 * @return Results
	 * created：2020年4月28日
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Results returnResultsPagem(String result, Class<T> clazz) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		if (200 != resultOjb.getInteger("code")) {
			return failResults(resultOjb.getInteger("code"), resultOjb.getString("msg"));
		}
		JSONObject jsonStr =  resultOjb.getJSONObject("data");
		JSONArray jsonStr1 =  jsonStr.getJSONArray("records");
		Integer totalCount = jsonStr.getInteger("total");
		Page page = new Page();
		page.setTotalCount(totalCount);
		page.setDataList(jsonStr1.toJavaList(clazz));
		return sucessResults(page);
		
	}
	
	/**
	 * @Description 返回带List结果
	 * <AUTHOR>
	 * @param result
	 * @param clazz
	 * @return
	 * @return Results
	 * created：2020年4月28日
	 */
	@SuppressWarnings("rawtypes")
	public Results returnResultsList(String result, Class<T> clazz) {
		JSONObject resultOjb = JSONObject.parseObject(result);
		if (200 != resultOjb.getInteger("code")) {
			return failResults(resultOjb.getInteger("code"), resultOjb.getString("msg"));
		}
		String jsonStr = resultOjb.get("data").toString();
		List<T> list = JSONArray.parseArray(jsonStr, clazz);
		return sucessResults(list);
	}


	/**
	 * 将前台传递过来的日期格式的字符串，自动转化为Date类型
	 */
	@InitBinder
	public void initBinder(WebDataBinder binder) {
		// Date 类型转换
		binder.registerCustomEditor(Date.class, new PropertyEditorSupport() {
			@Override
			public void setAsText(String text) {
				setValue(DateUtils.parseDate(text));
			}
		});
	}


	/**
	 * 响应请求分页数据(类型转换)
	 *
	 * @param <V>
	 */
	@SuppressWarnings({"rawtypes", "unchecked"})
	protected <T, V> PageData<V> getPageDataVo(PageData<T> pageData, Class<V> toObj) {
		PageData<V> newPageData = new PageData();
		newPageData.setDataList(copyList(pageData.getDataList(), toObj));
		newPageData.setTotalCount(pageData.getTotalCount());
		return newPageData;
	}


	/**
	 * 响应请求集合数据(类型转换)
	 *
	 * @param <V>
	 */
	@SuppressWarnings({"rawtypes", "unchecked"})
	protected <T, V> List<V> getListVo(List<T> list, Class<V> toObj) {
		return copyList(list, toObj);
	}

	/**
	 * 拷贝
	 */
	private static <T, S> List<S> copyList(List<T> fromList, Class<S> toObj) {
		if (null == fromList || fromList.size() <= 0) {
			return null;
		}
		if (null == toObj) {
			return null;
		}
		List<S> toList = new ArrayList<S>();
		fromList.forEach(f -> {
			S t = BeanUtil.deepCopyProperties(f, toObj);
			toList.add(t);
		});
		return toList;
	}
	
}
