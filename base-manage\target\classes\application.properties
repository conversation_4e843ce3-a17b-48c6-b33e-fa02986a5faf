spring.application.name=base-manage
server.port=8701
server.servlet.context-path=/baseManage
ribbon.eureka.enabled=true
eureka.instance.prefer-ip-address=true
eureka.client.service-url.defaultZone=http://localhost:1115/eureka

feign.okhttp.enabled=true
feign.hystrix.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=360000

ribbon.ReadTimeout=30000
ribbon.ConnectTimeout=30000

#reids#
spring.redis.database=0
spring.redis.host=127.0.0.1
spring.redis.port=6379

spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.file-size-threshold=10MB

console.file.upload.path=

console.file.path=


