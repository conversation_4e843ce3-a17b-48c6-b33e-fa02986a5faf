/**
 * 
 */
package com.bojun.base.manage.controller.homePage;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.IMenuService;
import com.bojun.base.manage.api.system.ISystemDictService;
import com.bojun.base.manage.controller.homePage.vo.HomeMenuInfoVO;
import com.bojun.base.manage.controller.homePage.vo.HomeSystemDictInfoVO;
import com.bojun.base.manage.controller.homePage.vo.HomeSystemDictParamVO;
import com.bojun.base.manage.controller.homePage.vo.HomeTreeMenuParamVO;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.response.Results;
import com.bojun.system.dto.MenuDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.vo.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
 * 
 * Model：主页管理
 * Description：主页管理
 * Author：lj
 * created： 2020年4月27日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("homePageMange")
@Api(tags = {"主页模块接口"})
@ApiSort(value = 0)
public class HomePageController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(HomePageController.class);
	
	@Autowired
	private ISystemDictService systemDictService;
	@Autowired
	private IMenuService menuService;
	
	/**
	 * @Description 主页产品列表
	 * <AUTHOR>
	 * @return
	 * Results<ManageUserLoginVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "主页产品列表", notes = "主页产品列表（lj）")
	@ApiOperationSupport(order = 1)
	@RequestMapping(value="/getSystemDictByRoleId", method = RequestMethod.POST)
	@AuthAnnotation(action = "getSystemDictByRoleId")
	public Results<Page<List<HomeSystemDictInfoVO>>> getSystemDictByRoleId(HttpServletRequest request, @RequestBody @Valid HomeSystemDictParamVO homeSystemDictParamVO) {		
		try {
			SystemDictDTO systemDictDTO = new SystemDictDTO();
			BeanUtils.copyProperties(homeSystemDictParamVO, systemDictDTO);
			String result = systemDictService.getSystemDictByRoleId(systemDictDTO);
			return returnResultsList(result, HomeSystemDictInfoVO.class);
		} catch (RuntimeException e) {
			logger.error("getSystemDictByRoleId:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getSystemDictByRoleId:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	/**
	 * @Description 所有产品列表下的菜单
	 * <AUTHOR>
	 * @return
	 * Results<ManageUserLoginVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "所有产品列表下的菜单", notes = "所有产品列表下的菜单（lj）")
	@ApiOperationSupport(order = 1)
	@RequestMapping(value="/getSystemMenuTreeByRoleId", method = RequestMethod.POST)
	@AuthAnnotation(action = "getSystemMenuTreeByRoleId")
	public Results<Page<List<HomeSystemDictInfoVO>>> getSystemMenuTreeByRoleId(HttpServletRequest request, @RequestBody @Valid HomeTreeMenuParamVO treeMenuParamVO) {		
		try {
			SystemDictDTO systemDictDTO = new SystemDictDTO();
			BeanUtils.copyProperties(treeMenuParamVO, systemDictDTO);
			String result = systemDictService.getSystemMenuTreeByRoleId(systemDictDTO);
			return returnResultsList(result, HomeSystemDictInfoVO.class);
		} catch (RuntimeException e) {
			logger.error("getSystemMenuTreeByRoleId:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getSystemMenuTreeByRoleId:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 主页树形菜单列表
	 * <AUTHOR>
	 * @return
	 * Results<ManageUserLoginVO>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "主页树形菜单列表", notes = "主页树形菜单列表（lj）")
	@ApiOperationSupport(order = 5)
	@RequestMapping(value="/getHomeMenuTree", method = RequestMethod.POST)
	@AuthAnnotation(action = "getHomeMenuTree")
	public Results<List<HomeMenuInfoVO>> getHomeMenuTree(@RequestBody @Valid HomeTreeMenuParamVO treeMenuParamVO) {		
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(treeMenuParamVO, menuDTO);
			String result = menuService.getMenuButtonTree(menuDTO);
			return returnResultsList(result, HomeMenuInfoVO.class);
		} catch (RuntimeException e) {
			logger.error("getHomeMenuTree:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getHomeMenuTree:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	

}
