/**
 * 
 */
package com.bojun.base.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.EducationDictService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.EducationDictDto;

/**
 * Model：学历信息表 Description：EducationDictController.java Author: 曾玲玲
 * created：2020年1月9日
 */
@RestController
public class EducationDictController extends BoJunBaseController {

	private static Log log = LogFactory.getLog(EducationDictController.class);

	@Autowired
	private EducationDictService educationDictService;

	/**
	 * @Description: 查询学历字典列表信息
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	@RequestMapping(value = "/getEducationDictList", method = RequestMethod.POST)
	@AuthAnnotation(action = "")
	public void getEducationDictList(HttpServletRequest request, @RequestBody Map<String, Object> map) {
		try {
			Integer count = educationDictService.getEducationDictListCount(map);
			if (null == count || 0 == count) {
				outJson(info(ResponseCodeEnum.NO_DATA.getCode(), "暂无数据"));
				return;
			}
			List<EducationDictDto> educationDictInfo = educationDictService.getEducationDictList(map);
			if (null == educationDictInfo || educationDictInfo.isEmpty()) {
				outJson(info(ResponseCodeEnum.NO_DATA.getCode(), "暂无数据"));
				return;
			}
			map.clear();
			map.put("totalCount", count);
			map.put("educationDictInfo", educationDictInfo);
			outJson(successPageInfo(educationDictInfo,count));
		} catch (Exception e) {
			log.error("getEducationDictList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @Description: 新增学历
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	@RequestMapping(value = "/addEducation", method = RequestMethod.POST)
	@AuthAnnotation(action = "")
	public void addEducation(HttpServletRequest request, @RequestBody EducationDictDto educationDictDto) {
		try {
			Map<String, Object> map = new HashMap<String, Object>();
			// 查询出最大学历code，为空则表示查询失败
			String num = educationDictService.queryMaximumEducationCode(map);
			if (StringUtils.isBlank(num)) {
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "查询职务最大学历code失败"));
				return;
			}
			// 校验新增时学历名称是否重复
			int count = educationDictService.checkEducationName(educationDictDto);
			if (count >= 1) {
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "学历名称已存在，请重新输入"));
				return;
			}
			// 新增学历，失败则结果小于等于零
			int result = educationDictService.addEducation(educationDictDto);
			if (result <= 0) {
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "新增学历失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("addEducation:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @Description: 编辑学历
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	@RequestMapping(value = "/updateEducation", method = RequestMethod.POST)
	@AuthAnnotation(action = "")
	public void updateEducation(HttpServletRequest request, @RequestBody EducationDictDto educationDictDto) {
		try {
			Integer id = educationDictDto.getId();
			if (null == id) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			// 校验编辑时学历名称是否重复 ，小于0则重复
			int count = educationDictService.checkEducationName(educationDictDto);
			if (count > 1) {
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "学历名称已存在，请重新输入"));
				return;
			}
			// 编辑学历，失败则结果小于等于零
			int result = educationDictService.updateEducation(educationDictDto);
			if (result <= 0) {
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "编辑学历失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateEducation:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @Description: 根据id查询学历字典
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	@RequestMapping(value = "/selectEducationDictById", method = RequestMethod.POST)
	@AuthAnnotation(action = "")
	public void selectEducationDictById(HttpServletRequest request, @RequestBody Map<String, Object> map) {
		try {
			Integer educationId = (Integer) map.get("id");
			if (null == educationId) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			EducationDictDto educationDictDto = educationDictService.selectEducationDictById(map);
			if (null == educationDictDto) {
				outJson(info(ResponseCodeEnum.NO_DATA.getCode(), "无所需数据"));
				return;
			}
			map.clear();
			map.put("educationDictDto", educationDictDto);
			outJson(successInfo(educationDictDto));
		} catch (Exception e) {
			log.error("selectEducationDictById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @Description: 根据id编辑学历是否启用
	 * @Author: 曾玲玲
	 * @create: 2019/11/22 15:42
	 * 
	 */
	@RequestMapping(value = "/updateEducationStatus", method = RequestMethod.POST)
	@AuthAnnotation(action = "")
	public void updateEducationStatus(HttpServletRequest request, @RequestBody EducationDictDto educationDictDto) {
		try {
			if (null == educationDictDto.getIsEnabled() || null == educationDictDto.getId()) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			// 编辑学历是否启用，失败则结果小于等于0
			int result = educationDictService.updateEducationStatus(educationDictDto);
			if (result <= 0) {
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "编辑失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateEducationStatus", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

}
