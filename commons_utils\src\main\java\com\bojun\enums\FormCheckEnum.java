package com.bojun.enums;

/**
 * Model:
 * Description:
 * Author: 张立新
 * created: 2019/7/24
 */
public class FormCheckEnum {

    public static final FormCheckEnum FIELD_SATISFY = new FormCheckEnum(200, "ok");

    public static final FormCheckEnum FIELD_LENGTH_OVERFLOW = new FormCheckEnum(201001, "%s限制%d字符");

    public static final FormCheckEnum FIELD_NUMBER_OVERFLOW = new FormCheckEnum(201002, "%s限制最大为%d");

    public static final FormCheckEnum FIELD_VERIFICATION = new FormCheckEnum(201003, "%s不能为空或者限制最大为%d");

    public static final FormCheckEnum FIELD_NOT_NULL = new FormCheckEnum(201004, "%s不能为空");

    private Integer code;
    private String errorDesc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getErrorDesc() {
        return errorDesc;
    }

    public void setErrorDescr(String errorDescr) {
        this.errorDesc = errorDescr;
    }

    FormCheckEnum(Integer code, String errorDesc) {
        this.code = code;
        this.errorDesc = errorDesc;
    }

    public FormCheckEnum addDetail(String field, Integer range) {
        String desc = String.format(this.errorDesc, field, range);
        return new FormCheckEnum(this.code, desc);
    }

    public FormCheckEnum addDetail(String field) {
        String desc = String.format(this.errorDesc, field);
        return new FormCheckEnum(this.code, desc);
    }


}
