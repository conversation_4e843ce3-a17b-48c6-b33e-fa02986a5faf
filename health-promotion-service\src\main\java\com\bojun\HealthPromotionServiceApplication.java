/**
 * 
 */
package com.bojun;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
*Model：健康宣教微服务入口
*Description：健康宣教微服务入口
*Author:段德鹏
*created：2021年5月27日
**/
@EnableFeignClients
@EnableCircuitBreaker
@EnableEurekaClient
@EnableTransactionManagement
@SpringBootApplication
public class HealthPromotionServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(HealthPromotionServiceApplication.class, args);
	}

}
