/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：菜单管理
 * Description：新增、修改菜单信息
 * Author：赖水秀
 * created： 2020年4月28日
 */
@ApiModel(value = "修改菜单信息", description = "修改菜单传入信息")
public class UpdateMenuPramVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;
	
	@NotEmpty(message = "菜单ID不能为空")
	@ApiModelProperty(value="菜单ID", required = true, example = "m12323")
	private String menuId;
	
	@NotEmpty(message = "菜单名称不能为空")
	@ApiModelProperty(value="菜单名称", required = true, example = "人员管理")
	private String menuName;
	
	@ApiModelProperty(value="上级菜单ID（一级菜单为空）")
	private String parentId;
	
	@NotNull(message = "父菜单级别不能为空")
	@ApiModelProperty(value="父菜单级别 1：一级 2：二级 （当上级菜单ID不为空的时候才有值）")
	private Integer parentLevel;
	
	@NotEmpty(message = "关联的系统id")
	@ApiModelProperty(value="关联的系统id", required = true, example = "1")
	private String systemId;	
	
	@NotEmpty(message = "菜单URL")
	@ApiModelProperty(value="菜单URL", required = true, example = "xxx/index")
	private String menuUrl;
		
	@ApiModelProperty(value="权限URL", required = true, example = "xxx/index")
	private String authorityUrl;
	
	@ApiModelProperty(value="显示顺序")
	private Integer showIndex;
	
	@ApiModelProperty(value="显示状态 ")
	private Integer isDisplay;
	
	@ApiModelProperty(value="菜单图标 ")
	private String icon;
	
	@ApiModelProperty(value="图标颜色 ")
	private String iconColor;
	
	@ApiModelProperty(value="备注")
	private String remark;

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public Integer getParentLevel() {
		return parentLevel;
	}

	public void setParentLevel(Integer parentLevel) {
		this.parentLevel = parentLevel;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getMenuUrl() {
		return menuUrl;
	}

	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}

	public String getAuthorityUrl() {
		return authorityUrl;
	}

	public void setAuthorityUrl(String authorityUrl) {
		this.authorityUrl = authorityUrl;
	}

	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}

	public Integer getIsDisplay() {
		return isDisplay;
	}

	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getIconColor() {
		return iconColor;
	}

	public void setIconColor(String iconColor) {
		this.iconColor = iconColor;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	
}
