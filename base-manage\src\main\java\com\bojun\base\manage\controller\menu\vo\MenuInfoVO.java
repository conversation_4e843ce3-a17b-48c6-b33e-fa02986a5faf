/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;
import java.util.List;

import com.bojun.system.dto.MenuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：菜单管理 
 * Description：查询产品列表返回信息
 * Author：赖水秀 
 * created： 2020年4月27日
 */
@ApiModel(value = "菜单列表", description = "查询菜单列表返回信息")
public class MenuInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 5508381353925610437L;

	@ApiModelProperty(value="菜单ID")
	private String menuId;
		
	@ApiModelProperty(value="菜单名称", required = true, example = "人员管理")
	private String menuName;
	
	@ApiModelProperty(value="上级菜单ID（一级菜单为空）")
	private String parentId;
		
	@ApiModelProperty(value="关联的系统id", required = true, example = "1")
	private String systemId;

	@ApiModelProperty(value="菜单类型 1：菜单 2：按钮", required = true, example = "1")
	private Integer menuType;
	
	@ApiModelProperty(value=" 菜单级别 1：一级 2：二级 ............类推", required = true, example = "1")
	private Integer level;
	
	@ApiModelProperty(value="菜单URL", required = true, example = "xxx/index")
	private String menuUrl;
		
	@ApiModelProperty(value="权限URL", required = true, example = "xxx/index")
	private String authorityUrl;
	
	@ApiModelProperty(value="显示顺序")
	private Integer showIndex;
	
	@ApiModelProperty(value="显示状态 ")
	private Integer isDisplay;
	
	@ApiModelProperty(value="菜单图标 ")
	private String icon;
	
	@ApiModelProperty(value="图标颜色 ")
	private String iconColor;
	
	@ApiModelProperty(value="菜单下的按钮名称")
	private String menuBtnName;
	
	@ApiModelProperty(value="子菜单")
	private List<MenuDTO> children;
	
	@ApiModelProperty(value="菜单下的按钮列表")
	private List<ButtonInfoVO> btnChildren;
	
	@ApiModelProperty(value="备注")
	private String remark;

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public Integer getMenuType() {
		return menuType;
	}

	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}

	public Integer getLevel() {
		return level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public String getMenuUrl() {
		return menuUrl;
	}

	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}

	public String getAuthorityUrl() {
		return authorityUrl;
	}

	public void setAuthorityUrl(String authorityUrl) {
		this.authorityUrl = authorityUrl;
	}

	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}

	public Integer getIsDisplay() {
		return isDisplay;
	}

	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public List<MenuDTO> getChildren() {
		return children;
	}

	public void setChildren(List<MenuDTO> children) {
		this.children = children;
	}

	public String getMenuBtnName() {
		return menuBtnName;
	}

	public void setMenuBtnName(String menuBtnName) {
		this.menuBtnName = menuBtnName;
	}

	public List<ButtonInfoVO> getBtnChildren() {
		return btnChildren;
	}

	public void setBtnChildren(List<ButtonInfoVO> btnChildren) {
		this.btnChildren = btnChildren;
	}

	public String getIconColor() {
		return iconColor;
	}

	public void setIconColor(String iconColor) {
		this.iconColor = iconColor;
	}	
	
}
