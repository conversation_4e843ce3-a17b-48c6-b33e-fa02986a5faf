package com.bojun.sphygmometer.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.push.GeTuiPushUtils;
import com.bojun.sphygmometer.api.SphygmometerRecordFeignClient;
import com.bojun.sphygmometer.common.PushConstants;
import com.bojun.sphygmometer.common.PushTypeEnum;
import com.bojun.sphygmometer.common.contants.SphygmomanometerContants;
import com.bojun.sphygmometer.dto.*;
import com.bojun.sphygmometer.entity.*;
import com.bojun.sphygmometer.service.*;
import com.bojun.sphygmometer.utils.SphygmometerUtil;
import com.bojun.utils.BeanUtil;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import com.gexin.rp.sdk.base.IPushResult;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * 血压记录信息表FeignController
 *
 * <AUTHOR>
 * @date 2021-03-20 10:22:09
 */
@Slf4j
@RestController
public class SphygmometerRecordFeignController extends BaseFeignController implements SphygmometerRecordFeignClient {
	@Autowired
	private SphygmometerRecordService sphygmometerRecordService;
	@Autowired
	private SphygmometerDeviceService sphygmometerDeviceService;
	@Autowired
	private SphygmometerUserService sphygmometerUserService;
	@Autowired
	private AppTaskDictService appTaskDictService;
	@Autowired
	private UserTaskRecordService userTaskRecordService;
	@Autowired
	private AppMessageNotificationService appMessageNotificationService;
	@Autowired
	private WxMpService wxMpService;
	@Autowired
	private SphygmometerUserRelativeService sphygmometerUserRelativeService;
	@Autowired
	private ResidentBasicInfoService residentBasicInfoService;

	/**
	 * 查询血压记录信息表列表
	 */
	@PostMapping(PREFIX + "/list")
	public PageData<SphygmometerRecordDTO> list(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		startPage(sphygmometerRecordDTO.getPageNum(), sphygmometerRecordDTO.getEveryPage());
		if (StringUtils.isNotBlank(sphygmometerRecordDTO.getMeasureStartTime())) {
			sphygmometerRecordDTO.setMeasureStartTime(sphygmometerRecordDTO.getMeasureStartTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(sphygmometerRecordDTO.getMeasureEndTime())) {
			sphygmometerRecordDTO.setMeasureEndTime(sphygmometerRecordDTO.getMeasureEndTime() + " 23:59:59");
		}
//		if (sphygmometerRecordDTO.getDeviceType() != null && sphygmometerRecordDTO.getDeviceType() == 2) {
//			sphygmometerRecordDTO.setRecordType(1);
//		}
		List<SphygmometerRecordDTO> list = sphygmometerRecordService.selectSphygmometerRecordList(sphygmometerRecordDTO);
		for (SphygmometerRecordDTO recordDTO : list) {
			if (StringUtils.isNotBlank(recordDTO.getMobile())) {
				String mobile = recordDTO.getMobile().replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
				recordDTO.setMobile(mobile);
			}
		}
		return getPageData(list);
	}


	/**
	 * 获取血压记录信息表详细信息
	 */
	@GetMapping(PREFIX + "/getCount")
	public Results<SphygmometerRecordDTO> getCount(@RequestParam("userId") Integer userId,@RequestParam("measureStartTime") String measureStartTime,
												   @RequestParam("measureEndTime") String measureEndTime) {
		if(null == userId){
			return Results.fail("用户ID为空");
		}
		SphygmometerRecordDTO sphygmometerRecordDTO = new SphygmometerRecordDTO();
		Integer count = 0;
		Integer highRiskCount =0;
		if(StringUtils.isNotBlank(measureStartTime) && StringUtils.isNotBlank(measureEndTime)){
			count = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					eq(SphygmometerRecord::getUserId,userId).lt(SphygmometerRecord::getMeasureTime,measureEndTime).
					gt(SphygmometerRecord::getMeasureTime,measureStartTime));
			highRiskCount = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					eq(SphygmometerRecord::getUserId,userId).lt(SphygmometerRecord::getMeasureTime,measureEndTime).
					gt(SphygmometerRecord::getMeasureTime,measureStartTime).eq(SphygmometerRecord::getPressureResult,3));
		}else{
			count = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					eq(SphygmometerRecord::getUserId,userId));
			highRiskCount = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					lt(SphygmometerRecord::getUserId,userId).
					eq(SphygmometerRecord::getPressureResult,3));
		}
		sphygmometerRecordDTO.setCount(count);
		sphygmometerRecordDTO.setHighRiskCount(highRiskCount);
		return Results.data(sphygmometerRecordDTO);
	}


	/**
	 * 获取血压记录信息表详细信息
	 */
	@GetMapping(PREFIX + "/getCountByWxApp")
	public Results<SphygmometerRecordDTO> getCountByWxApp(@RequestParam("userId") Integer userId,@RequestParam("measureStartTime") String measureStartTime,
												   @RequestParam("measureEndTime") String measureEndTime,@RequestParam("deviceType")Integer deviceType) {
		if(null == userId){
			return Results.fail("用户ID为空");
		}
		log.info("userId:{},measureStartTime:{},measureEndTime:{},deviceType:{}",userId,measureStartTime,measureEndTime,deviceType);
		SphygmometerRecordDTO sphygmometerRecordDTO = new SphygmometerRecordDTO();
		Integer count = 0;
		Integer highRiskCount =0;
		if(StringUtils.isNotBlank(measureStartTime) && StringUtils.isNotBlank(measureEndTime)){
			count = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					eq(SphygmometerRecord::getUserId,userId).lt(SphygmometerRecord::getMeasureTime,measureEndTime).
					gt(SphygmometerRecord::getMeasureTime,measureStartTime).eq(SphygmometerRecord::getDeviceType,deviceType));
			highRiskCount = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					eq(SphygmometerRecord::getUserId,userId).lt(SphygmometerRecord::getMeasureTime,measureEndTime).
					gt(SphygmometerRecord::getMeasureTime,measureStartTime).eq(SphygmometerRecord::getPressureResult,3).
					eq(SphygmometerRecord::getDeviceType,deviceType));
		}else{
			count = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					eq(SphygmometerRecord::getUserId,userId).
					eq(SphygmometerRecord::getDeviceType,deviceType));
			highRiskCount = sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>lambdaQuery().
					lt(SphygmometerRecord::getUserId,userId).
					eq(SphygmometerRecord::getPressureResult,3).
					eq(SphygmometerRecord::getDeviceType,deviceType));
		}
		sphygmometerRecordDTO.setCount(count);
		sphygmometerRecordDTO.setHighRiskCount(highRiskCount);
		return Results.data(sphygmometerRecordDTO);
	}

	/**
	 * 获取用户累计测量次数
	 * <AUTHOR>
	 * @date 2021/8/13
	 * @param userId
	 * @param deviceId
	 * @return Integer
	 */
	@GetMapping(PREFIX + "/getUserTotalTestCount")
	@Override
	public Integer getUserTotalTestCount(@RequestParam("userId") Integer userId, @RequestParam("deviceId") Integer deviceId) {
		return this.sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>query().lambda()
				.eq(SphygmometerRecord::getUserId, userId)
				.eq(SphygmometerRecord::getDeviceId, deviceId));
	}


	/**
	 * 查询血压记录信息表列表
	 */
	@PostMapping(PREFIX + "/getListExcel")
	public List<SphygmometerRecordDTO> getListExcel(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		if (null != sphygmometerRecordDTO.getMeasureEndTime() && !sphygmometerRecordDTO.getMeasureEndTime().equals("")) {
			sphygmometerRecordDTO.setMeasureEndTime(sphygmometerRecordDTO.getMeasureEndTime() + " 23:59:59");
		}
		List<SphygmometerRecordDTO> list = sphygmometerRecordService.getListExcel(sphygmometerRecordDTO);
		return list;
	}

	/**
	 * 测试模拟NB设备回调接口
	 * @return
	 */
	@PostMapping(PREFIX + "/testerSaveNBRecord")
	@Transactional(rollbackFor = Exception.class)
	public Results testerSaveNBRecord(@RequestBody TesterSaveNBRecordDTO testerSaveNBRecordDTO) {
		return this.saveNBRecord(null, testerSaveNBRecordDTO.getImei(),
				String.valueOf(System.currentTimeMillis()/1000), testerSaveNBRecordDTO.getUserType(),
				testerSaveNBRecordDTO.getSsy(), testerSaveNBRecordDTO.getSzy(),
				testerSaveNBRecordDTO.getXl(), "0");
	}

	/**
	 * 保存家庭式NB设备推送过来的数据
	 * @param decryptValue 解密后的内容
	 * @return
	 */
	@GetMapping(PREFIX + "/saveNBCallbackData")
	@Transactional(rollbackFor = Exception.class)
	public Results saveNBCallbackData(@RequestParam("decryptValue") String decryptValue) {
		String head = decryptValue.substring(0, 2);//包头, 02固定为包头
		String type = decryptValue.substring(2, 4);//类型, 64固定为血压计
		String iccid = decryptValue.substring(4, 24);//SIM卡上的ICCID号
		String imei = decryptValue.substring(24, 39);//NB模块上的ICCID号
		String time = decryptValue.substring(39, 49);//时间戳, 秒为单位
		String userType = decryptValue.substring(49, 50);//血压计的用户, 0用户2/1用户1
		String ssy = decryptValue.substring(50, 53);//收缩压
		String szy = decryptValue.substring(53, 56);//舒张压
		String xl = decryptValue.substring(56, 59);//脉搏
		String xlbq = decryptValue.substring(59, 60);//0为正常，1为心率不齐
		String dw = decryptValue.substring(60, 61);//0为mmHg, 1为KPa
		String power = decryptValue.substring(61, 63);//电量的百分比
		String xhqd = decryptValue.substring(63, 65);//信号强度

		return saveNBRecord(decryptValue, imei, time, userType, ssy, szy, xl, dw);
	}

	private Results saveNBRecord(String decryptValue, String imei, String time, String userType, String ssy, String szy, String xl, String dw) {
		SphygmometerDevice sphygmometerDevice = sphygmometerDeviceService.getOne(Wrappers.<SphygmometerDevice>query().lambda()
				.eq(SphygmometerDevice::getDeviceImei, imei).orderByDesc(SphygmometerDevice::getAddTime).last("LIMIT 1"));
		if (sphygmometerDevice == null) {
			log.error("控台未存该设备信息IMEI=," + imei + " 记录值: " + decryptValue);
			return Results.fail("暂无该设备");
		}
		log.info("DeviceId:" + sphygmometerDevice.getDeviceId() + "userType:" + userType);
		SphygmometerUser sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>query().lambda()
				.eq(SphygmometerUser::getBindDeviceId, sphygmometerDevice.getDeviceId())
				.eq(SphygmometerUser::getBindDeviceUserType, "0".equals(userType) ? 2 : 1));
		if (sphygmometerUser == null) {
			log.error("该设备IMEI=" + imei + "未绑定用户, 记录值: " + decryptValue);
			return Results.fail("该设备未绑定用户");
		}
		SphygmometerRecord existRecord = this.sphygmometerRecordService.getOne(Wrappers.<SphygmometerRecord>query().lambda()
				.eq(SphygmometerRecord::getDecryptValue, decryptValue));
		if (existRecord != null) {
			log.error("记录已存在, 重复推送, 记录值: " + decryptValue);
			return Results.fail("记录已存在, 重复推送");
		}
		Date currentDate = new Date();
		//毫秒级
		Long measureTime = Long.valueOf(time) * 1000;
		sphygmometerDevice.setHeartbeat(new Date());
		sphygmometerDevice.setIsOnline(1);//设置在线
		sphygmometerDeviceService.updateById(sphygmometerDevice);
		Integer deviceType = sphygmometerDevice.getDeviceType();
		//保存NB设备记录
		SphygmometerRecordDTO sphygmometerRecord = new SphygmometerRecordDTO();
		sphygmometerRecord.setDecryptValue(decryptValue);
		sphygmometerRecord.setUserId(sphygmometerUser.getUserId());
		sphygmometerRecord.setOrganizationId(sphygmometerDevice.getOrganizationId());
		sphygmometerRecord.setSerialNumber(sphygmometerDevice.getDeviceNo() + String.valueOf(measureTime));
		sphygmometerRecord.setDeviceId(sphygmometerDevice.getDeviceId());
		sphygmometerRecord.setDeviceType(deviceType);
		sphygmometerRecord.setRecordType(Integer.parseInt(userType) == 1 ? 1 : 2);
		if ("0".equals(dw)) {//mmHg
			sphygmometerRecord.setSystolicPressure(Integer.parseInt(ssy));
			sphygmometerRecord.setDiastolicPressure(Integer.parseInt(szy));
		} else {//KPa转为mmHg
			Double systolicPressure = Integer.parseInt(ssy) * 0.13328;
			Double diastolicPressure = Integer.parseInt(szy) * 0.13328;
			sphygmometerRecord.setSystolicPressure(systolicPressure.intValue());
			sphygmometerRecord.setDiastolicPressure(diastolicPressure.intValue());
		}
		Integer heartbeat = Integer.parseInt(xl);
		sphygmometerRecord.setHeartbeat(heartbeat);

		// 处理家庭式血压计异常结果
		int systolicPressure = sphygmometerRecord.getSystolicPressure();
		int diastolicPressure = sphygmometerRecord.getDiastolicPressure();
		Integer pressureResult = SphygmometerUtil.getBloodPressureResult(deviceType, systolicPressure, diastolicPressure);
		sphygmometerRecord.setPressureResult(pressureResult);
		Integer heartbeatResult = SphygmometerUtil.getHeartbeatResult(deviceType, heartbeat);
		sphygmometerRecord.setHeartbeatResult(heartbeatResult);
		sphygmometerRecord.setMeasurePlace(sphygmometerDevice.getLocation());
		//如果取的测量时间大于当前时间，则设置当前时间为测量时间
		if (measureTime > currentDate.getTime()) {
			sphygmometerRecord.setMeasureTime(new Date());
		} else {
			sphygmometerRecord.setMeasureTime(new Date(measureTime));
		}
		sphygmometerRecord.setIsRead("0");
		int num = this.sphygmometerRecordService.insertSphygmometerRecord(sphygmometerRecord);

		//处理任务相关业务
		List<AppTaskDictDTO> list = appTaskDictService.selectAppTaskDictList(new AppTaskDictDTO());
		//遍历匹配任务截止时间是否已过，修改任务状态
		for (AppTaskDictDTO appTaskDictDTO2 : list) {
			String YYYY_MM_DD = DateUtils.date2String(new Date(), DateUtils.LONG_DATE_FORMAT);
			Date startDate = DateUtils.stringtoDate(YYYY_MM_DD + " " + appTaskDictDTO2.getMatchBeginTime(), DateUtils.FORMAT_TWO);
			Date endDate = DateUtils.stringtoDate(YYYY_MM_DD + " " + appTaskDictDTO2.getMatchEndTime(), DateUtils.FORMAT_TWO);
			if (measureTime >= startDate.getTime() && measureTime <= endDate.getTime()) {
				UserTaskRecord userTaskRecord = new UserTaskRecord();
				userTaskRecord.setUserId(sphygmometerUser.getUserId());
				userTaskRecord.setTaskId(appTaskDictDTO2.getTaskId());
				userTaskRecord.setRecordId(sphygmometerRecord.getId());
				userTaskRecord.setRecordTime(new Date(measureTime));
				userTaskRecordService.insertUserTaskRecord(userTaskRecord);
			}
		}

		//我的消息插入
		AppMessageNotificationDTO appMessageNotification = new AppMessageNotificationDTO();
		appMessageNotification.setOrganizationId(sphygmometerUser.getManageOrganizationId());
		appMessageNotification.setIsDelete(0);
		appMessageNotification.setStatus(1);

		appMessageNotification.setPublishTime(currentDate);
		appMessageNotification.setCreateTime(currentDate);
		appMessageNotification.setUserId(sphygmometerUser.getUserId());
		//检测提醒
		if(pressureResult.intValue() == 3) {
			ResidentBasicInfoDTO residentBasicInfoDTO = residentBasicInfoService.selectResidentBasicInfoById(sphygmometerUser.getResidentId());
			appMessageNotification.setMsgType(PushTypeEnum.HIGH_RISK.getMgType());
			appMessageNotification.setTitle("高风险提醒");
			String content = residentBasicInfoDTO.getRealName() + " 您此次血压测量血压过高，有严重并发性可能，建议您尽快联系您的家庭医生，或在条件允许的情况下就近到医院就诊（建议直接拨打120）\n";
			appMessageNotification.setNoticeContent(content+
					"测量时间：" + DateUtils.date2String(sphygmometerRecord.getMeasureTime(), DateUtils.LONG_HH_MM_FORMAT) + "\n" +
					"舒张压(mmHg)：" + sphygmometerRecord.getDiastolicPressure() + "\n" +
					"收缩压(mmHg)：" + sphygmometerRecord.getSystolicPressure() + "\n" +
					"心率(次/分)：" + sphygmometerRecord.getHeartbeat());
			sphygmometerUser.setNickName(residentBasicInfoDTO.getRealName());
//			pushMessage(sphygmometerDevice, sphygmometerUser, sphygmometerRecord, appMessageNotification.getNoticeId());
		}else if (pressureResult.intValue() == 2 || heartbeatResult.intValue() == 2) {
			appMessageNotification.setMsgType(2);
			appMessageNotification.setTitle("检测提醒");
			appMessageNotification.setNoticeContent("您今日检测数据有一条疑似结果\n收缩压: " +
					sphygmometerRecord.getSystolicPressure() + "\n舒张压: " +
					sphygmometerRecord.getDiastolicPressure() + "\n心率: " +
					sphygmometerRecord.getHeartbeat() + "\n" +
					SphygmometerUtil.getBloodPressureDesc(deviceType, pressureResult) + ", " +
					SphygmometerUtil.getHeartbeatDesc(deviceType, heartbeatResult) + "\n" +
					SphygmometerUtil.getPromptMessage(deviceType, pressureResult, heartbeatResult));
		} else{
			appMessageNotification.setTitle("检测结果提示");
			appMessageNotification.setMsgType(2);
			appMessageNotification.setNoticeContent("血压测量完毕, 测量结果\n收缩压: " +
					sphygmometerRecord.getSystolicPressure() + "\n舒张压: " +
					sphygmometerRecord.getDiastolicPressure() + "\n心率: " +
					sphygmometerRecord.getHeartbeat() + "\n" +
					SphygmometerUtil.getBloodPressureDesc(deviceType, pressureResult) + ", " +
					SphygmometerUtil.getHeartbeatDesc(deviceType, heartbeatResult) + "\n" +
					SphygmometerUtil.getPromptMessage(deviceType, pressureResult, heartbeatResult));
		}
		appMessageNotificationService.insertAppMessageNotification(appMessageNotification);
		log.info("-------------pressureResult----------------"+pressureResult);
		if(pressureResult.intValue() == 3) {
			log.info("推送消息："+appMessageNotification.getNoticeId());
			pushMessage(sphygmometerDevice, sphygmometerUser, sphygmometerRecord, appMessageNotification.getNoticeId());
		}
		JSONObject jsonParams = new JSONObject();
		jsonParams.put("msgType", 0);
		boolean result = GeTuiPushUtils.pushPayloadToSingle(sphygmometerUser.getGeTuiClientId(), jsonParams.toJSONString());
		log.info("----------TransmissionPushResult---------:"+result);
		return Results.opResult(num);
	}


	/**
	 * 推送消息至app 和家属微信
	 * @param sphygmometerDevice
	 * @param sphygmometerUser
	 * @param sphygmometerRecord
	 */
	private void pushMessage(SphygmometerDevice sphygmometerDevice,SphygmometerUser sphygmometerUser,SphygmometerRecordDTO sphygmometerRecord,String noticeId){
		//推送血压异常信息
		Integer pressureResult = sphygmometerRecord.getPressureResult();
		Integer systolicPressure = sphygmometerRecord.getSystolicPressure();
		Integer diastolicPressure = sphygmometerRecord.getDiastolicPressure();
		if(sphygmometerDevice.getDeviceType().equals(2) && pressureResult.equals(3)){
			String title = "高风险提醒";
			//推送消息至APP
			String content = String.format(PushConstants.HIGH_RISK_REMIND_APP,sphygmometerUser.getNickName());
//			String param = "{'msgType':'"+ PushTypeEnum.HIGH_RISK.getMgType() +"','noticeId':'"+noticeId+"'}";
			JSONObject paramJson = new JSONObject();
			paramJson.put("msgType",PushTypeEnum.HIGH_RISK.getMgType());
			paramJson.put("noticeId",noticeId);
			boolean result = GeTuiPushUtils.pushMsgToSingle(sphygmometerUser.getGeTuiClientId(),title,content,paramJson.toJSONString());
			log.info("----------pushResult---------:"+result);
			//推送消息至微信
			try{
				//推送给自己
				String mTime = DateUtils.date2String(sphygmometerRecord.getMeasureTime(),DateUtils.LONG_HH_MM_FORMAT);
				if(StringUtils.isNotBlank(sphygmometerUser.getWxOpenId())){
					String first = String.format(PushConstants.HIGH_RISK_REMIND_WECHAT_FIRST,sphygmometerUser.getNickName());
					String remark = PushConstants.HIGH_RISK_REMIND_WECHAT_REMARK;
					pushWechatMsg(sphygmometerUser.getWxOpenId(),systolicPressure,diastolicPressure,mTime,sphygmometerRecord,first,remark);
				}
				//推送至亲属
				List<SphygmometerUserRelative> list = sphygmometerUserRelativeService.list(Wrappers.<SphygmometerUserRelative>lambdaQuery().
						eq(SphygmometerUserRelative::getUserId,sphygmometerUser.getUserId()));
				if(!CollectionUtils.isEmpty(list)){
					for(SphygmometerUserRelative vo:list){
						String first = String.format(PushConstants.HIGH_RISK_REMIND_WECHAT_FIRST_RELATIVE,sphygmometerUser.getNickName());
						String remark = PushConstants.HIGH_RISK_REMIND_WECHAT_REMARK;
						pushWechatMsg(vo.getWxOpenId(),systolicPressure,diastolicPressure,mTime,sphygmometerRecord,first,remark);
					}
				}
			}catch (Exception ex){
				ex.printStackTrace();
			}
		}
	}

	private void pushWechatMsg(String openId,Integer systolicPressure,Integer diastolicPressure,
							   String mTime,SphygmometerRecord sphygmometerRecord,String first,String remark){
		try{
			log.info("---------templateId---------:"+PushConstants.HIGH_RISK_REMIND_WX_TEMP_ID);
			WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder().
					toUser(openId)
					.templateId(PushConstants.HIGH_RISK_REMIND_WX_TEMP_ID)
					.build();
			templateMessage.addData(new WxMpTemplateData("first", first));
			templateMessage.addData(new WxMpTemplateData("keyword1",systolicPressure+"mmHg"));
			templateMessage.addData(new WxMpTemplateData("keyword2",diastolicPressure+"mmHg"));
			templateMessage.addData(new WxMpTemplateData("keyword3",sphygmometerRecord.getHeartbeat()+"mmHg"));
			templateMessage.addData(new WxMpTemplateData("keyword4","偏高"));
			templateMessage.addData(new WxMpTemplateData("keyword5",mTime));
			templateMessage.addData(new WxMpTemplateData("remark", remark));
			String result = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
			log.info("微信返回结果"+result);
		}catch (Exception exception){
			log.error("微信返回结果（错误）"+exception.getMessage());
		}
	}

	/**
	 * 筛查检测记录
	 * @param sphygmometerRecordDTO
	 * @return
	 */
	@PostMapping(PREFIX + "/getScreenTestList")
	@Override
	public Results<PageData<SphygmometerRecordDTO>> getScreenTestList(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		startPage(sphygmometerRecordDTO.getPageNum(), sphygmometerRecordDTO.getEveryPage());
		if (StringUtils.isNotBlank(sphygmometerRecordDTO.getMeasureStartTime())) {
			sphygmometerRecordDTO.setMeasureStartTime(sphygmometerRecordDTO.getMeasureStartTime() + " 00:00:00");
		}
		if (StringUtils.isNotBlank(sphygmometerRecordDTO.getMeasureEndTime())) {
			sphygmometerRecordDTO.setMeasureEndTime(sphygmometerRecordDTO.getMeasureEndTime() + " 23:59:59");
		}
		List<SphygmometerRecordDTO> list = this.sphygmometerRecordService.getScreenTestList(sphygmometerRecordDTO);
		return Results.list(getPageData(list));
	}

	/**
	 * 获取血压记录信息表详细信息
	 */
	@GetMapping(PREFIX + "/getInfo")
	public SphygmometerRecordDTO getInfo(@RequestParam("id") Integer id) {
		SphygmometerRecordDTO sphygmometerRecordDTO = sphygmometerRecordService.selectSphygmometerRecordById(id);
		return sphygmometerRecordDTO;
	}

	@GetMapping(PREFIX + "/getLastInfo")
	public SphygmometerRecordDTO getLastInfo(@RequestParam("userId") Integer userId) {
		SphygmometerRecordDTO sphygmometerRecordDTO = sphygmometerRecordService.selectSphygmometerLastOne(userId);
		return sphygmometerRecordDTO;
	}

	@GetMapping(PREFIX + "/getLastInfoForWxApp")
	@Override
	public SphygmometerRecordDTO getLastInfoForWxApp(Integer userId) {
		SphygmometerRecordDTO sphygmometerRecordDTO = new SphygmometerRecordDTO();
		sphygmometerRecordDTO.setUserId(userId);
		List<SphygmometerRecord> sphygmometerRecordDTOS = sphygmometerRecordService.list(Wrappers.<SphygmometerRecord>lambdaQuery()
				.eq(SphygmometerRecord::getUserId,userId).orderByDesc(SphygmometerRecord::getMeasureTime));
		if (null != sphygmometerRecordDTOS && !sphygmometerRecordDTOS.isEmpty()) {
			SphygmometerRecord sphygmometerRecord = sphygmometerRecordDTOS.get(0);
			sphygmometerRecordDTO = BeanUtil.deepCopyProperties(sphygmometerRecord,SphygmometerRecordDTO.class);
			// 血压计结果提示语处理
			sphygmometerRecordDTO.setPromptMessage(SphygmometerUtil.getPromptMessage(sphygmometerRecordDTO.getDeviceType(), sphygmometerRecordDTO.getPressureResult(), sphygmometerRecordDTO.getHeartbeatResult()));
			// 血压结果的描述
			sphygmometerRecordDTO.setBloodPressureDesc(SphygmometerUtil.getBloodPressureDesc(sphygmometerRecordDTO.getDeviceType(), sphygmometerRecordDTO.getPressureResult()));
			// 心率结果的描述
			sphygmometerRecordDTO.setHeartbeatDesc(SphygmometerUtil.getHeartbeatDesc(sphygmometerRecordDTO.getDeviceType(), sphygmometerRecordDTO.getHeartbeatResult()));
		}
		// 当建议提示语为空，则设置默认建议提示语
		if (org.apache.commons.lang3.StringUtils.isBlank(sphygmometerRecordDTO.getPromptMessage())) {
			sphygmometerRecordDTO.setPromptMessage(SphygmomanometerContants.DEFAULT_PROMPT_MESSAGE);
		}
		return sphygmometerRecordDTO;
	}

	/**
	 * 获取血压记录信息统计信息
	 */
	@PostMapping(PREFIX + "/selectStatisticsInfo")
	public SphygmometerRecordDTO selectStatisticsInfo(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		SphygmometerRecordDTO sphygmometerRecordDTO2 = sphygmometerRecordService
				.selectStatisticsInfo(sphygmometerRecordDTO);
		return sphygmometerRecordDTO2;
	}

	/**
	 * 新增血压记录信息表
	 */
	@PostMapping(PREFIX + "/add")
	public Integer add(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		return sphygmometerRecordService.insertSphygmometerRecord(sphygmometerRecordDTO);
	}

	/**
	 * 修改血压记录信息表
	 */
	@PostMapping(PREFIX + "/edit")
	public Integer edit(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		return sphygmometerRecordService.updateSphygmometerRecord(sphygmometerRecordDTO);
	}

	/**
	 * 删除血压记录信息表，多个以逗号分隔
	 */
	@GetMapping(PREFIX + "/removeByIds")
	public Integer removeByIds(@RequestParam("ids") String ids) {
		return sphygmometerRecordService.deleteSphygmometerRecordByIds(ids);
	}

	/**
	 * 查询异常信息及系统推送消息
	 */
	@PostMapping(PREFIX + "/selectMessageAndRecord")
	public PageData<SphygmometerRecordDTO> selectMessageAndRecord(
			@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		startPage(sphygmometerRecordDTO.getPageNum(), sphygmometerRecordDTO.getEveryPage());
		// 获取消息记录
		List<SphygmometerRecordDTO> list = sphygmometerRecordService.selectRecord(sphygmometerRecordDTO);
		for (SphygmometerRecordDTO sr : list) {
			// 类型为2的为异常提醒数据
			if (sr.getType().equals("2")) {
				sr.setNoticeId(sr.getId() + "");
				if (sr.getDeviceType() == 1) {
					sr.setTitle("臂筒式血压计检测数值异常提醒");
				} else {
					sr.setTitle("家庭绑带式血压计检测数值异常提醒");
				}
				// 拼接内容
				sr.setNoticeContent("异常数值:" + sr.getSystolicPressure() + "/" + sr.getDiastolicPressure() + "," + "测量时间:"
						+ sr.getMeasureTimeStr() + "," + "测量机构:" + sr.getOrganizationName() + "测量地点:"
						+ sr.getMeasurePlace() + "," + "居民手机号:" + sr.getMobile());
				sr.setPublishTime(sr.getMeasureTimeStr());
			}
		}
		return  getPageData(list);
	}

	/**
	 * 修改消息信息表
	 *
	 * @param sphygmometerRecordDTO 血压记录信息表
	 * @return 结果
	 */
	@PostMapping(PREFIX + "/updateMessage")
	public Integer updateMessage(@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		if (sphygmometerRecordDTO.getType().equals("1")) {
			return sphygmometerRecordService.updateMessage(sphygmometerRecordDTO.getNoticeId());
		} else {
			sphygmometerRecordDTO.setIsRead(1 + "");
			return sphygmometerRecordService.updateSphygmometerRecord(sphygmometerRecordDTO);
		}

	}

	/**
	 * 查询异常信息及系统推送消息
	 */
	@PostMapping(PREFIX + "/selectDetectionOrganizationInfo")
	public List<SphygmometerRecordDTO> selectDetectionOrganizationInfo(
			@RequestBody SphygmometerRecordDTO sphygmometerRecordDTO) {
		// 获取该机构下一级子机构
		List<SphygmometerRecordDTO> listSrd = sphygmometerRecordDTO.getOrgList();
		int totalCounts = 0;
		if (null != listSrd && !listSrd.isEmpty()) {
			// 遍历该机构下一级子机构
			for (SphygmometerRecordDTO srd : listSrd) {
				// 匹配是否统计异常数据
				if (null != sphygmometerRecordDTO.getMeasureResult() && sphygmometerRecordDTO.getMeasureResult() == 3) {
					srd.setMeasureResult(3);
				}
				// 匹配时间筛选
				if (null != sphygmometerRecordDTO.getMeasureStartTime()
						&& sphygmometerRecordDTO.getMeasureEndTime() != null) {
					srd.setMeasureStartTime(sphygmometerRecordDTO.getMeasureStartTime());
					srd.setMeasureEndTime(sphygmometerRecordDTO.getMeasureEndTime() + " 23:59:59");
				}
				Integer mainOrgId = srd.getMainOrgId();
				String orgIds = String.valueOf(mainOrgId) + "," + srd.getOrganizationIds();
				List<Integer> orgIdList = Arrays.asList(Convert.toIntArray(orgIds));
				if (null != orgIdList && !orgIdList.isEmpty() ) {
					srd.setOrgIdList(orgIdList);
				}
				// 查询该机构检测人数
				Integer counts = sphygmometerRecordService.selectDetectionStatisticsInfo(srd);
				// 添加该机构检测人数
				srd.setCounts(counts);
				totalCounts += counts;
			}
			listSrd.get(0).setTotalCounts(totalCounts);
			if(null != sphygmometerRecordDTO.getMeasureResult() && sphygmometerRecordDTO.getMeasureResult() == 3) {
				// 按权重排序 从小到大
				Collections.sort(listSrd, new Comparator<SphygmometerRecordDTO>() {
					@Override
					public int compare(SphygmometerRecordDTO o1, SphygmometerRecordDTO o2) {
						Integer showIndex = o1.getCounts();
						Integer showIndex1 = o2.getCounts();
						return showIndex1.compareTo(showIndex);
					}

				});
			}

		}

		return listSrd;
	}

	/**
	 * 获取某天的检测人数
	 *
	 * @param registerSource 注册源
	 * @param dayStartTime   开始时间
	 * @param dayEndTime     结束时间
	 * @return
	 */
	@GetMapping(PREFIX + "/getDayTestCount")
	public Integer getDayTestCount(@RequestParam("registerSource") Integer registerSource,
								   @RequestParam("dayStartTime") Date dayStartTime, @RequestParam("dayEndTime") Date dayEndTime) {
		return sphygmometerRecordService.getDayTestCount(registerSource, dayStartTime, dayEndTime);
	}

	/**
	 * 日均检测人数
	 *
	 * @param registerSource 注册源
	 * @param dayEndTime     统计日均截止时间
	 * @return
	 */
	@GetMapping(PREFIX + "/getAvgTestCount")
	public Double getAvgTestCount(@RequestParam("registerSource") Integer registerSource,
								  @RequestParam("dayEndTime") Date dayEndTime) {
		return sphygmometerRecordService.getAvgTestCount(registerSource, dayEndTime);
	}

	@PostMapping(PREFIX + "/getAbnormalTestPeople")
	public AbnormalHistogramDTO getAbnormalTestPeople(@RequestBody HomeStatisticsParamDTO homeStatisticsParamDTO) {
		AbnormalHistogramDTO abnormalHistogramDTO = new AbnormalHistogramDTO();

		List<OrganizationIdParamDTO> orgList = homeStatisticsParamDTO.getOrgList();
		String startDateStr = homeStatisticsParamDTO.getMeasureStartTime();
		String endDateStr = homeStatisticsParamDTO.getMeasureEndTime();
		Date startDate = DateUtils.stringtoDate(startDateStr + " 00:00:00", DateUtils.FORMAT_ONE);
		Date endDate = DateUtils.stringtoDate(endDateStr + " 23:59:59", DateUtils.FORMAT_ONE);

		List<AbnormalHistogramListDTO> dataList = new ArrayList<>();
		Integer totalCount = 0;

		for (OrganizationIdParamDTO org : orgList) {
			Integer mainOrgId = org.getMainOrgId();
			String orgIds = String.valueOf(mainOrgId) + "," + org.getOrganizationIds();
			List<Integer> orgIdList = Arrays.asList(Convert.toIntArray(orgIds));
			Integer orgTotalCount = this.sphygmometerRecordService.getAbnormalTestPeople(orgIdList, startDate, endDate);
			// 统计总数
			totalCount += orgTotalCount;

			AbnormalHistogramListDTO abnormalHistogramListDTO = new AbnormalHistogramListDTO();
			abnormalHistogramListDTO.setCount(orgTotalCount);
			abnormalHistogramListDTO.setOrganizationId(mainOrgId);
			abnormalHistogramListDTO.setOrganizationName(org.getMainOrgName());
			dataList.add(abnormalHistogramListDTO);
		}
		abnormalHistogramDTO.setTotalCount(totalCount);
		abnormalHistogramDTO.setDataList(dataList);
		return abnormalHistogramDTO;
	}

	@PostMapping(PREFIX + "/getAbnormalTestReport")
	public AbnormalHistogramDTO getAbnormalTestReport(@RequestBody HomeStatisticsParamDTO homeStatisticsParamDTO) {
		AbnormalHistogramDTO abnormalHistogramDTO = new AbnormalHistogramDTO();

		List<OrganizationIdParamDTO> orgList = homeStatisticsParamDTO.getOrgList();
		String startDateStr = homeStatisticsParamDTO.getMeasureStartTime();
		String endDateStr = homeStatisticsParamDTO.getMeasureEndTime();
		Date startDate = DateUtils.stringtoDate(startDateStr + " 00:00:00", DateUtils.FORMAT_ONE);
		Date endDate = DateUtils.stringtoDate(endDateStr + " 23:59:59", DateUtils.FORMAT_ONE);

		List<AbnormalHistogramListDTO> dataList = new ArrayList<>();
		Integer totalCount = 0;
		for (OrganizationIdParamDTO org : orgList) {
			Integer mainOrgId = org.getMainOrgId();
			String orgIds = String.valueOf(mainOrgId) + "," + org.getOrganizationIds();
			List<Integer> orgIdList = Arrays.asList(Convert.toIntArray(orgIds));
			Integer orgTotalCount = this.sphygmometerRecordService.count(Wrappers.<SphygmometerRecord>query().lambda()
					.and(consumer -> consumer/*.eq(SphygmometerRecord::getPressureResult, 2).or()*/
							.eq(SphygmometerRecord::getPressureResult, 3))
					.in(SphygmometerRecord::getOrganizationId, orgIdList)
					.between(SphygmometerRecord::getMeasureTime, startDate, endDate));
			// 统计总数
			totalCount += orgTotalCount;

			AbnormalHistogramListDTO abnormalHistogramListDTO = new AbnormalHistogramListDTO();
			abnormalHistogramListDTO.setCount(orgTotalCount);
			abnormalHistogramListDTO.setOrganizationId(org.getMainOrgId());
			abnormalHistogramListDTO.setOrganizationName(org.getMainOrgName());
			dataList.add(abnormalHistogramListDTO);
		}
		abnormalHistogramDTO.setTotalCount(totalCount);
		abnormalHistogramDTO.setDataList(dataList);
		return abnormalHistogramDTO;
	}

	@PostMapping(PREFIX + "/getAbnormalPieChart")
	public AbnormalPieChartDTO getAbnormalPieChart(@RequestBody HomeStatisticsParamDTO homeStatisticsParamDTO) {
		AbnormalPieChartDTO abnormalPieChartDTO = new AbnormalPieChartDTO();

		/**
		 * 正常率：展示权限范围内所有检测报告结果为正常的正常率，计算方式：（检测结果为正常的报告数/检测总报告数）*100%；
		 *
		 * 疑似率：展示权限范围内所有检测报告结果为疑似的疑似率，计算方式：（检测结果为疑似的报告数/检测总报告数）*100%；
		 *
		 * 危重率：展示权限范围内所有检测报告结果为危重的危重率，计算方式：（检测结果为危重的报告数/检测总报告数）*100%；
		 *
		 * 异常确诊率：展示权限范围内所有检测报告结果为疑似和危重的异常确诊率，计算方式：（检测结果为疑似和危重的报告数/检测总报告数）*100%
		 */
		Integer totalCount = 0;
		Integer normalCount = 0;
		Integer suspectedCount = 0;
		Integer criticalCount = 0;
		Integer abnormalDiagnosisCount = 0;
		List<OrganizationIdParamDTO> orgList = homeStatisticsParamDTO.getOrgList();
		String startDateStr = homeStatisticsParamDTO.getMeasureStartTime();
		String endDateStr = homeStatisticsParamDTO.getMeasureEndTime();
		Date startDate = DateUtils.stringtoDate(startDateStr + " 00:00:00", DateUtils.FORMAT_ONE);
		Date endDate = DateUtils.stringtoDate(endDateStr + " 23:59:59", DateUtils.FORMAT_ONE);
		for (OrganizationIdParamDTO org : orgList) {
			Integer mainOrgId = org.getMainOrgId();
			String orgIds = String.valueOf(mainOrgId) + "," + org.getOrganizationIds();
			List<Integer> orgIdList = Arrays.asList(Convert.toIntArray(orgIds));

			totalCount += sphygmometerRecordService.getTotalTestPeopleCount(null, null, orgIdList, startDate, endDate);
			//正常率
			List<Integer> normalPressureResultList = new ArrayList<>();
			normalPressureResultList.add(1);
			//异常率
			List<Integer> suspectedPressureResultList = new ArrayList<>();
			suspectedPressureResultList.add(2);
			//高风险
			List<Integer> criticalPressureResultList = new ArrayList<>();
			criticalPressureResultList.add(3);
			//异常率和高风险
			List<Integer> abnormalDiagnosisPressureResultList = new ArrayList<>();
			abnormalDiagnosisPressureResultList.add(2);
			abnormalDiagnosisPressureResultList.add(3);

			normalCount += sphygmometerRecordService.getTotalTestPeopleCount(normalPressureResultList, abnormalDiagnosisPressureResultList, orgIdList, startDate, endDate);
			suspectedCount += sphygmometerRecordService.getTotalTestPeopleCount(suspectedPressureResultList, criticalPressureResultList, orgIdList, startDate, endDate);
			criticalCount += sphygmometerRecordService.getTotalTestPeopleCount(criticalPressureResultList, null, orgIdList, startDate, endDate);
			abnormalDiagnosisCount += sphygmometerRecordService.getTotalTestPeopleCount(abnormalDiagnosisPressureResultList, null, orgIdList, startDate, endDate);
		}
		if (totalCount == 0) {
			abnormalPieChartDTO.setNormalRate(0.0);
			abnormalPieChartDTO.setSuspectedRate(0.0);
			abnormalPieChartDTO.setCriticalRate(0.0);
			abnormalPieChartDTO.setAbnormalDiagnosisRate(0.0);
		} else {
			BigDecimal totalCountBd = new BigDecimal(String.valueOf(totalCount));
			BigDecimal normalCountBd = new BigDecimal(String.valueOf(normalCount));
			BigDecimal suspectedCountBd = new BigDecimal(String.valueOf(suspectedCount));
			BigDecimal criticalCountBd = new BigDecimal(String.valueOf(criticalCount));
			BigDecimal abnormalDiagnosisCountBd = new BigDecimal(String.valueOf(abnormalDiagnosisCount));

			Double normalRate = normalCountBd.divide(totalCountBd, 4, BigDecimal.ROUND_HALF_DOWN)
					.multiply(new BigDecimal(100)).doubleValue();
			Double suspectedRate = suspectedCountBd.divide(totalCountBd, 4, BigDecimal.ROUND_HALF_DOWN)
					.multiply(new BigDecimal(100)).doubleValue();
			Double criticalRate = criticalCountBd.divide(totalCountBd, 4, BigDecimal.ROUND_HALF_DOWN)
					.multiply(new BigDecimal(100)).doubleValue();
			Double abnormalDiagnosisRate = abnormalDiagnosisCountBd.divide(totalCountBd, 4, BigDecimal.ROUND_HALF_DOWN)
					.multiply(new BigDecimal(100)).doubleValue();
			// 求百分比
			abnormalPieChartDTO.setNormalRate(normalRate);
			abnormalPieChartDTO.setSuspectedRate(suspectedRate);
			abnormalPieChartDTO.setCriticalRate(criticalRate);
			abnormalPieChartDTO.setAbnormalDiagnosisRate(abnormalDiagnosisRate);
		}
		return abnormalPieChartDTO;
	}

	@Override
	public SphygmometerRecordMonthDTO getMonthSphygmometerRecord(Integer userId, String yearAndMonth) {
		return sphygmometerRecordService.getMonthSphygmometerRecord(userId, yearAndMonth);
	}
}
