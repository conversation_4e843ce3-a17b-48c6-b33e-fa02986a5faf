package com.bojun.health.promotion.common.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.bojun.health.promotion.common.PageBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/6/1 16:24
 */
@Data
public class TopicInfoParamDTO extends PageBaseDTO {

    @ApiModelProperty(value = "栏目id", example = "")
    private Integer topicId;

    @ApiModelProperty(value = "栏目id集合", example = "")
    private List<Integer> topicIdList;

    @ApiModelProperty(value = "文章名称", example = "")
    private String title;

    @ApiModelProperty(value = "发布起期", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "发布止期", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "文章状态", example = "")
    private Integer status;

    @ApiModelProperty(value = "父级话题id", example = "")
    private Integer parentTopicId;

    @ApiModelProperty(value = "系统id", example = "")
    private String systemId;

    @ApiModelProperty("是否app请求，0否1是")
    private Integer isApp;
}
