/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model： 菜单管理
 * Description：保存按钮信息
 * Author：赖水秀
 * created： 2020年5月12日
 */
@ApiModel(value = "保存按钮信息", description = "保存按钮信息传入的数据")
public class SaveButtonParamVO implements Serializable {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = -2861615641502296513L;

	@NotEmpty(message = "菜单ID不能为空")
	
	@ApiModelProperty(value="菜单ID")
	private String menuId;
	
	
	@ApiModelProperty(value="系统ID")
	private String systemId;
	
	/**
	 * 菜单列表
	 */
	List<ButtonInfoVO> children;

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}

	public List<ButtonInfoVO> getChildren() {
		return children;
	}

	public void setChildren(List<ButtonInfoVO> children) {
		this.children = children;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	
	
	
}
