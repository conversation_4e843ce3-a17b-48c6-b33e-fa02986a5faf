package com.bojun.organization.dto;

import com.bojun.organization.entity.OrganizationInfo;


import java.util.List;

/**
 * Model： 机构管理
 * Description：机构信息DTO
 * Author：赖水秀
 * created： 2020年4月29日
 */
public class OrganizationInfoDTO extends OrganizationInfo implements Cloneable {

    /**
	 * 
	 */
	private static final long serialVersionUID = -2673798751450079704L;
	
	private String parentName; 	//父机构名称
	
	private String organizationTypeName; //机构类型名称

	private String provinceName; // 省份名称

	private String cityName; 	// 省份名称

    private String countyName; // 省份名称

	private Integer everyPage;    //每页显示数

	private Integer pageNum;   //页面数

	private Integer totalCount;  //总记录数

	private List<String> img;
	private String roleId;

	private Integer deptId;
	private String deptName;
	private String logoImgUrl;
	private String logoImg;

	private String type;

	private String token;

	/**
	 * 图片信息
	 */
	private List<OrganizationImgDTO> imgList;


    private Integer isInternetHospital; //是否开通网络问诊0 否；1 是

    private String organizationImage; //机构图片

    private String keyWord;
	
	private String   realName ;//姓名
	
	private Integer   gender ;//性别
	
	private String     nationName;//民族
	
	private String     isNumber;//身份证号
	
	private String     postName;//职务
	
	private String     postId;//职务id
	
	private String     postLevel;//等级
	
	private String     age;//年龄
	
	private String     entryDateString;//入职时间
	
	private String     educationName;//学历
	
	private String     graduateSchool;//毕业学校
    
	private String     majorName;//专业
	
	private String     idNumber;//身份证号
	
	private String     major;//所学专业
	
	private String     organizations;//所学专业


	private Integer dataPermissionNode;//数据权限节点
	
	private Integer userType;//1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维
	
	private List<Integer> organizationIds;
	
	
	public List<Integer> getOrganizationIds() {
		return organizationIds;
	}

	public void setOrganizationIds(List<Integer> organizationIds) {
		this.organizationIds = organizationIds;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getDataPermissionNode() {
		return dataPermissionNode;
	}

	public void setDataPermissionNode(Integer dataPermissionNode) {
		this.dataPermissionNode = dataPermissionNode;
	}
	private Integer showIndex;  //排序
	
	
	
	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}
	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getOrganizations() {
		return organizations;
	}

	public void setOrganizations(String organizations) {
		this.organizations = organizations;
	}

	public String getMajorName() {
		return majorName;
	}

	public void setMajorName(String majorName) {
		this.majorName = majorName;
	}

	public String getIdNumber() {
		return idNumber;
	}

	public void setIdNumber(String idNumber) {
		this.idNumber = idNumber;
	}

	public String getMajor() {
		return major;
	}

	public void setMajor(String major) {
		this.major = major;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getGender() {
		return gender;
	}

	public void setGender(Integer gender) {
		this.gender = gender;
	}

	public String getNationName() {
		return nationName;
	}

	public void setNationName(String nationName) {
		this.nationName = nationName;
	}

	public String getIsNumber() {
		return isNumber;
	}

	public void setIsNumber(String isNumber) {
		this.isNumber = isNumber;
	}

	public String getPostName() {
		return postName;
	}

	public void setPostName(String postName) {
		this.postName = postName;
	}

	public String getPostId() {
		return postId;
	}

	public void setPostId(String postId) {
		this.postId = postId;
	}

	public String getPostLevel() {
		return postLevel;
	}

	public void setPostLevel(String postLevel) {
		this.postLevel = postLevel;
	}

	public String getAge() {
		return age;
	}

	public void setAge(String age) {
		this.age = age;
	}

	public String getEntryDateString() {
		return entryDateString;
	}

	public void setEntryDateString(String entryDateString) {
		this.entryDateString = entryDateString;
	}

	public String getEducationName() {
		return educationName;
	}

	public void setEducationName(String educationName) {
		this.educationName = educationName;
	}

	public String getGraduateSchool() {
		return graduateSchool;
	}

	public void setGraduateSchool(String graduateSchool) {
		this.graduateSchool = graduateSchool;
	}


	public String getLogoImgUrl() {
		return logoImgUrl;
	}

	public void setLogoImgUrl(String logoImgUrl) {
		this.logoImgUrl = logoImgUrl;
	}

	public String getLogoImg() {
		return logoImg;
	}

	public void setLogoImg(String logoImg) {
		this.logoImg = logoImg;
	}

	public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public String getOrganizationImage() {
        return organizationImage;
    }

    public void setOrganizationImage(String organizationImage) {
        this.organizationImage = organizationImage;
    }

    public Integer getIsInternetHospital() {
		return isInternetHospital;
	}

	public void setIsInternetHospital(Integer isInternetHospital) {
		this.isInternetHospital = isInternetHospital;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getRoleId() {
		return roleId;
	}

	/**
	 * 子级机构
	 */
	private List<OrganizationInfoDTO> children;

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public List<OrganizationInfoDTO> getChildren() {
		return children;
	}

	public void setChildren(List<OrganizationInfoDTO> children) {
		this.children = children;
	}

	public Integer getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(Integer everyPage) {
		this.everyPage = everyPage;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public List<String> getImg() {
		return img;
	}

	public void setImg(List<String> img) {
		this.img = img;
	}

	public List<OrganizationImgDTO> getImgList() {
		return imgList;
	}

	public void setImgList(List<OrganizationImgDTO> imgList) {
		this.imgList = imgList;
	}

	public String getOrganizationTypeName() {
		return organizationTypeName;
	}

	public void setOrganizationTypeName(String organizationTypeName) {
		this.organizationTypeName = organizationTypeName;
	}
	
	@Override  
    public OrganizationInfoDTO clone() {  
		OrganizationInfoDTO stu = null;  
        try{  
            stu = (OrganizationInfoDTO)super.clone();  
      }catch(CloneNotSupportedException e) {  
            e.printStackTrace();  
        }  
        return stu;  
    } 
}
