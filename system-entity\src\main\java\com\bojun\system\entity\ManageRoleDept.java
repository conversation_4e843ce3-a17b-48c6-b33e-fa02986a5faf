package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：角色科室关联
 * Description：角色科室关联实体类
 * Author:刘俊
 * created：2020年4月30日
 */
public class ManageRoleDept implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;

    private Integer rdId; // 角色科室关联id

    private Integer roId; // 角色机构ID

    private Integer deptId; // 部门（科室）id

    private Integer wardId; // 区域（病区）id

    //是否有权限(全选半选之分)0无(半选)1有(全选)
    private Integer hasAuth;

    private String deptNumber; //科室（部门）编号（his系统dept_code）

    private String wardName = ""; //区域（病区）名称
    private String deptName = "";//部门（科室）名称


    public String getDeptNumber() {
        return deptNumber;
    }

    public void setDeptNumber(String deptNumber) {
        this.deptNumber = deptNumber;
    }

    public String getWardName() {
        return wardName;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getRdId() {
        return rdId;
    }

    public void setRdId(Integer rdId) {
        this.rdId = rdId;
    }


    public Integer getRoId() {
        return roId;
    }

    public void setRoId(Integer roId) {
        this.roId = roId;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }

	public Integer getHasAuth() {
		return hasAuth;
	}

	public void setHasAuth(Integer hasAuth) {
		this.hasAuth = hasAuth;
	}
}
