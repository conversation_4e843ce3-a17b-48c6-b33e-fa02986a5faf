<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="testTables" targetRuntime="MyBatis3">
        <commentGenerator>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--数据库连接的信息：驱动类、连接地址、用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="****************************************************************" userId="sphygmometer"
                        password="sphygmometer@112233">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- targetProject:生成实体类的位置 -->
        <javaModelGenerator targetPackage="com/bojun/health/promotion/entity/dto"
                            targetProject="D:/健康宣教/实体/src/main/java">
            <!-- enableSubPackages:是否让schema作为包的后缀 -->
            <property name="enableSubPackages" value="false"/>
            <!-- 从数据库返回的值被清理前后的空格 -->
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- mapper.xml位置-->
        <sqlMapGenerator targetPackage="mapper"
                         targetProject="D:/健康宣教/服务/health-promotion-service/src/main/resources/mybatis">
            <property name="enableSubPackages" value="false"/>
        </sqlMapGenerator>

        <!-- mapper 接口位置-->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com/bojun/health/promotion/mapper"
                             targetProject="D:/健康宣教/服务/health-promotion-service/src/main/java">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <!-- 指定数据库表，要生成哪些表，就写哪些表，要和数据库中对应，不能写错！ -->
        <table tableName="t_user_news_favorite"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false"
               enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

    </context>
</generatorConfiguration>
