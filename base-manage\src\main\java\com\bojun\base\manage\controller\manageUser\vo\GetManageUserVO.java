/**
 * 
 */
package com.bojun.base.manage.controller.manageUser.vo;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.bojun.vo.BaseQueryInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：查询用户 
 * Description：查询用户信息 
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "查询用户信息", description = "查询用户传入参数")
public class GetManageUserVO  extends BaseQueryInfoVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;


	@ApiModelProperty(value = "用户Id", required = true, example = "1")
	private Integer userId;

	@ApiModelProperty(value = "登录账号", required = true, example = "1")
	private String accountNo;

//	@NotNull(message = "机构id不能为空")
	@ApiModelProperty(value = "机构id", required = true, example = "1")
	private Integer organizationId;

//	@NotEmpty(message = "系统名称不能为空")
	@ApiModelProperty(value = "系统名称", required = true, example = "测试")
	private String organizationName;

//	@NotEmpty(message = "角色ID不能为空")
	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;

//	@NotEmpty(message = "工号不能为空")
	@ApiModelProperty(value = "工号", required = true, example = "1")
	private String workNumber;

//	@NotNull(message = "部门id不能为空")
	@ApiModelProperty(value = "部门id", required = true, example = "1")
	private Integer deptId;

//	@NotEmpty(message = "姓名不能为空")
	@ApiModelProperty(value = "姓名", required = true, example = "1")
	private String realName; //


	@NotNull(message = "用户类型不能为空")
	@ApiModelProperty(value = "用户类型 1：医疗机构人员 2：养老机构人员 3：监管人员 4：其他", required = true, example = "1")
	private Integer userType; //



//	@NotNull(message = "状态类型不能为空")
	@ApiModelProperty(value = "状态0:停用 1:启用", required = true, example = "1")
	private Integer status;


//	@NotNull(message = "区域（病区）id不能为空")
	@ApiModelProperty(value = "区域（病区）id", required = true, example = "1")
	private Integer wardId;

//	@NotEmpty(message = "手机号不能为空")
	@ApiModelProperty(value = "手机号", required = true, example = "1")
	private String mobile;
	
	@NotNull(message = "权限类型不能为空")
	@ApiModelProperty(value = "权限类型（0：超级管理员，1：普通管理员）", required = true, example = "1")
	private Integer authType; 
	
	@ApiModelProperty(value = "特殊启用", required = true, example = "1")
	private String isEnabledRole;

	
	@ApiModelProperty(value = "开始查询最后登录时间", required = true, example = "1")
	private String startLastestLoginTime;
	
	@ApiModelProperty(value = "结束查询最后登录时间", required = true, example = "1")
	private String endLastestLoginTime;
	
	@NotNull(message = "未传入账号数据权限类型")
	@ApiModelProperty(value = "数据权限", required = true, example = "1")
	private Integer dataPermissions;
	
	@ApiModelProperty(value = "职称id", required = true, example = "1")
	private Integer jobTitleId;
	
	public Integer getJobTitleId() {
		return jobTitleId;
	}

	public void setJobTitleId(Integer jobTitleId) {
		this.jobTitleId = jobTitleId;
	}

	public Integer getDataPermissions() {
		return dataPermissions;
	}

	public void setDataPermissions(Integer dataPermissions) {
		this.dataPermissions = dataPermissions;
	}

	public String getIsEnabledRole() {
		return isEnabledRole;
	}

	public void setIsEnabledRole(String isEnabledRole) {
		this.isEnabledRole = isEnabledRole;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getWorkNumber() {
		return workNumber;
	}

	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}

	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getWardId() {
		return wardId;
	}

	public void setWardId(Integer wardId) {
		this.wardId = wardId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getStartLastestLoginTime() {
		return startLastestLoginTime;
	}

	public void setStartLastestLoginTime(String startLastestLoginTime) {
		this.startLastestLoginTime = startLastestLoginTime;
	}

	public String getEndLastestLoginTime() {
		return endLastestLoginTime;
	}

	public void setEndLastestLoginTime(String endLastestLoginTime) {
		this.endLastestLoginTime = endLastestLoginTime;
	}

	public Integer getAuthType() {
		return authType;
	}

	public void setAuthType(Integer authType) {
		this.authType = authType;
	}

	

}
