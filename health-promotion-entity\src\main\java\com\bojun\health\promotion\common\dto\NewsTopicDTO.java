package com.bojun.health.promotion.common.dto;

import com.bojun.health.promotion.common.entity.NewsTopic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新闻话题关系表对象 t_news_topic
 * 
 * <AUTHOR>
 * @date 2021-06-01 16:08:00
 */
@ApiModel(value = "TNewsTopicDTO对象")
@Data
public class NewsTopicDTO extends NewsTopic
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
}
