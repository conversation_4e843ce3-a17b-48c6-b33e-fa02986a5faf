package com.bojun.utils;

import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
 
 
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {
 
	// 格式：年－月－日 小时：分钟：秒
    public static final String FORMAT_ONE = "yyyy-MM-dd HH:mm:ss";
 
    // 格式：年－月－日 小时：分钟
    public static final String FORMAT_TWO = "yyyy-MM-dd HH:mm";
 
    // 格式：年月日 小时分钟秒
    public static final String FORMAT_THREE = "yyyyMMdd-HHmmss";
    
    // 格式：年月日
    public static final String FORMAT_FOUR = "yyyyMMdd";
    
    // 格式：年－月－日
    public static final String LONG_DATE_FORMAT = "yyyy-MM-dd";
 
    // 格式：月－日
    public static final String SHORT_DATE_FORMAT = "MM-dd";
 
    // 格式：小时：分钟：秒
    public static final String LONG_TIME_FORMAT = "HH:mm:ss";

    // 格式：小时：分钟：秒
    public static final String LONG_HH_FORMAT = "HH";

    public static final String LONG_HH_MM_FORMAT = "HH:mm";


    //格式：年-月
    public static final String MONTG_DATE_FORMAT = "yyyy-MM";
 
    // 年的加减
    public static final int SUB_YEAR = Calendar.YEAR;
 
    // 月加减
    public static final int SUB_MONTH = Calendar.MONTH;
 
    // 天的加减
    public static final int SUB_DAY = Calendar.DATE;
 
    // 小时的加减
    public static final int SUB_HOUR = Calendar.HOUR;
 
    // 分钟的加减
    public static final int SUB_MINUTE = Calendar.MINUTE;
 
    // 秒的加减
    public static final int SUB_SECOND = Calendar.SECOND;
 
    static final String dayNames[] = { "星期日", "星期一", "星期二", "星期三", "星期四",
            "星期五", "星期六" };
 
    public DateUtils() {
    }
 
    /**
     * 把符合日期格式的字符串转换为日期类型
     */
    public static Date stringtoDate(String dateStr, String format) {
        Date d = null;
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            formater.setLenient(false);
            d = formater.parse(dateStr);
        } catch (Exception e) {
            // log.error(e);
            d = null;
        }
        return d;
    }
    
    /**
     * 把符合日期格式的字符串转换为日期类型
     */
    public static Date stringtoDate(String dateStr, String format,
            ParsePosition pos) {
        Date d = null;
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            formater.setLenient(false);
            d = formater.parse(dateStr, pos);
        } catch (Exception e) {
            d = null;
        }
        return d;
    }
 
    /**
     * 把日期转换为字符串
     */
    public static String dateToString(Date date, String format) {
        String result = "";
        SimpleDateFormat formater = new SimpleDateFormat(format);
        try {
            result = formater.format(date);
        } catch (Exception e) {
            // log.error(e);
        }
        return result;
    }
 
    /**
     * 获取当前时间的指定格式
     */
    public static String getCurrDate(String format) {
        return dateToString(new Date(), format);
    }
 
    /**
     * 
     * @Title:        dateSub
     * @Date          2014-1-9 上午10:44:02
     * @Description:  得到指定日期前(后)的日期
     * @param:        @param dateKind  例：Calendar.DAY_OF_MONTH
     * @param:        @param dateStr  指定日期
     * @param:        @param amount   增加(减去)的时间量
     * @param:        @return   
     * @return:       String   
     * @throws
     * <AUTHOR>
     */
    public static String dateSub(int dateKind, String dateStr, int amount) {
        Date date = stringtoDate(dateStr, MONTG_DATE_FORMAT);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(dateKind, amount);
        return dateToString(calendar.getTime(), FORMAT_ONE);
    }
    
    
    /**
	 * 在原日期加一段时间间隔
	 * @param date
	 * 		原日期
	 * @param field
	 * 		"yyyy":年
	 * 		"MM":月
	 * 		"dd":日
	 * @param amount
	 * 		间隔长度
	 * @return
	 */
	public static Date dateAdd(Date date, String field, int amount) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		
		if ("yyyy".equals(field)) {
			calendar.add(Calendar.YEAR, amount);
		} else if ("MM".equals(field)) {
			calendar.add(Calendar.MONTH, amount);
		} else if ("dd".equals(field)) {
			calendar.add(Calendar.DATE, amount);
		}
		return calendar.getTime();
	}
 
    /**
     * 昨日日期
     * @return
     */
    public static String yearthDate(String dateStr){
    	Date date = stringtoDate(dateStr, LONG_DATE_FORMAT);//取时间 
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date); 
        calendar.add(calendar.DATE,-1);//把日期往后增加一天.整数往后推,负数往前移动 
        //date=calendar.getTime();   //这个时间就是日期往后推一天的结果 
        return dateToString(calendar.getTime(), LONG_DATE_FORMAT);
    }


    /**
     * 昨日日期
     * @return
     */
    public static String dateCount(Date date,Integer day){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendar.DATE,day);//把日期往后增加一天.整数往后推,负数往前移动
        //date=calendar.getTime();   //这个时间就是日期往后推一天的结果
        return dateToString(calendar.getTime(), LONG_DATE_FORMAT);
    }
    
    /**
     * 两个日期相减
     * @return 相减得到的秒数
     */
    public static long timeSub(String firstTime, String secTime) {
        long first = stringtoDate(firstTime, FORMAT_ONE).getTime();
        long second = stringtoDate(secTime, FORMAT_ONE).getTime();
        return (second - first) / 1000;
    }
    /**
     * 两个日期相减
     * 参数地DATE
     * second 两个日期相差的秒
     * @return 相减得到的秒数 
     * 后面时间减去前面时间  再减去 相差秒数    如果大于0 返回 FASLE
     */
    public static boolean timeSub(Date firstTime, Date secTime,long  secs) {
        long first = firstTime.getTime();
        long second = secTime.getTime();
        // 判断两个时间 是否间隔那么长 secs。
        return (second - first - secs) > 0 ? false:true;
    }
    /**
     * 两个日期相减
     * 参数地DATE
     * @return 相减得到的秒数 
     * 后面时间减去前面时间  如果大于0 返回 false
     */
    public static boolean timeSub(Date firstTime, Date secTime) {
        long first = firstTime.getTime();
        long second = secTime.getTime();
        return (second - first)>0?false:true;
    }
    /**
     * 获得某月的天数
     */
    public static int getDaysOfMonth(String year, String month) {
        int days = 0;
        if (month.equals("1") || month.equals("3") || month.equals("5")
                || month.equals("7") || month.equals("8") || month.equals("10")
                || month.equals("12")) {
            days = 31;
        } else if (month.equals("4") || month.equals("6") || month.equals("9")
                || month.equals("11")) {
            days = 30;
        } else {
            if ((Integer.parseInt(year) % 4 == 0 && Integer.parseInt(year) % 100 != 0)
                    || Integer.parseInt(year) % 400 == 0) {
                days = 29;
            } else {
                days = 28;
            }
        }
 
        return days;
    }
 
    /**
     * 获取某年某月的天数
     */
    public static int getDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }
 
    /**
     * 获得当前日期
     */
    public static int getToday() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DATE);
    }
 
    /**
     * 获得当前月份
     */
    public static int getToMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 1;
    }
 
    /**
     * 获得当前年份
     */
    public static int getToYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }
 
    /**
     * 返回日期的天
     */
    public static int getDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DATE);
    }
    
    /**
     * 返回日期的年
     */
    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }
 
    /**
     * 返回日期的月份，1-12
     */
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }
 
    /**
     * 计算两个日期相差的天数，如果date2 > date1 返回正数，否则返回负数
     */
    public static long dayDiff(Date date1, Date date2) {
        return (date2.getTime() - date1.getTime()) / 86400000;
    }
 
    /**
     * 比较两个日期的年差
     */
    public static int yearDiff(String before, String after) {
        Date beforeDay = stringtoDate(before, LONG_DATE_FORMAT);
        Date afterDay = stringtoDate(after, LONG_DATE_FORMAT);
        return getYear(afterDay) - getYear(beforeDay);
    }
 
    /**
     * 比较指定日期与当前日期的差
     */
    public static int yearDiffCurr(String after) {
        Date beforeDay = new Date();
        Date afterDay = stringtoDate(after, LONG_DATE_FORMAT);
        return getYear(beforeDay) - getYear(afterDay);
    }
 
    /**
     * 获取每月的第一周
     */
    public static int getFirstWeekdayOfMonth(int year, int month) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.SATURDAY); // 星期天为第一天
        c.set(year, month - 1, 1);
        return c.get(Calendar.DAY_OF_WEEK);
    }
    
    /**
     * 获取每月的最后一周
     */
    public static int getLastWeekdayOfMonth(int year, int month) {
        Calendar c = Calendar.getInstance();
        c.setFirstDayOfWeek(Calendar.SATURDAY); // 星期天为第一天
        c.set(year, month - 1, getDaysOfMonth(year, month));
        return c.get(Calendar.DAY_OF_WEEK);
    }
 
    /**
     * 获得当前日期字符串，格式"yyyy-MM-dd HH:mm:ss"
     * 
     * @return
     */
    public static String getNow() {
        Calendar today = Calendar.getInstance();
        return dateToString(today.getTime(), FORMAT_ONE);
    }
 
   
 
    /**
     * 判断日期是否有效,包括闰年的情况
     * 
     * @param date
     *          YYYY-mm-dd
     * @return
     */
    public static boolean isDate(String date) {
        StringBuffer reg = new StringBuffer(
                "^((\\d{2}(([02468][048])|([13579][26]))-?((((0?");
        reg.append("[13578])|(1[02]))-?((0?[1-9])|([1-2][0-9])|(3[01])))");
        reg.append("|(((0?[469])|(11))-?((0?[1-9])|([1-2][0-9])|(30)))|");
        reg.append("(0?2-?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][12");
        reg.append("35679])|([13579][01345789]))-?((((0?[13578])|(1[02]))");
        reg.append("-?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))");
        reg.append("-?((0?[1-9])|([1-2][0-9])|(30)))|(0?2-?((0?[");
        reg.append("1-9])|(1[0-9])|(2[0-8]))))))");
        Pattern p = Pattern.compile(reg.toString());
        return p.matcher(date).matches();
    }
    
    
    /*****
     * 时间 增加、减少 n个小时以后时间
     * @param date
     *          YYYY-mm-dd HH:mm:ss
     * @param num>0  小时         
     * @param type  增加和减少标志  
     * **/
    public static Date adjustDateByHour(Date d ,Integer num, int  type) {
    	Calendar Cal= Calendar.getInstance();
   	 	DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
	    Cal.setTime(d);  
    	if(type==0){
    	    Cal.add(Calendar.MINUTE,-num);
    	   // System.out.println("date:"+df.format(Cal.getTime()));
    		
    	}else
    	{
    		Cal.add(Calendar.MINUTE,num);
    	    //System.out.println("date:"+df.format(Cal.getTime()));
    	}
    	return Cal.getTime();
    }
    /*****
     * 时间 增加、减少 n个分钟以后时间
     * @param date
     *          YYYY-mm-dd HH:mm:ss
     * @param num>0  分钟       
     * @param type  增加和减少标志  
     * **/
    public static Date adjustDateByMinutes(Date d ,Integer num, int  type) {
    	Calendar Cal= Calendar.getInstance();
   	 	DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
	    Cal.setTime(d);  
    	if(type==0){
    	    Cal.add(Calendar.MINUTE,-num);
    	  //  System.out.println("date:"+df.format(Cal.getTime()));
    		
    	}else
    	{
    		Cal.add(Calendar.MINUTE,num);
    	 //   System.out.println("date:"+df.format(Cal.getTime()));
    	}
    	return Cal.getTime();
    }
    
    /*****
     * 获取两个日期之间的周数
     * @param date
     *          YYYY-mm-dd HH:mm:ss
     * @param 
     * @param 
     * **/
    public static int getWeek(Date time) {
    	SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    	String end = df.format(new Date());
    	String start=df.format(time);
        long from=0;
        long to=0;
		try {
			from = df.parse(start).getTime();
			to= df.parse(end).getTime();
		} catch (ParseException e) {
			e.printStackTrace();
		}
		int a= new Long((to-from)/(1000*3600*24*7)).intValue();
	       return a;
        }
    /*****
     * 获取两个日期之间的周数多余的天数
     * @param date
     *          YYYY-mm-dd HH:mm:ss
     * @param 
     * @param 
     * **/
    public static int getWeekDay(Date time) {
    	SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
    	String end = df.format(new Date());
    	String start=df.format(time);
        long from=0;
        long to=0;
		try {
			from = df.parse(start).getTime();
			to= df.parse(end).getTime();
		} catch (ParseException e) {
			e.printStackTrace();
		}
		int a= new Long((to-from)/(1000*3600*24)%7).intValue();
       return a;
        }
   
    /**
     * @Description 获取某个季度的第一天
     * <AUTHOR>
     * @param [num, year]
     * @return java.util.Date
     * @date：2020-08-18日
     */
    public static Date getFirstDayOfQuarter(String num,String year) {
        Date date = null;
        Date result = null;
        date = getStaticDateOne(num,year);
        if(date!=null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) / 3 * 3);
            cal.set(Calendar.DAY_OF_MONTH, 1);
            result = cal.getTime();
        }
        return result;
    }

    /**
     * @Description 获取某个季度的最后一天
     * <AUTHOR>
     * @param [num, year]
     * @return java.util.Date
     * @date：2020-08-18日
     */
    public static Date getLastDayOfQuarter(String num,String year) {
        Date date = null;
        Date result = null;
        date = getStaticDateOne(num,year);
        if(date!=null) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.set(Calendar.MONTH, cal.get(Calendar.MONTH)/3 * 3 + 2);
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            result = cal.getTime();
        }
        return result;
    }

    /**
     * @Description 获取某个季度的String类型的第一天
     * <AUTHOR>
     * @param [num, year]
     * @return java.util.Date
     * @date：2020-08-18日
     */
    private static Date getStaticDateOne(String num,String year){
        Date date = null;
        switch (num) {
            case "1":
                date = getDateByString(year+"-01-01");
                break;
            case "2":
                date = getDateByString(year+"-04-01");
                break;
            case "3":
                date = getDateByString(year+"-07-01");
                break;
            case "4":
                date = getDateByString(year+"-10-01");
                break;
            default:
                break;
        }
        return date;
    }

    /**
     * @Description String转Date
     * <AUTHOR>
     * @param [d]
     * @return java.util.Date
     * @date：2020-08-18日
     */
    public static Date getDateByString(String d){
        LocalDate localDate = LocalDate.parse(d, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        return Date.from(instant);
    }

    /**
     * @Description 获取某个月的最后一天
     * <AUTHOR>
     * @param [year, month]
     * @return java.util.Date
     * @date：2020-08-18日
     */
    public static Date getLastDayOfMonth(int year,int month){
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        return cal.getTime();
    }

    /**
     * @Description 获取某个月的第一天
     * <AUTHOR>
     * @param [year, month]
     * @return java.util.Date
     * @date：2020-08-18日
     */
    public static Date getFisrtDayOfMonth(int year,int month){
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        return cal.getTime();
    }

    /**
     * @Description Date 转 String
     * <AUTHOR>
     * @param [d, pattern]
     * @return java.lang.String
     * @date：2020-09-26日
     */
    public static String date2String(Date d,String pattern){
        DateTimeFormatter df = DateTimeFormatter.ofPattern(pattern);
        return df.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(d.getTime()), ZoneId.of("Asia/Shanghai")));
    }

    /**
     * @Description 计算相差天数
     * <AUTHOR>
     * @param [startTime, endTime, dateType]
     * @return java.lang.Integer
     * @date：2020-09-26日
     */
    public static Integer dateTimeNums(String startTime,String endTime,String dateType){
        String fmtStr = null;
        int timeNum = 3600 * 24;
        switch(dateType){
            case "HOUR" :
                fmtStr = "yyyy-MM-dd HH";
                timeNum = 3600;
                break;
            case "DAY" :
                fmtStr = "yyyy-MM-dd";
                timeNum = 3600 * 24 ;
                break;
            case "MONTH" :
                fmtStr = "yyyy-MM";
                timeNum = 3600 * 24 * 30;
                break;
            case "YEAR" :
                fmtStr = "yyyy";
                timeNum = 3600 * 24 * 365 ;
                break;
            default :
                fmtStr = "yyyy-MM-dd";
                break;
        }

        Calendar startCal = Calendar.getInstance();
        Calendar endCal = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat(fmtStr);//此处修改日期格式
        Date startDate = null;
        Date endDate = null;
        try {
            startDate = sdf.parse(startTime);
            endDate = sdf.parse(endTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        startCal.setTime(startDate);
        endCal.setTime(endDate);
        startCal.compareTo(endCal);
        // 两个日期相差天数
        int nums = ((int)(endCal.getTime().getTime()/1000)-(int)(startCal.getTime().getTime()/1000))/timeNum;
        return nums;
    }

    /**
     * @Description 相加后日期
     * <AUTHOR>
     * @param [date, day]
     * @return java.util.Date
     * @date：2020-09-26日
     */
    public static Date addDayDate(Date date,Integer day){
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.add(Calendar.DATE, day);
        return ca.getTime();
    }

    /**
     * @Description 获取当天开始时间
     * <AUTHOR>
     * @param []
     * @return java.util.Date
     * @date：2020-09-28日
     */
    public static Date getStartTimeOfDay(){
        Calendar time = Calendar.getInstance();
        time.set(Calendar.HOUR_OF_DAY, 0);
        time.set(Calendar.MINUTE, 0);
        time.set(Calendar.SECOND, 0);
        return time.getTime();
    }
    /**
     * @Description 获取当天结束时间
     * <AUTHOR>
     * @param []
     * @return java.util.Date
     * @date：2020-09-28日
     */
    public static Date getEndTimeOfDay(){
        Calendar time = Calendar.getInstance();
        time.set(Calendar.HOUR_OF_DAY, 23);
        time.set(Calendar.MINUTE, 59);
        time.set(Calendar.SECOND, 59);
        return time.getTime();
    }
    /**
     * @Description d1和d2比较 -1 小于  0 等于 1 大于
     * <AUTHOR>
     * @param [d1, d2]
     * @return boolean
     * @date：2020-11-02日
     */
    public static int getTimeDiff(Date d1,Date d2){
        if(d1.getTime()-d2.getTime()>0){
            return 1;
        }else if(d1.getTime()-d2.getTime()==0){
            return 0;
        }else{
            return -1;
        }
    }

    /**
     * @Description 判断currentTime 是否在startDate 和 endDate 之间
     * <AUTHOR>
     * @param [d1, d2, d3]
     * @return boolean
     * @date：2020-11-03日
     */
    public static boolean isTimePeroid(Date currentTime,Date startDate,Date endDate){
        if(currentTime.getTime()>=startDate.getTime() && currentTime.getTime()<=endDate.getTime()){
            return true;
        }
        return false;
    }


    /**
     * 获取日期是星期几<br>
     *
     * @param date
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date date) {
        String[] weekDays = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }


    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 获取某天的开始时间，例：2020/11/12 00:00:00
     * 严峡华
     * @param day 天数，0是当天，可以是负数
     * @return
     */
    public static Date getDayStartTime(Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取某天的结束时间，例：2020/11/12 23:59:59
     * 严峡华
     * @param day 天数，0是当天，可以是负数
     * @return
     */
    public static Date getDayEndTime(Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取Date类型那天的开始时间，例：2020/11/12 00:00:00
     * 严峡华
     * @param date 时间
     * @return
     */
    public static Date getDayStartTimeByDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当年的开始时间戳
     * 严峡华
     * @param date 当年时间
     * @return
     */
    public static Date getYearStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, 0);
        calendar.add(Calendar.DATE, 0);
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当年的最后时间戳
     * 严峡华
     * @param date 当年时间
     * @return
     */
    public static Date getYearEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();// 获取当前日期
        calendar.setTime(date);
        int year = calendar.get(Calendar.YEAR);
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime();
    }

    /**
     * 获取Date类型那天的结束时间，例：2020/11/12 23:59:59
     * 严峡华
     * @param date 时间
     * @return
     */
    public static Date getDayEndTimeByDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获得本周一0点时间
     * @return
     */
    public static Date getTimesWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return cal.getTime();
    }

    /**
     * @Description 获取时间段内的每一天
     * <AUTHOR>
     * @param begintDate
     * @param endDate
     * @return
     * List<String>
     * 2020年12月14日
     */
	public static List<String> findDaysStr(Date begintDate, Date endDate) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//		Date dBegin = null;
//		Date dEnd = null;
//		try {
//			dBegin = sdf.parse(begintTime);
//			dEnd = sdf.parse(endTime);
//		} catch (ParseException e) {
//			e.printStackTrace();
//		}
		List<String> daysStrList = new ArrayList<String>();
		daysStrList.add(sdf.format(begintDate));
		Calendar calBegin = Calendar.getInstance();
		calBegin.setTime(begintDate);
		Calendar calEnd = Calendar.getInstance();
		calEnd.setTime(endDate);
		while (endDate.after(calBegin.getTime())) {
			calBegin.add(Calendar.DAY_OF_MONTH, 1);
			String dayStr = sdf.format(calBegin.getTime());
			daysStrList.add(dayStr);
		}
		return daysStrList;
	}
	
	/**
     * @Description 获取时间段内的每一月
     * <AUTHOR>
     * @param begintTime
     * @param endTime
     * @return
     * List<String>
     * 2020年12月14日
     */
	public static List<String> findMonthsStr(Date begintDate, Date endDate) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
//		Date dBegin = null;
//		Date dEnd = null;
//		try {
//			dBegin = sdf.parse(begintTime);
//			dEnd = sdf.parse(endTime);
//		} catch (ParseException e) {
//			e.printStackTrace();
//		}
		List<String> daysStrList = new ArrayList<String>();
		daysStrList.add(sdf.format(begintDate));
		Calendar calBegin = Calendar.getInstance();
		calBegin.setTime(begintDate);
		Calendar calEnd = Calendar.getInstance();
		calEnd.setTime(endDate);
		while (endDate.after(calBegin.getTime())) {
			calBegin.add(Calendar.MONTH, 1);
			String dayStr = sdf.format(calBegin.getTime());
			daysStrList.add(dayStr);
		}
		return daysStrList;
	}
	
	 public static void main(String[] args){
//	    	String dateStr = DateUtil.yearthDate("2017-05-30");
//	    	System.out.println(dateStr);
//	    	long min = DateUtil.timeSub("2017-04-12 00:00:00", "2017-04-13 00:00:00")/60;
//	    	System.out.println(min);
//	    	String settlementDate = DateUtils.dateToString(new Date(), "yyyy-MM-dd");
//	    	long day = DateUtils.dayDiff(DateUtils.stringtoDate("2017-06-22", "yyyy-MM-dd"),DateUtils.stringtoDate(settlementDate, "yyyy-MM-dd"));
//	    	if(day >= 0){
//	    		System.out.println(day);
//	    	}
//	    	
//	    	String goodsArriveTime = "2017-04-02 17:00-18:00";
//	    	int space_index = goodsArriveTime.indexOf(" ");
//	    	String arrive_date = goodsArriveTime.substring(0, space_index);
//			String arrive_time = goodsArriveTime.substring(space_index+1, goodsArriveTime.length());
//			
//			System.out.println(arrive_date);
//			System.out.println(arrive_time);
//			String arrive_start_time = arrive_time.substring(0, 2);
//			String arrive_end_time = arrive_time.substring(6,8);
//	    	
//			System.out.println(arrive_start_time);
//			System.out.println(arrive_end_time);
//			
//			String Time = DateUtils.getCurrDate("HH");
//			System.out.println(Time);
//			
//			String Time2 = DateUtils.getCurrDate("mm");
//			System.out.println(Time2);
		 	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			String begintTime = "2020-01-01";
			String endTime = "2020-01-31";
			try {
				for (String days : findDaysStr(sdf.parse(begintTime), sdf.parse(endTime))) {
					System.out.println(days.substring(5));
				}
			} catch (ParseException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
//			for (String months : findMonthsStr(begintTime, endTime)) {
//				System.out.println(months);
//			}
		 	
	    }

}