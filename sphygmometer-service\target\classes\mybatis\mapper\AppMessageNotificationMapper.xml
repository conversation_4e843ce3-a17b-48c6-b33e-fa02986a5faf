<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.AppMessageNotificationMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.AppMessageNotificationDTO" id="AppMessageNotificationDTOResult">
        <result property="noticeId"    column="notice_id"    />
		<result property="organizationId" column="organization_id"/>
        <result property="title"    column="title"    />
        <result property="noticeContent"    column="notice_content"    />
        <result property="status"    column="status"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="createTime"    column="create_time"    />
		<result property="msgType"    column="msg_type"    />
		<result property="userId"    column="user_id"    />
    </resultMap>

    <sql id="selectAppMessageNotification">
    	select
	        notice_id,
	        organization_id,
	        title,
	        notice_content,
	        status,
	        is_delete,
	        delete_time,
	        publish_time,
	        create_time,
	        notice_picture,
	        msg_type,
	        user_id
		from 
        	t_app_message_notification
    </sql>

    <select id="selectAppMessageNotificationById" parameterType="string" resultMap="AppMessageNotificationDTOResult">
		select
			tamn.notice_id,
			tamn.title,
			tamn.notice_content,
			tamn.status,
			tamn.is_delete,
			tamn.delete_time,
			tamn.publish_time,
			tamn.create_time,
			tamn.notice_picture,
			tamn.msg_type,
			tamn.user_id
		from
			t_app_message_notification tamn
		where 
        	tamn.notice_id = #{noticeId}
    </select>

	<select id="getInfoAndReadStatus" resultMap="AppMessageNotificationDTOResult">
		select
			tamn.notice_id,
			tamn.title,
			tamn.notice_content,
			tamn.status,
			tamn.is_delete,
			tamn.delete_time,
			tamn.publish_time,
			tamn.create_time,
			tamn.notice_picture,
			tamn.msg_type,
			tamn.user_id,
			(
			CASE WHEN tamr.is_read = 1 THEN 1
			WHEN tamr.is_read = 0 THEN 0
			ELSE 0 END
			) is_read
		from
			t_app_message_notification tamn
		left join t_app_message_read tamr on tamr.notice_id = tamn.notice_id and tamr.user_id = #{userId}
		where
        	tamn.notice_id = #{noticeId} and tamn.is_delete = 0
    </select>

    <select id="selectAppMessageNotificationList" parameterType="com.bojun.sphygmometer.dto.AppMessageNotificationDTO" resultMap="AppMessageNotificationDTOResult">
		select
			tamn.notice_id,
			tamn.title,
			tamn.notice_content,
			tamn.status,
			tamn.is_delete,
			tamn.delete_time,
			tamn.publish_time,
			tamn.create_time,
			tamn.notice_picture,
			tamn.msg_type,
			tamn.user_id
		from
			t_app_message_notification tamn
        <where>  
		<if test="noticeId != null  and noticeId != ''"> and tamn.notice_id = #{noticeId}</if>
		<if test="title != null  and title != ''"> and tamn.title = #{title}</if>
		<if test="noticeContent != null  and noticeContent != ''"> and tamn.notice_content = #{noticeContent}</if>
		<if test="status != null "> and tamn.status = #{status}</if>
		<if test="isDelete != null "> and tamn.is_delete = #{isDelete}</if>
		<if test="deleteTime != null "> and tamn.delete_time = #{deleteTime}</if>
		<if test="publishTime != null "> and tamn.publish_time = #{publishTime}</if>
		<if test="createTime != null "> and tamn.create_time = #{createTime}</if>
		<if test="msgType != null"> and tamn.msg_type = #{msgType}</if>
		<if test="userId != null"> and tamn.user_id = #{userId}</if>
        </where>
        order by create_time desc
    </select>

	<select id="getMyAppMessageList" parameterType="com.bojun.sphygmometer.dto.AppMessageNotificationDTO" resultMap="AppMessageNotificationDTOResult">
		select
			tamn.notice_id,
			tamn.title,
			tamn.status,
			tamn.is_delete,
			tamn.delete_time,
			tamn.publish_time,
			tamn.create_time,
		    tamn.notice_content,
			tamn.notice_picture,
			tamn.msg_type,
			tamn.user_id,
			(
			CASE WHEN tamr.is_read = 1 THEN 1
			WHEN tamr.is_read = 0 THEN 0
			ELSE 0 END
			) is_read
		from
			t_app_message_notification tamn
		left join t_app_message_read tamr on tamr.notice_id = tamn.notice_id and tamr.user_id = #{userId}
		left join t_sphygmometer_user tsu on tsu.user_id = tamn.user_id
		where
			tamn.is_delete = 0 and tamn.status = 1 and
		    (
			(tamn.msg_type = 1 and tamn.user_id is null and find_in_set(#{pushProduct},push_product) AND tamn.publish_time > (select register_time from t_sphygmometer_user where user_id = #{userId}) )
			or
			(tamn.msg_type != 1 and tamn.user_id = #{userId})
			) and tamn.msg_type != 2
			<if test="msgType != null"> and tamn.msg_type = #{msgType}</if>
			<if test="isRead != null and isRead == 0"> and (tamr.is_read is null or tamr.is_read = 0)</if>
			<if test="isRead != null and isRead == 1"> and  tamr.is_read =1 </if>
		order by create_time desc
	</select>

	<select id="getUnReadCount" resultType="java.lang.Integer">
		select
			count(1)
		from
			t_app_message_notification tamn
		left join t_app_message_read tamr on tamr.notice_id = tamn.notice_id and tamr.user_id = #{userId}
		where
			tamn.is_delete = 0 and tamn.status = 1 and
			(
				(tamn.msg_type = 1 and tamn.user_id is null and find_in_set(#{pushProduct},push_product) AND tamn.publish_time > (select register_time from t_sphygmometer_user where user_id = #{userId})) or
				(tamn.msg_type != 1 and tamn.user_id = #{userId})
			) and
			(tamr.is_read = 0 or tamr.is_read is null) and tamn.msg_type != 2
		<if test="msgType != null"> and tamn.msg_type = #{msgType}</if>
	</select>


	<select id="getUnReadCountWxapp" resultType="java.lang.Integer">
		select
		count(1)
		from
		t_app_message_notification tamn
		left join t_app_message_read tamr on tamr.notice_id = tamn.notice_id and tamr.user_id = #{userId}
		where
		tamn.is_delete = 0 and tamn.status = 1 and
		(
		(tamn.msg_type = 1 and tamn.user_id is null and find_in_set(#{pushProduct},push_product) AND tamn.publish_time > (select register_time from t_sphygmometer_user where user_id = #{userId})) or
		(tamn.msg_type != 1 and tamn.user_id = #{userId})
		) and
		(tamr.is_read = 0 or tamr.is_read is null) and tamn.msg_type != 2
		and tamn.msg_type in(1,3,7)
	</select>


	<select id="getHighRiskMessageList" parameterType="com.bojun.sphygmometer.dto.AppMessageNotificationDTO" resultMap="AppMessageNotificationDTOResult">
		select
		tamn.notice_id,
		tamn.title,
		tamn.notice_content,
		tamn.status,
		tamn.is_delete,
		tamn.delete_time,
		tamn.publish_time,
		tamn.create_time,
		tamn.notice_picture,
		tamn.msg_type,
		tamn.user_id,
		(
		CASE WHEN tamr.is_read = 1 THEN 1
		WHEN tamr.is_read = 0 THEN 0
		ELSE 0 END
		) is_read
		from
		t_app_message_notification tamn
		left join t_app_message_read tamr on tamr.notice_id = tamn.notice_id
		where
		tamn.is_delete = 0 and tamn.status = 1 and
		(
		(tamn.msg_type = 3 ) or
		(tamn.msg_type = 3 and tamr.user_id = #{userId})
		)
		<if test="organizationIdList != null and  organizationIdList.size() > 0 ">
			and tamn.organization_id in
			<foreach item="item" index="index" collection="organizationIdList"
					 open="(" separator="," close=")">
				#{item}
			</foreach>
		</if>
		<if test="title != null">
		and tamn.title LIKE CONCAT('%',#{title},'%')
		</if>
		<if test="startTime != null and endTime != null">
			and tamn.create_time between #{startTime} and #{endTime}
		</if>
		order by tamn.publish_time desc
	</select>
</mapper>