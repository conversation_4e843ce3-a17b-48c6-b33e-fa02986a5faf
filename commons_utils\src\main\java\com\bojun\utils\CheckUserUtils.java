package com.bojun.utils;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 
*Model：
*Description：用户实名认证
*Author:肖泽权
*created：2021年6月11日
*
 */
public class CheckUserUtils {
	
	public final static Log log = LogFactory.getLog(CheckUserUtils.class);
	
	private static String host = PropertiesUtils.getProperty("config.properties", "ali.checkuser.host");
	
	private static String path = PropertiesUtils.getProperty("config.properties", "ali.checkuser.path");
	
	private static String appcode = PropertiesUtils.getProperty("config.properties", "ali.checkuser.appcode");
	
	/**
	 * 
	 * @Description 阿里巴巴用户实名认证
	 * <AUTHOR>
	 * @param idNo 身份证号
	 * @param realName 真实姓名
	 * @return
	 * @return boolean
	 * created：2021年4月7日
	 */
	public static boolean aliCheck(String idNo, String realName){
			boolean result = true;
		    Map<String, String> headers = new HashMap<String, String>();
		    //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
		    headers.put("Authorization", "APPCODE " + appcode);
		    Map<String, String> querys = new HashMap<String, String>();
		    querys.put("cardNo", idNo);
		    querys.put("realName", realName);
		    try {
		    	HttpResponse response = AliHttpUtils.doGet(host, path, "GET", headers, querys);
		    	if(response == null){
		    		return false;
		    	}
		    	/***********验证成功返参*************/
		    	/*{
		    		"error_code": 0,
		    		"reason": "成功",
		    		"result": {
		    			"realname": "肖**",
		    			"idcard": "360782************",
		    			"isok": true,
		    			"IdCardInfor": {
		    				"province": "江西省",
		    				"city": "赣州市",
		    				"district": "南康市",
		    				"area": "江西省赣州市南康市",
		    				"sex": "男",
		    				"birthday": "1993-4-11"
		    			}
		    		}
		    	}*/
		    	/***********验证失败返参*************/
		    	/*{
		    		"error_code": 0,
		    		"reason": "成功",
		    		"result": {
		    			"realname": "肖**",
		    			"idcard": "360782************",
		    			"isok": false,
		    			"IdCardInfor": null
		    		}
		    	}*/
		    	String entity = EntityUtils.toString(response.getEntity());
		    	log.info("check user entity :" + entity);
		    	JSONObject obj = JSONObject.parseObject(entity);
		    	result = (boolean) JSONObject.parseObject(String.valueOf(obj.get("result"))).get("isok");
		    	log.info("check user result is :" + result);
		    } catch (Exception e) {
		    	e.printStackTrace();
		    }
		return result;
	}

}
