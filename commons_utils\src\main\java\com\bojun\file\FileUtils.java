/**
 * 
 */
package com.bojun.file;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import com.bojun.contants.Contants;
import com.bojun.contants.FilePathConstants;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 * 文件工具类
 */
public class FileUtils {
	
	 private static Log log = LogFactory.getLog(FileUtils.class);
	
	/**
	 * base64转文件
	 * @param savePath
	 * @param fileName
	 * @param base64
	 */
	public static boolean convertBase64DataToImage(String savePath , String fileName, String base64) {
        File file = null;
        //创建文件目录
        String filePath = savePath;
        File  dir = new File(filePath);
        if (!dir.exists() && !dir.isDirectory()) {
        	dir.mkdirs();
        }
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        try {
            byte[] bytes = Base64.getDecoder().decode(base64);
            file = new File(filePath + fileName);
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return true;
    }
	
	/**
	 * 获取图片拓展名
	 * @param headerStr
	 * @return
	 */
	public static String getBsese64ImageExt(String headerStr) {
		String[] headerArray = headerStr.split("\\/");
		if (headerArray.length != 2) {
			return null;
		}
		String[] extArray = headerArray[1].split(";");
		if (extArray.length != 2) {
			return null;
		}
		String ext = extArray[0].toString();
		if ("jpeg".equalsIgnoreCase(ext) 
				|| "jpg".equalsIgnoreCase(ext) 
				|| "gif".equalsIgnoreCase(ext) 
				|| "png".equalsIgnoreCase(ext)) {
			ext = ext.toLowerCase();
		} else {
			return null;
		}
		return ext;
		
	}
	
	/**
	 * 获取base64流多少字节
	 * @param image
	 * @return
	 */
	public static Integer getBase64ImageSize(String base64){
        //原来的字符流大小，单位为字节
        Integer strLength = base64.length();
        //计算后得到的文件流大小，单位为字节
        Integer size = strLength-(strLength/8)*2;
        return size;
    }
	
	 /**
     * 创建文件
     * 
     * @param filePath
     * */
    public static boolean createFile(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                file.mkdirs();
            }

            return true;
        } catch (Exception e) {
            log.info("创建路径" + filePath + "失败！" + e.getMessage());
            return false;
        }
    }

    /**
     * 创建文件
     * 
     * @param destFileName
     *            目标文件完整路径
     * */
    public static boolean createFile(String destFileName, int flag) {
        File file = new File(destFileName);
        if (file.exists()) {
            file.delete();
            createFile(destFileName, flag);
            return true;
        }

        // 判断目标文件所在的目录是否存在
        if (!file.getParentFile().exists()) {
            // System.out.println("创建" + file.getName() + "所在目录不存在，正在创建！");
            if (!file.getParentFile().mkdirs()) {// 判断父文件夹是否存在，如果存在则表示创建成功否则不成功
                log.info("创建目标文件所在的目录失败！");
                return false;
            }
        }
        // 创建目标文件
        try {
            if (file.createNewFile()) {// 调用createNewFile方法，判断创建指定文件是否成功
                log.info("创建文件" + destFileName + "成功！");
                return true;
            } else {
                log.info("创建文件" + destFileName + "失败！");
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            log.info("创建文件" + destFileName + "失败！" + e.getMessage());
            return false;
        }
    }

    /**
     * 获取指定目录下文件列表
     * 
     * @param dir
     *            目标目录
     */
    public static List<String> listFile(String dir) {
        List<String> fileList = new ArrayList<String>();
        if (!dir.endsWith(File.separator)) {
            dir += File.separator;
        }
        File dirFile = new File(dir);
        if (!dirFile.exists()) {
            dirFile.mkdirs();
        }
        File[] files = dirFile.listFiles();
        for (int i = 0; i < files.length; i++) {
            if (files[i].isDirectory()) {
                log.info("存在异常文件夹：" + files[i].getPath());
                deleteFile(files[i].getPath());
            } else {
                fileList.add(files[i].getName());
            }
        }
        return fileList;
    }

    /**
     * 删除文件
     * 
     * @param fileDir
     *            删除单个文件
     */
    public static boolean deleteFile(String fileDir) {
        File file = new File(fileDir);
        if (file.exists()) {
            file.delete();
            log.info("删除" + fileDir + "成功！");
            return true;
        }
        return false;
    }

    /**
     * @description:  计算文件大小
     * @author: 赖允翔
     * @date: 2020/6/5
     * @Param: url上传获得地址，baseUploadFolder本地路径，baseHttpUrl本地IIS
     * @return:
     */
    public static String fileSize(String fileName, String baseUploadFolder) {
        String fileUrl = baseUploadFolder + fileName;
        System.out.println(fileUrl);
        File file = new File(fileUrl);
        if (file.exists() && file.isFile()) {
            double fileSize = file.length() / 1024.00 / 1024.00;
            return String.format("%.2f", fileSize);
        }else {
            return null;
        }
    }

    public static String fileSize2(String fileName,String baseUploadFolder) {
    	URL url;
    	double fileSize=0.00;
		try {
		   url = new URL(baseUploadFolder+fileName);
		   URLConnection conn = url.openConnection();
		   fileSize = conn.getContentLength() / 1024.00 / 1024.00;
	       conn.getInputStream().close();
		} catch (IOException e) {
			e.printStackTrace();
		}
		 return String.format("%.2f", fileSize);
    }
    
    /**
     * @description: 获取链接文件大小
     * @author: 潘文泽
     * @date: 2021/5/17
     * @Param: 文件url地址
     * @return:
     */
    public static String fileSizeForUrl(String filrUrl) throws IOException {
        URL url = new URL(filrUrl);
        URLConnection conn = url.openConnection();
        int size = conn.getContentLength();
        double fileSizeDouble = (double) size / 1024.0D / 1024.0D;
        String fileSize = String.format("%.2f", fileSizeDouble);
        return fileSize;
    }

}
