/**
 * 
 */
package com.bojun.base.manage.config;

import com.bojun.base.manage.interceptor.UserAuthorityInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

/**
*Model：安全验证配置 （用户菜单权限拦截）
*Description：安全验证配置 （用户菜单权限拦截）
*Author: 段德鹏
*created：2020年3月3日
 */
@Configuration
public class SecurityConfiguration extends WebMvcConfigurationSupport {
	
	@Autowired
	private UserAuthorityInterceptor userAuthorityInterceptor;
	
	/**
	 * 用户权限拦截器配置
	 */
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(userAuthorityInterceptor).addPathPatterns("/**")
				.excludePathPatterns("/login/userLoginByPwd", "/error", "/v2/**", "/doc.html", "/webjars/**", "/swagger-resources/**",
						"/OrgMange/getOrganizationList","/OrgMange/getOrganizationTreeList","/OrgMange/getPushObject");
	}
	
	/**
	 * 访问静态资源
	 */
	@Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
		 registry.addResourceHandler("doc.html").addResourceLocations("classpath:/META-INF/resources/");
	     registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
	     super.addResourceHandlers(registry);
	}

}
