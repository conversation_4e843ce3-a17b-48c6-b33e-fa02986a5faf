package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.ManageUserV2Mapper;
import com.bojun.base.system.service.ManageUserV2Service;
import com.bojun.system.dto.ManageUserV2DTO;
import com.bojun.system.entity.ManageUserLogin;
import com.bojun.system.entity.ManageUserV2;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * ManageUserV2Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-06-05 16:05:04
 */
@Service
public class ManageUserV2ServiceImpl extends ServiceImpl<ManageUserV2Mapper, ManageUserV2> implements ManageUserV2Service
{

    /**
     * 查询系统管理员用户
     * 
     * @param userId 系统管理员用户ID
     * @return 系统管理员用户
     */
    @Override
    public ManageUserV2DTO selectManageUserById(Integer userId)
    {
        return this.baseMapper.selectManageUserById(userId);
    }

    /**
     * 查询系统管理员用户列表
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 系统管理员用户集合
     */
    @Override
    public List<ManageUserV2DTO> selectManageUserList(ManageUserV2DTO manageUserDTO)
    {
        return this.baseMapper.selectManageUserList(manageUserDTO);
    }

    /**
     * 新增系统管理员用户
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 结果
     */
    @Override
    public int insertManageUser(ManageUserV2DTO manageUserDTO)
    {
        manageUserDTO.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(manageUserDTO);
    }

    /**
     * 修改系统管理员用户
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 结果
     */
    @Override
    public int updateManageUser(ManageUserV2DTO manageUserDTO)
    {
        return this.baseMapper.updateById(manageUserDTO);
    }
    
    /**
     * 新增系统管理员用户
     * 
     * @param manageUser 系统管理员用户
     * @return 结果
     */
    @Override
    public int insertManageUser(ManageUserV2 manageUser)
    {
        manageUser.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(manageUser);
    }

    /**
     * 修改系统管理员用户
     * 
     * @param manageUser 系统管理员用户
     * @return 结果
     */
    @Override
    public int updateManageUser(ManageUserV2 manageUser)
    {
        return this.baseMapper.updateById(manageUser);
    }

    /**
     * 删除系统管理员用户对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteManageUserByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除系统管理员用户信息
     * 
     * @param userId 系统管理员用户ID
     * @return 结果
     */
    @Override
    public int deleteManageUserById(Integer userId)
    {
        return this.removeById(userId) ? 1 : 0;
    }

    @Override
    public ManageUserLogin selectLastUserLoginById(Integer userId, String systemId) {
        return this.baseMapper.selectLastUserLoginById(userId, systemId);
    }
}
