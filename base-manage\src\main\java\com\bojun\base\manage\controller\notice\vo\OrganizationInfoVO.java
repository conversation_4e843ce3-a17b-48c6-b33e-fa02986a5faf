package com.bojun.base.manage.controller.notice.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/30 15:20
 */
public class OrganizationInfoVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    private Integer deptId; // 机构id

    private String deptName; // 机构名称

    private String type;
    @ApiModelProperty(value = "启用状态标记  0：未启用  1：启用")
    private Integer isEnabled;

    private List<DepartmentInfoVO> subDeptList; //机构下的所有父科室

    private Integer organizationId; // 机构id

    private String organizationName; // 机构名称


    public Integer getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public List<DepartmentInfoVO> getSubDeptList() {
        return subDeptList;
    }

    public void setSubDeptList(List<DepartmentInfoVO> subDeptList) {
        this.subDeptList = subDeptList;
    }
}
