<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.ResidentHealthInfoMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.ResidentHealthInfoDTO" id="ResidentHealthInfoDTOResult">
        <result property="id"    column="id"    />
        <result property="residentId"    column="resident_id"    />
        <result property="height"    column="height"    />
        <result property="weight"    column="weight"    />
        <result property="bmi"    column="bmi"    />
        <result property="waistline"    column="waistline"    />
        <result property="bloodType"    column="blood_type"    />
        <result property="pastHistory"    column="past_history"    />
        <result property="hpi"    column="hpi"    />
        <result property="allergicHistory"    column="allergic_history"    />
        <result property="familyHistory"    column="family_history"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
    </resultMap>

    <sql id="selectResidentHealthInfo">
    	select
	        id,
	        resident_id,
	        height,
	        weight,
	        bmi,
	        waistline,
	        blood_type,
	        past_history,
	        hpi,
	        allergic_history,
	        family_history,
	        create_time,
	        create_user_id,
	        create_user_name,
	        update_time,
	        update_user_id,
	        update_user_name
		from 
        	t_resident_health_info
    </sql>

    <select id="selectResidentHealthInfoById" parameterType="int" resultMap="ResidentHealthInfoDTOResult">
		<include refid="selectResidentHealthInfo"/>
		where 
        	id = #{id}
    </select>

    <select id="selectResidentHealthInfoList" parameterType="com.bojun.sphygmometer.dto.ResidentHealthInfoDTO" resultMap="ResidentHealthInfoDTOResult">
        <include refid="selectResidentHealthInfo"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="residentId != null  and residentId != ''"> and resident_id = #{residentId}</if>
		<if test="height != null "> and height = #{height}</if>
		<if test="weight != null "> and weight = #{weight}</if>
		<if test="bmi != null "> and bmi = #{bmi}</if>
		<if test="waistline != null "> and waistline = #{waistline}</if>
		<if test="bloodType != null "> and blood_type = #{bloodType}</if>
		<if test="pastHistory != null  and pastHistory != ''"> and past_history = #{pastHistory}</if>
		<if test="hpi != null  and hpi != ''"> and hpi = #{hpi}</if>
		<if test="allergicHistory != null  and allergicHistory != ''"> and allergic_history = #{allergicHistory}</if>
		<if test="familyHistory != null  and familyHistory != ''"> and family_history = #{familyHistory}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createUserName != null  and createUserName != ''"> and create_user_name = #{createUserName}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateUserName != null  and updateUserName != ''"> and update_user_name = #{updateUserName}</if>
        </where>
    </select>

	<select id="getHealthInfoByResidentId" parameterType="string" resultMap="ResidentHealthInfoDTOResult">
		<include refid="selectResidentHealthInfo"/>
		where
		resident_id = #{residentId}
	</select>

</mapper>