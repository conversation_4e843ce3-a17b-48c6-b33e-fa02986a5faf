package com.bojun.health.promotion.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.health.promotion.common.dto.NewsInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoParamDTO;
import com.bojun.health.promotion.common.entity.NewsInfo;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NewsInfoMapper extends BaseMapper<NewsInfo> {


    int insertSelective(NewsInfoDTO record);

    /**
     * 获取文章详情
     *
     * @param newsId
     * @return
     */
    NewsInfoDTO selectByPrimaryKey(Integer newsId);

    int updateByPrimaryKeySelective(NewsInfoDTO record);

    int updateByPrimaryKey(NewsInfoDTO record);

    int deleteByPrimaryKey(NewsInfoDTO record);

    /**
     * 获取文章的集合
     *
     * @param topicInfo
     * @return
     */
    Page<NewsInfoDTO> getNewsInfo(TopicInfoParamDTO topicInfo);

    /**
     * 更新文章信息
     *
     * @param newsList
     * @return
     */
    int updateByNewsId(@Param("newsList") List<Integer> newsList);

    /**
     * 更新文章的阅读数量
     *
     * @return
     */
    int updateReadNumberByNewsId(NewsInfoDTO newsInfoDTO);


}