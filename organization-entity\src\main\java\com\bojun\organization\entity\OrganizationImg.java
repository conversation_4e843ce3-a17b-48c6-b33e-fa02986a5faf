package com.bojun.organization.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：机构管理
 * Description：机构图片实体
 * Author：赖水秀
 * created： 2020年5月7日
 */
public class OrganizationImg implements Serializable {
		
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -1597202195275277857L;
	

	private Integer id;	
	
	private Integer organizationId;

	private String organizationImage;
	
	private Integer showIndex;
	
	private Date uploadTime;
	
	private Integer isCover;//是否封面
    
	public Integer getIsCover() {
		return isCover;
	}

	public void setIsCover(Integer isCover) {
		this.isCover = isCover;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationImage() {
		return organizationImage;
	}

	public void setOrganizationImage(String organizationImage) {
		this.organizationImage = organizationImage;
	}

	public Integer getShowIndex() {
		return showIndex;
	}

	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}

	public Date getUploadTime() {
		return uploadTime;
	}

	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
	
	
}
