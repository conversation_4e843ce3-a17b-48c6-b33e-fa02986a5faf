/**
 * 
 */
package com.bojun.base.manage.api.organization;

import com.bojun.base.manage.api.organization.hystrix.OrganizationServiceHystrix;
import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Model： 机构管理服务
 * Description：机构管理服务
 * Author：赖水秀
 * created： 2020年5月4日
 */
@FeignClient(name="organization-service", fallback = OrganizationServiceHystrix.class)
public interface IOrganizationService {

    /**
     * @Description 保存 机构信息
     * <AUTHOR>
     * @param organizationId
     * @return
     * int
     * 2020年5月4日
     */
    @RequestMapping(value = "/saveOrganizationInfo", method = RequestMethod.POST)
    String saveOrganizationInfo(OrganizationInfoDTO organizationInfoDTO);


    /**
     * @Description 修改 机构信息
     * <AUTHOR>
     * @param organizationId
     * @return
     * int
     * 2020年5月4日
     */
    @RequestMapping(value = "/updateOrganizationInfo", method = RequestMethod.POST)
    String updateOrganizationInfo(OrganizationInfoDTO organizationId);


    /**
     * @Description 根据单个 机构信息
     * <AUTHOR>
     * @param organizationId
     * @return
     * SystemDictDTO
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationInfoById", method = RequestMethod.POST)
    String getOrganizationInfoById(@RequestParam(value="organizationId") Integer organizationId);

    /**
     * @Description 分页查询 机构信息列表
     * <AUTHOR>
     * @param systemDictDto
     * @return
     * List<OrganizationInfoDTO>
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationPageList", method = RequestMethod.POST)
    String getOrganizationPageList(OrganizationInfoDTO organizationInfoDTO);


    /**
     * @Description 查询树形 机构信息列表
     * <AUTHOR>
     * @param systemDictDto
     * @return
     * List<OrganizationInfoDTO>
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationTreeList", method = RequestMethod.POST)
    String getOrganizationTreeList(OrganizationInfoDTO organizationInfoDTO);

    /**
     * @Description 单个删除信息
     * <AUTHOR>
     * @param organizationId
     * @return
     * int
     * 2020年5月4日
     */
    @RequestMapping(value = "/deleteOrganizationById", method = RequestMethod.POST)
    String deleteOrganizationById(@RequestParam(value="organizationId") Integer organizationId);


    /**
     * @Description 批量删除信息
     * <AUTHOR>
     * @param organizationIds
     * @return
     * int
     * 2020年5月4日
     */
    @RequestMapping(value = "/batchDeleteOrganization", method = RequestMethod.POST)
    String batchDeleteOrganization(List<String> organizationIds);

    /**
     * @Description 查询机构类型列表
     * <AUTHOR>
     * @param classCode
     * @return
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationTypeList", method = RequestMethod.POST)
    String getOrganizationTypeList(@RequestParam(value="classCode") String classCode);

    /**
     * @param
     * @param objectVO
     * @param organizationInfoDTO
     * @description: 获取推送对象
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getPushObject")
    String getPushObject(OrganizationInfoDTO organizationInfoDTO);

    /**
     * @param
     * @param objectVO
     * @description: 获取推送对象
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getPushObjectByOrgId")
    String getPushObjectByOrgId(OrganizationInfoDTO organizationInfoDTO);

    /**
     * @param deptInfo
     * @return String
     * 2020年5月8日
     * @Description TODO
     * <AUTHOR>
     */
    @RequestMapping(value = "/deleteOrganizationImgById", method = RequestMethod.POST)
    String deleteOrganizationImgById(OrganizationImgDTO organizationImgDTO);

    @PostMapping(value = "/getPushObjectByOrgAndRoleId")
    String getPushObjectByOrgAndRoleId(OrganizationInfoDTO organizationInfoDTO);

    @PostMapping(value = "/getPushObjectByRoleId")
    String getPushObjectByRoleId(OrganizationInfoDTO organizationInfoDTO);
}
