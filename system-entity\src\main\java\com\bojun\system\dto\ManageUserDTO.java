package com.bojun.system.dto;

import java.util.List;

import com.bojun.system.entity.ManageUser;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Model：系统管理员用户表 Description：系统管理员用户表DTO Author:刘俊 created：2020年3月3日
 */
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ManageUserDTO extends ManageUser {

    private static final long serialVersionUID = -1344026328907379635L;

    private Integer everyPage; // 每页显示数

    private Integer pageNum; // 页面数

    private String newPwd;// 新密码

    private String oldPwd;// 旧密码

    private String handerToken;// handerToken

    private Integer dataPermissions;// 数据权限 1: 角色机构 2：账号机构

    private List<ManageRoleOrganDTO> manageRoleOrganList;// 走角色权限是，返回的机构

    private String organizationIds;//多个机构id

    private String isEnabledRole;//特殊启用

    private String deptNumber;//科室编号

    private String employeeId;//员工id

    private Integer isForced;//是否强制登录

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 系统名称
     */
    private String systemName;
    /**
     * 系统ID
     */
    private String systemId;
    /**
     * 系统名称
     */
    private String loginTime;
    /**
     * 管理员角色信息列表
     */

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 登陆类型
     */
    private Integer loginType;

    /**
     * 职称
     */
    private String jobTitleName;

    /**
     * 机构名称
     */
    private String organizationName;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 病区名称
     */
    private String wardName;
    /**
     * 开始查询最后登录时间
     */
    private String startLastestLoginTime;
    /**
     * 结束查询最后登录时间
     */
    private String endLastestLoginTime;
    /**
     * 是否启用
     */
    private Integer isEnabled;

    private Integer weight;
    private Integer employeeType;
    private String wxAccount;

    private String organizationImage;//封面图片名称

    private Integer jobTitleId;

    private String rongToken;

    private String AgainNewPwd;
    private String oldMobilePhone;
    private String newMobilePhone;
    private String code; //验证码
    private String doctorId;
    private Integer appUserId;
    @ApiModelProperty(value = "是否需要融云 1需要", example = "0")
    private Integer isRongYun;

    private String organizationImageUrl;//封面图片地址

    //机器编码-一体机
    private String machineId;

}
