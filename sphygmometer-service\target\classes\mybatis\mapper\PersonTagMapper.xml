<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.PersonTagMapper">
  <resultMap id="BaseResultMap" type="com.bojun.sphygmometer.dto.PersonTagDTO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="column_name" jdbcType="VARCHAR" property="columnName" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="tag_type" jdbcType="VARCHAR" property="tagType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, code, name, table_name, column_name, value, tag_type
  </sql>
  <select id="selectAll"  resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_person_tag
  </select>
  <select id="selectListForTypeAndValue"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_person_tag
    where tag_type = #{tagType}
    <if test="tagValue != null and tagValue != ''">
      and `value` = #{tagValue}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_person_tag
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bojun.sphygmometer.dto.PersonTagDTO">
    insert into t_person_tag (id, code, name, 
      table_name, column_name, value
      )
    values (#{id,jdbcType=INTEGER}, #{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{tableName,jdbcType=VARCHAR}, #{columnName,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bojun.sphygmometer.dto.PersonTagDTO">
    insert into t_person_tag
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="columnName != null">
        column_name,
      </if>
      <if test="value != null">
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bojun.sphygmometer.dto.PersonTagDTO">
    update t_person_tag
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="tableName != null">
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="columnName != null">
        column_name = #{columnName,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bojun.sphygmometer.dto.PersonTagDTO">
    update t_person_tag
    set code = #{code,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      table_name = #{tableName,jdbcType=VARCHAR},
      column_name = #{columnName,jdbcType=VARCHAR},
      value = #{value,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>



  <select id="selectListForCode"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_person_tag
    where code = #{code,jdbcType=VARCHAR}
  </select>


  <select id="getUserByPersonTag" resultType="Map">
    select distinct
      tu.ge_tui_client_id geTuiClientId,
      tu.user_id userId
    from ${tableName} aa left join t_sphygmometer_user tu  on aa.user_id= tu.user_id
    where aa.${columnName}  = #{value,jdbcType=VARCHAR}
  </select>

</mapper>