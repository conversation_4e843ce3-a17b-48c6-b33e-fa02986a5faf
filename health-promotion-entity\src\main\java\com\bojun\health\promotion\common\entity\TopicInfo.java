package com.bojun.health.promotion.common.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 话题信息表对象 t_topic_info
 * 
 * <AUTHOR>
 * @date 2021-08-26 10:07:05
 */
@ApiModel(value = "TopicInfo对象" , description = "话题信息表")
@Data
@TableName("t_topic_info")
public class TopicInfo implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 话题id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "topic_id", type = IdType.AUTO)
    private Integer topicId;

    /** 父级话题id */
    @ApiModelProperty(value = "父级话题id", example = "")
	@TableField("parent_topic_id")
    private Integer parentTopicId;

    /** 话题名称 */
    @ApiModelProperty(value = "话题名称", example = "")
	@TableField("topic_name")
    private String topicName;

    /** 排序下标 */
    @ApiModelProperty(value = "排序下标", example = "")
	@TableField("show_index")
    private Integer showIndex;

    /** 是否启动  0：否 1：是 */
    @ApiModelProperty(value = "是否启动  0：否 1：是", example = "")
	@TableField("is_enabled")
    private Integer isEnabled;

    /** 是否删除 0：否 1：是 */
    @ApiModelProperty(value = "是否删除 0：否 1：是", example = "")
	@TableField("is_delete")
    private Integer isDelete;

    /** 编辑人用户id */
    @ApiModelProperty(value = "编辑人用户id", example = "")
	@TableField("update_user_id")
    private Integer updateUserId;

    /** 编辑时间 */
    @ApiModelProperty(value = "编辑时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("update_time")
    private Date updateTime;

    /** 创建人用户id */
    @ApiModelProperty(value = "创建人用户id", example = "")
	@TableField("create_user_id")
    private Integer createUserId;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("create_time")
    private Date createTime;

    /** 置顶 */
    @ApiModelProperty(value = "置顶", example = "")
	@TableField("top")
    private Integer top;

    /** 系统id */
    @ApiModelProperty(value = "系统id", example = "")
	@TableField("system_id")
    private String systemId;
}
