spring.application.name=organization-service
spring.profiles.active=dev

mybatis.config-location=classpath:mybatis/mybatis-config.xml
mybatis-plus.mapper-locations=classpath:mybatis/mapper/*.xml
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.maximum-pool-size=100
spring.datasource.max-idle=10
spring.datasource.max-wait=10000
spring.datasource.min-idle=5
spring.datasource.initial-size=5
spring.datasource.validation-query=SELECT 1
spring.datasource.test-on-borrow=false
spring.datasource.test-while-idle=true
spring.datasource.time-between-eviction-runs-millis=18800
##pagehelper##
pagehelper.helperDialect=mysql
pagehelper.offsetAsPageNum=true
pagehelper.rowBoundsWithCount=true
pagehelper.pageSizeZero=true
pagehelper.reasonable=false
pagehelper.params=count=countSql
pagehelper.supportMethodsArguments=false
#reids#
spring.redis.database=0
spring.redis.jedis.pool.max-active=100
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0
spring.redis.timeout=10000
#spring.task.pool
spring.task.pool.corePoolSize=5
spring.task.pool.maxPoolSize=10
spring.task.pool.keepAliveSeconds=60