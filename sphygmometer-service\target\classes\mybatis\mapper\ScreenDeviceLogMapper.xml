<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.ScreenDeviceLogMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.ScreenDeviceLogDTO" id="ScreenDeviceLogDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="deviceNo"    column="device_no"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="status"    column="status"    />
        <result property="statusType"    column="status_type"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectScreenDeviceLog">
    	select
	        id,
	        user_id,
	        device_no,
	        organization_id,
	        status,
	        status_type,
	        remark,
	        create_time
		from 
        	t_screen_device_log
    </sql>

    <select id="selectScreenDeviceLogById" parameterType="int" resultMap="ScreenDeviceLogDTOResult">
		<include refid="selectScreenDeviceLog"/>
		where 
        	id = #{id}
    </select>

    <select id="selectScreenDeviceLogList" parameterType="com.bojun.sphygmometer.dto.ScreenDeviceLogDTO" resultMap="ScreenDeviceLogDTOResult">
		select
			tsdl.id,
			tsdl.user_id,
			tsdl.device_no,
			tsdl.organization_id,
			(select organization_name from organization.t_organization_info where tsdl.organization_id = organization_id) organization_name,
			tsdl.status,
			tsdl.status_type,
			tsdl.remark,
			tsdl.create_time,
			tsu.mobile
		from
			t_screen_device_log tsdl
		left join t_sphygmometer_user tsu on tsu.user_id = tsdl.user_id
        <where>  
			<if test="id != null "> and tsdl.id = #{id}</if>
			<if test="userId != null "> and tsdl.user_id = #{userId}</if>
			<if test="deviceNo != null  and deviceNo != ''"> and tsdl.device_no = #{deviceNo}</if>
			<if test="status != null "> and tsdl.status = #{status}</if>
			<if test="statusType != null "> and tsdl.status_type = #{statusType}</if>
			<if test="remark != null  and remark != ''"> and tsdl.remark = #{remark}</if>
			<if test="createTime != null "> and tsdl.create_time = #{createTime}</if>

			<if test="authOrgIdList != null  and authOrgIdList.size() > 0">
				and tsu.manage_organization_id in
				<foreach item="item" index="index"
						 collection="authOrgIdList" open="(" separator=","
						 close=")">
					#{item}
				</foreach>
			</if>
			<if test="searchContent != null and searchContent != ''">
				and (
				tsu.mobile LIKE concat( '%', #{searchContent}, '%' ) or
				tsdl.device_no LIKE concat( '%', #{searchContent}, '%' )
				)
			</if>
			<if test="startCreateTime != null and endCreateTime != null">
				and tsdl.create_time between #{startCreateTime} and #{endCreateTime}
			</if>
        </where>
		order by tsdl.create_time desc
	</select>

</mapper>