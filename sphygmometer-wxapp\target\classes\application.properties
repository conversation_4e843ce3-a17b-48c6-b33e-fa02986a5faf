spring.application.name=sphygmometer-wxapp
spring.profiles.active=dev
server.servlet.context-path=/sphygmometerWxapp

feign.okhttp.enabled=true
feign.hystrix.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=6000

ribbon.ReadTimeout=5000
ribbon.ConnectTimeout=3000

#reids#
spring.redis.database=0
spring.redis.lettuce.pool.max-active=100
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=0
spring.redis.timeout=10000

spring.jackson.time-zone=GMT+8

#swagger2 config#
swagger.project.title=\u9ad8\u8840\u538b\u5c0f\u7a0b\u5e8f\u7b5b\u67e5\u7cfb\u7edf\u63a5\u53e3\u6587\u6863
swagger.project.description=\u9ad8\u8840\u538b\u5c0f\u7a0b\u5e8f\u7b5b\u67e5\u7cfb\u7edf
swagger.project.groupname=1.0
swagger.project.version=1.0
swagger.project.base.package=com.bojun
swagger.project.base.url=http\://localhost\:${server.port}/sphygmometerWxapp