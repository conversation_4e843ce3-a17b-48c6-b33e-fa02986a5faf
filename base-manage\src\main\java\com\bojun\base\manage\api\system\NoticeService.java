package com.bojun.base.manage.api.system;

import com.bojun.base.manage.api.system.hystrix.NoticeServiceHystrix;
import com.bojun.system.dto.MessageNotificationDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 8:44
 */
@FeignClient(name="system-service", fallback = NoticeServiceHystrix.class)
public interface NoticeService {
    /**
     * @description: 通知公告查询（全查）
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value="/getNotices")
    String getNotices(@RequestBody MessageNotificationDTO noticeVO);
    /**
     * @description: 通知公告查询（单查）
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value="/getNoticeById")
    String getNoticeById(@RequestParam(value = "noticeId") String noticeId);
    /**
     * @description: 添加通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value="/addNotice")
    String addNotice(@RequestBody MessageNotificationDTO messageNotificationDTO);
    /**
     * @description: 删除通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     * @param
     */
    @PostMapping(value="/deleteNotices")
    String deleteNotice(@RequestBody MessageNotificationDTO messageNotificationDTO);

    /**
     * @param
     * @description: 修改通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value = "/updateNotice")
    String updateNotice(@RequestBody MessageNotificationDTO messageNotificationDTO);

    /**
     * @param
     * @description: 获取通知类型
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getNoticeType")
    String getNoticeType();


}
