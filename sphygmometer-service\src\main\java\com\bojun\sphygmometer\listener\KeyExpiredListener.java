package com.bojun.sphygmometer.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.push.GeTuiPushUtils;
import com.bojun.sphygmometer.common.PushConstants;
import com.bojun.sphygmometer.common.PushTypeEnum;
import com.bojun.sphygmometer.dto.AppMessageNotificationDTO;
import com.bojun.sphygmometer.dto.ResidentBasicInfoDTO;
import com.bojun.sphygmometer.entity.SphygmometerUser;
import com.bojun.sphygmometer.entity.SphygmometerUserRelative;
import com.bojun.sphygmometer.entity.UserMedicationRemind;
import com.bojun.sphygmometer.entity.UserTestPlanRecord;
import com.bojun.sphygmometer.service.*;
import com.bojun.utils.SpringUtils;
import com.gexin.rp.sdk.base.IPushResult;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.joda.time.DateTime;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class KeyExpiredListener extends KeyExpirationEventMessageListener {

    public KeyExpiredListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }


    private static final String USER_TEST_PLAN_APP_TEMP = "%s 您测量血压的时间到了，记得按时测量血压哦！\n" +
            "测量时间：%s - %s\n";
    /*
     * 用户检测计划微信模板
     **/
//    private static final String USER_TEST_PLAN_WX_TEMP_ID = "9bc8Mbvr4gfU8M8Zznj9hTR0kqDnJoaB7Xyr0vvMVOw"; --测试环境

    private static String USER_TEST_PLAN_WX_TEMP_ID; //--正式环境

    @Value("${wx.mp.user_test_plan_wx_temp_id}")
    public void setUserTestPlanWxTempId(String userTestPlanWxTempId) {
        this.USER_TEST_PLAN_WX_TEMP_ID = userTestPlanWxTempId;
    }


    private static Logger logger = LoggerFactory.getLogger(KeyExpiredListener.class);

    @Override
    public void onMessage(Message message, byte[] pattern) {
        logger.info("----------------------------监控redisKye过期------------------------------------" + message.toString());
        String expiredKey = message.toString();
        RedisUtil redisUtil = SpringUtils.getBean(RedisUtil.class);
        SphygmometerUserService sphygmometerUserService = SpringUtils.getBean(SphygmometerUserService.class);
        SphygmometerUserRelativeService sphygmometerUserRelativeService = SpringUtils.getBean(SphygmometerUserRelativeService.class);
        if (expiredKey.contains("userMedicationRemind")) {
            // 用药计划提醒
            processUserMedicationRemind(expiredKey, redisUtil, sphygmometerUserService, sphygmometerUserRelativeService);
        }
        if (expiredKey.startsWith("UserTestPlanRecordKey:") || expiredKey.startsWith("UserTestPlanRecordKey_")) {
            // 检测计划提醒
            try {
                processUserTestPlanRecord(expiredKey, sphygmometerUserService, sphygmometerUserRelativeService);
            } catch (Exception e) {
                logger.error(">>> processUserTestPlanRecord throw ex, with expiredKey=[{}] e=[{}]", expiredKey, JSON.toJSONString(e.getStackTrace()));
            }
        }
        if (expiredKey.startsWith("pushNews_")) {
            // 文章推送
            pushNews(expiredKey);
        }
    }

    public void processUserTestPlanRecord(String expiredKey, SphygmometerUserService sphygmometerUserService,
                                          SphygmometerUserRelativeService sphygmometerUserRelativeService) {
        logger.debug(">>> processUserTestPlanRecord, with expiredKey=[{}]", expiredKey);
        String[] arr = expiredKey.split("_");
        if (arr.length != 2) {
            arr = expiredKey.split(":");
        }
        Integer recordId = Integer.valueOf(arr[1]);
        UserTestPlanRecordService userTestPlanRecordService = SpringUtils.getBean(UserTestPlanRecordService.class);
        UserTestPlanRecord record = userTestPlanRecordService.getById(recordId);
        if (record == null) {
            logger.debug(">>> processUserTestPlanRecord record is null, with recordId=[{}]", recordId);
            return;
        }
        if (!Objects.equals(0, record.getPressureResult())) {
            // 已经检测过了
            record.setSendMsg(3);
            record.setUpdateTime(new Date());
            userTestPlanRecordService.updateById(record);
            return;
        }
        String startTime = new DateTime(record.getStartTime()).toString("HH:mm");
        String endTime = new DateTime(record.getEndTime()).toString("HH:mm");
        // 未检测
        SphygmometerUser user = sphygmometerUserService.getById(record.getUserId());
        ResidentBasicInfoService residentBasicInfoService = SpringUtils.getBean(ResidentBasicInfoService.class);
        ResidentBasicInfoDTO residentBasicInfoDTO = residentBasicInfoService.selectResidentBasicInfoById(user.getResidentId());
        user.setNickName(residentBasicInfoDTO.getRealName());
        String appContent = String.format(USER_TEST_PLAN_APP_TEMP, user.getNickName(), startTime, endTime);
        // 推送app消息
        pushApp(record, user, appContent, userTestPlanRecordService, startTime, endTime);

        WxMpService wxMpService = SpringUtils.getBean(WxMpService.class);
//        发送本人微信
        sendWxMsg(wxMpService, user.getWxOpenId(), USER_TEST_PLAN_WX_TEMP_ID, getWxTempOwnerMap(startTime, endTime, user));
        // 亲属微信
        List<SphygmometerUserRelative> list = sphygmometerUserRelativeService.list(Wrappers.<SphygmometerUserRelative>lambdaQuery().
                eq(SphygmometerUserRelative::getUserId, user.getUserId()));
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(relative -> {
//        发送家属微信
            sendWxMsg(wxMpService, relative.getWxOpenId(), USER_TEST_PLAN_WX_TEMP_ID, getWxTempRelativeMap(startTime, endTime, user));
        });

    }


    private void processUserMedicationRemind(String expiredKey, RedisUtil redisUtil, SphygmometerUserService sphygmometerUserService,
                                             SphygmometerUserRelativeService sphygmometerUserRelativeService) {
        //推送至手机APP
        String[] arr = expiredKey.split(":");
        Integer userId = Integer.valueOf(arr[1]);
        SphygmometerUser sphygmometerUser = sphygmometerUserService.getById(userId);
        ResidentBasicInfoService residentBasicInfoService = SpringUtils.getBean(ResidentBasicInfoService.class);
        ResidentBasicInfoDTO residentBasicInfoDTO = residentBasicInfoService.selectResidentBasicInfoById(sphygmometerUser.getResidentId());
        sphygmometerUser.setNickName(residentBasicInfoDTO.getRealName());
        String appPushValue = (String) redisUtil.get(expiredKey + "_appPushValue");
        String[] appArr = appPushValue.split("：");
        //保存APP消息
        String noticeId = saveNotification(appPushValue, sphygmometerUser);
        pushMedicationRemindAppMsg(appPushValue, sphygmometerUser, expiredKey, redisUtil, appArr[1], noticeId);
        //推送消息至微信
        String wechatPushValue = (String) redisUtil.get(expiredKey + "_WechatPushValue");
        pushMedicationRemindWechatMsg(wechatPushValue, sphygmometerUser, sphygmometerUserRelativeService, expiredKey, redisUtil);
    }

    private void pushMedicationRemindAppMsg(String appPushValue, SphygmometerUser sphygmometerUser,
                                            String expiredKey, RedisUtil redisUtil, String remindTime, String noticeId) {
        if (!StringUtils.isEmpty(appPushValue)) {
            UserMedicationRemindService userMedicationRemindService = SpringUtils.getBean(UserMedicationRemindService.class);
            List<UserMedicationRemind> list = userMedicationRemindService.list(Wrappers.<UserMedicationRemind>lambdaQuery().
                    eq(UserMedicationRemind::getUserId, sphygmometerUser.getUserId()));
            Integer alarmClockRemind = 1;
            if (!CollectionUtils.isEmpty(list)) {
                alarmClockRemind = list.get(0).getAlarmClockRemind();
            }
            JSONObject paramJson = new JSONObject();
            paramJson.put("msgType", PushTypeEnum.MEDICATION_REMIND.getMgType());
            paramJson.put("remindTime", remindTime);
            paramJson.put("alarmClockRemind", alarmClockRemind);
            paramJson.put("noticeId", noticeId);
//            String param = "{'msgType':'" + PushTypeEnum.MEDICATION_REMIND.getMgType() + "','remindTime':'"+remindTime+"','alarmClockRemind':'"+alarmClockRemind+"'}";
            boolean pushResult = GeTuiPushUtils.pushMsgToSingle(sphygmometerUser.getGeTuiClientId(),
                    PushConstants.MEDICATION_REMIND_TITLE, appPushValue, paramJson.toString());
            logger.info("个推返回结果：" + pushResult);
            if (pushResult) {
                redisUtil.del(expiredKey + "_appPushValue");
            }
        }
    }

    //推送用药提醒微信消息
    private void pushMedicationRemindWechatMsg(String wechatPushValue, SphygmometerUser sphygmometerUser, SphygmometerUserRelativeService sphygmometerUserRelativeService, String expiredKey, RedisUtil redisUtil) {
        if (!StringUtils.isEmpty(wechatPushValue)) {
            String[] wpvArr = wechatPushValue.split("_");
            WxMpService wxMpService = SpringUtils.getBean(WxMpService.class);
            try {
                if (!StringUtils.isEmpty(sphygmometerUser.getWxOpenId())) {
                    //推送至自己微信
                    logger.info("---------templateId---------:" + PushConstants.USER_MEDICATION_REMIND_WX_TEMP_ID);
                    WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                            .toUser(sphygmometerUser.getWxOpenId())
                            .templateId(PushConstants.USER_MEDICATION_REMIND_WX_TEMP_ID)
//                            .url("https://vip.bj-ihealthcare.com")
                            .build();
                    templateMessage.addData(new WxMpTemplateData("first", String.format(PushConstants.MEDICATION_REMIND_WECHAT_FIRST, sphygmometerUser.getNickName())));
                    templateMessage.addData(new WxMpTemplateData("keyword1", "高血压用药"));
                    templateMessage.addData(new WxMpTemplateData("keyword2", getTestFrequency(wpvArr[1]) + wpvArr[0]));
                    templateMessage.addData(new WxMpTemplateData("remark", PushConstants.MEDICATION_REMIND_WECHAT_REMARK));
                    String result = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                    logger.info("微信发送结果：" + result);
                }
                //推送至家属微信
                logger.info("-------------------userId----------:" + sphygmometerUser.getUserId());
                List<SphygmometerUserRelative> list = sphygmometerUserRelativeService.list(Wrappers.<SphygmometerUserRelative>lambdaQuery().
                        eq(SphygmometerUserRelative::getUserId, sphygmometerUser.getUserId()));
                if (!CollectionUtils.isEmpty(list)) {
                    for (SphygmometerUserRelative vo : list) {
                        logger.info("-------------wxOpenId---------:" + vo.getWxOpenId());
                        WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                                .toUser(vo.getWxOpenId())
                                .templateId(PushConstants.USER_MEDICATION_REMIND_WX_TEMP_ID)
//                                .url("https://vip.bj-ihealthcare.com")
                                .build();
                        templateMessage.addData(new WxMpTemplateData("first", String.format(PushConstants.MEDICATION_REMIND_WECHAT_FIRST_RELATIVE, sphygmometerUser.getNickName())));
                        templateMessage.addData(new WxMpTemplateData("keyword1", "高血压用药"));
                        templateMessage.addData(new WxMpTemplateData("keyword2", getTestFrequency(wpvArr[1]) + wpvArr[0]));
                        templateMessage.addData(new WxMpTemplateData("remark", PushConstants.MEDICATION_REMIND_WECHAT_REMARK_RELATIVE));
                        String result = wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
                        logger.info("微信发送结果：" + result);
                    }
                }
                redisUtil.del(expiredKey + "_WechatPushValue");
            } catch (Exception ex) {
                logger.error("微信发送结果(错误)：" + ex.getMessage());
//                ex.printStackTrace();
            }
        }
    }

    private String getTestFrequency(String wechatPushValue) {
        if (StringUtils.isEmpty(wechatPushValue) || wechatPushValue.equals("1-7")) {
            return "每天";
        }
        String[] arr = wechatPushValue.split(",");
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        String testFrequency = "";
        for (int i = 0; i < arr.length; i++) {
            Integer v = Integer.valueOf(arr[i]);
            testFrequency = testFrequency + weekDays[v - 1] + ",";
        }
        return testFrequency;
    }

    /**
     * 保存消息至数据库
     *
     * @param appPushValue
     * @param sphygmometerUser
     */
    private String saveNotification(String appPushValue, SphygmometerUser sphygmometerUser) {
        //将消息保存至数据库
        AppMessageNotificationDTO appMessageNotification = new AppMessageNotificationDTO();
        appMessageNotification.setIsDelete(0);
        appMessageNotification.setStatus(1);
        appMessageNotification.setMsgType(4);
        Date currentDate = new Date();
        appMessageNotification.setPublishTime(currentDate);
        appMessageNotification.setCreateTime(currentDate);
        appMessageNotification.setUserId(sphygmometerUser.getUserId());
        appMessageNotification.setTitle(PushConstants.MEDICATION_REMIND_TITLE);
        appMessageNotification.setNoticeContent(appPushValue);
        AppMessageNotificationService appMessageNotificationService = SpringUtils.getBean(AppMessageNotificationService.class);
        appMessageNotificationService.insertAppMessageNotification(appMessageNotification);
        return appMessageNotification.getNoticeId();
    }


    private void sendWxMsg(WxMpService wxMpService, String wxOpenId, String tempId, Map<String, String> paramMap) {
        if (StringUtils.isEmpty(wxOpenId)) {
            logger.debug(">>> >>> KeyExpiredListener.sendWxMsg, but wxOpenId is empty. paramMap=[{}]", paramMap);
            return;
        }
        try {
            WxMpTemplateMessage message = WxMpTemplateMessage.builder()
                    .toUser(wxOpenId)
                    .templateId(tempId)
                    .build();
            paramMap.forEach((k, v) -> message.addData(new WxMpTemplateData(k, v)));
            logger.debug(">>> KeyExpiredListener.sendWxMsg, with message=[{}]", message.toJson());
            String result = wxMpService.getTemplateMsgService().sendTemplateMsg(message);
            logger.debug(">>> KeyExpiredListener.sendWxMsg, with result=[{}]", result);
        } catch (WxErrorException e) {
            logger.error("sendTemplateMsg throw ex, with wxOpenId=[{}] tempId=[{}] paramMap=[{}] e=[{}]", wxOpenId, tempId, paramMap, e.getMessage());
        }
    }

    private Map<String, String> getWxTempOwnerMap(String startTime, String endTime, SphygmometerUser user) {
        return new HashMap<String, String>() {{
            put("first", String.format("%s 您测量血压的时间到了！", user.getNickName()));
            put("keyword1", "血压");
            put("keyword2", String.format("%s - %s", startTime, endTime));
            put("remark", "请按时测量，祝您健康");
        }};
    }

    private Map<String, String> getWxTempRelativeMap(String startTime, String endTime, SphygmometerUser user) {
        return new HashMap<String, String>() {{
            put("first", String.format("您的家人 %s 测量血压的时间到了", user.getNickName()));
            put("keyword1", "血压");
            put("keyword2", String.format("%s - %s ", startTime, endTime));
            put("remark", "请按时测量，祝您健康");
        }};
    }

    private void pushApp(UserTestPlanRecord record, SphygmometerUser user, String appContent, UserTestPlanRecordService userTestPlanRecordService, String startTime, String endTime) {
        Integer mgType = PushTypeEnum.DETECTION_REMINDER.getMgType();
        String comment = PushTypeEnum.DETECTION_REMINDER.getComment();
        //将消息保存至数据库
        AppMessageNotificationDTO appMessageNotification = new AppMessageNotificationDTO();
        appMessageNotification.setIsDelete(0);
        appMessageNotification.setStatus(1);
        appMessageNotification.setMsgType(mgType);
        Date currentDate = new Date();
        appMessageNotification.setPublishTime(currentDate);
        appMessageNotification.setCreateTime(currentDate);
        appMessageNotification.setUserId(user.getUserId());
        appMessageNotification.setTitle(comment);
        appMessageNotification.setNoticeContent(appContent);
        AppMessageNotificationService appMessageNotificationService = SpringUtils.getBean(AppMessageNotificationService.class);
        appMessageNotificationService.insertAppMessageNotification(appMessageNotification);
//        String param = "{'msgType':'" + mgType + "','startTime':'" + startTime + "','endTime':'" + endTime + "','noticeId':'" + appMessageNotification.getNoticeId() + "'}";
        com.alibaba.fastjson.JSONObject  param = new com.alibaba.fastjson.JSONObject();
        param.put("msgType",mgType);
        param.put("startTime",startTime);
        param.put("endTime",endTime);
        param.put("noticeId",appMessageNotification.getNoticeId());
        logger.debug(">>> processUserTestPlanRecord pushApp request, param=[{}] userId=[{}] appContent=[{}]", param.toJSONString(), user.getUserId(), appContent);
        boolean pushResult = GeTuiPushUtils.pushMsgToSingle(user.getGeTuiClientId(), comment, appContent, param.toJSONString());
        logger.debug(">>> processUserTestPlanRecord pushApp, result=[{}]", JSON.toJSONString(pushResult));
        if (pushResult) {
            // 发送成功
            record.setSendMsg(1);
        } else {
            // 发送失败
            record.setSendMsg(2);
        }
        userTestPlanRecordService.updateById(record);
    }

    /**
     * 文章推送
     *
     * @param expiredKey
     */
    private void pushNews(String expiredKey) {
        String[] arr = expiredKey.split("_");
        Integer newsId = Integer.valueOf(arr[1]);
        NewsPushRecordService newsPushRecordService = SpringUtils.getBean(NewsPushRecordService.class);
        newsPushRecordService.pushNews(newsId);
    }
}
