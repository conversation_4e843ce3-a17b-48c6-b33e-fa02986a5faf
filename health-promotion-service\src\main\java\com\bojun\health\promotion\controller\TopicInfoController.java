package com.bojun.health.promotion.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.common.entity.TopicInfo;
import com.bojun.health.promotion.service.api.TopicInfoFeignClient;
import com.bojun.health.promotion.service.impl.TopicInfoServiceImpl;
import com.bojun.page.Results;
import com.bojun.utils.BeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 10:09
 */
@RestController
public class TopicInfoController extends BoJunBaseController implements TopicInfoFeignClient {

    private static Logger logger = LoggerFactory.getLogger(TopicInfoController.class);
    @Autowired
    private TopicInfoServiceImpl topicInfoService;

    @Override
    @PostMapping(value = "/getNewsTopicList")
    public Results<List<TopicInfoDTO>> getNewsTopicList(@RequestBody TopicInfoDTO topicInfo) {
        try {
            List<TopicInfoDTO> topicInfoList = topicInfoService.getTopicInfo(topicInfo);
            return Results.list(topicInfoList);
        } catch (Exception e) {
            logger.info("/getNewsTopicList", e);
            return null;
        }
    }


    @Override
    @PostMapping(value = "/addNewsTopic")
    public Integer addNewsTopic(@RequestBody TopicInfoDTO record) {
        boolean save = topicInfoService.save(record);
        return save ? 1 : -1;
    }


    @Override
    @PostMapping(value = "/updateNewsTopic")
    public Integer updateNewsTopic(@RequestBody TopicInfoDTO record) {
        return topicInfoService.updateByPrimaryKey(record);
    }

    @Override
    @PostMapping(value = "/deleteNewsTopic")
    public Integer deleteNewsTopic(@RequestBody TopicInfoDTO record) throws Exception {
        return topicInfoService.deleteByPrimaryKey(record);
    }

    @Override
    @GetMapping(value = "/getById")
    public TopicInfoDTO getById(@RequestParam("topicId") Integer topicId) {
        TopicInfo topic = topicInfoService.getById(topicId);
        return BeanUtil.deepCopyProperties(topic, TopicInfoDTO.class);
    }
}
