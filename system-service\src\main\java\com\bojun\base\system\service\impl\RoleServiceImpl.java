package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.RoleMapper;
import com.bojun.base.system.service.RoleService;
import com.bojun.system.dto.RoleDTO;
import com.bojun.system.entity.Role;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * RoleService业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-15 11:01:35
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    /**
     * 查询角色表
     *
     * @param roleId 角色表ID
     * @return 角色表
     */
    @Override
    public RoleDTO selectRoleById(String roleId) {
        return this.baseMapper.selectRoleById(roleId);
    }

    /**
     * 查询角色表列表
     *
     * @param roleDTO 角色表
     * @return 角色表集合
     */
    @Override
    public List<RoleDTO> selectRoleList(RoleDTO roleDTO) {
        return this.baseMapper.selectRoleList(roleDTO);
    }

    /**
     * 新增角色表
     *
     * @param roleDTO 角色表
     * @return 结果
     */
    @Override
    public int insertRole(RoleDTO roleDTO) {
        roleDTO.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(roleDTO);
    }

    /**
     * 修改角色表
     *
     * @param roleDTO 角色表
     * @return 结果
     */
    @Override
    public int updateRole(RoleDTO roleDTO) {
        return this.baseMapper.updateById(roleDTO);
    }

    /**
     * 新增角色表
     *
     * @param role 角色表
     * @return 结果
     */
    @Override
    public int insertRole(Role role) {
        role.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(role);
    }

    /**
     * 修改角色表
     *
     * @param role 角色表
     * @return 结果
     */
    @Override
    public int updateRole(Role role) {
        return this.baseMapper.updateById(role);
    }

    /**
     * 删除角色表对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteRoleByIds(String ids) {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除角色表信息
     *
     * @param roleId 角色表ID
     * @return 结果
     */
    @Override
    public int deleteRoleById(String roleId) {
        return this.removeById(roleId) ? 1 : 0;
    }
}
