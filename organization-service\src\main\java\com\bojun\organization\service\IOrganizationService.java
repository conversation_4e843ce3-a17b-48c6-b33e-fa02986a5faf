package com.bojun.organization.service;

import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.organization.dto.OrganizationTypeDTO;
import com.bojun.organization.entity.OrganizationInfo;
import com.github.pagehelper.Page;

import java.util.List;

/**
 * Model：机构管理
 * Description：机构管理Service
 * Author：赖水秀
 * created： 2020年5月4日
 */
public interface IOrganizationService {

    /**
	 * @Description 新增机构信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * int
	 * 2020年5月4日
	 */
	public int saveOrganizationInfo(OrganizationInfoDTO organizationInfoDTO);
	
	/**
	 * @Description 修改机构信息信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * int
	 * 2020年5月4日
	 */
	public int updateOrganizationInfo(OrganizationInfoDTO organizationInfoDTO);


	/**
	 * @Description 查询单个机构信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * OrganizationInfoDTO
	 * 2020年5月4日
	 */
	public OrganizationInfoDTO getOrganizationInfoById(Integer organizationId);


	List<OrganizationInfoDTO> getOrganizationByName(OrganizationInfoDTO organizationInfoDTO);
		
	/**
	 * @Description 分页查询
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * Page<List<OrganizationInfoDTO>>
	 * 2020年5月4日
	 */
	public Page<OrganizationInfoDTO> getOrganizationPageList(OrganizationInfoDTO organizationInfoDTO);


	Page<OrganizationInfo> getOrganizationListPage(OrganizationInfoDTO organizationInfoDTO);
	
	/**
	 * @Description 查询机构信息列表
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年5月4日
	 */
	public List<OrganizationInfoDTO> getOrganizationList(OrganizationInfoDTO organizationInfoDTO);
			
	/**
	 * @Description 根据id查询子机构列表
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年5月4日
	 */
	public List<OrganizationInfoDTO> getSubOrganizationList(Integer organizationId);
	
	/**
	 * @Description  删除机构信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * int
	 * 2020年5月4日
	 */
	public int deleteOrganizationById(Integer organizationId);
	
	/**
	 * @Description 查询机构类型数据
	 * <AUTHOR>
	 * @param classCode
	 * @return
	 * List<OrganizationTypeDTO>
	 * 2020年5月6日
	 */
	public List<OrganizationTypeDTO> getOrganizationTypeList(String classCode);

	List<OrganizationInfoDTO> getPushObject(OrganizationInfoDTO organizationInfo);
	
	
	/**
     * @description:获取医疗机构信息（二级机构、app端登录选择机构、切换机构）
     * @author: 肖泽权
     * @date: 2020/11/26
     * @Param:
     * @return:
     */
	public List<OrganizationInfoDTO> getOrgInfoForApp(OrganizationInfoDTO organizationInfo);

	OrganizationInfoDTO getPushObjectByOrgId(OrganizationInfoDTO organizationInfo);

    /**
     * @param organizationImgDTO
     * @return int
     * 2020年5月8日
     * @Description 单个删除机构图片信息
     * <AUTHOR>
     */
    public int deleteOrganizationImgById(OrganizationImgDTO organizationImgDTO);

    /**
     * @return int
     * 2020年5月8日
     * @Description 获取指定机构与角色ID下的所有科室病区
     * <AUTHOR>
     */
    OrganizationInfoDTO getPushObjectByOrgAndRoleId(OrganizationInfoDTO organizationInfo);

    List<OrganizationInfoDTO> getPushObjectByRoleId(OrganizationInfoDTO organizationInfo);

    /**
     * @return OrganizationInfoDTO
     * 2020年5月29日
     * @Description 根据条件查询机构信息
     * <AUTHOR>
     */
    OrganizationInfoDTO getOrganizationInfoByCode(String organizationCode);

    /**
     * @return OrganizationInfoDTO
     * 2020年5月29日
     * @Description
     * <AUTHOR>
     */
    List<OrganizationInfoDTO> getChilrenOrg(OrganizationInfoDTO infoDTO);

    List<OrganizationInfoDTO> getChilrenOrgByRoleId(OrganizationInfoDTO org);

	/**
	 * @description: 获取机构的下级数量（绩效统计使用）
	 * @author: 严峡华
	 * @date: 2020/7/23
	 */
	Integer getChildrenOrganizationCount(Integer organizationId);

	/**
	 * @description: 获取传参机构及其下级机构（绩效统计使用）
	 * @author: 严峡华
	 * @date: 2020/7/23
	 */
	List<OrganizationInfo> getStatisticOrganizationList(Integer organizationId);

	List<OrganizationInfoDTO> getAllDeptByOrgId(OrganizationInfoDTO org);

	List<OrganizationInfoDTO> getChildDeptByHigerDeptTransOrg(OrganizationInfoDTO dept);

	List<OrganizationInfoDTO> getAllWardByDeptIdTransOrg(Integer deptId);

	/**
	 * @description: 根据ClassCode获取其机构列表（目前绩效统计使用）
	 * @author: 严峡华
	 * @date: 2020/08/01
	 */
    List<OrganizationInfo> getOrganizationsByClassCode(String classCode);

	/**
	 * @description: 根据条件获取机构列表，返回平级集合，非树形
	 * @author: 严峡华
	 * @date: 2021/3/31
	 */
    List<OrganizationInfoDTO> getOrgListByRoleId(OrganizationInfoDTO organizationInfoDTO);
}