<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mq.mapper.SphygmometerDeviceMapper">
	
	<!-- 根据设备编号获取设备信息 -->
    <select id="getSphygmometerDeviceByDeviceNo" parameterType="String" resultType="com.bojun.sphygmometer.mq.entity.SphygmometerDevice">
		select device_id, device_no, device_type, organization_id, location,is_enabled,
		(select organization_name from organization.t_organization_info where tsd.organization_id = organization_id) organization_name
		from t_sphygmometer_device tsd
		where device_no = #{deviceNo}
    </select>
    
    <!-- 更新血压计设备在线状态 -->
    <update id="updateSphygmometerDeviceOnline">
    	UPDATE t_sphygmometer_device SET is_online = #{isOnline} WHERE device_no = #{deviceNo}
    </update>
    
    <!-- 更新血压计设备心跳 -->
    <update id="updateSphygmometerDeviceHeartbeat">
    	UPDATE t_sphygmometer_device SET heartbeat = now() WHERE device_no = #{deviceNo}
    </update>
    
    <!-- 获取设备列表 -->
    <select id="getSphygmometerDeiviceList" resultType="com.bojun.sphygmometer.mq.entity.SphygmometerDevice">
		SELECT
			tsd.device_id,
		    tsd.device_no,
		    tsd.heartbeat,
		    (select organization_name from organization.t_organization_info where tsd.organization_id = organization_id) organization_name
		FROM
		    t_sphygmometer_device tsd
		WHERE
		    tsd.device_type = 1
		ORDER BY
		    tsd.heartbeat DESC
    </select>



</mapper>