package com.bojun.export;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;

public class TxtUtilExt {
	private static File file; 
	private static FileOutputStream fos;
	private static OutputStreamWriter osw;
	private static BufferedWriter bw;
	
	public static void createFile(String fullFileName, String encode) throws IOException{
		file = new File(fullFileName);
		if(file.exists()){
			file.createNewFile();
		}
		fos = new FileOutputStream(file);
		osw = new OutputStreamWriter(fos, encode);
		bw = new BufferedWriter(osw);
	}
	
	public static void writeToFile(String text) throws IOException{
		bw.write(text);
	}
	
	public static void writeToFileLn(String text) throws IOException{
		bw.write(text);
		bw.newLine();
	}
	
	public static void closeFile() throws IOException{
		fos.flush();
		osw.flush();
		bw.flush();
		
		fos.close();
		osw.close();
		bw.close();
	}
}
