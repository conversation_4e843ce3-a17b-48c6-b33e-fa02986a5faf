package com.bojun.utils;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * Model： 求最大公约数三种方法
 * Description： 求最大公约数三种方法
 * Author：赖水秀
 * created： 2019年1月25日
 */
public class GreatestCommonDivisor {
	
	/**
     * 求最大公约数 辗转相除法(欧几里德算法)
     * @param m
     * @param n
     * @return
     */
	public static int getGCD(int m, int n) {
		int result = 0;
		while (n != 0) {
			result = m % n;
			m = n;
			n = result;
		}
		return m;		
	}
	
	/**
	     * 质因数分解法：把每个数分别分解质因数，再把各数中的全部公有质因数提取出来连乘，所得的积就是这几个数的最大公约数。	     * 
	     * @param m
	     * @param n
	     * @return
	     */
	public static int primeGCD(int m, int n) {
		int result = 1;
		Set<Integer> set1 = getFactor(m);
		Set<Integer> set2 = getFactor(n);
		// 取交集
		set1.retainAll(set2);
		// 取最大
		result = Collections.max(set1);
		return result;
	}
	
	
	/**
     * 更相减损术”,即“可半者半之，不可半者，副置分母、子之数，以少减多，更相减损，求其等也。以等数约之。”
     * @param m
     * @param n
     * @return
     */
	public static int equalGCD(int m, int n) {
		while (m != n) {
			if (m > n)
				m -= n;
			else
				n -= m;
		}
		return m;
	}
	
	 /**
     * 获取某一数值的所有因数
     * @param m
     * @return
     */
	private static Set<Integer> getFactor(int m) {
		Set<Integer> set = new HashSet<Integer>();
		for (int i = 2; i <= m; i++) {
			if (m % i == 0) {
				set.add(i);
			}
		}
		return set;
	}
	
	public static void main(String[] args) {
		//      int result = GCD(32, 48);
		//      int result = PrimeGCD(32, 48);
		System.out.println(getGCD(6, 7));
		System.out.println("~~~~~~~~~~~~~~~");
		System.out.println(primeGCD(198, 200));
		System.out.println("~~~~~~~~~~~~~~~");
		int result = equalGCD(32, 48);
		System.out.println(result);
	}

	
}
