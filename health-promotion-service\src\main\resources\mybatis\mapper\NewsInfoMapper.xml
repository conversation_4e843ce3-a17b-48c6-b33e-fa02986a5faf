<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.health.promotion.mapper.NewsInfoMapper">
  <resultMap id="BaseResultMap" type="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    <id column="news_id" jdbcType="INTEGER" property="newsId" />
    <result column="organization_id" jdbcType="INTEGER" property="organizationId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="cover_image" jdbcType="VARCHAR" property="coverImage" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="show_index" jdbcType="INTEGER" property="showIndex" />
    <result column="is_home" jdbcType="INTEGER" property="isHome" />
    <result column="home_index" jdbcType="INTEGER" property="homeIndex" />
    <result column="read_number" jdbcType="INTEGER" property="readNumber" />
    <result column="publish_type" jdbcType="INTEGER" property="publishType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="auth_status" jdbcType="INTEGER" property="authStatus" />
    <result column="auth_user_id" jdbcType="INTEGER" property="authUserId" />
    <result column="auth_time" jdbcType="TIMESTAMP" property="authTime" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="accurate_push" jdbcType="INTEGER" property="accuratePush" />
    <result column="publish_user_name" jdbcType="VARCHAR" property="publishUserName" />
    <result column="publish_user_id" jdbcType="INTEGER" property="publishUserId" />
    <result column="push_type" jdbcType="INTEGER" property="pushType" />
    <result column="push_time" jdbcType="INTEGER" property="pushTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>

  <resultMap extends="ResultMapWithBLOBs" id="QueryResultMap" type="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    <result column="is_top" jdbcType="INTEGER" property="isTop" />
    <result column="news_show_index" jdbcType="INTEGER" property="newsShowIndex" />
    <result column="person_tag_code" jdbcType="VARCHAR" property="personTag" />
    <result column="favorite_num" jdbcType="INTEGER" property="favoriteNum" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    ti.news_id,
	ti.organization_id,
	ti.title,
	ti.dept_id,
	ti.cover_image,
	ti.file_name,
	ti.show_index,
	ti.is_home,
	ti.home_index,
	ti.read_number,
	ti.publish_type,
	ti.STATUS,
	ti.auth_status,
	ti.auth_user_id,
	ti.auth_time,
	ti.publish_time,
	ti.update_time,
	ti.create_user_id,
	ti.create_time,
	ti.is_delete,
	ti.accurate_push,
	ti.publish_user_name,
	ti.publish_user_id,
	ti.push_type,
	ti.push_time,
	tpt.person_tag_code personTag
	</sql>
  <sql id="Blob_Column_List">
    ti.content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />,
    (select tgi.organization_name from system.t_manage_user tmu LEFT JOIN   organization.t_organization_info tgi on tmu.organization_id=tgi.organization_id where tmu.user_id=ti.publish_user_id) organization_name
    from t_news_info ti LEFT JOIN sphygmometer.t_news_person_tag tpt ON ti.news_id = tpt.news_id
    where ti.news_id = #{newsId,jdbcType=INTEGER}
  </select>


  <insert id="insertSelective" useGeneratedKeys="true" keyProperty="newsId" keyColumn="news_id" parameterType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    insert into t_news_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="newsId != null">
        news_id,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="coverImage != null">
        cover_image,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="showIndex != null">
        show_index,
      </if>
      <if test="isHome != null">
        is_home,
      </if>
      <if test="homeIndex != null">
        home_index,
      </if>
      <if test="readNumber != null">
        read_number,
      </if>
      <if test="publishType != null">
        publish_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="authStatus != null">
        auth_status,
      </if>
      <if test="authUserId != null">
        auth_user_id,
      </if>
      <if test="authTime != null">
        auth_time,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="accuratePush != null">
        accurate_push,
      </if>
      <if test="publishUserName != null">
        publish_user_name,
      </if>
      <if test="publishUserId != null">
        publish_user_id,
      </if>
      <if test="pushType != null">
        push_type,
      </if>
      <if test="pushTime != null">
        push_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="newsId != null">
        #{newsId,jdbcType=INTEGER},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="coverImage != null">
        #{coverImage,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="showIndex != null">
        #{showIndex,jdbcType=INTEGER},
      </if>
      <if test="isHome != null">
        #{isHome,jdbcType=INTEGER},
      </if>
      <if test="homeIndex != null">
        #{homeIndex,jdbcType=INTEGER},
      </if>
      <if test="readNumber != null">
        #{readNumber,jdbcType=INTEGER},
      </if>
      <if test="publishType != null">
        #{publishType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="authStatus != null">
        #{authStatus,jdbcType=INTEGER},
      </if>
      <if test="authUserId != null">
        #{authUserId,jdbcType=INTEGER},
      </if>
      <if test="authTime != null">
        #{authTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="isDelete != null">
            #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="accuratePush != null">
        #{accuratePush,jdbcType=INTEGER},
      </if>
      <if test="publishUserName != null">
        #{publishUserName,jdbcType=INTEGER},
      </if>
      <if test="publishUserId != null">
        #{publishUserId,jdbcType=INTEGER},
      </if>
      <if test="pushType != null">
        #{pushType,jdbcType=INTEGER},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    update t_news_info
    <set>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=INTEGER},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="coverImage != null">
        cover_image = #{coverImage,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="showIndex != null">
        show_index = #{showIndex,jdbcType=INTEGER},
      </if>
      <if test="isHome != null">
        is_home = #{isHome,jdbcType=INTEGER},
      </if>
      <if test="homeIndex != null">
        home_index = #{homeIndex,jdbcType=INTEGER},
      </if>
      <if test="readNumber != null">
        read_number = #{readNumber,jdbcType=INTEGER},
      </if>
      <if test="publishType != null">
        publish_type = #{publishType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="authStatus != null">
        auth_status = #{authStatus,jdbcType=INTEGER},
      </if>
      <if test="authUserId != null">
        auth_user_id = #{authUserId,jdbcType=INTEGER},
      </if>
      <if test="authTime != null">
        auth_time = #{authTime,jdbcType=TIMESTAMP},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="accuratePush != null">
        accurate_push = #{accuratePush,jdbcType=INTEGER},
      </if>
      <if test="publishUserName != null">
        publish_user_name = #{publishUserName,jdbcType=INTEGER},
      </if>
      <if test="publishUserId != null">
        publish_user_id = #{publishUserId,jdbcType=INTEGER},
      </if>
      <if test="pushType != null">
        push_type = #{pushType,jdbcType=INTEGER},
      </if>
      <if test="pushTime != null">
        push_time = #{pushTime,jdbcType=INTEGER},
      </if>
    </set>
    where news_id = #{newsId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    update t_news_info
    set status = 2
    where news_id = #{newsId,jdbcType=INTEGER}
  </update>

  <update id="deleteByPrimaryKey" parameterType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
    update t_news_info
    set is_delete = 1
    where news_id = #{newsId,jdbcType=INTEGER}
  </update>

   <select id="getNewsInfo" parameterType="java.lang.Integer" resultMap="QueryResultMap">
    select DISTINCT
     ti.news_id,
    ti.organization_id,
    ti.title,
    ti.dept_id,
    ti.cover_image,
    ti.file_name,
    ti.show_index,
    ti.is_home,
    ti.home_index,
    ti.read_number,
    ti.publish_type,
    ti.STATUS,
    ti.auth_status,
    ti.auth_user_id,
    ti.auth_time,
    ti.publish_time,
    ti.update_time,
    ti.create_user_id,
    ti.create_time,
    ti.is_delete,
    ti.content,
    ti.accurate_push,
    ti.publish_user_name,
    ti.publish_user_id,
     <if test="topicId != null">
    tn.is_top,
    tn.news_show_index,
     </if>
    ti.push_type,
    ti.push_time,
    IFNULL((select count(1) from sphygmometer.t_user_news_favorite where news_id = ti.news_id),0) favorite_num
     <if test="topicId == null">
       ,( SELECT GROUP_CONCAT(topic_name) FROM t_topic_info WHERE topic_id in (select tn.topic_id from t_news_topic tn where tn.news_id = ti.news_id) ) topic_name
     </if>
     from  t_news_info ti
     <if test="topicId != null">
      LEFT JOIN t_news_topic tn on tn.news_id=ti.news_id
     </if>
    where ti.is_delete = 0
     <if test="topicIdList != null and topicIdList.size() > 0">
       and tn.topic_id in
       <foreach item="item" index="index" collection="topicIdList" open="(" separator="," close=")">
         #{item}
       </foreach>
     </if>

     <if test="title != null and title != ''">
        and ti.title LIKE CONCAT('%',#{title},'%')
     </if>

     <if test="startTime != null and endTime != null">
        and ti.create_time between #{startTime} and #{endTime}
     </if>

     <if test="status != null">
        and ti.status = #{status,jdbcType=INTEGER}
     </if>
     <if test="systemId != null">
       and ti.system_id = #{systemId}
     </if>
     <if test="topicId != null">
      ORDER BY tn.is_top,tn.news_show_index,ti.update_time desc
     </if>
     <if test="topicId == null">
       ORDER BY ti.update_time desc
     </if>


     </select>

     <update id="updateByNewsId" parameterType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
       update t_news_info
       set status = 2
       where
       news_id in
       <foreach item="item" index="index" collection="newsList" open="(" separator="," close=")">
         #{item}
       </foreach>
     </update>


     <update id="updateReadNumberByNewsId" parameterType="com.bojun.health.promotion.common.dto.NewsInfoDTO">
       update t_news_info
       set read_number = read_number + 1
       where news_id = #{newsId,jdbcType=INTEGER}
     </update>

   </mapper>