package com.bojun.system.dto;

import com.bojun.system.entity.MessageNotificationSystemV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息通知关联产品表对象 t_message_notification_system_v2
 * 
 * <AUTHOR>
 * @date 2021-07-22 16:11:35
 */
@ApiModel(value = "MessageNotificationSystemV2DTO对象")
@Data
public class MessageNotificationSystemV2DTO extends MessageNotificationSystemV2 {
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
}
