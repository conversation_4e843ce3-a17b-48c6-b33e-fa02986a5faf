# 血压计管理系统 (Sphygmometer Management System)

## 项目简介

博钧血压计管理系统是一个基于Spring Cloud微服务架构的智能血压监测平台，集成了血压设备管理、用户健康数据采集、微信小程序/公众号服务、实时数据通信等功能。系统支持多种血压设备的接入，提供完整的血压筛查、数据分析和健康管理解决方案。

## 系统架构

### 技术栈
- **后端框架**: Spring Boot 2.x + Spring Cloud
- **微服务治理**: Eureka Server (服务注册与发现)
- **服务调用**: OpenFeign + Hystrix (熔断器)
- **数据库**: MySQL + MyBatis-Plus
- **缓存**: Redis (Lettuce连接池)
- **消息通信**: Netty (Socket通信) + WebSocket
- **微信集成**: 微信小程序 + 微信公众号
- **API文档**: Swagger + Swagger-Bootstrap-UI
- **连接池**: HikariCP
- **构建工具**: Maven

### 微服务架构

```
├── eureka-server              # 服务注册中心
├── sphygmometer-service       # 血压计核心业务服务
├── sphygmometer-service-api   # 血压计服务API接口
├── organization-service       # 机构管理服务
├── organization-service-api   # 机构服务API接口
├── system-service            # 系统管理服务
├── system-service-api        # 系统服务API接口
├── health-promotion-service  # 健康促进服务
└── health-promotion-service-api # 健康促进服务API接口
```

### 应用模块

```
├── sphygmometer-manage       # 后台管理系统
├── sphygmometer-app         # 移动端应用
├── sphygmometer-wxapp       # 微信小程序后端
├── sphygmometer-mp          # 微信公众号服务
├── sphygmometer-socket      # Socket通信服务
├── base-manage             # 基础管理模块
└── commons_*               # 公共组件库
```

## 核心功能模块

### 1. 血压设备管理
- **设备注册与认证**: 支持多种血压设备的接入和管理
- **设备状态监控**: 实时监控设备在线状态和工作状态
- **设备配置管理**: 设备参数配置、校准和维护
- **设备日志记录**: 完整的设备操作日志和异常记录

### 2. 血压数据采集与处理
- **实时数据采集**: 通过Socket通信实时接收设备测量数据
- **数据验证与处理**: 血压数据的有效性验证和标准化处理
- **测量结果分析**: 收缩压、舒张压、心率的智能分析
- **健康评估**: 基于测量数据的血压等级评估和健康建议

### 3. 用户管理系统
- **用户注册与认证**: 支持手机号、微信等多种注册方式
- **用户档案管理**: 完整的用户健康档案和基本信息管理
- **家庭成员管理**: 支持家庭成员关联和数据共享
- **权限管理**: 基于角色的访问控制

### 4. 微信生态集成
- **微信小程序**:
  - 用户注册登录
  - 实时血压测量
  - 历史数据查看
  - 健康报告生成
- **微信公众号**:
  - 设备绑定和扫码测量
  - 测量结果推送
  - 健康提醒服务
  - 模板消息通知

### 5. 数据统计与分析
- **测量记录管理**: 完整的血压测量历史记录
- **数据可视化**: 血压趋势图表和统计分析
- **健康报告**: 个人和群体健康分析报告
- **异常预警**: 血压异常的智能预警机制

### 6. 机构管理
- **医疗机构管理**: 医院、社区卫生服务中心等机构信息管理
- **设备分配**: 设备与机构的关联管理
- **数据权限**: 机构级别的数据访问控制
- **统计报表**: 机构维度的数据统计和分析

## 项目结构详解

### 实体模块 (Entity)
```
├── sphygmometer-entity       # 血压计相关实体类
├── organization-entity       # 机构相关实体类
├── system-entity            # 系统相关实体类
└── health-promotion-entity  # 健康促进实体类
```

### 服务模块 (Service)
```
├── sphygmometer-service     # 血压计业务逻辑实现
├── organization-service     # 机构管理业务逻辑
├── system-service          # 系统管理业务逻辑
└── health-promotion-service # 健康促进业务逻辑
```

### API接口模块 (Service API)
```
├── sphygmometer-service-api     # 血压计服务接口定义
├── organization-service-api     # 机构服务接口定义
├── system-service-api          # 系统服务接口定义
└── health-promotion-service-api # 健康促进服务接口定义
```

### 公共组件库 (Commons)
```
├── commons_encrypt    # 加密工具组件
├── commons_ip        # IP地址处理组件
├── commons_redis     # Redis缓存组件
├── commons_sms       # 短信服务组件
└── commons_utils     # 通用工具组件
```

## 通信架构

### 1. Socket通信 (sphygmometer-socket)
- **设备连接管理**: 基于Netty的高性能Socket服务器
- **协议解析**: 血压设备通信协议的解析和处理
- **数据转发**: 设备数据的实时转发和处理
- **连接监控**: 设备连接状态的实时监控

### 2. WebSocket通信
- **实时推送**: 测量结果的实时推送到前端
- **状态同步**: 设备状态和测量进度的实时同步
- **消息广播**: 系统消息和通知的实时广播

### 3. HTTP API
- **RESTful接口**: 标准的REST API设计
- **服务间调用**: 基于Feign的微服务间通信
- **负载均衡**: Ribbon负载均衡策略
- **熔断保护**: Hystrix熔断器保护

## 数据流程

### 血压测量流程
1. **用户扫码**: 用户通过微信扫描设备二维码
2. **设备绑定**: 系统验证设备有效性并建立用户-设备关联
3. **开始测量**: 用户在设备上进行血压测量
4. **数据上传**: 设备通过Socket将测量数据上传到服务器
5. **数据处理**: 系统对测量数据进行验证、分析和存储
6. **结果推送**: 通过WebSocket和微信消息推送测量结果
7. **健康评估**: 系统生成健康建议和异常预警

### 数据存储结构
- **用户数据**: 用户基本信息、健康档案
- **设备数据**: 设备信息、状态记录、配置参数
- **测量数据**: 血压记录、测量环境、设备信息
- **机构数据**: 机构信息、设备分配、权限配置
- **系统数据**: 操作日志、系统配置、统计数据

## 部署架构

### 服务端口分配
```
├── eureka-server: 1115          # 服务注册中心
├── sphygmometer-service: 8704   # 血压计核心服务
├── organization-service: 8703   # 机构管理服务
├── system-service: 8702         # 系统管理服务
├── sphygmometer-manage: 8701    # 后台管理系统
├── sphygmometer-app: 8708       # 移动端应用
├── sphygmometer-wxapp: 8709     # 微信小程序后端
├── sphygmometer-mp: 8706        # 微信公众号服务
└── sphygmometer-socket: 15002   # Socket通信服务
```

### 环境配置
- **开发环境**: application-dev.properties
- **生产环境**: application-prod.properties
- **测试环境**: application-test.properties

### 数据库配置
```sql
数据库名: sphygmometer
字符集: UTF-8
时区: GMT+8
连接池: HikariCP
```

### Redis配置
```
数据库: 0
连接池: Lettuce
最大连接数: 100
超时时间: 10000ms
```

## 快速开始

### 环境要求
- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 3.2+
- Node.js 12+ (前端开发)

### 安装步骤

1. **克隆项目**
```bash
git clone [repository-url]
cd sphygmometer-project
```

2. **数据库初始化**
```bash
# 创建数据库
CREATE DATABASE sphygmometer CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 导入数据库脚本
mysql -u username -p sphygmometer < database/init.sql
```

3. **配置文件修改**
```bash
# 修改各模块的application-dev.properties文件
# 配置数据库连接信息
# 配置Redis连接信息
# 配置微信相关参数
```

4. **启动服务**
```bash
# 1. 启动注册中心
cd eureka-server && mvn spring-boot:run

# 2. 启动核心服务
cd sphygmometer-service && mvn spring-boot:run
cd organization-service && mvn spring-boot:run
cd system-service && mvn spring-boot:run

# 3. 启动应用服务
cd sphygmometer-manage && mvn spring-boot:run
cd sphygmometer-app && mvn spring-boot:run
cd sphygmometer-wxapp && mvn spring-boot:run
cd sphygmometer-mp && mvn spring-boot:run
cd sphygmometer-socket && mvn spring-boot:run
```

5. **访问系统**
```
管理后台: http://localhost:8701/sphygmometerManage
移动端API: http://localhost:8708/sphygmometerApp
微信小程序API: http://localhost:8709/sphygmometerWxApp
API文档: http://localhost:8701/sphygmometerManage/doc.html
```

## API文档

系统集成了Swagger API文档，启动服务后可通过以下地址访问：

- **管理后台API**: http://localhost:8701/sphygmometerManage/doc.html
- **移动端API**: http://localhost:8708/sphygmometerApp/doc.html
- **微信小程序API**: http://localhost:8709/sphygmometerWxApp/doc.html

## 主要接口说明

### 血压记录相关接口
- `POST /sphygmometerRecord/list` - 查询血压记录列表
- `GET /sphygmometerRecord/getLastInfo` - 获取最新测量记录
- `GET /sphygmometerRecord/getInfo` - 获取测量记录详情
- `POST /sphygmometerRecord/count` - 统计测量记录

### 用户管理相关接口
- `POST /user/login` - 用户登录
- `POST /user/register` - 用户注册
- `GET /user/getInfo` - 获取用户信息
- `POST /user/updateInfo` - 更新用户信息

### 设备管理相关接口
- `GET /device/list` - 获取设备列表
- `GET /device/getInfo` - 获取设备详情
- `POST /device/bind` - 设备绑定
- `POST /device/unbind` - 设备解绑

### 微信相关接口
- `POST /wx/login` - 微信登录
- `POST /wx/getEncryptPhone` - 获取微信手机号
- `GET /wx/getQrcode` - 获取二维码

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用统一的代码格式化配置
- 接口和类必须添加完整的注释
- 异常处理必须规范化

### 数据库规范
- 表名使用下划线命名法
- 字段名使用驼峰命名法
- 必须包含创建时间和更新时间字段
- 软删除使用is_deleted字段

### 接口规范
- 统一使用Results包装返回结果
- 统一的异常处理机制
- 接口版本控制
- 请求参数验证

### 日志规范
- 使用SLF4J + Logback
- 分级别记录日志
- 敏感信息脱敏处理
- 异常日志必须包含堆栈信息

## 监控与运维

### 健康检查
- Spring Boot Actuator健康检查
- 数据库连接监控
- Redis连接监控
- 微服务状态监控

### 日志管理
- 应用日志按日期滚动
- 错误日志单独存储
- 日志文件大小限制
- 日志保留策略

### 性能监控
- JVM性能监控
- 数据库性能监控
- 接口响应时间监控
- 系统资源使用监控

## 常见问题

### 1. 服务启动失败
- 检查端口是否被占用
- 检查数据库连接配置
- 检查Redis连接配置
- 查看启动日志错误信息

### 2. 微服务调用失败
- 检查Eureka注册中心状态
- 检查服务是否正常注册
- 检查网络连接
- 检查Feign配置

### 3. 数据库连接问题
- 检查数据库服务状态
- 检查连接参数配置
- 检查数据库用户权限
- 检查连接池配置

### 4. Redis连接问题
- 检查Redis服务状态
- 检查Redis配置参数
- 检查网络连接
- 检查Redis密码配置

## 版本历史

### v1.0.0 (当前版本)
- 基础血压测量功能
- 微信小程序集成
- 设备管理功能
- 用户管理功能
- 数据统计分析

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: 博钧团队
- 邮箱: [<EMAIL>]
- 项目地址: [项目仓库地址]

## 致谢

感谢所有为这个项目做出贡献的开发者和测试人员。

