<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mq.mapper.ResidentBasicInfoMapper">
    
    <resultMap type="com.bojun.sphygmometer.mq.dto.ResidentBasicInfoDTO" id="ResidentBasicInfoDTOResult">
        <result property="id"    column="id"    />
        <result property="realName"    column="real_name"    />
        <result property="idNo"    column="id_no"    />
        <result property="gender"    column="gender"    />
        <result property="birthday"    column="birthday"    />
        <result property="isMarried"    column="is_married"    />
        <result property="nationCode"    column="nation_code"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="cityCode"    column="city_code"    />
        <result property="countyCode"    column="county_code"    />
        <result property="townCode"    column="town_code"    />
        <result property="villageCode"    column="village_code"    />
        <result property="liveAddress"    column="live_address"    />
        <result property="professionType"    column="profession_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="healthCode"    column="health_code"    />
        <result property="hasDocument"    column="has_document"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
    </resultMap>

    <sql id="selectResidentBasicInfo">
    	select
	        id,
	        real_name,
	        id_no,
	        gender,
	        birthday,
	        is_married,
	        nation_code,
	        province_code,
	        city_code,
	        county_code,
	        town_code,
	        village_code,
	        live_address,
	        profession_type,
	        create_time,
	        health_code,
	        has_document,
	        create_user_id,
	        create_user_name,
	        update_time,
	        update_user_id,
	        update_user_name
		from 
        	t_resident_basic_info
    </sql>

    <select id="selectResidentBasicInfoById" parameterType="string" resultMap="ResidentBasicInfoDTOResult">
		<include refid="selectResidentBasicInfo"/>
		where 
        	id = #{id}
    </select>

    <select id="selectResidentBasicInfoList" parameterType="com.bojun.sphygmometer.mq.dto.ResidentBasicInfoDTO" resultMap="ResidentBasicInfoDTOResult">
        <include refid="selectResidentBasicInfo"/>
        <where>  
		<if test="id != null  and id != ''"> and id = #{id}</if>
		<if test="realName != null  and realName != ''"> and real_name = #{realName}</if>
		<if test="idNo != null  and idNo != ''"> and id_no = #{idNo}</if>
		<if test="gender != null "> and gender = #{gender}</if>
		<if test="birthday != null "> and birthday = #{birthday}</if>
		<if test="isMarried != null "> and is_married = #{isMarried}</if>
		<if test="nationCode != null  and nationCode != ''"> and nation_code = #{nationCode}</if>
		<if test="provinceCode != null  and provinceCode != ''"> and province_code = #{provinceCode}</if>
		<if test="cityCode != null  and cityCode != ''"> and city_code = #{cityCode}</if>
		<if test="countyCode != null  and countyCode != ''"> and county_code = #{countyCode}</if>
		<if test="townCode != null  and townCode != ''"> and town_code = #{townCode}</if>
		<if test="villageCode != null  and villageCode != ''"> and village_code = #{villageCode}</if>
		<if test="liveAddress != null  and liveAddress != ''"> and live_address = #{liveAddress}</if>
		<if test="professionType != null  and professionType != ''"> and profession_type = #{professionType}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="healthCode != null  and healthCode != ''"> and health_code = #{healthCode}</if>
		<if test="hasDocument != null "> and has_document = #{hasDocument}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createUserName != null  and createUserName != ''"> and create_user_name = #{createUserName}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateUserName != null  and updateUserName != ''"> and update_user_name = #{updateUserName}</if>
        </where>
    </select>

</mapper>