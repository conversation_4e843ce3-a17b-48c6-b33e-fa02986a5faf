package com.bojun.system.dto;

import com.bojun.system.entity.MessageNotificationReceiverV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 消息通知接收人信息表对象 t_message_notification_receiver
 * 
 * <AUTHOR>
 * @date 2021-06-07 15:53:16
 */
@ApiModel(value = "MessageNotificationReceiverDTO对象")
@Data
public class MessageNotificationReceiverV2DTO extends MessageNotificationReceiverV2
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
    @ApiModelProperty(value = "通知id集合", example = "")
    private List<String> noticeIdList;
}
