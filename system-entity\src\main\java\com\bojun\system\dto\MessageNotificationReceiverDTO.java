package com.bojun.system.dto;

import com.bojun.system.entity.MessageNotificationReceiver;
/**
 * 
*Model：消息通知接收人信息表
*Description：消息通知接收人信息表DTO
*Author:刘修纬
*created：2020年1月7日
 */
public class MessageNotificationReceiverDTO  extends MessageNotificationReceiver {

   private static final long serialVersionUID = -1344026328907379635L;

   private String deleteTimeStr;
   private String readTimeStr;
   private String receiveTimeStr;
   private String createUserName; // 发布人用户名称
   private String noticeContent; // 消息内容
   private String noticeTypeId; // 消息通知类型id
   private String synchronizationPlatform; // 同步平台  1：全部  2：OA系统  3：账务系统  4：邮件系统  5：微信公众号 6：网站
   private String fileName; // 附近文件名
   private String fileUrl; // 附近文件地址
   private Integer isStatistics; // 是否统计阅读量
   private String noticeTypeName;//类型名称
   private String publishTimeStr;
   private String title;//标题
   

public String getFileUrl() {
	return fileUrl;
}
public void setFileUrl(String fileUrl) {
	this.fileUrl = fileUrl;
}
public String getTitle() {
	return title;
}
public void setTitle(String title) {
	this.title = title;
}
public String getPublishTimeStr() {
	return publishTimeStr;
}
public void setPublishTimeStr(String publishTimeStr) {
	this.publishTimeStr = publishTimeStr;
}
public String getCreateUserName() {
	return createUserName;
}
public void setCreateUserName(String createUserName) {
	this.createUserName = createUserName;
}
public String getNoticeContent() {
	return noticeContent;
}
public void setNoticeContent(String noticeContent) {
	this.noticeContent = noticeContent;
}
public String getNoticeTypeId() {
	return noticeTypeId;
}
public void setNoticeTypeId(String noticeTypeId) {
	this.noticeTypeId = noticeTypeId;
}
public String getSynchronizationPlatform() {
	return synchronizationPlatform;
}
public void setSynchronizationPlatform(String synchronizationPlatform) {
	this.synchronizationPlatform = synchronizationPlatform;
}
public String getFileName() {
	return fileName;
}
public void setFileName(String fileName) {
	this.fileName = fileName;
}
public Integer getIsStatistics() {
	return isStatistics;
}
public void setIsStatistics(Integer isStatistics) {
	this.isStatistics = isStatistics;
}
public String getNoticeTypeName() {
	return noticeTypeName;
}
public void setNoticeTypeName(String noticeTypeName) {
	this.noticeTypeName = noticeTypeName;
}
public String getDeleteTimeStr() {
	return deleteTimeStr;
}
public void setDeleteTimeStr(String deleteTimeStr) {
	this.deleteTimeStr = deleteTimeStr;
}
public String getReadTimeStr() {
	return readTimeStr;
}
public void setReadTimeStr(String readTimeStr) {
	this.readTimeStr = readTimeStr;
}
public String getReceiveTimeStr() {
	return receiveTimeStr;
}
public void setReceiveTimeStr(String receiveTimeStr) {
	this.receiveTimeStr = receiveTimeStr;
}

}
