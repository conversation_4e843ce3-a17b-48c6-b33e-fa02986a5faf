package com.bojun.base.system.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import com.bojun.base.system.mapper.ManageUserMemorandumMapper;
import com.bojun.base.system.service.ManageUserMemorandumService;
import com.bojun.system.dto.ManageUserMemorandumDTO;
import com.github.pagehelper.PageHelper;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月29日
*/
@Service
public class ManageUserMemorandumServiceImpl implements ManageUserMemorandumService{
	
	@Autowired
	ManageUserMemorandumMapper manageUserMemorandumMapper;
	
	
	/**
	 * 
	 * @Description 查询消息通知（站内）信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<ManageUserMemorandumDTO>
	 */
	public List<ManageUserMemorandumDTO> getManageUserMemorandum(@RequestBody Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<ManageUserMemorandumDTO> resList = manageUserMemorandumMapper.getManageUserMemorandum(mapPara);
		return resList;
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer queryManageUserMemorandumCount(@RequestBody Map<String, Object> paramsMap) {
		return (int) manageUserMemorandumMapper.queryManageUserMemorandumCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer addManageUserMemorandum(@RequestBody ManageUserMemorandumDTO manageUserMemorandumDTO) {
		return manageUserMemorandumMapper.addManageUserMemorandum(manageUserMemorandumDTO);
	}
	/**
	 * 
	 * @Description 删除消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	public int deleteManageUserMemorandum(@RequestBody Map<String, Object> paramsMap) {
		return manageUserMemorandumMapper.deleteManageUserMemorandum(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	public int updateManageUserMemorandum(@RequestBody ManageUserMemorandumDTO manageUserMemorandumDTO) {
		return manageUserMemorandumMapper.updateManageUserMemorandum(manageUserMemorandumDTO);
	}
	/**
	 * 
	 * @Description 查询单个消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return ManageUserMemorandumDTO
	 */
	public ManageUserMemorandumDTO getManageUserMemorandumById(@RequestBody Map<String, Object> paramsMap) {
		return manageUserMemorandumMapper.getManageUserMemorandumById(paramsMap);
	}

}

