package com.bojun.base.manage.controller.notice.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 15:17
 */
@ApiModel(value = "添加通知通告", description = "添加通知通告")
public class AddNoticeVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;

    @ApiModelProperty(value = "是否立即发送 1是  0否")
    private int isImmediately;
    
    @ApiModelProperty(value = "定时发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timingTime;
    
    @ApiModelProperty(value = "状态：1：已发布 2：待发布  3：已撤回   4：重发")
    private int status;
    
    @ApiModelProperty(value = "推送内容")
    private String noticeContent;
    
    @ApiModelProperty(value = "通知类型")
    private String noticeTypeId;
    
    @ApiModelProperty(value = "通知标题")
    private String title;
    
    @ApiModelProperty(value = "推送产品id", example = "1,2,3")
    private String synchronizationPlatform;
    
    @ApiModelProperty(value = "接收部门（多个逗号隔开）")
    private List<ObjectVO> objectVO;


    public List<ObjectVO> getObjectVO() {
        return objectVO;
    }

    public void setObjectVO(List<ObjectVO> objectVO) {
        this.objectVO = objectVO;
    }

    public int getIsImmediately() {
        return isImmediately;
    }

    public void setIsImmediately(int isImmediately) {
        this.isImmediately = isImmediately;
    }

    public Date getTimingTime() {
        return timingTime;
    }

    public void setTimingTime(Date timingTime) {
        this.timingTime = timingTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getNoticeTypeId() {
        return noticeTypeId;
    }

    public void setNoticeTypeId(String noticeTypeId) {
        this.noticeTypeId = noticeTypeId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

	public String getSynchronizationPlatform() {
		return synchronizationPlatform;
	}

	public void setSynchronizationPlatform(String synchronizationPlatform) {
		this.synchronizationPlatform = synchronizationPlatform;
	}

}
