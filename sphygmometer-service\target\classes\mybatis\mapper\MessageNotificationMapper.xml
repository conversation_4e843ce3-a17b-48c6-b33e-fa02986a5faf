<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.MessageNotificationMapper">

    <resultMap type="com.bojun.sphygmometer.dto.MessageNotificationDTO" id="MessageNotificationDTOResult">
        <result property="noticeId" column="notice_id"/>
        <result property="title" column="title"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="noticeTypeId" column="notice_type_id"/>
        <result property="synchronizationPlatform" column="synchronization_platform"/>
        <result property="fileName" column="file_name"/>
        <result property="receiveDeptId" column="receive_dept_id"/>
        <result property="receiveUserId" column="receive_user_id"/>
        <result property="isImmediately" column="is_immediately"/>
        <result property="isStatistics" column="is_statistics"/>
        <result property="timingTime" column="timing_time"/>
        <result property="status" column="status"/>
        <result property="isDelete" column="is_delete"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="publishTime" column="publish_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createTime" column="create_time"/>
        <result property="isRead" column="is_read"/>
    </resultMap>

    <sql id="selectMessageNotification">
    	select
	        notice_id,
	        title,
	        notice_content,
	        notice_type_id,
	        synchronization_platform,
	        file_name,
	        receive_dept_id,
	        receive_user_id,
	        is_immediately,
	        is_statistics,
	        timing_time,
	        status,
	        is_delete,
	        delete_time,
	        publish_time,
	        create_user_id,
	        create_time,
	        is_read
		from 
        	system.t_message_notification
    </sql>

    <select id="selectMessageNotificationById" parameterType="string" resultMap="MessageNotificationDTOResult">
        <include refid="selectMessageNotification"/>
        where
        notice_id = #{noticeId}
    </select>

    <select id="selectMessageNotificationList" parameterType="com.bojun.sphygmometer.dto.MessageNotificationDTO"
            resultMap="MessageNotificationDTOResult">
        select
			tmn.notice_id,
			tmn.title,
			tmn.notice_content,
			tmn.notice_type_id,
			tmn.synchronization_platform,
			tmn.file_name,
			tmn.receive_dept_id,
			tmn.receive_user_id,
			tmn.is_immediately,
			tmn.is_statistics,
			tmn.timing_time,
			tmn.status,
			tmn.is_delete,
			tmn.delete_time,
			tmn.publish_time,
			tmn.create_user_id,
			tmn.create_time,
			tmn.is_read
        from
            system.t_message_notification tmn
        left join system.t_message_notification_system tmns on tmns.notice_id = tmn.notice_id
        <where>
            <if test="noticeId != null  and noticeId != ''">and tmn.notice_id = #{noticeId}</if>
            <if test="title != null  and title != ''">and tmn.title = #{title}</if>
            <if test="noticeContent != null  and noticeContent != ''">and tmn.notice_content = #{noticeContent}</if>
            <if test="noticeTypeId != null  and noticeTypeId != ''">and tmn.notice_type_id = #{noticeTypeId}</if>
            <if test="synchronizationPlatform != null  and synchronizationPlatform != ''">and tmn.synchronization_platform =
                #{synchronizationPlatform}
            </if>
            <if test="fileName != null  and fileName != ''">and tmn.file_name = #{fileName}</if>
            <if test="receiveDeptId != null  and receiveDeptId != ''">and tmn.receive_dept_id = #{receiveDeptId}</if>
            <if test="receiveUserId != null  and receiveUserId != ''">and tmn.receive_user_id = #{receiveUserId}</if>
            <if test="isImmediately != null ">and tmn.is_immediately = #{isImmediately}</if>
            <if test="isStatistics != null ">and tmn.is_statistics = #{isStatistics}</if>
            <if test="timingTime != null ">and tmn.timing_time = #{timingTime}</if>
            <if test="status != null ">and tmn.status = #{status}</if>
            <if test="isDelete != null ">and tmn.is_delete = #{isDelete}</if>
            <if test="deleteTime != null ">and tmn.delete_time = #{deleteTime}</if>
            <if test="publishTime != null ">and tmn.publish_time = #{publishTime}</if>
            <if test="createUserId != null ">and tmn.create_user_id = #{createUserId}</if>
            <if test="createTime != null ">and tmn.create_time = #{createTime}</if>
            <if test="isRead != null  and isRead != ''">and tmn.is_read = #{isRead}</if>
			<if test="systemId != null  and systemId != ''">and tmns.system_id = #{systemId}</if>
        </where>
        <if test="startTime != null and endTime != null">
            and tmn.create_time between #{startTime} and #{endTime}
        </if>
    </select>

</mapper>