package com.bojun.system.entity;

import java.io.Serializable;

/**
 * 
*Model：管理员备忘录信息表
*Description：管理员备忘录信息表实体类
*Author:刘修纬
*created：2020年3月21日
 */
public class ManageUserMemorandum implements Serializable {

   private static final long serialVersionUID = -1344026328907379635L;


      private Integer id; // 自增主键id
      private Integer userId; // 用户id
      private String systemId; // 系统id
      private String content; // 内容
      private java.sql.Date memoDate; // 备忘日期
      private Integer isDelete; // 是否删除
      private java.sql.Date deleteTime; // 删除时间
      private java.sql.Date createTime; // 创建时间
    
      
    
    public Integer getId()
    {
        return id;
    }
    
    public void setId(Integer id)
    {
        this.id = id;
    }   	
      
    
    public Integer getUserId()
    {
        return userId;
    }
    
    public void setUserId(Integer userId)
    {
        this.userId = userId;
    }   	
      
    
    public String getSystemId()
    {
        return systemId;
    }
    
    public void setSystemId(String systemId)
    {
        this.systemId = systemId;
    }   	
      
    
    public String getContent()
    {
        return content;
    }
    
    public void setContent(String content)
    {
        this.content = content;
    }   	
      
    
    public java.sql.Date getMemoDate()
    {
        return memoDate;
    }
    
    public void setMemoDate(java.sql.Date memoDate)
    {
        this.memoDate = memoDate;
    }   	
      
    
    public Integer getIsDelete()
    {
        return isDelete;
    }
    
    public void setIsDelete(Integer isDelete)
    {
        this.isDelete = isDelete;
    }   	
      
    
    public java.sql.Date getDeleteTime()
    {
        return deleteTime;
    }
    
    public void setDeleteTime(java.sql.Date deleteTime)
    {
        this.deleteTime = deleteTime;
    }   	
      
    
    public java.sql.Date getCreateTime()
    {
        return createTime;
    }
    
    public void setCreateTime(java.sql.Date createTime)
    {
        this.createTime = createTime;
    }   	
    

}
