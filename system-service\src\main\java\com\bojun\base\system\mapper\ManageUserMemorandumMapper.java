
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.ManageUserMemorandumDTO;




/**
 * 
*Model：消息通知（站内）
*Description：消息通知（站内）
*Author:李欣颖
*created：2020年1月7日
 */
@Mapper
public interface ManageUserMemorandumMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	long queryManageUserMemorandumCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询消息通知（站内）信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<ManageUserMemorandumDTO>
	 */
	List<ManageUserMemorandumDTO> getManageUserMemorandum(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	Integer addManageUserMemorandum(ManageUserMemorandumDTO manageUserMemorandumDTO);

	/**
	 * 
	 * @Description 删除消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	Integer deleteManageUserMemorandum(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改消息通知（站内）信息
	 * <AUTHOR>
	 * @param manageUserMemorandumDTO
	 * @return
	 * @return int
	 */
	Integer updateManageUserMemorandum(ManageUserMemorandumDTO manageUserMemorandumDTO);

	/**
	 * 
	 * @Description 查询单个消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return ManageUserMemorandumDTO
	 */
	ManageUserMemorandumDTO getManageUserMemorandumById(Map<String, Object> mapPara);


	
}
