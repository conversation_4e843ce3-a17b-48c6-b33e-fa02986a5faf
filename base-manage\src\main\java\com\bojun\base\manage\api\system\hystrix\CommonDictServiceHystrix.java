/**
 * 
 */
package com.bojun.base.manage.api.system.hystrix;


import org.springframework.stereotype.Component;

import com.bojun.base.manage.api.system.ICommonDictService;
import com.bojun.exception.BaseRuntimeException;

/**
 * Model： 公共字典接口熔断器 
 * Description：公共字典接口熔断器 
 * Author：赖水秀
 * created： 2020年5月7日
 */
@Component
public class CommonDictServiceHystrix implements ICommonDictService {

	@Override
	public String getProvinceList() {
		throw new BaseRuntimeException("getProvinceList接口服务已断开");
	}

	@Override
	public String getCityList(String provinceCode) {
		throw new BaseRuntimeException("getProvinceList接口服务已断开");
	}

	@Override
	public String getCountyList(String cityCode) {
		throw new BaseRuntimeException("getProvinceList接口服务已断开");
	}

	@Override
	public String getTownList(String countyCode) {
		throw new BaseRuntimeException("getTownList接口服务已断开");
	}

	@Override
	public String getVillageList(String townCode) {
		throw new BaseRuntimeException("getVillageList接口服务已断开");
	}
	
}
