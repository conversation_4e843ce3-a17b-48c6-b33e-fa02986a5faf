package com.bojun.base.manage.controller.organization.vo.ward;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/4 19:35
 */
@ApiModel(value = "查询病区", description = "查询病区")
public class GetWardManagerVo extends AddWardManagerVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "关键字")
    private String keyWord;
    @ApiModelProperty(value = "病区ID,单查时用")
    private Integer wardId;
    @ApiModelProperty(value = "页数")
    @NotEmpty
    private int pageNum;
    @ApiModelProperty(value = "每页条数")
    @NotEmpty
    private int everyPage;
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;
    @ApiModelProperty(value = "科室名称")
    private String deptName;
    @ApiModelProperty(value = "姓名")
    private String realName;
    @ApiModelProperty(value = "工号")
    private String workNumber;
    @ApiModelProperty(value = "员工Id")
    private String employeeId;
    @ApiModelProperty(value = "结构ID")
    private Integer organizationId;
    private String planeImageUrl;

    public String getPlaneImageUrl() {
        return planeImageUrl;
    }

    public void setPlaneImageUrl(String planeImageUrl) {
        this.planeImageUrl = planeImageUrl;
    }

    @Override
    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getEveryPage() {
        return everyPage;
    }

    public void setEveryPage(int everyPage) {
        this.everyPage = everyPage;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }


    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

}
