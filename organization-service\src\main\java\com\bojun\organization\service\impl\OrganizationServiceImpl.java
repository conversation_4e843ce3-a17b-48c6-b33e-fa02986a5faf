/**
 * 
 */
package com.bojun.organization.service.impl;

import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.organization.dto.OrganizationTypeDTO;
import com.bojun.organization.entity.OrganizationInfo;
import com.bojun.organization.mapper.OrganizationInfoMapper;
import com.bojun.organization.service.IOrganizationService;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Model：机构管理模块服务实现类
 * Description：机构管理模块服务实现类
 * Author：赖水秀
 * created： 2020年4月28日
 */
@Service
public class OrganizationServiceImpl implements IOrganizationService {
	
	@Autowired
	private OrganizationInfoMapper organizationInfoMapper;
	
	/**
	 * @Description 新增机构信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * int
	 * 2020年5月4日
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int saveOrganizationInfo(OrganizationInfoDTO organizationInfoDTO) {
		//getOrganizationInfoById
		//获取上级ID
		Integer parentId = organizationInfoDTO.getParentId();
		if (null==parentId) {
			organizationInfoDTO.setWeight(1);
		}
		else {
			OrganizationInfoDTO organizationInfoById = organizationInfoMapper.getOrganizationInfoById(parentId);
			organizationInfoDTO.setWeight(organizationInfoById.getWeight()==null?1:organizationInfoById.getWeight()+1);
		}
		//图片list
		List<OrganizationImgDTO> imgList = organizationInfoDTO.getImgList();
		int addOrganizationId = organizationInfoMapper.saveOrganizationInfo(organizationInfoDTO);		
		if(imgList != null && imgList.size() > 0){
			for (OrganizationImgDTO organizationImgDTO : imgList) {
				organizationImgDTO.setOrganizationId(organizationInfoDTO.getOrganizationId());
			}
			organizationInfoMapper.batchSaveOrganizationImg(imgList);
		}

		return addOrganizationId;
	}
	
	/**
	 * @Description 修改机构信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * int
	 * 2020年5月4日
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int updateOrganizationInfo(OrganizationInfoDTO organizationInfoDTO) {		
		//图片list
		List<OrganizationImgDTO> imgList = organizationInfoDTO.getImgList();
		int updateOrgNum = organizationInfoMapper.updateOrganizationInfo(organizationInfoDTO);
		organizationInfoMapper.deleteOrganizationImg(organizationInfoDTO.getOrganizationId());
		if(imgList != null && imgList.size() > 0){
			for (OrganizationImgDTO organizationImgDTO : imgList) {
				organizationImgDTO.setOrganizationId(organizationInfoDTO.getOrganizationId());
			}
			organizationInfoMapper.batchSaveOrganizationImg(imgList);
		}
		return updateOrgNum;
	}
	
	/**
	 * @Description 查询单个机构信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * OrganizationInfoDTO
	 * 2020年5月4日
	 */
	@Override
	public OrganizationInfoDTO getOrganizationInfoById(Integer organizationId) {
		return organizationInfoMapper.getOrganizationInfoById(organizationId);
	}


	@Override
	public List<OrganizationInfoDTO> getOrganizationByName(OrganizationInfoDTO organizationInfoDTO) {
		return organizationInfoMapper.getOrganizationByName(organizationInfoDTO);
	}


	/**
	 * @Description 分页查询
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * Page<List<OrganizationInfoDTO>>
	 * 2020年5月4日
	 */
	@Override
	public Page<OrganizationInfoDTO> getOrganizationPageList(OrganizationInfoDTO organizationInfoDTO) {		
		return organizationInfoMapper.getOrganizationPageList(organizationInfoDTO);
	}


	@Override
	public Page<OrganizationInfo> getOrganizationListPage(OrganizationInfoDTO organizationInfoDTO) {
		return organizationInfoMapper.getOrganizationListPage(organizationInfoDTO);
	}


	/**
	 * @Description 查询机构信息列表
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年5月4日
	 */
	@Override
	public List<OrganizationInfoDTO> getOrganizationList(OrganizationInfoDTO organizationInfoDTO) {
		return organizationInfoMapper.getOrganizationList(organizationInfoDTO);
	}
	
	/**
	 * @Description 根据id查询子机构列表
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年5月4日
	 */
	@Override
	public List<OrganizationInfoDTO> getSubOrganizationList(Integer organizationId) {
		return organizationInfoMapper.getSubOrganizationList(organizationId);
	}
	
	/**
	 * @Description  删除机构信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * int
	 * 2020年5月4日
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int deleteOrganizationById(Integer organizationId) {
		int deleteOrgNum = organizationInfoMapper.deleteOrganizationById(organizationId);
		organizationInfoMapper.deleteOrganizationImg(organizationId);
		return deleteOrgNum;
	}
	
	/**
	 * @Description  查询机构类型数据
	 * <AUTHOR>
	 * @param classCode
	 * @return
	 * int
	 * 2020年5月6日
	 */
	@Override
	public List<OrganizationTypeDTO> getOrganizationTypeList(String classCode) {		
		return organizationInfoMapper.getOrganizationTypeList(classCode);
	}
	
	/**
	 * @param
	 * @param organizationInfo
	 * @description: 获取推送对象
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	@Override
	public List<OrganizationInfoDTO> getPushObject(OrganizationInfoDTO organizationInfo) {
		return organizationInfoMapper.getPushObject(organizationInfo);
	}

	@Override
	public OrganizationInfoDTO getPushObjectByOrgId(OrganizationInfoDTO organizationInfo) {
		return organizationInfoMapper.getPushObjectByOrgId(organizationInfo);
	}
	
    /**
	 * @Description  单个删除机构图片信息
	 * <AUTHOR>
	 * @param organizationImgDTO
	 * @return
	 * int
	 * 2020年5月8日
	 */
	@Override
	public int deleteOrganizationImgById(OrganizationImgDTO organizationImgDTO) {
		return organizationInfoMapper.deleteOrganizationImgById(organizationImgDTO);
	}

	@Override
	public OrganizationInfoDTO getPushObjectByOrgAndRoleId(OrganizationInfoDTO organizationInfo) {
		return organizationInfoMapper.getPushObjectByOrgAndRoleId(organizationInfo);
	}

	@Override
	public List<OrganizationInfoDTO> getPushObjectByRoleId(OrganizationInfoDTO organizationInfo) {
		return null;
	}


	/**
     * @return OrganizationInfoDTO
     * 2020年5月29日
     * @Description 根据条件查询机构信息
     * <AUTHOR>
     */
    @Override
    public OrganizationInfoDTO getOrganizationInfoByCode(String organizationCode) {
        return organizationInfoMapper.getOrganizationInfoByCode(organizationCode);
    }

	@Override
	public List<OrganizationInfoDTO> getChilrenOrg(OrganizationInfoDTO infoDTO) {
        return organizationInfoMapper.getChilrenOrg(infoDTO);
    }

	@Override
	public List<OrganizationInfoDTO> getChilrenOrgByRoleId(OrganizationInfoDTO org) {
        return organizationInfoMapper.getChilrenOrgByRoleId(org);
    }

	@Override
	public Integer getChildrenOrganizationCount(Integer organizationId) {
		return this.organizationInfoMapper.getChildrenOrganizationCount(organizationId);
	}

	@Override
	public List<OrganizationInfo> getStatisticOrganizationList(Integer organizationId) {
		return this.organizationInfoMapper.getStatisticOrganizationList(organizationId);
	}

	@Override
	public List<OrganizationInfoDTO> getAllDeptByOrgId(OrganizationInfoDTO org) {
		return this.organizationInfoMapper.getAllDeptByOrgId(org);
	}

	@Override
	public List<OrganizationInfoDTO> getChildDeptByHigerDeptTransOrg(OrganizationInfoDTO dept) {
		return this.organizationInfoMapper.getChildDeptByHigerDeptTransOrg(dept);
	}

	@Override
	public List<OrganizationInfoDTO> getAllWardByDeptIdTransOrg(Integer deptId) {
		return this.organizationInfoMapper.getAllWardByDeptIdTransOrg(deptId);
	}

	/**
	 * @description: 根据ClassCode获取其机构列表（目前绩效统计使用）
	 * @author: 严峡华
	 * @date: 2020/08/01
	 */
	@Override
	public List<OrganizationInfo> getOrganizationsByClassCode(String classCode) {
		return this.organizationInfoMapper.getOrganizationsByClassCode(classCode);
	}

	/**
	 * @description: 根据条件获取机构列表，返回平级集合，非树形
	 * @author: 严峡华
	 * @date: 2021/3/31
	 */
	@Override
	public List<OrganizationInfoDTO> getOrgListByRoleId(OrganizationInfoDTO organizationInfoDTO) {
		return this.organizationInfoMapper.getOrgListByRoleId(organizationInfoDTO);
	}


	/**
     * @description:获取医疗机构信息（二级机构、app端登录选择机构、切换机构）
     * @author: 肖泽权
     * @date: 2020/11/26
     * @Param:
     * @return:
     */
	public List<OrganizationInfoDTO> getOrgInfoForApp(OrganizationInfoDTO organizationInfo){
		return organizationInfoMapper.getOrgInfoForApp(organizationInfo);
	}
	
	
}
