spring.application.name=sphygmometer-app
spring.profiles.active=dev
server.servlet.context-path=/sphygmometerApp

feign.okhttp.enabled=true
feign.hystrix.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=6000

ribbon.ReadTimeout=5000
ribbon.ConnectTimeout=3000

#reids#
spring.redis.database=0
spring.redis.lettuce.pool.max-active=100
spring.redis.lettuce.pool.max-wait=-1
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=0
spring.redis.timeout=10000

spring.jackson.time-zone=GMT+8

#swagger2 config#
swagger.project.title=\u9AD8\u8840\u538BAPP\u7CFB\u7EDF
swagger.project.description=\u9AD8\u8840\u538BAPP\u7CFB\u7EDF\u63A5\u53E3\u6587\u6863
swagger.project.groupname=1.0
swagger.project.version=1.0
swagger.project.base.package=com.bojun
swagger.project.base.url=http\://localhost\:8892/sphygmometerApp