# Aspose依赖问题解决方案

## 问题描述
```
Dependency 'com.aspose:aspose-words:14.9.0' not found
```

## ✅ 已实施的解决方案

### 1. 注释掉Aspose依赖
已将所有Aspose依赖注释掉，因为：
- Aspose是商业软件，需要购买许可证
- 不在公共Maven仓库中
- 需要从官方网站下载或购买

### 2. 添加Apache POI替代方案
已添加Apache POI作为开源替代方案：

```xml
<!-- 替代方案：使用Apache POI处理Office文档 -->
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-scratchpad</artifactId>
    <version>4.1.2</version>
</dependency>
<dependency>
    <groupId>org.apache.poi</groupId>
    <artifactId>poi-ooxml-schemas</artifactId>
    <version>4.1.2</version>
</dependency>
```

## 功能对比

| 功能 | Aspose | Apache POI |
|------|--------|------------|
| Word文档处理 | ✅ 强大 | ✅ 基础功能 |
| Excel文档处理 | ✅ 强大 | ✅ 完善 |
| PowerPoint处理 | ✅ 强大 | ✅ 基础功能 |
| 许可证 | 💰 商业 | ✅ 免费开源 |
| 文档转换 | ✅ 强大 | ⚠️ 有限 |

## 如果需要使用Aspose

### 方案1：购买许可证并手动安装
```bash
# 1. 从Aspose官网下载jar包
# 2. 安装到本地Maven仓库
mvn install:install-file -Dfile=aspose-words-14.9.0.jar \
    -DgroupId=com.aspose \
    -DartifactId=aspose-words \
    -Dversion=14.9.0 \
    -Dpackaging=jar
```

### 方案2：使用Aspose官方仓库
```xml
<repositories>
    <repository>
        <id>aspose-maven-repository</id>
        <url>https://repository.aspose.com/repo/</url>
    </repository>
</repositories>
```

### 方案3：使用最新版本
```xml
<dependency>
    <groupId>com.aspose</groupId>
    <artifactId>aspose-words</artifactId>
    <version>24.10</version>
</dependency>
```

## 代码适配建议

### 1. 使用条件编译
```java
@ConditionalOnClass(name = "com.aspose.words.Document")
@Component
public class AsposeDocumentService {
    // Aspose相关代码
}

@ConditionalOnMissingClass("com.aspose.words.Document")
@Component
public class PoiDocumentService {
    // Apache POI相关代码
}
```

### 2. 使用配置开关
```properties
# application.properties
document.processor=poi  # 或 aspose
```

```java
@Value("${document.processor:poi}")
private String documentProcessor;

public DocumentService getDocumentService() {
    if ("aspose".equals(documentProcessor)) {
        return asposeDocumentService;
    }
    return poiDocumentService;
}
```

## 验证解决方案

执行以下命令验证问题是否解决：
```bash
# 清理并重新构建commons_utils模块
cd commons_utils
mvn clean compile

# 如果成功，构建整个项目
cd ..
mvn clean compile
```

## 总结

✅ **推荐方案**: 使用Apache POI替代Aspose
- 免费开源
- 功能基本满足需求
- 社区支持良好
- 无许可证问题

⚠️ **如果必须使用Aspose**: 需要购买许可证并手动安装jar包
