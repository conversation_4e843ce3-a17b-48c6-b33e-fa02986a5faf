package com.bojun.push;

import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import cn.jpush.api.schedule.ScheduleResult;
import cn.jpush.api.schedule.model.SchedulePayload;
import cn.jpush.api.schedule.model.TriggerPayload;
import com.bojun.utils.PropertiesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description // 极光推送工具类
 * @Date 2020/11/13 15:34
 * @Param
 * @return
 **/
public class JGPushUtils {
    protected static final Logger logger = LoggerFactory.getLogger(JGPushUtils.class);

    private static String MASTER_SECRET = PropertiesUtils.getProperty("config.properties", "jgpush.mastersecret");

    private static String APP_KEY = PropertiesUtils.getProperty("config.properties", "jgpush.appkey");


    public static void main(String[] args) {
    }


    /**
     * @return cn.jpush.api.push.model.PushPayload
     * <AUTHOR>
     * @Description //构建安卓和ios推送消息
     * @Date 2020/11/13 15:46
     * @Param [title, content, params]
     **/
    public static PushPayload buildPushObject_android_and_ios(String title, String content, Map<String, String> params, String alias) {
        return PushPayload.newBuilder()
                .setPlatform(Platform.android_ios())
                .setAudience(Audience.all())
                .setNotification(Notification.newBuilder()
                        .setAlert(content)
                        .addPlatformNotification(AndroidNotification.newBuilder()
                                .setTitle(title)
                                .addExtras(params).build())
                        .addPlatformNotification(IosNotification.newBuilder()
                                .incrBadge(1)
                                .addExtra("extra_key", "extra_value").build())
                        .build())
                .build();
    }


    /**
     * @return cn.jpush.api.push.model.PushPayload
     * <AUTHOR>
     * @Description //构建安卓和ios推送消息
     * @Date 2020/11/13 15:46
     * @Param [title, content, params]
     **/
    public static PushPayload buildPushObject_android_and_ios_alias(String title, String content, Map<String, String> params, String alias) {
        return PushPayload.newBuilder()
                .setPlatform(Platform.android_ios())
                .setAudience(Audience.alias(alias))
                .setNotification(Notification.newBuilder()
                        .setAlert(content)
                        .addPlatformNotification(AndroidNotification.newBuilder()
                                .setTitle(title)
                                .addExtras(params).build())
                        .addPlatformNotification(IosNotification.newBuilder()
                                .incrBadge(1)
                                .addExtras(params).build())
                        .build())
                .build();
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @Description //推送消息
     * @Date 2020/11/13 15:46
     * @Param [title, content, params]
     **/
    public static PushResult sendPush(String title, String content, Map<String, String> params, String alias) {
        ClientConfig clientConfig = ClientConfig.getInstance();
        PushResult result = null;
        JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY, null, clientConfig);
        try {
            PushPayload payload = buildPushObject_android_and_ios(title, content, params, alias);
            result = jpushClient.sendPush(payload);
            logger.info("Got result - " + result);
        } catch (APIConnectionException e) {
            logger.error("Connection error. Should retry later. ", e);
        } catch (APIRequestException e) {
            logger.error("Error response from JPush server. Should review and fix it. ", e);
            logger.info("HTTP Status: " + e.getStatus());
            logger.info("Error Code: " + e.getErrorCode());
            logger.info("Error Message: " + e.getErrorMessage());
        }
        return result;
    }


    /**
     * @return boolean
     * <AUTHOR>
     * @Description //推送消息
     * @Date 2020/11/13 15:46
     * @Param [title, content, params]
     **/
    public static PushResult sendPushByAlias(String title, String content, Map<String, String> params, String alias) {
        ClientConfig clientConfig = ClientConfig.getInstance();
        PushResult result = null;
        JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY, null, clientConfig);
        try {
            PushPayload payload = buildPushObject_android_and_ios_alias(title, content, params, alias);
            result = jpushClient.sendPush(payload);
            logger.info("Got result - " + result);
        } catch (APIConnectionException e) {
            logger.error("Connection error. Should retry later. ", e);
        } catch (APIRequestException e) {
            logger.error("Error response from JPush server. Should review and fix it. ", e);
            logger.info("HTTP Status: " + e.getStatus());
            logger.info("Error Code: " + e.getErrorCode());
            logger.info("Error Message: " + e.getErrorMessage());
        }
        return result;
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description //TODO 创建定时任务
     * @Date 2020/11/13 16:04
     * @Param [title, content, time]
     **/
    public static ScheduleResult createSingleSchedule(String title, String content, String time, Map<String, String> params, String alias) {
        PushPayload push = buildPushObject_android_and_ios(title, content, params, alias);
        ScheduleResult result = null;
        final JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        try {
            result = jpushClient.createSingleSchedule(title, time, push);
            logger.info("schedule result is " + result);
        } catch (APIConnectionException e) {
            logger.error("Connection error. Should retry later. ", e);
        } catch (APIRequestException e) {
            logger.error("Error response from JPush server. Should review and fix it. ", e);
            logger.info("HTTP Status: " + e.getStatus());
            logger.info("Error Code: " + e.getErrorCode());
            logger.info("Error Message: " + e.getErrorMessage());
        }
        return result;
    }


    /**
     * @return void
     * <AUTHOR>
     * @Description //TODO 删除定时任务
     * @Date 2020/11/13 16:10
     * @Param []
     **/
    public static boolean deleteSchedule(String scheduleId) {
        final JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        try {
            jpushClient.deleteSchedule(scheduleId);
        } catch (APIConnectionException e) {
            logger.error("Connection error. Should retry later. ", e);
        } catch (APIRequestException e) {
            logger.error("Error response from JPush server. Should review and fix it. ", e);
            logger.info("HTTP Status: " + e.getStatus());
            logger.info("Error Code: " + e.getErrorCode());
            logger.info("Error Message: " + e.getErrorMessage());
        }
        return true;
    }


    /**
     * @return boolean
     * <AUTHOR>
     * @Description //TODO 修改定时任务
     * @Date 2020/11/14 10:39
     * @Param [scheduleId, title, content, time, params, alias]
     **/
    public static boolean updateSchedule(String scheduleId, String title, String content, String time, Map<String, String> params, String alias) {
        final JPushClient jpushClient = new JPushClient(MASTER_SECRET, APP_KEY);
        PushPayload push = buildPushObject_android_and_ios(title, content, params, alias);
        TriggerPayload trigger = TriggerPayload.newBuilder()
                .setSingleTime(time)
                .buildSingle();
        SchedulePayload payload = SchedulePayload.newBuilder()
                .setName(title)
                .setEnabled(false)
                .setTrigger(trigger)
                .setPush(push)
                .build();
        try {
            ScheduleResult result = jpushClient.updateSchedule(scheduleId, payload);
            if (!result.isResultOK()) {
                return false;
            }
        } catch (APIConnectionException e) {
            logger.error("Connection error. Should retry later. ", e);
        } catch (APIRequestException e) {
            logger.error("Error response from JPush server. Should review and fix it. ", e);
            logger.info("HTTP Status: " + e.getStatus());
            logger.info("Error Code: " + e.getErrorCode());
            logger.info("Error Message: " + e.getErrorMessage());
        }
        return true;
    }

}

