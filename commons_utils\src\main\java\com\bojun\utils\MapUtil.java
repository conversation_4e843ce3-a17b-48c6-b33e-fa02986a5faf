package com.bojun.utils;

import org.apache.commons.collections.MapUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;

import static com.google.common.base.Preconditions.checkArgument;

/**
 * Model：Map工具类
 * Description：Map工具类
 * Author: 潘文泽
 * created：2021年3月11日
 */
public class MapUtil {
    public static final int MAX_POWER_OF_TWO = 1 << (Integer.SIZE - 2);

    public MapUtil() {
    }

    public static String getString(Map map, Object key) {
        return MapUtils.getString(map, key);
    }

    public static Integer getInteger(Map map, Object key) {
        return MapUtils.getInteger(map, key);
    }

    public static Long getLong(Map map, Object key) {
        return MapUtils.getLong(map, key);
    }

    public static Number getNumber(Map map, Object key) {
        return MapUtils.getNumber(map, key);
    }

    public static Date getDate(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null && answer instanceof Date) {
                return (Date) answer;
            }
        }

        return null;
    }

    public static BigDecimal getBigDecimal(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                if (answer instanceof BigDecimal) {
                    return (BigDecimal) answer;
                }

                if (answer instanceof String) {
                    return new BigDecimal((String) answer);
                }
            }
        }

        return null;
    }

    public static Boolean getBoolean(Map map, Object key) {
        if (map != null) {
            Object answer = map.get(key);
            if (answer != null) {
                if (answer instanceof Boolean) {
                    return (Boolean) answer;
                }

                if (answer instanceof String) {
                    return new Boolean((String) answer);
                }

                if (answer instanceof Number) {
                    Number n = (Number) answer;
                    return n.intValue() != 0 ? Boolean.TRUE : Boolean.FALSE;
                }
            }
        }

        return false;
    }

    public static List getList(Map map, Object key) {
        if (map == null) {
            return null;
        } else if (key == null) {
            return null;
        } else if (!map.containsKey(key)) {
            return null;
        } else {
            Object value = map.get(key);
            if (value == null) {
                return null;
            } else {
                return List.class.isAssignableFrom(value.getClass()) ? (List) value : null;
            }
        }
    }

    public static boolean isContainsValue(Map map, Object key) {
        if (map != null && map.containsKey(key)) {
            Object answer = map.get(key);
            if (answer != null) {
                boolean flag = ObjectUtil.isEmpty(answer);
                return !flag;
            }
        }

        return false;
    }

    public static Map<String, Object> convertMap(Map<String, String> map) {
        if (null != map) {
            Map<String, Object> resultMap = new HashMap();
            resultMap.putAll(map);
            String[] replaceKeys = new String[]{"id", "delInd"};

            for (int i = 0; i < replaceKeys.length; ++i) {
                String replaceKey = replaceKeys[i];
                if (resultMap.containsKey(replaceKey)) {
                    String value = (String) resultMap.get(replaceKey);
                    if (!StringUtil.isEmpty(value)) {
                        resultMap.put(replaceKey, Integer.parseInt(value));
                    }
                }
            }

            return resultMap;
        } else {
            return null;
        }
    }

    /**
     * 将Object对象里面的属性和值转化成Map对象
     *
     * @param obj
     * @return
     * @throws IllegalAccessException
     */
    public static Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<String, Object>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            String fieldName = field.getName();
            Object value = field.get(obj);
            map.put(fieldName, value);
        }
        return map;
    }

    public static List<Map<String, Object>> collectionToListMap(Collection list) throws IllegalAccessException {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<Map<String, Object>> result = new ArrayList<>(10);
        for (Object o : list) {
            Map<String, Object> map = objectToMap(o);
            result.add(map);
        }
        return result;
    }

    /**
     * 传入初始容量初始化HashMap
     * expectedSize表示HashMap中 k-v实际个数
     * Creates a {@code HashMap} instance, with a high enough "initial capacity"
     * that it <i>should</i> hold {@code expectedSize} elements without growth.
     * This behavior cannot be broadly guaranteed, but it is observed to be true
     * for OpenJDK 1.6. It also can't be guaranteed that the method isn't
     * inadvertently <i>oversizing</i> the returned map.
     *
     * @param expectedSize the number of elements you expect to add to the
     *                     returned map
     * @return a new, empty {@code HashMap} with enough capacity to hold {@code
     * expectedSize} elements without resizing
     * @throws IllegalArgumentException if {@code expectedSize} is negative
     */
    public static <K, V> HashMap<K, V> newHashMapWithExpectedSize(
            int expectedSize) {
        return new HashMap<K, V>(capacity(expectedSize));
    }

    /**
     * 返回HashMap容量，
     * threshold = loadFactor * capacity
     * 装载因子默认0.75f，capacity为2的幂次方
     * Returns a capacity that is sufficient to keep the map from being resized as
     * long as it grows no larger than expectedSize and the load factor is >= its
     * default (0.75).
     */
    static int capacity(int expectedSize) {
        if (expectedSize < 3) {
            checkArgument(expectedSize >= 0);
            return expectedSize + 1;
        }
        if (expectedSize < MAX_POWER_OF_TWO) {
            return expectedSize + expectedSize / 3;
        }
        return Integer.MAX_VALUE; // any large value
    }
}
