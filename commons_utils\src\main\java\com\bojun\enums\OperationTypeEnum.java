/**
 * 
 */
package com.bojun.enums;

/**
*Model：请求操作类型枚举
*Description：请求操作类型枚举
*Author:段德鹏
*created：2020年4月24日
**/
public enum OperationTypeEnum {
	
	ADD_REQUEST(1, "新增"),
	UPDATE_REQUEST(2, "修改"),
	DELETE_REQUEST(3, "删除"),
	LOGIN_IN_REQUEST(4, "登陆"),
	LOGIN_OUT_REQUEST(5, "登陆");
	
	/**
	 * 类型code
	 */
	private int code;
	
	/**
	 * 类型描述
	 */
	private String 	desc;
	
	private OperationTypeEnum(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}
	
	public static String getCodeDesc(String code) {
		String desc = "";
		for (OperationTypeEnum operationTypeEnum : OperationTypeEnum.values()) {
			if (code.equals(operationTypeEnum.getCode())) {
				desc = operationTypeEnum.getDesc();
				break;
			}
		}
		return desc;
	}

	public int getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}

}
