package com.bojun.base.system.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.ManageUserMemorandumService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.dto.ManageUserMemorandumDTO;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月29日
*/
@RestController
public class ManageUserMemorandumController extends BoJunBaseController{
	
	@Autowired
	private ManageUserMemorandumService  manageUserMemorandumService;
	
	private final static Log log = LogFactory.getLog(ManageUserMemorandumController.class);
	
	/**
	 * 
	 * @Description 查询消息通知（站内）信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/getManageUserMemorandum", method = RequestMethod.POST)
	@ResponseBody
	public void getManageUserMemorandum(@RequestBody Map<String, Object> paramsMap, HttpServletRequest request) {
		try {
			// 当前日历数据
			List<ManageUserMemorandumDTO> resList = manageUserMemorandumService.getManageUserMemorandum(paramsMap);
			outJson(successInfo(resList));
		} catch (Exception e) {

			log.error("getManageUserMemorandum:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	@RequestMapping(value = "/getToDayManageUserMemorandum", method = RequestMethod.POST)
	@ResponseBody
	public void getToDayManageUserMemorandum(@RequestBody Map<String, Object> paramsMap, HttpServletRequest request) {
		try {
			if (null == paramsMap || StringUtils.isBlank((String) paramsMap.get("nowTime"))) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			// 当前今天数据
			List<ManageUserMemorandumDTO> todayList = manageUserMemorandumService.getManageUserMemorandum(paramsMap);
			outJson(successInfo(todayList));
		} catch (Exception e) {

			log.error("getManageUserMemorandum:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 新增消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/addManageUserMemorandum", method = RequestMethod.POST)
	@ResponseBody
	public void addManageUserMemorandum(@RequestBody ManageUserMemorandumDTO manageUserMemorandumDTO,
			HttpServletRequest request) {
		try {
			if (null == manageUserMemorandumDTO || StringUtils.isBlank(manageUserMemorandumDTO.getMemoDateStr())) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			Integer nNum = manageUserMemorandumService.addManageUserMemorandum(manageUserMemorandumDTO);
			if (null == nNum || nNum.intValue() <= 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("addManageUserMemorandum:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 删除消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/deleteManageUserMemorandum", method = RequestMethod.POST)
	@ResponseBody
	public void deleteManageUserMemorandum(@RequestBody ManageUserMemorandumDTO manageUserMemorandumDTO) {
		try {
			if (null == manageUserMemorandumDTO || null == manageUserMemorandumDTO.getId()) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			// 需要数据库时间
			manageUserMemorandumDTO.setDeleteTimeStr("1");
			manageUserMemorandumDTO.setIsDelete(1);
			Integer nNum = manageUserMemorandumService.updateManageUserMemorandum(manageUserMemorandumDTO);
			if (null == nNum || nNum.intValue() < 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}

			outJson(successInfo());

		} catch (Exception e) {

			log.error("deleteManageUserMemorandum:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 修改消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 */
	@RequestMapping(value = "/updateManageUserMemorandum", method = RequestMethod.POST)
	@ResponseBody
	public void updateManageUserMemorandum(@RequestBody ManageUserMemorandumDTO manageUserMemorandumDTO) {
		try {
			if (null == manageUserMemorandumDTO || null == manageUserMemorandumDTO.getId()) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			Integer nNum = manageUserMemorandumService.updateManageUserMemorandum(manageUserMemorandumDTO);
			if (null == nNum || nNum.intValue() < 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateManageUserMemorandum:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}


}

