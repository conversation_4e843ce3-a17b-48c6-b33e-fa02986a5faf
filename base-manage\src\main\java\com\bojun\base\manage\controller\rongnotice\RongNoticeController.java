package com.bojun.base.manage.controller.rongnotice;


import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.RongNoticeService;
import com.bojun.base.manage.controller.notice.vo.UpdateNoticeVO;
import com.bojun.base.manage.controller.rongnotice.vo.AddRongNoticeParamVo;
import com.bojun.base.manage.controller.rongnotice.vo.GetRongNoticeParamVo;
import com.bojun.base.manage.controller.rongnotice.vo.RongNoticeVo;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.RongNoticeDTO;
import com.bojun.system.entity.ManageUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知前端控制器
 * @Description: 基础控台融云消息通知前端控制器
 * @since 2020-12-04
 */
@RestController
@Api(tags = {"融云消息通知"})
@RequestMapping("/rongNotice")
public class RongNoticeController extends BaseController {
    Log log = LogFactory.getLog(RongNoticeController.class);

    @Autowired
    private RongNoticeService rongnoticeService;

    /**
     * @return 2020-12-04
     * @Description 添加基础控台融云消息通知
     * <AUTHOR>
     */
    @ApiOperation(value = "添加基础控台融云消息通知", notes = "添加基础控台融云消息通知（赖允翔）")
    @ApiOperationSupport(order = 1)
    @RequestMapping(value = "/saveRongNotice", method = RequestMethod.POST)
    @AuthAnnotation(action = "saveRongNotice")
    public Results saveRongNotice(@RequestBody AddRongNoticeParamVo addRongNoticePramVo, HttpServletRequest request) {
        try {
            ManageUser manageUser = (ManageUser) request.getAttribute("manageUser");
            RongNoticeDTO rongnoticeDTO = new RongNoticeDTO();
            BeanUtils.copyProperties(addRongNoticePramVo, rongnoticeDTO);
//            rongnoticeDTO.setCreateUserId(manageUser.getUserId());
            rongnoticeDTO.setCreateUserId(1);
            String result = rongnoticeService.saveRongNotice(rongnoticeDTO);
            return returnResults(result);
        } catch (Exception e) {
            log.error("saveRongNotice:", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    /**
     * @return 2020-12-04
     * @Description 删除基础控台融云消息通知
     * <AUTHOR>
     */
    @ApiOperation(value = "删除基础控台融云消息通知", notes = "删除基础控台融云消息通知（赖允翔）")
    @ApiOperationSupport(order = 2)
    @RequestMapping(value = "/deleteRongNotice", method = RequestMethod.POST)
    @AuthAnnotation(action = "deleteRongNotice")
    public Results deleteRongNotice(@RequestBody List<String> noticeId) {
        try {
            String result = rongnoticeService.deleteRongNotice(noticeId);
            return returnResults(result);
        } catch (Exception e) {
            log.error("deleteRongNotice:", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    /**
     * @return 2020-12-04
     * @Description 添加基础控台融云消息通知
     * <AUTHOR>
     */
    @ApiOperation(value = "全查基础控台融云消息通知", notes = "添加基础控台融云消息通知（赖允翔）")
    @ApiOperationSupport(order = 3)
    @RequestMapping(value = "/getRongNotice", method = RequestMethod.POST)
    @AuthAnnotation(action = "getRongNotice")
    public Results<RongNoticeVo> getRongNotice(@RequestBody GetRongNoticeParamVo getRongNoticeParamVo) {
        try {
            RongNoticeDTO rongnoticeDTO = new RongNoticeDTO();
            BeanUtils.copyProperties(getRongNoticeParamVo, rongnoticeDTO);
            String result = rongnoticeService.getRongNotice(rongnoticeDTO);
            return returnResultsPage(result, RongNoticeVo.class);
        } catch (Exception e) {
            log.error("getRongNotice:", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    /**
     * @return 2020-12-04
     * @Description 单查基础控台融云消息通知
     * <AUTHOR>
     */
    @ApiOperation(value = "单查基础控台融云消息通知", notes = "单查基础控台融云消息通知（赖允翔）")
    @ApiOperationSupport(order = 4)
    @RequestMapping(value = "/getRongNoticeById", method = RequestMethod.POST)
    @AuthAnnotation(action = "getRongNoticeById")
    public Results<RongNoticeVo> getRongNoticeById(@RequestParam("noticeId") String noticeId) {
        try {
            RongNoticeDTO rongnoticeDTO = new RongNoticeDTO();
            rongnoticeDTO.setNoticeId(noticeId);
            String result = rongnoticeService.getRongNoticeById(rongnoticeDTO);
            return returnResults(result, RongNoticeVo.class);
        } catch (Exception e) {
            log.error("getRongNotice:", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    /**
     * @return 2020-12-04
     * @Description 单查基础控台融云消息通知
     * <AUTHOR>
     */
    @ApiOperation(value = "修改基础控台融云消息通知/立即推送", notes = "修改基础控台融云消息通知/立即推送（赖允翔）")
    @ApiOperationSupport(order = 5)
    @RequestMapping(value = "/updateRongNoticeById", method = RequestMethod.POST)
    @SystemLog(action = "deleteRongNotice", description = "修改基础控台融云消息通知/立即推送 ", operationType = Contants.UPDATE_REQUEST)
    @AuthAnnotation(action = "updateRongNoticeById")
    public Results updateRongNoticeById(@RequestBody UpdateNoticeVO updateNoticeVO) {
        try {
            RongNoticeDTO rongnoticeDTO = new RongNoticeDTO();
            BeanUtils.copyProperties(updateNoticeVO, rongnoticeDTO);
            String result = rongnoticeService.updateRongNoticeById(rongnoticeDTO);
            return returnResults(result, RongNoticeVo.class);
        } catch (Exception e) {
            log.error("updateRongNoticeById:", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

}



