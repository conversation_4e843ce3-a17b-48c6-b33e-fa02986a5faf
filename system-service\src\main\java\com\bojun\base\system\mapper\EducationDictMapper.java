/**
 * 
 */
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.web.bind.annotation.RequestBody;

import com.bojun.system.dto.EducationDictDto;




/**
*Model：学历信息表
*Description：EducationDictMapper.java
*Author: 曾玲玲
*created：2020年1月9日
*/
@Mapper
public interface EducationDictMapper {
	
	/**
	 * @Description: 查询学历字典列表总数
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * Integer
	 */
	public Integer getEducationDictListCount(@RequestBody Map<String, Object> map);


	/**
	 * @Description: 查询学历字典列表信息
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	public List<EducationDictDto> getEducationDictList(@RequestBody Map<String, Object> map);
	
	/**
	 * @Description: 查询学历字典列表信息
	 * @Author: 
	 * @create: 2019年12月30日
	 *
	 */
	public List<Map<String,Object>> getMapEducationDict(@RequestBody Map<String, Object> map);
	
	/**
	 * @Description: 新增学历
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	public int addEducation(@RequestBody EducationDictDto educationDictDto);
	
	/**
	 * @Description: 查询出最大学历code
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	public String queryMaximumEducationCode(@RequestBody Map<String, Object> map);

	/**
	 * @Description: 编辑学历
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	public int updateEducation(@RequestBody EducationDictDto educationDictDto);

	/**
	 * @Description: 根据id查询学历字典
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	public EducationDictDto selectEducationDictById(@RequestBody Map<String, Object> map);

	/**
	 * @Description: 校验学历名称是否合规
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	public int checkEducationName(@RequestBody EducationDictDto educationDictDto);
	
	/**
	 * @Description: 根据id编辑学历是否启用
	 * @Author: 曾玲玲
	 * @create: 2019/11/22 15:42
	 * 
	 */
	public int updateEducationStatus(@RequestBody EducationDictDto educationDictDto);


}
