package com.bojun.base.manage.controller.organization.vo.dept;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
*Model：机构管理
*Description：--科室新增参数vo类
*Author: 肖泽权
*created：2020年5月5日
*/
@ApiModel(value = "新增机构--科室信息", description = "新增机构--科室传入信息")
public class AddOrganizationDeptParamVO implements Serializable{
	
	private static final long serialVersionUID = 1868103868918369125L;
	
	@ApiModelProperty(value="父级科室编码", required = true, example = "18120405")
    private Integer parentDeptId;
	
	@NotNull(message = "机构ID不能为空")
	@ApiModelProperty(value="机构ID", required = true, example = "1812")
    private Integer organizationId;
	
	@NotEmpty(message = "科室编码不能为空")
	@ApiModelProperty(value="科室编码", required = true, example = "18120404")
    private String deptNumber;
    
    
    @NotEmpty(message = "科室名称不能为空")
	@ApiModelProperty(value="科室名称", required = true, example = "泌尿外科")
    private String deptName;
    
    
    //@NotNull(message = "科室属性不能为空")
	@ApiModelProperty(value="科室属性", required = true, example = "2")
    private Integer deptAttr;

	//@NotNull(message = "门诊住院类型不能为空")
	@ApiModelProperty(value = "门诊住院类型", required = true, example = "1")
	private Integer outpOrInp;


	@NotNull(message = "科室类型不能为空")
	@ApiModelProperty(value = "科室类型", required = true, example = "1")
	private Integer internalOrSergery;

	@ApiModelProperty(value = "科室主任", example = "719881")
	private String directorId;

	@ApiModelProperty(value = "科室护士长", example = "719882")
	private String matronId;

	@ApiModelProperty(value = "科室图片", required = true, example = "233r22.jpg")
	private String deptImage;

	@ApiModelProperty(value = "科室描述", required = true, example = "优秀内科科室")
	private String describes;

	@ApiModelProperty(value = "是否启用", required = true, example = "1")
	private Integer isEnabled;
    
    @ApiModelProperty(value="科室电话", required = true, example = "88888888888")
    private String telephone;
    
    @ApiModelProperty(value="科室位置", required = true, example = "住院部三楼")
    private String position;

	@ApiModelProperty(value = "科室图片集合", example = "")
    private List<DepartmentImgParamVO> imgList;
    
	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public List<DepartmentImgParamVO> getImgList() {
		return imgList;
	}

	public void setImgList(List<DepartmentImgParamVO> imgList) {
		this.imgList = imgList;
	}

	public Integer getParentDeptId() {
		return parentDeptId;
	}

	public void setParentDeptId(Integer parentDeptId) {
		this.parentDeptId = parentDeptId;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getDeptNumber() {
		return deptNumber;
	}

	public void setDeptNumber(String deptNumber) {
		this.deptNumber = deptNumber;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getDeptAttr() {
		return deptAttr;
	}

	public void setDeptAttr(Integer deptAttr) {
		this.deptAttr = deptAttr;
	}

	public Integer getOutpOrInp() {
		return outpOrInp;
	}

	public void setOutpOrInp(Integer outpOrInp) {
		this.outpOrInp = outpOrInp;
	}

	public Integer getInternalOrSergery() {
		return internalOrSergery;
	}

	public void setInternalOrSergery(Integer internalOrSergery) {
		this.internalOrSergery = internalOrSergery;
	}

	public String getDirectorId() {
		return directorId;
	}

	public void setDirectorId(String directorId) {
		this.directorId = directorId;
	}

	public String getMatronId() {
		return matronId;
	}

	public void setMatronId(String matronId) {
		this.matronId = matronId;
	}

	public String getDeptImage() {
		return deptImage;
	}

	public void setDeptImage(String deptImage) {
		this.deptImage = deptImage;
	}

	public String getDescribes() {
		return describes;
	}

	public void setDescribes(String describes) {
		this.describes = describes;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}
    
}

