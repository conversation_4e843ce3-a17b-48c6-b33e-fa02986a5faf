-------------------------------------------------------------------------------
Test set: com.bojun.sphygmometer.service.impl.SphygmometerUserServiceImplTest
-------------------------------------------------------------------------------
Tests run: 8, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 5.215 s <<< FAILURE! - in com.bojun.sphygmometer.service.impl.SphygmometerUserServiceImplTest
getSignedUserAnalysisDTO(com.bojun.sphygmometer.service.impl.SphygmometerUserServiceImplTest)  Time elapsed: 0.205 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\OrganizationManageHypertensionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select         sum(manage_hypertension_count) manage_hypertension_count         from         t_organization_manage_hypertension          WHERE  organization_id in                 (                     ?                 )
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
	at com.bojun.sphygmometer.service.impl.SphygmometerUserServiceImplTest.getSignedUserAnalysisDTO(SphygmometerUserServiceImplTest.java:103)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
	at com.bojun.sphygmometer.service.impl.SphygmometerUserServiceImplTest.getSignedUserAnalysisDTO(SphygmometerUserServiceImplTest.java:103)

