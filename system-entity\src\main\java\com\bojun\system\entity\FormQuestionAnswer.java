package com.bojun.system.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：呼叫记录
 * Description：呼叫记录实体类
 * Author:刘修纬
 * created：2020年5月11日
 */
public class FormQuestionAnswer implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;


    private Integer answerId; // 答案id
    private Integer questionnaireId; // 问卷id
    private Integer questionId; // 问题id
    private Integer questionItemId;//问题子项id
    private String title; // 问题标题
    private String answerUserId; // 答题人用户id
    private Integer optionId; // 选项id
    private Integer score; // 分值
    private Date answerTime; // 答题时间
    private String content; // 填空题内容

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getQuestionItemId() {
        return questionItemId;
    }

    public void setQuestionItemId(Integer questionItemId) {
        this.questionItemId = questionItemId;
    }

    public Integer getAnswerId() {
        return answerId;
    }

    public void setAnswerId(Integer answerId) {
        this.answerId = answerId;
    }


    public Integer getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(Integer questionnaireId) {
        this.questionnaireId = questionnaireId;
    }


    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


    public String getAnswerUserId() {
        return answerUserId;
    }

    public void setAnswerUserId(String answerUserId) {
        this.answerUserId = answerUserId;
    }


    public Integer getOptionId() {
        return optionId;
    }

    public void setOptionId(Integer optionId) {
        this.optionId = optionId;
    }


    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }


    public Date getAnswerTime() {
        return answerTime;
    }

    public void setAnswerTime(Date answerTime) {
        this.answerTime = answerTime;
    }
}
