<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserNewsFavoriteMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.UserNewsFavoriteDTO" id="UserNewsFavoriteDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="newsId"    column="news_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserNewsFavorite">

        select
            distinct
            tnf.id,
            tnf.user_id,
            tnf.news_id,
            tnf.create_time,
            tni.title,
            tni.content,
            tni.cover_image
        from
            t_user_news_favorite tnf LEFT JOIN health_promotion.t_news_info tni on tnf.news_id=tni.news_id
    </sql>

    <select id="selectUserNewsFavoriteById" parameterType="int" resultMap="UserNewsFavoriteDTOResult">
		<include refid="selectUserNewsFavorite"/>
		where 
        	id = #{id}
    </select>

    <select id="selectUserNewsFavoriteList" parameterType="com.bojun.sphygmometer.dto.UserNewsFavoriteDTO" resultType="com.bojun.sphygmometer.dto.UserNewsFavoriteDTO">
        <include refid="selectUserNewsFavorite"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="newsId != null "> and news_id = #{newsId}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
        order by tnf.create_time desc
    </select>

    <delete id="deleteUserNewsFavoriteByUserId" parameterType="com.bojun.sphygmometer.dto.UserNewsFavoriteDTO">
        delete from t_user_news_favorite where user_id =#{userId} and news_id in
        <foreach item="item" index="index" collection="newsIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="getCount" parameterType="Map" resultType="integer">
        select count(1) from t_user_news_favorite where
        <if test="newsId != null "> news_id = #{newsId}</if>
        <if test="userId != null "> and user_id = #{userId}</if>
    </select>

</mapper>