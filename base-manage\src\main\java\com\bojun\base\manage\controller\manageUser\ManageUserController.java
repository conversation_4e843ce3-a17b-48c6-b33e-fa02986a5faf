/**
 * 
 */
package com.bojun.base.manage.controller.manageUser;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.parser.ParserConfig;
import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.IManageUserService;
import com.bojun.base.manage.api.system.ISystemDictService;
import com.bojun.base.manage.controller.manageUser.vo.AddManageUserVO;
import com.bojun.base.manage.controller.manageUser.vo.EnableDisableUserVO;
import com.bojun.base.manage.controller.manageUser.vo.GetManageUserVO;
import com.bojun.base.manage.controller.manageUser.vo.ManageUserVO;
import com.bojun.base.manage.controller.manageUser.vo.UpdateManageUserVO;
import com.bojun.base.manage.controller.systemDict.vo.AddSystemDictPramVO;
import com.bojun.base.manage.controller.systemDict.vo.GetSystemDictParamVO;
import com.bojun.base.manage.controller.systemDict.vo.SystemDictInfoVO;
import com.bojun.base.manage.controller.systemDict.vo.SystemDictTypeVO;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.entity.ManageUser;
import com.bojun.vo.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
 * 
 * Model：用户管理
 * Description：用户管理
 * Author：lj
 * created： 2020年4月27日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("manageUser")
@Api(tags = {"用户管理模块接口"})
@ApiSort(value = 3)
public class ManageUserController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(ManageUserController.class);
	
	@Autowired
	private IManageUserService manageUserService;
	
	
	 
	/**
	 * @Description 新增用户信息
	 * <AUTHOR>
	 * @return
	 * Results<addManageUser>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "新增用户", notes = "新增用户信息（lj）")
	@ApiOperationSupport(order = 2)
	@RequestMapping(value="/addManageUser", method = RequestMethod.POST)
	@SystemLog(action = "addManageUser", description = "新增用户", operationType = Contants.ADD_REQUEST)
	@AuthAnnotation(action = "addManageUser")
	public Results addManageUser(HttpServletRequest request, @RequestBody @Valid AddManageUserVO addManageUser) {
		try {
			ManageUser manageUser = new ManageUser();
			BeanUtils.copyProperties(addManageUser, manageUser);
			String result = manageUserService.addManageUser(manageUser);
			return returnResults(result);
		} catch (RuntimeException e) {
			logger.error("addManageUser:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("addManageUser:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 编辑用户信息
	 * <AUTHOR>
	 * @return
	 * Results<updateManageUser>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "编辑用户", notes = "编辑用户信息（lj）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value="/updateManageUser", method = RequestMethod.POST)
	@SystemLog(action = "updateManageUser", description = "新增用户", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateManageUser")
	public Results updateManageUser(HttpServletRequest request, @RequestBody @Valid UpdateManageUserVO addManageUser) {
		try {
			ManageUser manageUser = new ManageUser();
			BeanUtils.copyProperties(addManageUser, manageUser);
			String result = manageUserService.updateManageUser(manageUser);
			return returnResults(result);
		} catch (RuntimeException e) {
			logger.error("updateManageUser:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateManageUser:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 启用禁用用户状态
	 * <AUTHOR>
	 * @return
	 * Results<updateManageUser>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "启用禁用用户状态", notes = "启用禁用用户状态（lj）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value="/enableDisableUser", method = RequestMethod.POST)
	@SystemLog(action = "enableDisableUser", description = "启用禁用用户状态", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "enableDisableUser")
	public Results enableDisableUser(HttpServletRequest request, @RequestBody @Valid EnableDisableUserVO addManageUser) {
		try {
			ManageUser manageUser = new ManageUser();
			BeanUtils.copyProperties(addManageUser, manageUser);
			String result = manageUserService.enableDisableUser(manageUser);
			return returnResults(result);
		} catch (RuntimeException e) {
			logger.error("updateManageUser:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateManageUser:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	
	/**
	 * @Description 查询用户列表数据
	 * <AUTHOR>
	 * @return
	 * Results<ManageUserLoginVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "查询用户列表", notes = "查询用户列表数据（lj）")
	@ApiOperationSupport(order = 5)
	@RequestMapping(value="/getManageUserList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getManageUserList")
	public Results<Page<List<ManageUserVO>>> getManageUserList(HttpServletRequest request, @RequestBody @Valid GetManageUserVO getManageUserVO) {		
		try {
			ParserConfig.getGlobalInstance().setAutoTypeSupport(true);
			ManageUserDTO manageUserDTO=new ManageUserDTO();
			manageUserDTO.setHanderToken(request.getHeader("token"));
			BeanUtils.copyProperties(getManageUserVO, manageUserDTO);
			String result = manageUserService.getManageUserList(manageUserDTO);
			return returnResultsPage(result, ManageUserVO.class);
		} catch (RuntimeException e) {
			logger.error("getManageUserList:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getManageUserList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
		
	}
	/**
	  * 
	  * @Description 修改密码
	  * <AUTHOR>
	  * @param request
	  * @param paramsMap
	  * void
	  * 2020年6月2日
	  */
	 @RequestMapping(value="/updateUserPasswords", method = RequestMethod.POST)
	 @AuthAnnotation(action = "updateUserPasswords")
	 @ApiOperation(value = "修改管理员密码", notes = "修改密码（lj）")
	 @ApiImplicitParams({
	        @ApiImplicitParam(name = "newPwd",value = "新密码", dataType = "String", paramType = "query"),
	        @ApiImplicitParam(name = "oldPwd",value = "旧密码", dataType = "String", paramType = "query")
	 })
	 @ApiOperationSupport(order = 3)
	 @SystemLog(action = "updateUserPasswords", description = "修改管理员密码", operationType = Contants.UPDATE_REQUEST)
	 public Results updateUserPasswords(HttpServletRequest request,
	   @RequestParam(value = "newPwd", required = true) String newPwd,
	   @RequestParam(value = "oldPwd", required = true) String oldPwd) {
	  try {
	   String token = request.getHeader("token");
	   ManageUserDTO manageUser = new ManageUserDTO();
	   manageUser.setOldPwd(oldPwd);
	   manageUser.setNewPwd(newPwd);
	   manageUser.setHanderToken(token);
	   // 请求system-service服务修改用户信息
	   String result = manageUserService.updateUserPasswords(manageUser);
	   return returnResults(result);
	  } catch (RuntimeException e) {
	   logger.error("updateUserPasswords", e);
	   return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
	  } catch (Exception e) {
	   logger.error("updateUserPasswords:", e);
	   return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
	  }
	 }

}
