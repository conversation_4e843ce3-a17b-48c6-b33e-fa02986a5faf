/**
 * 
 */
package com.bojun.base.controller;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bojun.enums.ResponseCodeEnum;

/**
 * <AUTHOR>
 *
 */
public class BoJunBaseController {
	
	private static Log log = LogFactory.getLog(BoJunBaseController.class);
	
	 /**
     * 错误信息
     * 
     * @param errorInfo
     * @return
     */
	public Map<String, Object> errorInfo(Integer code) {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        resultInfo.put("code", code);
        resultInfo.put("msg", ResponseCodeEnum.getErrorCodeDescr(code));
        return resultInfo;
    }
	
	/**
     * 错误信息
     * 
     * @param errorInfo
     * @return
     */
	public Map<String, Object> errorInfo(Integer code, String msg) {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        resultInfo.put("code", code);
        resultInfo.put("msg", msg);
        return resultInfo;
    }

    /**
     * 成功信息（带object返回类型）
     * 
     * @return
     */
    public Map<String, Object> successInfo(Object obj) {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        resultInfo.put("code", ResponseCodeEnum.SUCCESS_REQUEST.getCode());
        resultInfo.put("msg", ResponseCodeEnum.getErrorCodeDescr(ResponseCodeEnum.SUCCESS_REQUEST.getCode()));
        resultInfo.put("data",obj);
        return resultInfo;
    }
    
    /**
     * 成功信息（带object返回类型）
     * 
     * @return
     */
    public Map<String, Object> successInfo(Object obj,String msg) {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        resultInfo.put("code", ResponseCodeEnum.SUCCESS_REQUEST.getCode());
        resultInfo.put("msg", msg);
        resultInfo.put("data",obj);
        return resultInfo;
    }
    
    /**
     * 成功信息（带page返回类型）
     * 
     * @return
     */
    public Map<String, Object> successPageInfo(Object obj, long totalCount) {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        resultInfo.put("code", ResponseCodeEnum.SUCCESS_REQUEST.getCode());
        resultInfo.put("msg", ResponseCodeEnum.getErrorCodeDescr(ResponseCodeEnum.SUCCESS_REQUEST.getCode()));
        resultInfo.put("totalCount", totalCount);
        resultInfo.put("data", obj);
        return resultInfo;
    }
    
    
    public Map<String, Object> successInfo() {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        resultInfo.put("code", ResponseCodeEnum.SUCCESS_REQUEST.getCode());
        resultInfo.put("msg", "操作成功");
        return resultInfo;
    }
    
    
    /**
     * 返回自定义信息
     * @param data
     * @return
     */
    public Map<String, Object> info(Object... data) {
        Map<String, Object> resultInfo = new HashMap<String, Object>();
        if (null == data || data.length == 0) {
            return errorInfo(ResponseCodeEnum.NO_DATA.getCode());
        } 
        resultInfo.put("code", data[0]);
        if (data.length == 2 && null != data[1]) {
        	 resultInfo.put("msg", data[1]);
        }
        return resultInfo;
    }
    
	  
	 /**
     * 将字符串转换成JSON字符串发送（UTF-8）
     * 
     * @param str
     */
    public void outJsonString(String str) {
        getHttpResponse().setContentType("text/html;charset=UTF-8");
        if (StringUtils.isBlank(str)) {
            str = StringUtils.EMPTY;
        }
        outString(str);
    }

    /**
     * 将对象转换成JSON字符串发送
     * 
     * @param obj
     */
    public void outJson(Object obj) {
        outJsonString(JSON.toJSONString(obj));
    }

    /**
     * 将数组转换成JSON字符串发送
     * 
     * @param array
     */
    public void outJsonArray(Object array) {
        outJsonString(JSONArray.toJSONString(array));
    }

    /**
     * 将字符串转换成JSON字符串发送
     * 
     * @param str
     */
    public void outString(String str) {
        try {
            getHttpResponse().setCharacterEncoding("UTF-8");
            PrintWriter out = getHttpResponse().getWriter();
            out.write(str);
            out.flush();
            out.close();
        } catch (IOException e) {
            log.error(e);
        }
    }

    /**
     * 将XML字符串转换成JSON格式字符串发送
     * 
     * @param xmlStr
     */
    public void outXMLString(String xmlStr) {
        getHttpResponse().setContentType("application/xml;charset=UTF-8");
        outString(xmlStr);
    }

    public HttpServletRequest getHttpRequest() {
        return ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest();
    }

    public HttpServletResponse getHttpResponse() {
        return ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getResponse();
    }

    public HttpSession getHttpSession() {
        return getHttpRequest().getSession();
    }

    public ServletContext getServletContext() {
        return ((ServletRequestAttributes)RequestContextHolder.getRequestAttributes()).getRequest().getServletContext();
    }

    public String getRealyPath(String path) {
        return getServletContext().getRealPath(path);
    }

}
