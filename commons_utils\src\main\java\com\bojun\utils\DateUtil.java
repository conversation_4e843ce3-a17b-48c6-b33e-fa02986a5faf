package com.bojun.utils;

import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;

/**
 * DateUtil
 * Date格式类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2016-03-31
 */

public class DateUtil {

    /**
     * 根据Date获取年龄函数+++
     *
     * @param birthDay
     * @return
     */
    public static Integer getAge(Date birthDay) {

        try {
            if (birthDay == null) return null;

            Calendar cal = Calendar.getInstance();
            if (cal.before(birthDay)) {
                throw new IllegalArgumentException("The birthDay is before Now.It's unbelievable!");
            }
            int yearNow = cal.get(Calendar.YEAR);
            int monthNow = cal.get(Calendar.MONTH);
            int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
            cal.setTime(birthDay);

            int yearBirth = cal.get(Calendar.YEAR);
            int monthBirth = cal.get(Calendar.MONTH);
            int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

            int age = yearNow - yearBirth;

            if (monthNow <= monthBirth) {
                if (monthNow == monthBirth) {
                    if (dayOfMonthNow < dayOfMonthBirth) age--;
                } else {
                    age--;
                }
            }
            return age;
        } catch (Exception e) {

        }

        return null;
    }

    /**
     * 默认格式(YYYY-MM-DD HH:MM:SS) 日期转字符串
     *
     * @param d
     * @return
     */
    public static String toString(Date d) {
        if (d == null) return "";

        return DateUtil.getFormatDate(d, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 获取时间不包括秒
     *
     * @param d
     * @return
     */
    public static String toStringWithOutSecond(Date d) {
        if (d == null) return "";

        return DateUtil.getFormatDate(d, "HH:mm");
    }

    /**
     * 获取Date的日期部分
     *
     * @param d
     * @return
     */
    public static String toStringWithTime(Date d) {
        if (d == null) return "";

        return DateUtil.getFormatDate(d, "yyyy-MM-dd");
    }

    /**
     * 获取Date的日期 最小值
     *
     * @param d
     * @return
     */
    public static String toStringL(Date d) {
        if (d == null) return "";

        return DateUtil.getFormatDate(d, "yyyy-MM-dd 00:00:00");
    }

    /**
     * 获取Date的日期 最大值
     *
     * @param d
     * @return
     */
    public static String toStringR(Date d) {
        if (d == null) return "";

        return DateUtil.getFormatDate(d, "yyyy-MM-dd 23:59:59");
    }

    /**
     * 指定格式-字符串转日期
     *
     * @param d
     * @param format
     * @return
     */
    public static Date toDate(String d, String format) {
        try {
            return new SimpleDateFormat(format).parse(d);
        } catch (Exception e) {

        }

        return null;
    }

    /**
     * 将日期转为当天最大时间
     *
     * @param d
     * @return
     */
    public static Date toMax(Date d) {
        return DateUtil.toDate(DateUtil.toStringR(d));
    }

    /**
     * 将日期转为当天最小时间
     *
     * @param d
     * @return
     */
    public static Date toMin(Date d) {
        return DateUtil.toDate(DateUtil.toStringL(d));
    }

    /**
     * 默认格式-（YYYY-MM-DD HH:MM:SS） 字符串转日期
     *
     * @param d
     * @return
     */
    public static Date toDate(String d) {
        return toDate(d, "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 两个日期相差的天数
     * 日期格式：yyyy-MM-dd
     */
    public static Integer daysBetween(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date start = df.parse(sdate);
        Date end = df.parse(edate);

        return DateUtil.daysBetween(start, end);
    }

    /**
     * 两个日期相差的天数
     * 日期格式：yyyy-MM-dd
     */
    public static Integer daysBetween(Date sdate, Date edate) throws ParseException {
        long start = sdate.getTime();
        long end = edate.getTime();
        long between_days = (end - start) / (1000 * 60 * 60 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 两个日期相差的小时数
     * 日期格式：yyyy-MM-dd HH:mm:ss
     */
    public static Integer hoursBetween(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long start = df.parse(sdate).getTime();
        long end = df.parse(edate).getTime();
        long between_hours = (end - start) / (1000 * 60 * 60);
        return Integer.parseInt(String.valueOf(between_hours));
    }

    /**
     * 两个日期相差的小时数
     * 日期格式：yyyy-MM-dd HH:mm
     */
    public static Integer hoursBetweenm(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        long start = df.parse(sdate).getTime();
        long end = df.parse(edate).getTime();
        long between_hours = (end - start) / (1000 * 60 * 60);
        return Integer.parseInt(String.valueOf(between_hours));
    }

    /**
     * 两个日期相差的分钟数
     * 日期格式：yyyy-MM-dd HH:mm:ss
     */
    public static Integer minutesBetween(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long start = df.parse(sdate).getTime();
        long end = df.parse(edate).getTime();
        long between_hours = (end - start) / (1000 * 60);
        return Integer.parseInt(String.valueOf(between_hours));
    }

    /**
     * 两个日期相差的分钟数
     * 日期格式：yyyy-MM-dd HH:mm:ss
     */
    public static Integer minutesBetweenn(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long start = df.parse(sdate).getTime();
        long end = df.parse(edate).getTime();
        Double between_hours = (Double.valueOf(String.valueOf((end - start)))) / (1000 * 60);
        return (int) Math.ceil(between_hours);
    }

    /**
     * 两个日期相差的分钟数
     *
     * @param sdate
     * @param edate
     * @return
     * @throws ParseException
     */
    public static String minutesBetweens(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long start = df.parse(sdate).getTime();
        long end = df.parse(edate).getTime();
        long minutes = (end - start) / (1000 * 60);
        long seconds = ((end - start) % (1000 * 60)) / 1000;
        return minutes + "分" + seconds + "秒";
    }

    /**
     * 两个日期相差的分钟数
     * 日期格式：yyyy-MM-dd HH:mm
     */
    public static Integer minutesBetweenm(String sdate, String edate) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        long start = df.parse(sdate).getTime();
        long end = df.parse(edate).getTime();
        long between_hours = (end - start) / (1000 * 60);
        return Integer.parseInt(String.valueOf(between_hours));
    }

    /**
     * 获取yyyy-MM-dd格式的日期加上某个天数后得到的新的yyyy-MM-dd格式日期
     *
     * @throws ParseException
     */
    public static String getDateAddDay(String startDateString, Integer day) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = sdf.parse(startDateString);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, day);
        String endDateString = sdf.format(cal.getTime());
        return endDateString;
    }

    /**
     * 获取指定日期为星期几
     */
    public static int getNowAppointDateWeek(Date d) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        int week = cal.get(Calendar.DAY_OF_WEEK) - 1;
        return week;
    }

    /**
     * 获取当前日期是星期几<br>
     *
     * @param dt
     * @return 当前日期是星期几汉字
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }
    
    /**
     * 获取当前日期是星期几<br>
     *
     * @param dt
     * @return 当前日期是星期几汉字
     */
    public static String getWeekOfDate2(Date dt) {
        String[] weekDays = {"周日", "周一", "周二", "周三", "周四", "周五", "周六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
        return weekDays[w];
    }

    /**
     * 获取指定日期为星期几
     */
    public static String getYestTodayDate(String format) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        Date d = cal.getTime();
        SimpleDateFormat sp = new SimpleDateFormat(format);
        return sp.format(d);// 获取昨天日期
    }

    /**
     * 获取指定格式为yyyy-MM-dd的日期
     */
    public static String getAppointDate(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(d);
    }


    /**
     * 获取指定格式的日期
     */
    public static String getFormatDate(Date d, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(d);
    }

    /**
     * 获取指定格式为MM-dd HH:mm的日期
     */
    public static String getAppointDatey(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat("MM-dd HH:mm");
        return sdf.format(d);
    }

    /**
     * 获取指定格式为yy.MM.dd的日期
     */
    public static String getAppointDatee(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat("yy.MM.dd");
        return sdf.format(d);
    }

    /**
     * 获取指定格式为yyyy-MM-dd HH:mm:ss的日期
     */
    public static String getAppointDates(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(d);
    }

    /**
     * 获取指定格式为yyyy-MM-dd HH:mm的日期
     */
    public static String getAppointDatem(Date d) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return sdf.format(d);
    }

    /**
     * 获取格式为yyyyMMddHHmmss的当前时间
     */
    public static String getNowTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        return sdf.format(new Date());
    }

    /**
     * 获取格式为yyyyMMddHHmmssS的当前时间(精确到毫秒S)
     */
    public static String getNowTimehm() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssS");
        return sdf.format(new Date());
    }

    public static Date getEndOfDay(Date date) {
        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTime(date);
        calendarEnd.set(Calendar.HOUR_OF_DAY, 23);
        calendarEnd.set(Calendar.MINUTE, 59);
        calendarEnd.set(Calendar.SECOND, 59);

        // 防止mysql自动加一秒,毫秒设为0
        calendarEnd.set(Calendar.MILLISECOND, 0);
        return calendarEnd.getTime();

    }

    // 获得某天最小时间 2018-03-20 00:00:00
    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
                ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());

    }

    /**
     * 判断两个时间是否是同一年
     */
    public static boolean isSameYear(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(date1);
        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(date2);
        boolean isSameYear = cal1.get(Calendar.YEAR) == cal2
                .get(Calendar.YEAR);
/*		boolean isSameMonth = isSameYear
				&& cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
		boolean isSameDate = isSameMonth
				&& cal1.get(Calendar.DAY_OF_MONTH) == cal2
				.get(Calendar.DAY_OF_MONTH);*/
        return isSameYear;
    }

    /**
     * 获取格式为yyyyMMdd的当前时间
     */
    public static String getNowTimeToTYYYYMMdd() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(new Date());
    }

    /**
     * 获取格式为yyyyMMdd的当前时间
     */
    public static String getNowTimeToString() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(new Date());
    }

    /**
     * 主函数测试
     *
     * @throws ParseException
     */
    public static void main(String[] args) throws ParseException {
        //System.out.println(checkWorkDay(new Date()));

    }

    /**
     * 获取连续天数
     *
     * @param dateList
     * @return
     * @throws ParseException
     */
    public static int getContinuousSignInDay(List<Date> dateList) throws ParseException {
        if (CollectionUtils.isEmpty(dateList)) {
            return 0;
        }
        //continuousDay 连续签到数
        int continuousDay = 1;
        boolean todaySignIn = false;
        // today 当天日期
        Date today = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String todays = sdf.format(today);

        for (int i = 0; i < dateList.size(); i++) {
            int intervalDay = distanceDay(sdf.parse(todays), sdf.parse(sdf.format(dateList.get(i))));
            if (intervalDay == 0 && i == 0) {
                todaySignIn = true;
            } else if (intervalDay == continuousDay) {
                continuousDay++;
            } else {
                //不连续，终止判断
                break;
            }
        }
        if (!todaySignIn) {
            continuousDay--;
        }
        return continuousDay;
    }


    //判断当天日期 与以往签到日期相隔天数
    private static int distanceDay(Date largeDay, Date smallDay) {
        int day = (int) ((largeDay.getTime() - smallDay.getTime()) / (1000 * 60 * 60 * 24));
        return day;
    }

    /**
     * 获取日期数量
     *
     * @param dateList
     * @return
     */
    public static int getDateCount(List<Date> dateList) {
        if (CollectionUtils.isEmpty(dateList)) {
            return 0;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Set<String> dateSet = new HashSet<>();
        dateList.forEach(date -> {
            dateSet.add(sdf.format(date));
        });
        return dateSet.size();
    }

    /**
     * 是否包含日期
     *
     * @return
     */
    public static Boolean isContainDate(List<Date> dateList, Date date) {
        if (CollectionUtils.isEmpty(dateList) || ObjectUtil.isEmpty(date)) {
            return Boolean.FALSE;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String dateStr = sdf.format(date);
        for (Date date1 : dateList) {
            String dateStr1 = sdf.format(date1);
            if (ObjectUtil.equal(dateStr, dateStr1)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }
}
