package com.bojun.base.system.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.MainDictService;
import com.bojun.employee.entity.dto.EmployeeInfoDTO;
import com.bojun.employee.enums.UploadImgFilePathEnum;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.system.entity.OrganizationlLeader;
import com.bojun.utils.PropertiesUtils;

/**
 * Model：模块名称 Description：文件描述 Author: 肖泽权 created：2020年1月12日
 */
@RestController
public class MainDictController extends BoJunBaseController {

	private final static Log log = LogFactory.getLog(MainDictController.class);

	@Autowired
	private MainDictService mainDictService;
	
	// 展示图片根目录
	private String SHOW_FILE_PATH = PropertiesUtils.getProperty("config.properties", "personnel.path");

	/**
	 * 
	 * @Description 获取主体字典信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/getMainDictInfoByKey", method = RequestMethod.POST)
	@AuthAnnotation(action="/getMainDictInfoByKey")
	public void getMainDictInfoByKey(HttpServletRequest request, @RequestBody Map<String, Object> map) {
		try {
			OrganizationInfoDTO organizationInfoDTO = mainDictService.getMainDictInfoByKey(map);
			if(null == organizationInfoDTO){
				outJson(info(ResponseCodeEnum.NO_DATA.getCode(), "暂无数据"));
				return;
			}
			organizationInfoDTO.setLogoImgUrl(SHOW_FILE_PATH + UploadImgFilePathEnum.MAIN_DICT_HOSPITAL_LOGO.getModuleImgPath() + 
					organizationInfoDTO.getLogoImg());
			//查询在编人数
			map.put("jobType", 1);
			map.put("isIncumbency", 1);
			Integer count = mainDictService.queryEmployeeCount(map);
			organizationInfoDTO.setActualAuthorize((int)count);

			outJson(successInfo(organizationInfoDTO));
		} catch (Exception e) {
			log.error("getMainDictInfoByKey",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		} 

	}
	
	/**
	 * 
	 * @Description 获取在职编制人员数量
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/getPersonnelCountOfOnJob", method = RequestMethod.POST)
	@AuthAnnotation(action="/getPersonnelCountOfOnJob")
	public void getPersonnelCountOfOnJob(HttpServletRequest request, @RequestBody Map<String, Object> map) {
		try {
			Integer personnelCount = mainDictService.getPersonnelCountOfOnJob(map);
			if(null == personnelCount){
				outJson(info(ResponseCodeEnum.NO_DATA.getCode(), "暂无数据"));
				return;
			}
			outJson(successInfo(personnelCount));
		} catch (Exception e) {
			log.error("getMainDictInfoByKey",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		} 
	}
	
	/**
	 * 
	 * @Description 新增主体字典信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/addMainDictInfo", method = RequestMethod.POST)
	@AuthAnnotation(action="/addMainDictInfo")
	public void addMainDictInfo(HttpServletRequest request, @RequestBody OrganizationInfoDTO organizationInfoDTO) {
		try {
			if (StringUtils.isBlank(organizationInfoDTO.getOrganizationName())) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			Integer result = mainDictService.addMainDictInfo(organizationInfoDTO);
			if(null == result || result <= 0){
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "新增主体字典信息失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("addMainDictInfo",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}

	}
	
	/**
	 * 
	 * @Description 更新主体字典信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/updateMainDictInfo", method = RequestMethod.POST)
	@AuthAnnotation(action="/updateMainDictInfo")
	public void updateMainDictInfo(HttpServletRequest request, @RequestBody OrganizationInfoDTO organizationInfoDTO) {
		try {
			if (null == organizationInfoDTO.getOrganizationId()) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			Integer result = mainDictService.updateMainDictInfo(organizationInfoDTO);
			if(null == result || result <= 0){
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "更新主体字典信息失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateMainDictInfo",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}

	}
	
	/**
	 * 
	 * @Description 新增主体字典相关领导信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/addMainDictOfHospitalLeaderInfo", method = RequestMethod.POST)
	@AuthAnnotation(action="/addMainDictOfHospitalLeaderInfo")
	public void addMainDictOfHospitalLeaderInfo(HttpServletRequest request, @RequestBody Map<String,Object> map) {
		try {
			//员工id集合
			List<String> empIds = (List<String>)map.get("empIds");
			if (null == map.get("empIds") || empIds.isEmpty()) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			Integer count = mainDictService.checkHospitalLeaderInfo(map);
			if(null == count || count >= 1){
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "该员工已存在于领导列表，请重新选择"));
				return;
			}
			Integer result = mainDictService.addMainDictOfHospitalLeaderInfo(map);
			if(null == result || result <= 0){
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "新增领导班子信息失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("addMainDictOfHospitalLeaderInfo",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}

	}
	
	/**
	 * 
	 * @Description 查询相关领导列表信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/getMainDictOfHospitalLeaderInfoList", method = RequestMethod.POST)
	@AuthAnnotation(action="/getMainDictOfHospitalLeaderInfoList")
	public void getMainDictOfHospitalLeaderInfoList(HttpServletRequest request, @RequestBody Map<String,Object> map) {
		try {
			List<OrganizationlLeader> OrganizationlLeaderList = mainDictService.getMainDictOfHospitalLeaderInfoList(map);
			if(null == OrganizationlLeaderList || OrganizationlLeaderList.isEmpty()){
				outJson(info(ResponseCodeEnum.NO_DATA.getCode(), "暂无数据"));
				return;
			}
			outJson(successInfo(OrganizationlLeaderList));
		} catch (Exception e) {
			log.error("getMainDictOfHospitalLeaderInfoList",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	/**
	 * 
	 * @Description 删除相关领导列表信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	@RequestMapping(value = "/deleteMainDictOfHospitalLeaderInfoById", method = RequestMethod.POST)
	@AuthAnnotation(action="/deleteMainDictOfHospitalLeaderInfoById")
	public void deleteMainDictOfHospitalLeaderInfoById(HttpServletRequest request, @RequestBody Map<String,Object> map) {
		try {
			if (null == map.get("id")) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "参数校验失败"));
				return;
			}
			Integer result = mainDictService.deleteMainDictOfHospitalLeaderInfoById(map);
			if(null == result || result <= 0){
				outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "删除领导班子信息失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("deleteMainDictOfHospitalLeaderInfoById",e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

}
