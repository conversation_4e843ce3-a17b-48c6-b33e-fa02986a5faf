package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.MessageNotificationSystemV2DTO;
import com.bojun.system.entity.MessageNotificationSystemV2;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * MessageNotificationSystemV2Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-07-22 16:11:35
 */
@Mapper
public interface MessageNotificationSystemV2Mapper extends BaseMapper<MessageNotificationSystemV2> {

    /**
     * 查询消息通知关联产品表
     *
     * @param id 消息通知关联产品表ID
     * @return 消息通知关联产品表
     */
    public MessageNotificationSystemV2DTO selectMessageNotificationSystemV2ById(Integer id);

    /**
     * 查询消息通知关联产品表列表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 消息通知关联产品表集合
     */
    public List<MessageNotificationSystemV2DTO> selectMessageNotificationSystemV2List(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO);
}
