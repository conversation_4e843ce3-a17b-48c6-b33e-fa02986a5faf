package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.employee.entity.dto.EmployeeInfoDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.system.entity.OrganizationlLeader;


/**
 * Model：模块名称 Description：文件描述 Author: 肖泽权 created：2020年1月12日
 */
@Mapper
public interface MainDictMapper {

	/**
	 * 
	 * @Description 获取主体字典信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 *            void 2020年1月12日
	 */
	public OrganizationInfoDTO getMainDictInfoByKey(Map<String, Object> map);
	
	/**
	 * 
	 * @Description 获取在职编制人员数量
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	public Integer getPersonnelCountOfOnJob(Map<String, Object> map);

	/**
	 * 
	 * @Description 新增主体字典信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 *            void 2020年1月12日
	 */
	public Integer addMainDictInfo(OrganizationInfoDTO organizationInfoDTO);

	/**
	 * 
	 * @Description 更新主体字典信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 *            void 2020年1月12日
	 */
	public Integer updateMainDictInfo(OrganizationInfoDTO organizationInfoDTO);

	/**
	 * 
	 * @Description 新增主体字典相关领导信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 *            void 2020年1月12日
	 */
	public Integer addMainDictOfHospitalLeaderInfo(List<String> list);
	
	/**
	 * 
	 * @Description 校验领导信息是否存在
	 * <AUTHOR>
	 * @param request
	 * @param map
	 *            void 2020年1月12日
	 */
	public Integer checkHospitalLeaderInfo(List<String> list);
	
	/**
	 * 
	 * @Description 查询相关领导列表信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	public List<OrganizationlLeader> getMainDictOfHospitalLeaderInfoList(Map<String,Object> map);
	
	/**
	 * 
	 * @Description 删除领导列表信息
	 * <AUTHOR>
	 * @param request
	 * @param map
	 * void
	 * 2020年1月12日
	 */
	public Integer deleteMainDictOfHospitalLeaderInfoById(Map<String,Object> map);

	/**
	 * @Description 查询在编人数
	 * <AUTHOR>
	 * @param map
	 * @return
	 * @return long
	 */
	public Integer queryEmployeeCount(Map<String, Object> map);

}
