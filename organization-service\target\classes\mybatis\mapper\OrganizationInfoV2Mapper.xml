<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.organization.mapper.OrganizationInfoV2Mapper">
    
    <resultMap type="com.bojun.organization.dto.OrganizationInfoV2DTO" id="OrganizationInfoDTOResult">
        <result property="organizationId"    column="organization_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="organizationCode"    column="organization_code"    />
        <result property="organizationName"    column="organization_name"    />
        <result property="weight"    column="weight"    />
        <result property="logoImg"    column="logo_img"    />
        <result property="socialCreditCode"    column="social_credit_code"    />
        <result property="organizationProperty"    column="organization_property"    />
        <result property="organizationScale"    column="organization_scale"    />
        <result property="organizationClassCode"    column="organization_class_code"    />
        <result property="organizationTypeCode"    column="organization_type_code"    />
        <result property="organizationDirector"    column="organization_director"    />
        <result property="telephoneNumber"    column="telephone_number"    />
        <result property="provinceCode"    column="province_code"    />
        <result property="cityCode"    column="city_code"    />
        <result property="countyCode"    column="county_code"    />
        <result property="organizationAddress"    column="organization_address"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="organizationIntroduction"    column="organization_introduction"    />
        <result property="organizationHistory"    column="organization_history"    />
        <result property="organizationHonor"    column="organization_honor"    />
        <result property="frontBusinessLicense"    column="front_business_license"    />
        <result property="backBusinessLicense"    column="back_business_license"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="decideAuthorize"    column="decide_authorize"    />
        <result property="actualAuthorize"    column="actual_authorize"    />
        <result property="emptyAuthorize"    column="empty_authorize"    />
        <result property="decideDirector"    column="decide_director"    />
        <result property="actualDirector"    column="actual_director"    />
        <result property="decideViceDirector"    column="decide_vice_director"    />
        <result property="actualViceDirector"    column="actual_vice_director"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="townCode"    column="town_code"    />
        <result property="villageCode"    column="village_code"    />
        <result property="townName"    column="town_name"    />
        <result property="villageName"    column="village_name"    />
    </resultMap>

    <sql id="selectOrganizationInfo">
    	select
	        organization_id,
	        parent_id,
	        organization_code,
	        organization_name,
	        weight,
	        logo_img,
	        social_credit_code,
	        organization_property,
	        organization_scale,
	        organization_class_code,
	        organization_type_code,
	        organization_director,
	        telephone_number,
	        province_code,
	        city_code,
	        county_code,
	        organization_address,
	        longitude,
	        latitude,
	        organization_introduction,
	        organization_history,
	        organization_honor,
	        front_business_license,
	        back_business_license,
	        is_enabled,
	        decide_authorize,
	        actual_authorize,
	        empty_authorize,
	        decide_director,
	        actual_director,
	        decide_vice_director,
	        actual_vice_director,
	        create_user_id,
	        create_time,
	        town_code,
	        village_code,
	        town_name,
	        village_name
		from 
        	t_organization_info
    </sql>

    <select id="selectOrganizationInfoById" parameterType="int" resultMap="OrganizationInfoDTOResult">
		<include refid="selectOrganizationInfo"/>
		where 
        	organization_id = #{organizationId}
    </select>

    <select id="selectOrganizationInfoList" parameterType="com.bojun.organization.dto.OrganizationInfoV2DTO" resultMap="OrganizationInfoDTOResult">
        <include refid="selectOrganizationInfo"/>
        <where>  
		<if test="organizationId != null "> and organization_id = #{organizationId}</if>
		<if test="parentId != null "> and parent_id = #{parentId}</if>
		<if test="organizationCode != null  and organizationCode != ''"> and organization_code = #{organizationCode}</if>
		<if test="organizationName != null  and organizationName != ''"> and organization_name = #{organizationName}</if>
		<if test="weight != null "> and weight = #{weight}</if>
		<if test="logoImg != null  and logoImg != ''"> and logo_img = #{logoImg}</if>
		<if test="socialCreditCode != null  and socialCreditCode != ''"> and social_credit_code = #{socialCreditCode}</if>
		<if test="organizationProperty != null "> and organization_property = #{organizationProperty}</if>
		<if test="organizationScale != null  and organizationScale != ''"> and organization_scale = #{organizationScale}</if>
		<if test="organizationClassCode != null  and organizationClassCode != ''"> and organization_class_code = #{organizationClassCode}</if>
		<if test="organizationTypeCode != null  and organizationTypeCode != ''"> and organization_type_code = #{organizationTypeCode}</if>
		<if test="organizationDirector != null  and organizationDirector != ''"> and organization_director = #{organizationDirector}</if>
		<if test="telephoneNumber != null  and telephoneNumber != ''"> and telephone_number = #{telephoneNumber}</if>
		<if test="provinceCode != null  and provinceCode != ''"> and province_code = #{provinceCode}</if>
		<if test="cityCode != null  and cityCode != ''"> and city_code = #{cityCode}</if>
		<if test="countyCode != null  and countyCode != ''"> and county_code = #{countyCode}</if>
		<if test="organizationAddress != null  and organizationAddress != ''"> and organization_address = #{organizationAddress}</if>
		<if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
		<if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
		<if test="organizationIntroduction != null  and organizationIntroduction != ''"> and organization_introduction = #{organizationIntroduction}</if>
		<if test="organizationHistory != null  and organizationHistory != ''"> and organization_history = #{organizationHistory}</if>
		<if test="organizationHonor != null  and organizationHonor != ''"> and organization_honor = #{organizationHonor}</if>
		<if test="frontBusinessLicense != null  and frontBusinessLicense != ''"> and front_business_license = #{frontBusinessLicense}</if>
		<if test="backBusinessLicense != null  and backBusinessLicense != ''"> and back_business_license = #{backBusinessLicense}</if>
		<if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
		<if test="decideAuthorize != null "> and decide_authorize = #{decideAuthorize}</if>
		<if test="actualAuthorize != null "> and actual_authorize = #{actualAuthorize}</if>
		<if test="emptyAuthorize != null "> and empty_authorize = #{emptyAuthorize}</if>
		<if test="decideDirector != null "> and decide_director = #{decideDirector}</if>
		<if test="actualDirector != null "> and actual_director = #{actualDirector}</if>
		<if test="decideViceDirector != null "> and decide_vice_director = #{decideViceDirector}</if>
		<if test="actualViceDirector != null "> and actual_vice_director = #{actualViceDirector}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="townCode != null  and townCode != ''"> and town_code = #{townCode}</if>
		<if test="villageCode != null  and villageCode != ''"> and village_code = #{villageCode}</if>
		<if test="townName != null  and townName != ''"> and town_name = #{townName}</if>
		<if test="villageName != null  and villageName != ''"> and village_name = #{villageName}</if>
        </where>
    </select>

	<select id="getChildrenOrgListByRoleId" parameterType="com.bojun.organization.dto.OrganizationInfoV2DTO"
			resultType="com.bojun.organization.dto.OrganizationInfoV2DTO">
		SELECT
		<if test="roleId !=null and roleId !='' ">
			ro.role_id,
			ro.has_auth,
		</if>
			oi.*
		FROM
		organization.t_organization_info oi
		<if test="roleId !=null and roleId !='' ">
			LEFT JOIN system.t_role_organization ro ON oi.organization_id=ro.organization_id
		</if>
		WHERE is_enabled=1
		<if test="roleId !=null and roleId !='' ">
			and ro.role_id=#{roleId}
		</if>
		<if test="organizationId !=null and organizationId !=''">
			and parent_id =#{organizationId}
		</if>
		<if test="parentIds != null  and parentIds.size() > 0">
			and parent_id in
			<foreach item="item" index="index"
					 collection="parentIds" open="(" separator=","
					 close=")">
				#{item}
			</foreach>
		</if>
	</select>

</mapper>