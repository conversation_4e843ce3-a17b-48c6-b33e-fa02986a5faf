/**
 * 
 */
package com.bojun.base.manage.api.system.hystrix;


import org.springframework.stereotype.Component;

import com.bojun.base.manage.api.system.ISystemDictService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.dto.SystemDictDTO;

/**
 * Model： 产品管理接口熔断器
 * Description：产品管理接口熔断器
 * Author：赖水秀
 * created： 2020年4月27日
 */
@Component
public class SystemDictServiceHystrix implements ISystemDictService {

	@Override
	public String getSystemDictTypeList() {		
		throw new BaseRuntimeException("getSystemDictTypeList接口服务已断开");
	}

	@Override
	public String saveSystemDict(SystemDictDTO systemDictDto) {
		throw new BaseRuntimeException("saveSystemDict接口服务已断开");
	}

	@Override
	public String updateSystemDict(SystemDictDTO systemDictDto) {
		throw new BaseRuntimeException("updateSystemDict接口服务已断开");
	}

	@Override
	public String getSystemDictById(String systemId) {
		throw new BaseRuntimeException("getSystemDictById接口服务已断开");
	}

	@Override
	public String getSystemDictList(SystemDictDTO systemDictDto) {
		throw new BaseRuntimeException("getSystemDictList接口服务已断开");
	}
	
	@Override
	public String getSystemDictByRoleId(SystemDictDTO systemDictDto) {
		// TODO Auto-generated method stub
		throw new BaseRuntimeException("getSystemDictByRoleId接口服务已断开");
	}

	@Override
	public String getSystemDictTreeList() {
		throw new BaseRuntimeException("getSystemDictTreeList接口服务已断开");
	}

	@Override
	public String getSystemMenuTreeByRoleId(SystemDictDTO systemDictDto) {
		throw new BaseRuntimeException("getSystemMenuTreeByRoleId接口服务已断开");
	}

	

}
