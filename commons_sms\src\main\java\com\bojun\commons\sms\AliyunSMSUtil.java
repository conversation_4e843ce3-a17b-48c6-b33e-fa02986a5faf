/**
 * 
 */
package com.bojun.commons.sms;

import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.bojun.utils.PropertiesUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
*Model：阿里云短信工具类
*Description：阿里云短信工具类
*Author:段德鹏
*created：2020年11月9日
**/
@Slf4j
public class AliyunSMSUtil {
	
	//key
	private static String ACCESS_KEY_ID = PropertiesUtils.getProperty("config.properties", "aliyun.sms.accessKeyId");
	
	//秘钥
	private static String ACCESS_KEY_SECRET = PropertiesUtils.getProperty("config.properties", "aliyun.sms.accessKeySecret");
	
	//短信签名名称
	private static String SIGN_NAME = PropertiesUtils.getProperty("config.properties", "aliyun.sms.signName");
	
	//API 版本号
	private static String SYS_VERSION = PropertiesUtils.getProperty("config.properties", "aliyun.sms.sysVersion");
	
	//短信服务API支持的区域id
	private static String REGION_ID = PropertiesUtils.getProperty("config.properties", "aliyun.sms.regionId");
	
	//短信服务API域名
	private static String SYS_DOMAIN = PropertiesUtils.getProperty("config.properties", "aliyun.sms.sysDomain");
	
	
	/**
	 * @Description 发送短信
	 * <AUTHOR>
	 * @param phoneNumbers 接收短信手机号 (支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码)
	 * @param templateCode 短信模板代码(预先在阿里短信平台设置好的短信模板code)
	 * @param verifyCode  验证码
	 * @param jsonParam  模板参数
	 * @return SMSResponse
	 * created：2021年3月30日
	 */
	public static SMSResponse sendSms(String phoneNumbers, String templateCode, String verifyCode, String jsonParam){
		// 响应参数
        SMSResponse smsResponse = new SMSResponse();
        //发送验证码短信
		if(StringUtils.isNotBlank(verifyCode)){
			smsResponse = sendVerifyCodeMessage(phoneNumbers, verifyCode, templateCode);
			return smsResponse;
		}
		//发送普通短信
		smsResponse = sendNotificationMessage(phoneNumbers, jsonParam, templateCode);
		return smsResponse;
	}
	
	
	/**
	 * @Description 发送验证码短信
	 * <AUTHOR>
	 * @param phoneNumbers 接收短信手机号 (支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码)
	 * @param code  验证码
	 * @param templateCode 短信模板代码
	 * @return SMSResponse
	 * created：2020年11月9日
	 */
	public static SMSResponse sendVerifyCodeMessage(String phoneNumbers, String code, String templateCode) {
		try {
			// 获取client
	        IAcsClient client = getIAcsClient();
	        // 设置请求参数
	        CommonRequest request = setCommonRequest(phoneNumbers, templateCode);
	        // 设置模板参数
	        Map<String, String> map = new HashMap<>();
		    map.put("code", code);
		    String json = JSONObject.toJSONString(map);
	        request.putQueryParameter("TemplateParam", json);
	        // 发送请求
	        CommonResponse response = client.getCommonResponse(request);
	        // 解析json
	        String jsonStr = response.getData();
	        JSONObject resultOjb = JSONObject.parseObject(jsonStr);
	        // 响应参数
	        SMSResponse smsResponse = new SMSResponse();
	        if (!AliyunSMSConstants.OK.equals(resultOjb.getString("Code"))) {
	        	smsResponse.setSuccess(false);
	        	smsResponse.setMsg(resultOjb.getString("Message"));
	        	return smsResponse;
	        }
	        smsResponse.setSuccess(true);
	        return smsResponse;
		} catch (Exception e) {
			log.info("调用异常，原因：" + e.getMessage());
        	throw new RuntimeException(e.getMessage(), e);
		}
	}
	
	/**
	 * @Description 发送通知短信
	 * <AUTHOR>
	 * @param phoneNumbers  接收短信手机号 (支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码)
	 * @param templateCode 短信模板代码
	 * @return SMSResponse
	 * created：2020年11月10日
	 */
	public static SMSResponse sendNotificationMessage(String phoneNumbers, String templateCode) {
		try {
			// 获取client
	        IAcsClient client = getIAcsClient();
	        // 设置请求参数
	        CommonRequest request = setCommonRequest(phoneNumbers, templateCode);
	        // 发送请求
	        CommonResponse response = client.getCommonResponse(request);
	        // 解析json
	        String jsonStr = response.getData();
	        JSONObject resultOjb = JSONObject.parseObject(jsonStr);
	        // 响应参数
	        SMSResponse smsResponse = new SMSResponse();
	        if (!AliyunSMSConstants.OK.equals(resultOjb.getString("Code"))) {
	        	smsResponse.setSuccess(false);
	        	smsResponse.setMsg(resultOjb.getString("Message"));
	        	return smsResponse;
	        }
	        smsResponse.setSuccess(true);
	        return smsResponse;
		} catch (Exception e) {
			log.info("调用异常，原因：" + e.getMessage());
        	throw new RuntimeException(e.getMessage(), e);
		}
	}
	
	/**
	 * @Description 发送通知短信
	 * <AUTHOR>
	 * @param phoneNumbers 接收短信手机号 (支持对多个手机号码发送短信，手机号码之间以英文逗号（,）分隔。上限为1000个手机号码)
	 * @param jsonParam 短信模板参数（json格式）
	 * @param templateCode 短信模板代码
	 * @return SMSResponse
	 * created：2020年11月10日
	 */
	public static SMSResponse sendNotificationMessage(String phoneNumbers, String jsonParam, String templateCode) {
		try {
			// 获取client
	        IAcsClient client = getIAcsClient();
	        // 设置请求参数
	        CommonRequest request = setCommonRequest(phoneNumbers, templateCode);
	        // 设置模板参数
	        request.putQueryParameter("TemplateParam", jsonParam);
	        // 发送请求
	        CommonResponse response = client.getCommonResponse(request);
	        // 解析json
	        String jsonStr = response.getData();
	        JSONObject resultOjb = JSONObject.parseObject(jsonStr);
	        // 响应参数
	        SMSResponse smsResponse = new SMSResponse();
	        if (!AliyunSMSConstants.OK.equals(resultOjb.getString("Code"))) {
	        	smsResponse.setSuccess(false);
	        	smsResponse.setMsg(resultOjb.getString("Message"));
	        	return smsResponse;
	        }
	        smsResponse.setSuccess(true);
	        return smsResponse;
		} catch (Exception e) {
			log.info("调用异常，原因：" + e.getMessage());
        	throw new RuntimeException(e.getMessage(), e);
		}
	}
	
	/**
	 * @Description 获取client
	 * <AUTHOR>
	 * @return IAcsClient
	 * created：2020年11月10日
	 */
	public static IAcsClient getIAcsClient() {
		DefaultProfile profile = DefaultProfile.getProfile(REGION_ID, ACCESS_KEY_ID, ACCESS_KEY_SECRET);
        return new DefaultAcsClient(profile);
	}
	
	/**
	 * @Description 设置请求参数
	 * <AUTHOR>
	 * @param phoneNumbers 接收短信手机号
	 * @param templateCode 短信模板代码
	 * @return CommonRequest
	 * created：2020年11月10日
	 * @throws UnsupportedEncodingException 
	 */
	public static CommonRequest setCommonRequest(String phoneNumbers, String templateCode) throws UnsupportedEncodingException {
		CommonRequest request = new CommonRequest();
        request.setSysMethod(MethodType.POST);
        request.setSysDomain(SYS_DOMAIN); //短信API域名
        request.setSysAction(AliyunSMSConstants.SEND_SMS_ACITON); //发送短信接口名
        request.setSysVersion(SYS_VERSION); //短信API版本
        request.putQueryParameter("SignName", SIGN_NAME); //短信签名
        request.putQueryParameter("PhoneNumbers", phoneNumbers);//接收短信手机号
        request.putQueryParameter("TemplateCode", templateCode); //短信模板代码
        return request;
	}
	
	public static void main(String[] args) {
		SMSResponse rep = AliyunSMSUtil.sendVerifyCodeMessage("18617113999", "1111", 
				AliyunSMSTemplateEnum.LOGIN_TEMPLATE_CODE.getTemplateCode());
		System.out.println("code:" + rep.isSuccess() + "msg:" + rep.getMsg());
	}
	

}
