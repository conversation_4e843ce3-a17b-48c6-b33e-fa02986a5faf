
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import com.bojun.system.dto.*;
import org.apache.ibatis.annotations.Mapper;


/**
 * Model： 公共字典DAO
 * Description：公共字典DAO
 * Author：赖水秀
 * created： 2020年5月7日
 */
@Mapper
public interface CommonDictMapper {
	
	/**
	 * @Description 查询省份列表
	 * <AUTHOR>
	 * @return
	 * List<ProvinceDictDTO>
	 * 2020年5月7日
	 */
	List<ProvinceDictDTO> getProvinceList();
	
	/**
	 * @Description 查询市区列表
	 * <AUTHOR>
	 * @return
	 * List<CityDictDTO>
	 * 2020年5月7日
	 */
	List<CityDictDTO> getCityList(String provinceCode);
	
	/**
	 * @Description 查询县区列表
	 * <AUTHOR>
	 * @return
	 * List<CityDictDTO>
	 * 2020年5月7日
	 */
	List<CountyDictDTO> getCountyList(String cityCode);
	
	/**
	 * 
	 * @Description 查询乡镇列表
	 * <AUTHOR>
	 * @param countyCode
	 * @return
	 * @return List<TownDictDTO>
	 * created：2020年6月24日
	 */
	List<TownDictDTO> getTownList(String countyCode);
	
	/**
	 * 
	 * @Description 查询村居委会列表
	 * <AUTHOR>
	 * @param townCode
	 * @return
	 * @return List<VillageDictDTO>
	 * created：2020年6月24日
	 */
	List<VillageDictDTO> getVillageList(String townCode);
	
	
	/**
	 * 
	 * @Description 查询民族列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<Map<String,Object>>
	 * created：2020年7月25日
	 */
	List<NationDTO> getNationList(Map<String, Object> paramsMap);

}
