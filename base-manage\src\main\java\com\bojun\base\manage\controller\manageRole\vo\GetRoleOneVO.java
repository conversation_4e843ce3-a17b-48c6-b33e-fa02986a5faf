/**
 * 
 */
package com.bojun.base.manage.controller.manageRole.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：角色管理 
 * Description：查询角色
 *  Author：lj 
 *  created： 2020年4月27日
 */
@ApiModel(value = "查询角色", description = "查询角色传入参数")
public class GetRoleOneVO    implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;



	@NotEmpty(message = "角色id不能为空")
	@ApiModelProperty(value = "角色id", required = true, example = "1")
	private String roleId;



	public String getRoleId() {
		return roleId;
	}



	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}



	

}
