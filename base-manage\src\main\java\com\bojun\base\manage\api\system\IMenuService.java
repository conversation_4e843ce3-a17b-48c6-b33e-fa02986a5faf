/**
 * 
 */
package com.bojun.base.manage.api.system;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.bojun.base.manage.api.system.hystrix.MenuServiceHystrix;
import com.bojun.system.dto.MenuDTO;

/**
 * Model： 菜单管理服务
 * Description：菜单管理服务
 * Author：赖水秀
 * created： 2020年4月28日
 */
@FeignClient(name="system-service", fallback = MenuServiceHystrix.class)
public interface IMenuService {
		
	/**
	 * @Description 保存菜单信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/saveMenu", method = RequestMethod.POST)
	String saveMenu(MenuDTO menuDTO);
	
	
	/**
	 * @Description 修改菜单信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/updateMenu", method = RequestMethod.POST)
	String updateMenu(MenuDTO menuDTO);
	
	
	/**
	 * @Description 根据单个菜单信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * SystemDictDTO
	 * 2020年4月28日
	 */	
	@RequestMapping(value = "/getMenuById", method = RequestMethod.POST)
	String getMenuById(@RequestParam(value="menuId") String menuId);

	/**
	 * @Description 查询菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getMenuPageList", method = RequestMethod.POST)
	String getMenuPageList(MenuDTO menuDTO);	
	
	
	/**
	 * @Description 查询树形菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/getMenuTreeList", method = RequestMethod.POST)
	String getMenuTreeList(MenuDTO menuDTO);
	
	/**
	 * @Description 单个删除信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月29日
	 */
	@RequestMapping(value = "/deleteMenuById", method = RequestMethod.POST)
	String deleteMenuById(@RequestParam(value="menuId") String menuId);
	
	
	/**
	 * @Description 批量删除信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月29日
	 */
	@RequestMapping(value = "/batchDeleteMenu", method = RequestMethod.POST)
	String batchDeleteMenu(List<String> menuIds);
	
	
	/**
	 * @Description 获取菜单下的所有按钮
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * String
	 * 2020年5月12日
	 */
	@RequestMapping(value = "/getButtonListByMenu", method = RequestMethod.POST)
	String getButtonListByMenu(@RequestParam(value="menuId") String menuId);
	
	
	/**
	 * @Description 保存按钮信息
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * String
	 * 2020年5月12日
	 */
	@RequestMapping(value = "/saveButtonInfo", method = RequestMethod.POST)
	String saveButtonInfo(MenuDTO menuDTO);
	
	/**
	 * @Description 禁用/启用菜单
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * String
	 * 2020年6月4日
	 */
	@RequestMapping(value = "/updateMenuStatus", method = RequestMethod.POST)
	String updateMenuStatus(MenuDTO menuDTO);
	/**
	 * @Description 查询菜单按钮树
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月28日
	 */
	@RequestMapping(value = "/getMenuButtonTree", method = RequestMethod.POST)
	String getMenuButtonTree(MenuDTO menuDTO);
}
