/**
 * 
 */
package com.bojun.base.manage.interceptor;

import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.CommonUserCacheBean;
import com.bojun.system.dto.ManageUserDTO;

/**
*Model：用户权限拦截器 
*Description：用户权限拦截器 
*Author: 段德鹏
*created：2020年3月3日
 */
@Component
public class UserAuthorityInterceptor extends HandlerInterceptorAdapter {

	private Log log = LogFactory.getLog(UserAuthorityInterceptor.class);

	@Autowired
	private RedisUtil redisUtil;
	
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		
		log.info("--------------自定义拦截器----------------");
		
		if (!(handler instanceof HandlerMethod)) {
			return true;
		}
		// 登录判断
		String token = request.getHeader("token");
		log.info("--------------token----------------:" + token);
		JSONObject json = new JSONObject();
		if (StringUtils.isBlank(token)) {
			json.put("code", ResponseCodeEnum.UNLOGIN_REQUEST.getCode());
			json.put("msg", ResponseCodeEnum.UNLOGIN_REQUEST.getErrorDescr());
			outString(response, JSON.toJSONString(json));
			return false;
		}
//		判断缓存key是否存在
		if (!redisUtil.hasKey(token)) {
			json.put("code", ResponseCodeEnum.UNLOGIN_REQUEST.getCode());
			json.put("msg", ResponseCodeEnum.UNLOGIN_REQUEST.getErrorDescr());
			outString(response, JSON.toJSONString(json));
			return false;
		}
		String jsonString = JSON.toJSONString(redisUtil.get(token));
		CommonUserCacheBean userCacheBean = JSON.parseObject(jsonString, CommonUserCacheBean.class);
		ManageUserDTO manageUser = new ManageUserDTO();
		BeanUtils.copyProperties(userCacheBean, manageUser);
		if (0 == manageUser.getStatus()) {
			json.put("code", ResponseCodeEnum.BAD_REQUEST.getCode());
			json.put("msg", "用户状态非正常，请联系管理员");
			outString(response, JSON.toJSONString(json));
			return false;
		}
		
//		HandlerMethod handlerMethod = (HandlerMethod) handler;
//		String actionName = handlerMethod.getMethod().getName();
//		String authority = null;
//		try {
//			authority = handlerMethod.getMethodAnnotation(AuthAnnotation.class).action();
//		} catch (Exception e) {
//			log.error("找不到权限注解方法异常", e);
//			
//		}
//		if (StringUtils.isBlank(authority)) {
//			json.put("code", ResponseCodeEnum.BAD_REQUEST.getCode());
//       	json.put("msg", "拦截器找不到方法异常");
//       	outString(response, JSON.toJSONString(json));
//       	return false;
//		}
//		//超级管理不用过滤权限
		if (0 == manageUser.getAuthType() ) {
			redisUtil.set(token, userCacheBean, 30*60*1000);
			request.setAttribute("manageUser", manageUser);
			return true;
		}
//		//获取用户权限列表
//		List<String> authorityMenu = menuManageService.getUserAuthorityMenuList(manageUser.getRoleId());
//		if (null == authorityMenu || (!authorityMenu.contains(authority) 
//				&& !"common".equals(authority))) {
//			json.put("code", ResponseCodeEnum.UNAUTHORITY_REQUEST.getCode());
//       	json.put("msg", ResponseCodeEnum.UNAUTHORITY_REQUEST.getErrorDescr());
//       	outString(response, JSON.toJSONString(json));
//       	return false;
//       }
		redisUtil.set(token, userCacheBean, 30*60*1000);
		request.setAttribute("manageUser", manageUser);
		
		return true;
	}

	public void afterCompletion(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, Exception arg3)
			throws Exception {

	}

	public void postHandle(HttpServletRequest arg0, HttpServletResponse arg1, Object arg2, ModelAndView arg3)
			throws Exception {

	}

	/**
	 * 将字符串转换成JSON字符串发送
	 * 
	 * @param str
	 */
	public void outString(HttpServletResponse response, String str) {
		try {
			response.setCharacterEncoding("UTF-8");
			PrintWriter out = response.getWriter();
			out.write(str);
			out.flush();
			out.close();
		} catch (IOException e) {
			log.error(e);
		}
	}

}
