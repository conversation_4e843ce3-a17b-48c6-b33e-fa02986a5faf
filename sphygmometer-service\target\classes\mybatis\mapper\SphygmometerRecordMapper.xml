<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.SphygmometerRecordMapper">

    <resultMap type="com.bojun.sphygmometer.dto.SphygmometerRecordDTO" id="SphygmometerRecordDTOResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceType" column="device_type"/>
        <result property="recordType" column="record_type"/>
        <result property="systolicPressure" column="systolic_pressure"/>
        <result property="diastolicPressure" column="diastolic_pressure"/>
        <result property="heartbeat" column="heartbeat"/>
        <result property="pressureResult" column="pressure_result"/>
        <result property="heartbeatResult" column="heartbeat_result"/>
        <result property="measurePlace" column="measure_place"/>
        <result property="measureTime" column="measure_time"/>
    </resultMap>

    <sql id="selectSphygmometerRecord">
        select tsr.id,
               tsr.user_id,
               serial_number,
               device_id,
               device_type,
               record_type,
               systolic_pressure,
               diastolic_pressure,
               heartbeat,
               pressure_result,
               heartbeat_result,
               measure_place,
               measure_time,
               organization_id,
               (case
                    when
                        (pressure_result = 3 and
                         heartbeat_result = 3) then 2
                    when (
                            pressure_result = 2 or
                            heartbeat_result = 2) then 3
                    else 1 end) as                                                                   measureResult,
               (select device_no from t_sphygmometer_device tsd where tsd.device_id = tsr.device_id) device_no
        from t_sphygmometer_record tsr
    </sql>

    <select id="selectSphygmometerRecordById" parameterType="int"
            resultMap="SphygmometerRecordDTOResult">
        <include refid="selectSphygmometerRecord"/>
        where
        tsr.id = #{id}
    </select>

    <select id="selectSphygmometerRecordList" parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO" resultMap="SphygmometerRecordDTOResult">
        select
            tsr.id,
            tsr.user_id,
            tsu.mobile,
            tsu.nick_name,
            tsr.record_type,
            tsu.manage_organization_id,
            tsu.register_organization_id,
            tsr.organization_id as measure_organization_id,
            (select organization_name from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
            (select organization_name from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
            (select organization_name from organization.t_organization_info where tsr.organization_id = organization_id) measure_organization_name,
            serial_number,
            tsr.device_id,
            tsd.device_no,
            tsr.device_type,
            systolic_pressure,
            diastolic_pressure,
            tsr.heartbeat,
            pressure_result,
            heartbeat_result,
            measure_place,
            measure_time,
            (case when (pressure_result = 3 and heartbeat_result = 3) then 2 when (pressure_result = 2 or heartbeat_result = 2) then 3 else 1 end) as measureResult,
            tsu.wx_open_id,
            #{measureStartTime} as measure_start_time,
            #{measureEndTime}   as measure_end_time,
            tsd.relation_org_id
        from t_sphygmometer_record tsr
        left join t_sphygmometer_user tsu on tsr.user_id = tsu.user_id
        left join t_sphygmometer_device tsd on tsr.device_id = tsd.device_id
        <where>
            <if test="id != null ">and tsr.id = #{id}</if>
            <if test="userId != null ">and tsr.user_id = #{userId}</if>
            <if test="serialNumber != null  and serialNumber != ''">and serial_number = #{serialNumber}</if>
            <if test="deviceId != null ">AND tsd.device_id = #{deviceId}</if>
            <if test="deviceType != null ">and tsr.device_type = #{deviceType}</if>
            <if test="recordType != null ">and tsr.record_type = #{recordType}</if>
            <if test="systolicPressure != null ">and systolic_pressure = #{systolicPressure}</if>
            <if test="diastolicPressure != null ">and diastolic_pressure = #{diastolicPressure}</if>
            <if test="heartbeat != null ">and tsr.heartbeat = #{heartbeat}</if>
            <if test="pressureResult != null ">and pressure_result = #{pressureResult}</if>
            <if test="heartbeatResult != null ">and heartbeat_result = #{heartbeatResult}</if>
            <if test="measurePlace != null  and measurePlace != ''">and measure_place = #{measurePlace}</if>
            <if test="measureStartTime != null  and measureStartTime != ''">
                AND tsr.measure_time >= #{measureStartTime}
                AND
                tsr.measure_time &lt;= #{measureEndTime}
            </if>
            <if test="manageOrgIdList != null  and manageOrgIdList.size() > 0">
                and (tsu.manage_organization_id in
                <foreach item="item" index="index"
                         collection="manageOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
                or tsr.organization_id in
                <foreach item="item" index="index"
                         collection="manageOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="measureOrgIdList != null  and measureOrgIdList.size() > 0">
                and tsr.organization_id in
                <foreach item="item" index="index"
                         collection="measureOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="organizationId != null  and organizationId != ''">
                AND tsr.organization_id = #{organizationId}
            </if>
            <if test="measureResult != null  and measureResult == 1">
                AND pressure_result = 1 and heartbeat_result = 1
            </if>
            <if test="measureResult != null  and measureResult == 2">
                AND (pressure_result = 2 or heartbeat_result = 2)
                and
                pressure_result != 3 and heartbeat_result != 3
            </if>
            <if test="measureResult != null  and measureResult == 3">
                AND (pressure_result = 3 or heartbeat_result = 3)
            </if>
            <if test="measureResult != null  and measureResult == 4">
                AND (pressure_result = 3 or heartbeat_result = 3
                OR
                pressure_result = 2 or heartbeat_result = 2)
            </if>
            <if test="searchContent != null  and searchContent != ''">
                AND (
                tsu.nick_name LIKE concat( '%', '${searchContent}', '%' ) OR
                tsu.mobile LIKE concat( '%', '${searchContent}', '%' ) OR
                tsd.device_no LIKE concat( '%', '${searchContent}', '%' )
                )
            </if>
        </where>
        order by measure_time desc
    </select>

    <select id="getListExcel" parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultMap="SphygmometerRecordDTOResult">
        SELECT
        tsr.id,
        tsr.user_id,
        tsu.nick_name,
        tsr.record_type,
        <if test="needExportMobile != null  and needExportMobile == 1">
            tsu.mobile,
        </if>
        ( SELECT organization_name FROM organization.t_organization_info WHERE tsr.organization_id = organization_id )
        measure_organization_name,
        ( SELECT organization_name FROM organization.t_organization_info WHERE tsu.manage_organization_id =
        organization_id ) organization_name,
        serial_number,
        tsd.device_id,
        tsd.device_no,
        tsd.device_type,
        systolic_pressure,
        diastolic_pressure,
        tsr.heartbeat,
        pressure_result,
        heartbeat_result,
        measure_place,
        measure_time,
        ( CASE WHEN ( pressure_result = 3 AND heartbeat_result = 3 ) THEN 2 WHEN ( pressure_result = 2 OR
        heartbeat_result = 2 ) THEN 3 ELSE 1 END ) AS measureResult,
        tsu.wx_open_id,
        #{measureStartTime} as measure_start_time,
        #{measureEndTime} as measure_end_time
        FROM
        t_sphygmometer_record tsr
        LEFT JOIN t_sphygmometer_user tsu ON tsr.user_id = tsu.user_id
        LEFT JOIN t_sphygmometer_device tsd ON tsr.device_id = tsd.device_id
        <where>
            <if test="pressureResult != null or heartbeatResult != null">
                (
                <if test="pressureResult != null ">pressure_result = #{pressureResult}</if>
                <if test="pressureResult != null and heartbeatResult != null">OR</if>
                <if test="heartbeatResult != null ">heartbeat_result = #{heartbeatResult}</if>
                )
            </if>
            <if test="deviceType != null ">and tsr.device_type = #{deviceType}</if>
            <if test="measureStartTime != null  and measureStartTime != ''">
                AND tsr.measure_time >= #{measureStartTime}
                AND
                tsr.measure_time &lt;= #{measureEndTime}
            </if>
            <if test="manageOrgIdList != null  and manageOrgIdList.size() > 0">
                and (tsu.register_organization_id in
                <foreach item="item" index="index"
                         collection="manageOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
                or tsr.organization_id in
                <foreach item="item" index="index"
                         collection="manageOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="measureOrgIdList != null  and measureOrganizationLists.size() > 0">
                and tsr.organization_id in
                <foreach item="item" index="index"
                         collection="measureOrgIdList" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="organizationId != null  and organizationId != ''">
                AND tsr.organization_id = #{organizationId}
            </if>
            <if test="searchContent != null  and searchContent != ''">
                AND (
                tsu.nick_name LIKE concat( '%', '${searchContent}', '%' ) OR
                tsu.mobile LIKE concat( '%', '${searchContent}', '%' ) OR
                tsd.device_no LIKE concat( '%', '${searchContent}', '%' )
                )
            </if>
        </where>
        order by measure_time desc
    </select>


    <select id="selectStatisticsInfo" parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultMap="SphygmometerRecordDTOResult">
        select
        t1.counts,
        t2.counts as anomalyCounts,
        (t2.counts/t1.counts*100) as anomalyCountsRole
        from
        (select
        count(1) counts
        from
        t_sphygmometer_record tsr
        LEFT JOIN t_sphygmometer_user tsu ON tsr.user_id = tsu.user_id
        where
        to_days( tsr.measure_time) = to_days( now() )
        <if test="manageOrgIdList != null  and manageOrgIdList.size() > 0">
            and (tsu.register_organization_id in
            <foreach item="item" index="index"
                     collection="manageOrgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            or tsr.organization_id in
            <foreach item="item" index="index"
                     collection="manageOrgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            )
        </if>
        ) t1,
        (select
        count(1) counts
        from
        t_sphygmometer_record tsr
        LEFT JOIN t_sphygmometer_user tsu ON tsr.user_id = tsu.user_id
        where
        to_days(tsr.measure_time) = to_days( now() ) and
        (tsr.pressure_result in(2,3) or tsr.heartbeat_result =2)
        <if test="manageOrgIdList != null  and manageOrgIdList.size() > 0">
            and (tsu.register_organization_id in
            <foreach item="item" index="index"
                     collection="manageOrgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            or tsr.organization_id in
            <foreach item="item" index="index"
                     collection="manageOrgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
            )
        </if>
        ) t2
    </select>
    <select id="selectRecord"
            parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultMap="SphygmometerRecordDTOResult">

        select
        1 id,
        1 user_id,
        1 organization_id,
        1 mobile,
        notice_id,
        1
        organization_name,
        1 serial_number,
        1 device_id,
        1 device_type,
        1
        systolic_pressure,
        1 diastolic_pressure,
        1 heartbeat,
        1 pressure_result,
        1 heartbeat_result,
        1 measure_place,
        title,
        notice_content,
        publish_time,
        1 as type,
        is_read,
        1 measureTimeStr
        from
        system.t_message_notification
        where status = 1
        <if test="platform != null and platform != ''">
            AND synchronization_platform in (#{platform})
        </if>
        union all
        select
        id,
        user_id,
        organization_id,
        (select mobile from
        t_sphygmometer_user where
        tsr.user_id = user_id) mobile,
        1 notice_id,
        (select organization_name
        from organization.t_organization_info where
        tsr.organization_id =
        organization_id) organization_name,
        serial_number,
        device_id,
        device_type,
        systolic_pressure,
        diastolic_pressure,
        heartbeat,
        pressure_result,
        heartbeat_result,
        measure_place,
        1 title,
        1
        notice_content,
        measure_time publish_time,
        2 type
        ,
        is_read,
        DATE_FORMAT(measure_time,
        '%Y-%m-%d %H:%i:%s' ) as
        measureTimeStr
        from
        t_sphygmometer_record tsr
        where
        (pressure_result in (2,3)
        or heartbeat_result = 2)
        <if
                test="measureOrgIdList != null  and measureOrgIdList.size() > 0">
            and organization_id in
            <foreach item="item" index="index"
                     collection="measureOrgIdList" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="measureStartTime != null  and measureStartTime != ''">
            to_days( measure_time) = to_days( now() ))
        </if>
        order by type,publish_time desc
    </select>

    <update id="updateMessage" parameterType="string">
        update
            system.t_message_notification
        set is_read = 1
        where notice_id = #{id}
    </update>

    <select id="selectDetectionStatisticsInfo"
            parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultType="java.lang.Integer">
        select count(DISTINCT user_id) from t_sphygmometer_record
        where 1=1
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND organization_id in
            <foreach item="item" index="index" collection="orgIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="measureResult != null">
            AND pressure_result = #{measureResult}
        </if>
        <if test="measureStartTime != null  and measureStartTime != ''">
            AND measure_time >= #{measureStartTime}
            AND measure_time
            &lt;= #{measureEndTime}
        </if>
    </select>


    <select id="getDayTestCount" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT tsr.user_id)
        FROM
        t_sphygmometer_record tsr
        LEFT
        JOIN t_sphygmometer_user tsu ON tsu.user_id = tsr.user_id
        <where>
            <if test="registerSource != null">
                AND tsu.register_source = #{registerSource}
            </if>
            <if test="dayStartTime != null and dayEndTime != null">
                AND tsr.measure_time BETWEEN #{dayStartTime} AND
                #{dayEndTime}
            </if>
        </where>
    </select>

    <select id="getAvgTestCount" resultType="java.lang.Double">
        SELECT
        AVG( t1.user_count )
        FROM
        (
        SELECT
        COUNT( DISTINCT tsr.user_id ) AS
        user_count,
        DATE_FORMAT( tsr.measure_time, '%Y%m%d' )
        FROM
        t_sphygmometer_record tsr
        LEFT JOIN t_sphygmometer_user tsu ON
        tsu.user_id = tsr.user_id
        <where>
            <if test="registerSource != null">
                AND tsu.register_source = #{registerSource}
            </if>
            <if test="dayEndTime != null">
                AND tsr.measure_time <![CDATA[<]]>
                #{dayEndTime}
            </if>
        </where>
        GROUP BY
        DATE_FORMAT( tsr.measure_time, '%Y%m%d' )
        ) t1
    </select>

    <select id="getAbnormalTestPeople"
            resultType="java.lang.Integer">
        SELECT
        COUNT( DISTINCT tsr.user_id ) AS user_count
        FROM
        t_sphygmometer_record tsr
        WHERE
        (tsr.pressure_result = 2 or
        tsr.pressure_result = 3)
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND tsr.organization_id in
            <foreach item="item" index="index" collection="orgIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and endDate != null">
            AND tsr.measure_time between #{startDate} and #{endDate}
        </if>
    </select>

    <select id="getTotalTestPeopleCount"
            resultType="java.lang.Integer">
        SELECT
        COUNT( DISTINCT tsr1.user_id ) AS user_count
        FROM
        t_sphygmometer_record tsr1
        <where>
            <if test="inPressureResultList != null and inPressureResultList.size() > 0">
                AND tsr1.pressure_result in
                <foreach item="item" index="index" collection="inPressureResultList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="notInPressureResultList != null and notInPressureResultList.size() > 0">
                AND tsr1.user_id not in (
                SELECT
                DISTINCT tsr2.user_id
                FROM
                t_sphygmometer_record tsr2
                WHERE
                tsr2.pressure_result in
                <foreach item="item" index="index" collection="notInPressureResultList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="startDate != null and endDate != null">
                    AND tsr2.measure_time between #{startDate} and #{endDate}
                </if>
                )
            </if>
            <if test="orgIdList != null and orgIdList.size() > 0">
                AND tsr1.organization_id in
                <foreach item="item" index="index" collection="orgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startDate != null and endDate != null">
                AND tsr1.measure_time between #{startDate} and #{endDate}
            </if>
        </where>
    </select>

    <select id="getScreenTestList" parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultMap="SphygmometerRecordDTOResult">
        select
            tsr.id,
            tsr.user_id,
            tsr.device_id,
            tsr.device_type,
            tsr.systolic_pressure,
            tsr.diastolic_pressure,
            tsr.heartbeat,
            tsr.pressure_result,
            tsr.heartbeat_result,
            tsr.measure_place,
            tsr.measure_time,
            tsd.device_no,
            tsu.manage_organization_id,
            tsu.register_organization_id,
            tsr.organization_id as measure_organization_id,
            (select organization_name from organization.t_organization_info where tsu.manage_organization_id = organization_id) manage_organization_name,
            (select organization_name from organization.t_organization_info where tsu.register_organization_id = organization_id) register_organization_name,
            (select organization_name from organization.t_organization_info where tsr.organization_id = organization_id) measure_organization_name
        from
            t_sphygmometer_record tsr
        left join t_sphygmometer_user tsu on tsu.user_id = tsr.user_id
        left join t_sphygmometer_device tsd on tsd.device_id = tsr.device_id
        where
        tsr.device_type = 1
        <if test="userId != null ">
            AND tsr.user_id = #{userId}
        </if>
        <if test="pressureResult != null ">
            AND tsr.pressure_result = #{pressureResult}
        </if>
        <if test="heartbeatResult != null  ">
            AND tsr.heartbeat_result = #{heartbeatResult}
        </if>
        <if test="measureStartTime != null  and measureStartTime != ''">
            AND tsr.measure_time >= #{measureStartTime}
            AND tsr.measure_time <![CDATA[<=]]> #{measureEndTime}
        </if>
        <if test="fullAuthOrgList != null  and fullAuthOrgList.size() > 0">
            and tsr.organization_id in
            <foreach item="item" index="index"
                     collection="fullAuthOrgList" open="(" separator=","
                     close=")">
                #{item.organizationId}
            </foreach>
        </if>
        <if test="searchContent != null  and searchContent != ''">
            AND (
            tsd.device_no LIKE concat( '%', '${searchContent}', '%' )
            )
        </if>
        order by
        tsr.measure_time desc
    </select>

    <select id="getScreenUserCount"
            parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultType="com.bojun.sphygmometer.dto.ScreenUserBarGraphDTO">
        select
        SUM( CASE WHEN pressure_result = 1 THEN 1 ELSE 0 END) AS normalCount,
        SUM( CASE WHEN pressure_result = 2 THEN 1 ELSE 0 END) AS exCount,
        SUM( CASE WHEN pressure_result = 3 THEN 1 ELSE 0 END) AS highRiskCount,
        SUM( CASE WHEN pressure_result = 4 THEN 1 ELSE 0 END) AS reachCount
        from t_sphygmometer_record tsr
        inner join t_sphygmometer_user tsu on tsu.user_id = tsr.user_id
        where 1=1
        <if test="deviceType != null  ">
            AND tsr.device_type = #{deviceType}
        </if>
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND tsu.manage_organization_id in
            <foreach item="item" index="index" collection="orgIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="measureStartTime != null  and measureStartTime != ''">
            AND tsr.measure_time >= #{measureStartTime}
            AND tsr.measure_time
            &lt;= #{measureEndTime}
        </if>
    </select>

    <select id="getScreenUserBarGraphDTOByDto"
            parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultType="com.bojun.sphygmometer.dto.ScreenUserBarGraphDTO">
        SELECT
        SUM( CASE WHEN t1.pressure_result = 1 THEN 1 ELSE 0 END) AS normalCount,
        SUM( CASE WHEN t1.pressure_result = 2 THEN 1 ELSE 0 END) AS exCount,
        SUM( CASE WHEN t1.pressure_result = 3 THEN 1 ELSE 0 END) AS highRiskCount
        FROM
        (
        SELECT t.*
        FROM
        (
        SELECT user_id, max(pressure_result) pressure_result FROM t_sphygmometer_record
        WHERE pressure_result != 4
        <if test="deviceType != null  ">
            AND device_type = #{deviceType}
        </if>

        <if test="measureStartTime != null  and measureEndTime != null">
            and measure_time between #{measureStartTime} and #{measureEndTime}
        </if>
        GROUP BY user_id ORDER BY pressure_result
        ) t inner join t_sphygmometer_user tsu on tsu.user_id = t.user_id
        <where>
            <if test="orgIdList != null and orgIdList.size() > 0">
                AND tsu.manage_organization_id in
                <foreach item="item" index="index" collection="orgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ) t1
    </select>

    <select id="getHasDocCount"
            parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO"
            resultType="com.bojun.sphygmometer.dto.ScreenUserHasDocDTO">
        select
        SUM( CASE WHEN trbi.has_document = 0 THEN 1 ELSE 0 END) AS noneDocumentCount,
        SUM( CASE WHEN trbi.has_document = 1 THEN 1 ELSE 0 END) AS hasDocumentCount
        FROM
        (SELECT DISTINCT user_id FROM t_sphygmometer_record WHERE device_type = #{deviceType}
        <if test="measureStartTime != null and measureEndTime != null">
            AND measure_time between #{measureStartTime} and #{measureEndTime}
        </if>
        ) tsr
        inner join t_sphygmometer_user tsu on tsu.user_id = tsr.user_id
        inner join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND tsu.manage_organization_id in
            <foreach item="item" index="index" collection="orgIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getScreenCount" parameterType="com.bojun.sphygmometer.dto.ScreenCountParamDTO" resultType="Integer">
        select
        IFNULL( count(
        <if test="isDistinct == null">
            1
        </if>
        <if test="isDistinct != null and isDistinct == 0">
            1
        </if>
        <if test="isDistinct != null and isDistinct == 1">
            distinct tsr.user_id
        </if>
        ) ,0)

        from
        t_sphygmometer_record tsr
        inner join t_sphygmometer_user tsu on tsu.user_id = tsr.user_id
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        <where>
            <if test="isSignContract == 1">
                AND tsu.is_sign = #{isSignContract}
            </if>
            <if test="deviceType != null">
                AND tsr.device_type = #{deviceType}
            </if>
            <if test="measureStartTime != null and measureEndTime != null">
                AND tsr.measure_time between #{measureStartTime} and #{measureEndTime}
            </if>
            <if test="pressureResults != null  and pressureResults != ''">
                AND tsr.pressure_result in (${pressureResults})
            </if>
            <if test="authOrgIdList != null and authOrgIdList.size() > 0">
                AND tsu.manage_organization_id in
                <foreach item="item" index="index" collection="authOrgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="screenOrgIdList != null and screenOrgIdList.size() > 0">
                AND tsr.organization_id in
                <foreach item="item" index="index" collection="screenOrgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getScreenTestExCount" parameterType="com.bojun.sphygmometer.dto.ScreenCountParamDTO"
            resultType="com.bojun.sphygmometer.dto.ScreenUserTestExDTO">
        SELECT
        IFNULL( SUM( CASE WHEN t.cc = 1 or t.cc = 2 THEN 1 ELSE 0 END) ,0) as testEx2Count,
        IFNULL( SUM( CASE WHEN t.cc > 2 THEN 1 ELSE 0 END) ,0) as testEx3Count
        FROM
        (
        SELECT user_id,count(id) as cc FROM t_sphygmometer_record
        WHERE device_type = 1 AND pressure_result IN (2,3)
        <if test="measureStartTime != null and measureEndTime != null">
            AND measure_time between #{measureStartTime} and #{measureEndTime}
        </if>
        GROUP BY user_id
        ) t inner join t_sphygmometer_user tsu on tsu.user_id = t.user_id
        <where>
            <if test="authOrgIdList != null and authOrgIdList.size() > 0">
                tsu.manage_organization_id in
                <foreach item="item" index="index" collection="authOrgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getScreenTestEx0Count" parameterType="com.bojun.sphygmometer.dto.ScreenCountParamDTO"
            resultType="java.lang.Integer">
        SELECT
        IFNULL(count(1),0)
        FROM
        (
        SELECT user_id FROM t_sphygmometer_record tsr
        WHERE device_type = 1 AND (SELECT count(1) FROM t_sphygmometer_record where pressure_result != 1 and user_id =
        tsr.user_id
        <if test="measureStartTime != null and measureEndTime != null">
            AND tsr.measure_time between #{measureStartTime} and #{measureEndTime}
        </if>
        ) = 0
        <if test="measureStartTime != null and measureEndTime != null">
            AND tsr.measure_time between #{measureStartTime} and #{measureEndTime}
        </if>
        GROUP BY user_id
        ) t inner join t_sphygmometer_user tsu on tsu.user_id = t.user_id
        <where>
            <if test="authOrgIdList != null and authOrgIdList.size() > 0">
                tsu.manage_organization_id in
                <foreach item="item" index="index" collection="authOrgIdList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getScreenDeviceDataAnalysisDTOList"
            parameterType="com.bojun.sphygmometer.dto.ScreenDeviceDataAnalysisParamDTO"
            resultType="com.bojun.sphygmometer.dto.ScreenDeviceDataAnalysisDTO">
        SELECT count(1) screenCount,
        count(DISTINCT t.user_id) screenUserCount,
        t.measure_time screenTime
        FROM (
        SELECT user_id,
        <if test="month != -1">
            DATE_FORMAT(measure_time, '%c-%e') measure_time
        </if>
        <if test="month == -1">
            DATE_FORMAT(measure_time, '%c月') measure_time
        </if>
        FROM t_sphygmometer_record
        WHERE device_type = 1
        <if test="measureStartTime != null and measureEndTime != null">
            AND measure_time between #{measureStartTime} and #{measureEndTime}
        </if>
        <if test="orgIdList != null and orgIdList.size() > 0">
            AND organization_id in
            <foreach item="item" index="index" collection="orgIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) t
        GROUP BY t.measure_time

    </select>

    <select id="getDeviceBindedUserList" resultType="com.bojun.sphygmometer.dto.DeviceBindUserListDTO">
        select distinct
            tsr.user_id,
            tsr.device_id as bind_device_id,
            tsr.record_type as bind_device_user_type,
            trbi.real_name
        from
            t_sphygmometer_record tsr
        left join t_sphygmometer_user tsu on tsu.user_id = tsr.user_id
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsr.device_type = 2 and
            tsr.device_id = #{deviceId}
    </select>
</mapper>