package com.bojun.base.manage.api.system.hystrix;

import com.bojun.base.manage.api.system.VersionManagerService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.entity.AppVersion;
import org.springframework.stereotype.Component;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 16:15
 */

@Component
public class VersionManagerServiceHystrix implements VersionManagerService {
    @Override
    public String addVersion(AppVersion appVersion) {
        throw new BaseRuntimeException("addVersion 接口服务已断开");
    }

    @Override
    public String getVersionManager(AppVersion appVersion) {
        throw new BaseRuntimeException("getVersionManager 接口服务已断开");
    }

    @Override
    public String getSystemDict() {
        throw new BaseRuntimeException("getSystemDict 接口服务已断开");
    }

}
