<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserTaskRecordMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.UserTaskRecordDTO" id="UserTaskRecordDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="recordId"    column="record_id"    />
        <result property="recordTime"    column="record_time"    />
    </resultMap>

    <sql id="selectUserTaskRecord">
    	select
	        id,
	        user_id,
	        task_id,
	        record_id,
	        record_time
		from 
        	t_user_task_record
    </sql>

    <select id="selectUserTaskRecordById" parameterType="int" resultMap="UserTaskRecordDTOResult">
		<include refid="selectUserTaskRecord"/>
		where 
        	id = #{id}
    </select>

    <select id="selectUserTaskRecordList" parameterType="com.bojun.sphygmometer.dto.UserTaskRecordDTO" resultMap="UserTaskRecordDTOResult">
        <include refid="selectUserTaskRecord"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="taskId != null "> and task_id = #{taskId}</if>
		<if test="recordId != null "> and record_id = #{recordId}</if>
		<if test="recordTime != null "> and record_time = #{recordTime}</if>
        </where>
    </select>

</mapper>