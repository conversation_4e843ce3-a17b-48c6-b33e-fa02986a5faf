<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="sphygmometer-manage" />
        <module name="system-service-api" />
        <module name="sphygmometer-app" />
        <module name="health-promotion-entity" />
        <module name="sphygmometer-wxapp" />
        <module name="system-service" />
        <module name="health-promotion-service-api" />
        <module name="commons_encrypt" />
        <module name="organization-entity" />
        <module name="commons_redis" />
        <module name="sphygmometer-service" />
        <module name="sphygmometer-mp" />
        <module name="system-entity" />
        <module name="eureka-server" />
        <module name="sphygmometer-service-api" />
        <module name="commons_utils" />
        <module name="sphygmometer-socket" />
        <module name="organization-service-api" />
        <module name="commons_sms" />
        <module name="base-manage" />
        <module name="organization-service" />
        <module name="commons_ip" />
        <module name="sphygmometer-entity" />
        <module name="health-promotion-service" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="sphygmometer-project" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="base-manage" options="-parameters" />
      <module name="eureka-server" options="-parameters" />
      <module name="health-promotion-service" options="-parameters" />
      <module name="health-promotion-service-api" options="-parameters" />
      <module name="organization-service" options="-parameters" />
      <module name="organization-service-api" options="-parameters" />
      <module name="sphygmometer-app" options="-parameters" />
      <module name="sphygmometer-manage" options="-parameters" />
      <module name="sphygmometer-mp" options="-parameters" />
      <module name="sphygmometer-service" options="-parameters" />
      <module name="sphygmometer-service-api" options="-parameters" />
      <module name="sphygmometer-socket" options="-parameters" />
      <module name="sphygmometer-wxapp" options="-parameters" />
      <module name="system-service" options="-parameters" />
      <module name="system-service-api" options="-parameters" />
    </option>
  </component>
</project>