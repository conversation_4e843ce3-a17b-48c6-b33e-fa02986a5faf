<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.AppBannerMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.AppBannerDTO" id="AppBannerDTOResult">
        <result property="bannerId"    column="banner_id"    />
        <result property="title"    column="title"    />
        <result property="bannerImg"    column="banner_img"    />
        <result property="showIndex"    column="show_index"    />
        <result property="redirectUrl"    column="redirect_url"    />
        <result property="isEnabled"    column="is_enabled"    />
		<result property="remark"    column="remark"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectAppBanner">
    	select
	        banner_id,
	        title,
	        banner_img,
	        show_index,
	        redirect_url,
	        is_enabled,
	        remark,
	        update_user_id,
	        update_time,
	        create_user_id,
	        create_time
		from 
        	t_app_banner
    </sql>

    <select id="selectAppBannerById" parameterType="int" resultMap="AppBannerDTOResult">
		<include refid="selectAppBanner"/>
		where 
        	banner_id = #{bannerId}
    </select>

    <select id="selectAppBannerList" parameterType="com.bojun.sphygmometer.dto.AppBannerDTO" resultMap="AppBannerDTOResult">
        <include refid="selectAppBanner"/>
        <where>  
		<if test="bannerId != null "> and banner_id = #{bannerId}</if>
		<if test="title != null  and title != ''"> and title LIKE concat( '%', '${title}', '%' )</if>
		<if test="bannerImg != null  and bannerImg != ''"> and banner_img = #{bannerImg}</if>
		<if test="showIndex != null "> and show_index = #{showIndex}</if>
		<if test="redirectUrl != null  and redirectUrl != ''"> and redirect_url = #{redirectUrl}</if>
		<if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
			<if test="remark != null "> and remark = #{remark}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
		order by show_index asc
    </select>

</mapper>