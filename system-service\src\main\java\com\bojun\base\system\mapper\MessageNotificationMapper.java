
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.MessageNotificationDTO;



/**
 * 
*Model：消息通知（站内）
*Description：消息通知（站内）
*Author:李欣颖
*created：2020年1月6日
 */
@Mapper
public interface MessageNotificationMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	long queryMessageNotificationCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询消息通知（站内）信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	List<MessageNotificationDTO> getMessageNotification(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	Integer addMessageNotification(MessageNotificationDTO messageNotificationDTO);

	/**
	 * 
	 * @Description 删除消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	Integer deleteMessageNotification(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	Integer updateMessageNotification(MessageNotificationDTO messageNotificationDTO);

	/**
	 * 
	 * @Description 查询单个消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return MessageNotificationDTO
	 */
	MessageNotificationDTO getMessageNotificationById(Map<String, Object> mapPara);

	List<MessageNotificationDTO> findListUser(Map<String, Object> paramsMap);
	List<MessageNotificationDTO> findListDept(Map<String, Object> paramsMap);

	List<MessageNotificationDTO> getMessageNotificationType();


	
}
