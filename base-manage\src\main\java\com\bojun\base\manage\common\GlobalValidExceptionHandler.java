/**
 * 
 */
package com.bojun.base.manage.common;

import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.bojun.enums.ResponseCodeEnum;
import com.bojun.response.Results;

/**
*Model：全局异常处理类
*Description：全局异常处理类
*Author:段德鹏
*created：2020年4月23日
**/
@ControllerAdvice
public class GlobalValidExceptionHandler {
	
	@ExceptionHandler(MethodArgumentNotValidException.class)
	@ResponseBody
	public Results<Object> hanldeMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
		BindingResult bindingResult = ex.getBindingResult();
		StringBuilder errorMessage = new StringBuilder(bindingResult.getFieldErrors().size() * 16);
		for (int i = 0; i < bindingResult.getFieldErrors().size(); ++i) {
			if (i > 0) {
				errorMessage.append(",");
			}
			FieldError fieldError = bindingResult.getFieldErrors().get(i);
			errorMessage.append(fieldError.getField());
			errorMessage.append(":");
			errorMessage.append(fieldError.getDefaultMessage());
		}
		Results<Object> results = new Results<>(ResponseCodeEnum.BAD_REQUEST.getCode(), errorMessage.toString(), null);
		return results;
	}

}
