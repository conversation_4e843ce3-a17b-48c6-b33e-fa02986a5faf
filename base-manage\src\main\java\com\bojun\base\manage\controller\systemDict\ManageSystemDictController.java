/**
 * 
 */
package com.bojun.base.manage.controller.systemDict;

import java.util.List;

import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.ISystemDictService;
import com.bojun.base.manage.controller.systemDict.vo.AddSystemDictPramVO;
import com.bojun.base.manage.controller.systemDict.vo.GetSystemDictParamVO;
import com.bojun.base.manage.controller.systemDict.vo.SystemDictInfoVO;
import com.bojun.base.manage.controller.systemDict.vo.SystemDictTreeVO;
import com.bojun.base.manage.controller.systemDict.vo.SystemDictTypeVO;
import com.bojun.base.manage.controller.systemDict.vo.UpdateSystemDictStatusPramVO;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.vo.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
 * 
 * Model：产品管理
 * Description：产品管理
 * Author：赖水秀
 * created： 2020年4月27日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("systemDictMange")
@Api(tags = {"产品管理模块接口"})
@ApiSort(value = 3)
public class ManageSystemDictController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(ManageSystemDictController.class);
	
	@Autowired
	private ISystemDictService systemDictService;
	
	/**
	 * @Description 查询产品类型下拉列表数据
	 * <AUTHOR>
	 * @return
	 * Results<ManageUserLoginVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "查询产品分类", notes = "查询产品类型下拉列表数据（赖水秀）")
	@ApiOperationSupport(order = 1)
	@RequestMapping(value="/getSystemDictTypeList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getSystemDictTypeList")
	public Results<List<SystemDictTypeVO>> getSystemDictTypeList() {		
		try {			
			String result = systemDictService.getSystemDictTypeList();
			return returnResultsList(result, SystemDictTypeVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("saveSystemDict:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	 
	/**
	 * @Description 新增产品信息
	 * <AUTHOR>
	 * @return
	 * Results<AddSystemDictVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "新增产品", notes = "新增产品信息（赖水秀）")
	@ApiOperationSupport(order = 2)
	@RequestMapping(value="/saveSystemDict", method = RequestMethod.POST)
	@SystemLog(action = "saveSystemDict", description = "新增产品", operationType = Contants.ADD_REQUEST)
	@AuthAnnotation(action = "saveSystemDict")
	public Results saveSystemDict(@RequestBody @Valid AddSystemDictPramVO addSystemDictVO) {
		try {
			SystemDictDTO systemDictDTO = new SystemDictDTO();
			BeanUtils.copyProperties(addSystemDictVO, systemDictDTO);
			String result = systemDictService.saveSystemDict(systemDictDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("saveSystemDict:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 编辑产品信息
	 * <AUTHOR>
	 * @return
	 * Results<updateSystemDict>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "编辑产品", notes = "编辑产品信息（赖水秀）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value="/updateSystemDict", method = RequestMethod.POST)
	@SystemLog(action = "updateSystemDict", description = "编辑产品", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateSystemDict")
	public Results updateSystemDict(@RequestBody @Valid AddSystemDictPramVO addSystemDictVO) {
		try {
			SystemDictDTO systemDictDTO = new SystemDictDTO();
			BeanUtils.copyProperties(addSystemDictVO, systemDictDTO);
			String result = systemDictService.updateSystemDict(systemDictDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateSystemDict:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 禁用、启用产品信息
	 * <AUTHOR>
	 * @return
	 * Results<updateSystemDict>
	 * 2020年4月29日
	 */
	@ApiOperation(value = "禁用、启用产品", notes = "禁用、启用产品信息（赖水秀）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value="/updateSystemDictStatus", method = RequestMethod.POST)
	@SystemLog(action = "updateSystemDictStatus", description = "禁用、启用产品", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateSystemDictStatus")
	public Results updateSystemDictStatus(@RequestBody @Valid UpdateSystemDictStatusPramVO updateSystemDictStatusPramVO) {
		try {
			SystemDictDTO systemDictDTO = new SystemDictDTO();
			BeanUtils.copyProperties(updateSystemDictStatusPramVO, systemDictDTO);
			String result = systemDictService.updateSystemDict(systemDictDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateSystemDictStatus:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 查询查询单个产品数据
	 * <AUTHOR>
	 * @return
	 * Results<SystemDictInfoVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "查询单个产品", notes = "查询单个产品数据（赖水秀）")
	@ApiOperationSupport(order = 4)
	@RequestMapping(value = "/getSystemDictById", method = RequestMethod.POST)
	@AuthAnnotation(action = "getSystemDictById")
	public Results<SystemDictInfoVO> getSystemDictById(@RequestParam(value="systemId") String systemId) {		
		try {			
			String result = systemDictService.getSystemDictById(systemId);
			return returnResults(result, SystemDictInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("saveSystemDict:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 查询产品列表数据
	 * <AUTHOR>
	 * @return
	 * Results<Page<List<SystemDictInfoVO>>>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "查询产品列表", notes = "查询产品列表数据（赖水秀）")
	@ApiOperationSupport(order = 5)
	@RequestMapping(value="/getSystemDictList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getSystemDictList")
	public Results<Page<SystemDictInfoVO>> getSystemDictList(@RequestBody @Valid GetSystemDictParamVO getSystemDictVO) {		
		try {
			SystemDictDTO systemDictDTO = new SystemDictDTO();
			BeanUtils.copyProperties(getSystemDictVO, systemDictDTO);
			String result = systemDictService.getSystemDictList(systemDictDTO);
			return returnResultsPage(result, SystemDictInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getSystemDictList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	/**
	 * @Description 查询产品树形列表数据
	 * <AUTHOR>
	 * @return	 
	 * 2020年4月30日
	 */
	@ApiOperation(value = "查询产品树形列表", notes = "查询产品树形列表（赖水秀）")
	@ApiOperationSupport(order = 6)
	@RequestMapping(value="/getSystemDictTreeList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getSystemDictTreeList")
	public Results<List<SystemDictTreeVO>> getSystemDictTreeList() {		
		try {			
			String result = systemDictService.getSystemDictTreeList();			
			return returnResultsList(result,  SystemDictTreeVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getSystemDictTreeList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

}
