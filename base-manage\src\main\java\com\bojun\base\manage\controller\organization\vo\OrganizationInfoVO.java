/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：机构信息
 * Author：赖水秀
 * created： 2020年4月28日
 */
@ApiModel(value = "机构信息", description = "查询机构返回信息")
public class OrganizationInfoVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 8102272971119613722L;
	
	@ApiModelProperty(value="机构id")
	private Integer organizationId; // 机构id
	
	@ApiModelProperty(value="机构名称")
	private String organizationName; // 机构名称
	
	@ApiModelProperty(value="机构代码")
	private String organizationCode; // 机构代码
	
	@ApiModelProperty(value="社会信用代码证号")
	private String socialCreditCode; // 社会信用代码证号
	
	@ApiModelProperty(value="单位性质 1:企业 2：事业单位（国家行政机关） 3：政府")
	private Integer organizationProperty; // 单位性质 1:企业 2：事业单位（国家行政机关） 3：政府
	
	@ApiModelProperty(value="机构规模")
	private String organizationScale; // 机构规模
	
	@ApiModelProperty(value="机构分类")
	private String organizationClassCode; // 机构分类code
	
	@ApiModelProperty(value="机构类型id")
	private String organizationTypeCode; // 机构类型code
	
	@ApiModelProperty(value="单位负责人")
	private String organizationDirector; // 单位负责人
		
	@ApiModelProperty(value="联系电话")
	private String telephoneNumber; // 联系电话
	
	@ApiModelProperty(value="省份code")
	private String provinceCode; // 省份code
	
	@ApiModelProperty(value="城市code")
	private String cityCode; // 城市code
	
	@ApiModelProperty(value="县code")
	private String countyCode; // 县code
	
	@ApiModelProperty(value="单位地址")
	private String organizationAddress; // 单位地址
	
	@ApiModelProperty(value="经度")
	private String longitude; // 经度
	
	@ApiModelProperty(value="纬度")
	private String latitude; // 纬度
	
	@ApiModelProperty(value="机构介绍")
	private String organizationIntroduction; // 机构介绍
	
	@ApiModelProperty(value="机构历史")
	private String organizationHistory; // 机构历史
	
	@ApiModelProperty(value="机构荣誉")
	private String organizationHonor; // 机构荣誉
	
	@ApiModelProperty(value="营业执照正面")
	private String frontBusinessLicense; // 营业执照正面
	
	@ApiModelProperty(value="营业执照背面")
	private String backBusinessLicense; // 营业执照背面
	
	@ApiModelProperty(value="创建人用户id")
	private Integer createUserId; // 创建人用户id
	
	@ApiModelProperty(value="创建时间")
	private Date createTime; // 创建时间

	@ApiModelProperty(value="上级机构id")
	private Integer parentId; // 上级机构id
	
	@ApiModelProperty(value="是否启用  0：否  1:是 ")
	private Integer isEnabled;
	
	@ApiModelProperty(value="子菜单")
	private List<OrganizationInfoVO> children;
	
	@ApiModelProperty(value="机构图片集合")
    private List<OrganizationImgVO> imgList;
	
	@ApiModelProperty(value="机构下科室数")
	private Integer deptCount;
	
	@ApiModelProperty(value="机构下病区数")
	private Integer wardCount;
	
	@ApiModelProperty(value="机构下人员数")
	private Integer employeeCount;
	
	@ApiModelProperty(value="上级机构名称")
	private String parentName;
	
	@ApiModelProperty(value="机构类型名称")
	private String organizationTypeName;
	
	@ApiModelProperty(value="乡镇名称")
	private String townName;
		
	@ApiModelProperty(value="村居委会名称")
	private String villageName;
	
	@ApiModelProperty(value="乡镇代码")
	private String townCode; // 乡镇代码
	
	@ApiModelProperty(value="村居委会代码")
	private String villageCode; // 村居委会名称

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getOrganizationCode() {
		return organizationCode;
	}

	public void setOrganizationCode(String organizationCode) {
		this.organizationCode = organizationCode;
	}

	public String getSocialCreditCode() {
		return socialCreditCode;
	}

	public void setSocialCreditCode(String socialCreditCode) {
		this.socialCreditCode = socialCreditCode;
	}

	public Integer getOrganizationProperty() {
		return organizationProperty;
	}

	public void setOrganizationProperty(Integer organizationProperty) {
		this.organizationProperty = organizationProperty;
	}

	public String getOrganizationScale() {
		return organizationScale;
	}

	public void setOrganizationScale(String organizationScale) {
		this.organizationScale = organizationScale;
	}

	public String getOrganizationClassCode() {
		return organizationClassCode;
	}

	public void setOrganizationClassCode(String organizationClassCode) {
		this.organizationClassCode = organizationClassCode;
	}

	public String getOrganizationTypeCode() {
		return organizationTypeCode;
	}

	public void setOrganizationTypeCode(String organizationTypeCode) {
		this.organizationTypeCode = organizationTypeCode;
	}

	public String getOrganizationDirector() {
		return organizationDirector;
	}

	public void setOrganizationDirector(String organizationDirector) {
		this.organizationDirector = organizationDirector;
	}

	public String getTelephoneNumber() {
		return telephoneNumber;
	}

	public void setTelephoneNumber(String telephoneNumber) {
		this.telephoneNumber = telephoneNumber;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCountyCode() {
		return countyCode;
	}

	public void setCountyCode(String countyCode) {
		this.countyCode = countyCode;
	}

	public String getOrganizationAddress() {
		return organizationAddress;
	}

	public void setOrganizationAddress(String organizationAddress) {
		this.organizationAddress = organizationAddress;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getOrganizationIntroduction() {
		return organizationIntroduction;
	}

	public void setOrganizationIntroduction(String organizationIntroduction) {
		this.organizationIntroduction = organizationIntroduction;
	}

	public String getOrganizationHistory() {
		return organizationHistory;
	}

	public void setOrganizationHistory(String organizationHistory) {
		this.organizationHistory = organizationHistory;
	}

	public String getOrganizationHonor() {
		return organizationHonor;
	}

	public void setOrganizationHonor(String organizationHonor) {
		this.organizationHonor = organizationHonor;
	}

	public String getFrontBusinessLicense() {
		return frontBusinessLicense;
	}

	public void setFrontBusinessLicense(String frontBusinessLicense) {
		this.frontBusinessLicense = frontBusinessLicense;
	}

	public String getBackBusinessLicense() {
		return backBusinessLicense;
	}

	public void setBackBusinessLicense(String backBusinessLicense) {
		this.backBusinessLicense = backBusinessLicense;
	}

	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
	
	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public List<OrganizationInfoVO> getChildren() {
		return children;
	}

	public void setChildren(List<OrganizationInfoVO> children) {
		this.children = children;
	}

	public List<OrganizationImgVO> getImgList() {
		return imgList;
	}

	public void setImgList(List<OrganizationImgVO> imgList) {
		this.imgList = imgList;
	}

	public Integer getDeptCount() {
		return deptCount;
	}

	public void setDeptCount(Integer deptCount) {
		this.deptCount = deptCount;
	}

	public Integer getWardCount() {
		return wardCount;
	}

	public void setWardCount(Integer wardCount) {
		this.wardCount = wardCount;
	}

	public Integer getEmployeeCount() {
		return employeeCount;
	}

	public void setEmployeeCount(Integer employeeCount) {
		this.employeeCount = employeeCount;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public String getOrganizationTypeName() {
		return organizationTypeName;
	}

	public void setOrganizationTypeName(String organizationTypeName) {
		this.organizationTypeName = organizationTypeName;
	}

	public String getTownName() {
		return townName;
	}

	public void setTownName(String townName) {
		this.townName = townName;
	}

	public String getVillageName() {
		return villageName;
	}

	public void setVillageName(String villageName) {
		this.villageName = villageName;
	}

	public String getTownCode() {
		return townCode;
	}

	public void setTownCode(String townCode) {
		this.townCode = townCode;
	}

	public String getVillageCode() {
		return villageCode;
	}

	public void setVillageCode(String villageCode) {
		this.villageCode = villageCode;
	}
	
}
