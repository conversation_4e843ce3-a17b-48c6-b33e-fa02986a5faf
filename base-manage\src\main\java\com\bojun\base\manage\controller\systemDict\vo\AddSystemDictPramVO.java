/**
 * 
 */
package com.bojun.base.manage.controller.systemDict.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：产品管理
 * Description：新增、修改产品信息
 * Author：赖水秀
 * created： 2020年4月27日
 */
@ApiModel(value = "新增、修改产品信息", description = "新增、修改产品传入和返回信息")
public class AddSystemDictPramVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;
	
	@NotEmpty(message = "系统ID不能为空")
	@ApiModelProperty(value="系统id", required = true, example = "S001")
	private String systemId;
	
	@NotEmpty(message = "系统名称不能为空")
	@ApiModelProperty(value="系统名称", required = true, example = "互联网医院配置管理系统")
	private String systemName;
	
	@NotNull(message = "系统类型ID不能为空")
	@ApiModelProperty(value="系统类型id", required = true, example = "1")
	private Integer systemTypeId;
	
	@NotNull(message = "终端类型不能为空")
	@ApiModelProperty(value="是否是移动端(0:移动端，1：web端)", required = true, example = "0")
	private Integer isMobile;
	
	@ApiModelProperty(value="主页URL")
	private String homeUrl;
	
	@NotNull(message = "状态不能为空")
	@ApiModelProperty(value="启用标记   0：否   1：是", required = true, example = "1")
	private Integer isEnabled;
		
	@ApiModelProperty(value="图标样式")
	private String iconCass;
	
	@ApiModelProperty(value="备注")
	private String remark;

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public Integer getIsMobile() {
		return isMobile;
	}

	public void setIsMobile(Integer isMobile) {
		this.isMobile = isMobile;
	}

	public String getHomeUrl() {
		return homeUrl;
	}

	public void setHomeUrl(String homeUrl) {
		this.homeUrl = homeUrl;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getIconCass() {
		return iconCass;
	}

	public void setIconCass(String iconCass) {
		this.iconCass = iconCass;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}
