/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：市区信息
 * Author：赖水秀
 * created： 2020年5月7日
 */
@ApiModel(value = "市区信息", description = "查询市区返回信息")
public class CityDictVO implements Serializable {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = 957032474834757531L;

	@ApiModelProperty(value="市区code")
	private String cityCode;

	@ApiModelProperty(value="市区名称")
	private String cityName;

	@ApiModelProperty(value="省份code")
	private String provinceCode;

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

}
