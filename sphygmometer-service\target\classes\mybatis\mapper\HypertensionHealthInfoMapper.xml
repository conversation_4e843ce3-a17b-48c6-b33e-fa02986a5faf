<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.HypertensionHealthInfoMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.HypertensionHealthInfoDTO" id="HypertensionHealthInfoDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="hypertensionType"    column="hypertension_type"    />
        <result property="remark"    column="remark"    />
        <result property="dynamicBloodPressureImg"    column="dynamic_blood_pressure_img"    />
        <result property="compliance"    column="compliance"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
    </resultMap>

    <sql id="selectHypertensionHealthInfo">
    	select
	        id,
	        user_id,
	        hypertension_type,
	        remark,
	        dynamic_blood_pressure_img,
	        compliance,
	        create_time,
	        create_user_id,
	        create_user_name,
	        update_time,
	        update_user_id,
	        update_user_name
		from 
        	t_hypertension_health_info
    </sql>

    <select id="selectHypertensionHealthInfoById" parameterType="int" resultMap="HypertensionHealthInfoDTOResult">
		<include refid="selectHypertensionHealthInfo"/>
		where 
        	id = #{id}
    </select>

    <select id="selectHypertensionHealthInfoList" parameterType="com.bojun.sphygmometer.dto.HypertensionHealthInfoDTO" resultMap="HypertensionHealthInfoDTOResult">
        <include refid="selectHypertensionHealthInfo"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="hypertensionType != null  and hypertensionType != ''"> and hypertension_type = #{hypertensionType}</if>
		<if test="remark != null  and remark != ''"> and remark = #{remark}</if>
		<if test="dynamicBloodPressureImg != null  and dynamicBloodPressureImg != ''"> and dynamic_blood_pressure_img = #{dynamicBloodPressureImg}</if>
		<if test="compliance != null "> and compliance = #{compliance}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createUserName != null  and createUserName != ''"> and create_user_name = #{createUserName}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateUserName != null  and updateUserName != ''"> and update_user_name = #{updateUserName}</if>
        </where>
    </select>

	<select id="getHealthInfoByUserId" parameterType="int" resultMap="HypertensionHealthInfoDTOResult">
		<include refid="selectHypertensionHealthInfo"/>
		where
		user_id = #{userId}
	</select>

	<update id="updateHypertensionHealthInfo" parameterType="com.bojun.sphygmometer.dto.HypertensionHealthInfoDTO">
		update
			t_hypertension_health_info
		set
			hypertension_type = #{hypertensionType},
			remark = #{remark},
			dynamic_blood_pressure_img = #{dynamicBloodPressureImg},
			compliance = #{compliance},
			update_time = #{updateTime},
			update_user_id = #{updateUserId},
			update_user_name = #{updateUserName}
		where
			user_id = #{userId}
	</update>

</mapper>