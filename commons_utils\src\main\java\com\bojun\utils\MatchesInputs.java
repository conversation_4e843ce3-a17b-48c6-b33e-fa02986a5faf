package com.bojun.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

/**
 * 正则验证公共类
 * 
 * <AUTHOR>
 * 
 */
public class MatchesInputs {

    /**
     * 自定义正则校验
     * 
     * @param reg
     *            正则表达式
     * @param str
     *            需要校验的字符串
     * @return校验通过 false 校验不通过
     */
    public static boolean customMatches(String reg, String str) {
        if (StringUtils.isBlank(reg) || StringUtils.isBlank(str)) {
            return false;
        }
        Pattern p = Pattern.compile(reg);
        Matcher m = p.matcher(str);
        return m.matches();
    }

    /**
     * 密码为字母、数字、下划线两者及以上8-20个字符!
     * 
     * @param matcheStr
     * @return
     */
    public static boolean matchesPWD(String matcheStr) {
        String regEx = "^(?![0-9]+$)(?![a-zA-Z]+$)(?![_]+$)[0-9A-Za-z_]{8,20}$";
        if (StringUtils.isBlank(matcheStr)) {
            return false;
        }
        Pattern pat = Pattern.compile(regEx);
        Matcher mat = pat.matcher(matcheStr);
        return mat.matches();
    }

    /**
     * 判断手机号
     * 
     * @param mobile
     * @return
     */
    public static boolean matchesMobile(String mobile) {
        if (StringUtils.isBlank(mobile)) {
            return false;
        }
        // 移动手机
        Pattern p1 = Pattern.compile("^((13[4-9])|(147)|(15[0-2,7-9])|(172)|(178)|(198)|(18[1-4,7-8]))\\d{8}$");
        Matcher m1 = p1.matcher(mobile);
        // 联通
        Pattern p2 = Pattern.compile("^((13[0-2])|(145)|(15[5-6])|(166)|(171)|(175)|(176)|(186)|(185))\\d{8}$");
        Matcher m2 = p2.matcher(mobile);
        // 电信
        Pattern p3 = Pattern.compile("^((133)|(149)|(173)|(177)|(191)|(199)|(153)|(18[0,9]))\\d{8}$");
        Matcher m3 = p3.matcher(mobile);

        //补全验证, 20210407严峡华
        Pattern p4 = Pattern.compile("^((13[0-9])|(14[0,1,4-9])|(15[0-3,5-9])|(16[2,5,6,7])|(17[0-8])|(18[0-9])|(19[0-3,5-9]))\\d{8}$");
        Matcher m4 = p4.matcher(mobile);
        if (m1.matches() || m2.matches() || m3.matches() || m4.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断邮箱格式
     * 
     * @param email
     * @return
     */
    public static boolean matchesEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return false;
        }
        String str = "^([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)*@([a-zA-Z0-9]*[-_]?[a-zA-Z0-9]+)+[\\.][A-Za-z]{2,3}([\\.][A-Za-z]{2})?$";
        Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(email);
        return m.matches();
    }

    /**
     * 判断银行账号格式
     * 
     * @param bankCard
     * @return
     */
    public static boolean matchesBankCard(String bankCard) {
        if (StringUtils.isBlank(bankCard)) {
            return false;
        }
        return Pattern.matches("[0-9]{12,25}", bankCard);
    }

    /**
     * 判断身份证格式
     * 
     * @param identNo
     * @return
     */
    public static boolean matchesIdentNo(String identNo) {
        if (StringUtils.isBlank(identNo)) {
            return false;
        }
        return Pattern.matches("((11|12|13|14|15|21|22|23|31|32|33|34|35|36|37|41|42|43|44|45|46|50|51|52|53|54|61|62|63|64|65|71|81|82|91)\\d{4})((((19|20)(([02468][048])|([13579][26]))0229))|((20[0-9][0-9])|(19[0-9][0-9]))((((0[1-9])|(1[0-2]))((0[1-9])|(1\\d)|(2[0-8])))|((((0[1,3-9])|(1[0-2]))(29|30))|(((0[13578])|(1[02]))31))))((\\d{3}(x|X))|(\\d{4}))",
                identNo);
    }

    /**
     * 校验URL
     * 
     * @param url
     * @return
     */
    public static boolean matchesURL(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }
        String str = "^(\\w+:\\/\\/)?\\w+(\\.\\w+)+.*$";
        Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(url);
        return m.matches();
    }

    /**
     * 校验邮编
     * 
     * @param postcode
     * @return
     */
    public static boolean matchesPostco(String postcode) {
        if (StringUtils.isBlank(postcode)) {
            return false;
        }
        String str = "^[1-9]\\d{5}(?!\\d)";
        Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(postcode);
        return m.matches();
    }

    /**
     * 校验国内座机电话
     * 
     * @param phone
     * @return
     */
    public static boolean matchesPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        String str = "^\\d{3}-\\d{8}|\\d{4}-\\d{7,8}$";
        Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(phone);
        return m.matches();
    }

    /**
     * 校验正浮点数
     * 
     * @param phone
     * @return
     */
    public static boolean matchesFloat(String phone) {
        if (StringUtils.isBlank(phone)) {
            return false;
        }
        String str = "^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$";
        Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(phone);
        return m.matches();
    }

    /**
     * 校验是否为数字
     * 
     * @param number
     * @return
     */
    public static boolean matchesNumber(String number) {
        if (StringUtils.isBlank(number)) {
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(number).matches();
    }

    /**
     * 校验是否为正整数
     * 
     * @param number
     * @return
     */
    public static boolean matchesIntegerOrFloat(String number) {
    	boolean flag = false;
        if (StringUtils.isBlank(number)) {
            return false;
        }
        String str = "^[1-9]\\d*\\.\\d*|0\\.\\d*[1-9]\\d*$";
        Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(number);
        
        Pattern pattern = Pattern.compile("^[0-9]*[1-9][0-9]*$");
        if(m.matches() || pattern.matcher(number).matches()) {
        	flag = true;
        }
        return flag;
    }
    
    /**
     * 校验时间日期格式
     * @param dateStr
     * @return
     */
    public static boolean matchesDate(String dateStr) {
    	if (StringUtils.isBlank(dateStr)) {
            return false;
        }
    	String str = "^[1-9]\\d{3}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])\\s+(20|21|22|23|[0-1]\\d):[0-5]\\d:[0-5]\\d$";
    	Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(dateStr);
        return m.matches();
    }
    
    /**
     * 校验小时时间格式
     * @param dateStr
     * @return
     */
    public static boolean matchDateHour(String dateStr) {
    	if (StringUtils.isBlank(dateStr)) {
            return false;
        }
    	String str = "^(20|21|22|23|[0-1]\\d):[0-5]\\d$";
    	Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(dateStr);
        return m.matches();
    }
    
    /**
     * 校验日期格式
     * @param dateStr
     * @return
     */
    public static boolean matchesDateStr(String dateStr) {
    	if (StringUtils.isBlank(dateStr)) {
            return false;
        }
    	String str = "^[1-9]\\d{3}(-|/)(0[1-9]|1[0-2])(-|/)(0[1-9]|[1-2][0-9]|3[0-1])$";
    	Pattern p = Pattern.compile(str);
        Matcher m = p.matcher(dateStr);
        return m.matches();
    }
    
    /**
     * 校验base64
     * @param base64
     * @return
     */
    public static boolean matchesBase64(String base64) {
        String base64Pattern = "^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$";
        return Pattern.matches(base64Pattern, base64);
    }
    
    
    public static void main(String[] args) {
		System.out.println(matchesDateStr("2018/05/01"));
	}
}
