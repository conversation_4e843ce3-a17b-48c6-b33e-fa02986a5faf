package com.bojun.base.manage.controller.organization.vo.dept;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.bojun.vo.BaseQueryInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：机构管理
*Description：--科室列表参数vo类
*Author: 肖泽权
*created：2020年5月5日
*/
@ApiModel(value = "机构--科室列表信息", description = "机构--科室列表传入信息")
public class GetOrganizationDeptListParamVO extends BaseQueryInfoVO implements Serializable{
	
	private static final long serialVersionUID = -2912721611873936755L;

	@ApiModelProperty(value="科室属性", required = false, example = "内科")
	private String keyWords;
	
	@ApiModelProperty(value="科室属性", required = false, example = "1")
    private Integer deptAttr;
    
    
	@ApiModelProperty(value="门诊住院类型", required = false, example = "2")
    private Integer outpOrInp;
    
    
	@ApiModelProperty(value="科室类型", required = false, example = "3")
    private Integer internalOrSergery;

	@NotNull
	@ApiModelProperty(value="机构编码", required = true, example = "1812")
    private Integer organizationId;

	public String getKeyWords() {
		return keyWords;
	}


	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}


	public Integer getDeptAttr() {
		return deptAttr;
	}


	public void setDeptAttr(Integer deptAttr) {
		this.deptAttr = deptAttr;
	}


	public Integer getOutpOrInp() {
		return outpOrInp;
	}


	public void setOutpOrInp(Integer outpOrInp) {
		this.outpOrInp = outpOrInp;
	}


	public Integer getInternalOrSergery() {
		return internalOrSergery;
	}


	public void setInternalOrSergery(Integer internalOrSergery) {
		this.internalOrSergery = internalOrSergery;
	}


	public Integer getOrganizationId() {
		return organizationId;
	}


	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}
	
}

