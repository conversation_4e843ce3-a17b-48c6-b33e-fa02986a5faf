/**
 * 
 */
package com.bojun.base.system.service.impl;

import java.util.List;
import java.util.Map;

import com.bojun.system.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.CommonDictMapper;
import com.bojun.base.system.service.ICommonDictService;


/**
 * Model： 公共字典服务实现类
 * Description：公共字典服务实现类
 * Author：赖水秀
 * created： 2020年5月7日
 */
@Service
public class CommonDictServiceImpl implements ICommonDictService {

	@Autowired
	private CommonDictMapper commonDictMapper;

	/**
	 * 查询省份列表
	 */
	@Override
	public List<ProvinceDictDTO> getProvinceList() {		
		return commonDictMapper.getProvinceList();
	}
	
	/**
	 * 查询市区列表
	 */
	@Override
	public List<CityDictDTO> getCityList(String provinceCode) {		
		return commonDictMapper.getCityList(provinceCode);
	}
	
	/**
	 * 查询县区列表
	 */
	@Override
	public List<CountyDictDTO> getCountyList(String cityCode) {		
		return commonDictMapper.getCountyList(cityCode);
	}
	
	
	/**
	 * 
	 * @Description 查询乡镇列表
	 * <AUTHOR>
	 * @param countyCode
	 * @return
	 * @return List<TownDictDTO>
	 * created：2020年6月24日
	 */
	@Override
	public List<TownDictDTO> getTownList(String countyCode) {
		
		return commonDictMapper.getTownList(countyCode);
	}
	
	/**
	 * 
	 * @Description 查询村居委会列表
	 * <AUTHOR>
	 * @param townCode
	 * @return
	 * @return List<VillageDictDTO>
	 * created：2020年6月24日
	 */
	@Override
	public List<VillageDictDTO> getVillageList(String townCode) {
		
		return commonDictMapper.getVillageList(townCode);
	}
	
	
	/**
	 * 
	 * @Description 查询民族列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<Map<String,Object>>
	 * created：2020年7月25日
	 */
	@Override
	public List<NationDTO> getNationList(Map<String, Object> paramsMap){
		
		return commonDictMapper.getNationList(paramsMap);
	}

}
