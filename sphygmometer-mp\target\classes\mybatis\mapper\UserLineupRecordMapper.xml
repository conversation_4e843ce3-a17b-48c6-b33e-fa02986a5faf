<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mq.mapper.UserLineupRecordMapper">
    
    <resultMap type="com.bojun.sphygmometer.mq.dto.UserLineupRecordDTO" id="UserLineupRecordDTOResult">
        <result property="id"    column="id"    />
        <result property="wxOpenId"    column="wx_open_id"    />
        <result property="lineUpSize"    column="line_up_size"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectUserLineupRecord">
    	select
	        id,
	        wx_open_id,
	        line_up_size,
	        create_time
		from 
        	t_user_lineup_record
    </sql>

    <select id="selectUserLineupRecordById" parameterType="int" resultMap="UserLineupRecordDTOResult">
		<include refid="selectUserLineupRecord"/>
		where 
        	id = #{id}
    </select>

    <select id="selectUserLineupRecordList" parameterType="com.bojun.sphygmometer.mq.dto.UserLineupRecordDTO" resultMap="UserLineupRecordDTOResult">
        <include refid="selectUserLineupRecord"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="wxUserId != null "> and wx_user_id = #{wxUserId}</if>
		<if test="wxOpenId != null  and wxOpenId != ''"> and wx_open_id = #{wxOpenId}</if>
		<if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
		<if test="lineUpSize != null "> and line_up_size = #{lineUpSize}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>

</mapper>