package com.bojun.health.promotion.service.api.hystrix;

import com.bojun.exception.BaseRuntimeException;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.service.api.TopicInfoFeignClient;
import com.bojun.page.Results;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 13:23
 */
@Component
public class TopicInfoFeignClientHystrix implements TopicInfoFeignClient {
    @Override
    public Results<List<TopicInfoDTO>> getNewsTopicList(TopicInfoDTO topicInfo) {
        throw new BaseRuntimeException("getNewsTopic   接口服务已断开");
    }

    @Override
    public Integer addNewsTopic(TopicInfoDTO topicInfo) {
        throw new BaseRuntimeException("addNewsTopic   接口服务已断开");
    }

    @Override
    public Integer updateNewsTopic(TopicInfoDTO topicInfo) {
        throw new BaseRuntimeException("updateNewsTopic   接口服务已断开");
    }

    @Override
    public Integer deleteNewsTopic(TopicInfoDTO topicInfo) {
        throw new BaseRuntimeException("deleteNewsTopic   接口服务已断开");
    }

    @Override
    public TopicInfoDTO getById(Integer topicId) {
        throw new BaseRuntimeException("getById   接口服务已断开");
    }
}
