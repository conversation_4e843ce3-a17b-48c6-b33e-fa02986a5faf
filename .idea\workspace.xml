<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="009c5dc9-20d0-48f2-9dc2-84ccd5faf4c9" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\Program Files\Apache\apache-maven-3.9.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\Program Files\Apache\apache-maven-3.9.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 1
}]]></component>
  <component name="ProjectId" id="21tDaT02Bi7XPt78n7xK5QusWtB" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "last_opened_file_path": "D:/WorkSpace/idea/sphygmometer-project",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql",
      "mysql_aurora"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Leon\idea_workspace\sphygmometer-project" />
      <recent name="C:\Leon\idea_workspace\sphygmometer-project\organization-service\src\main\java\com\bojun\organization" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.bojun.system.constant" />
      <recent name="com.bojun.organization.service" />
      <recent name="com.bojun.organization.dto" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.SphygmometerServiceApplication">
    <configuration default="true" type="ArquillianTestNG" factoryName="" nameIsGenerated="true">
      <option name="arquillianRunConfiguration">
        <value>
          <option name="containerStateName" value="" />
        </value>
      </option>
      <option name="TEST_OBJECT" value="CLASS" />
      <properties />
      <listeners />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaseManageApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="base-manage" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.BaseManageApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="EurekaServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="eureka-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.eureka.server.EurekaServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="HealthPromotionServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="health-promotion-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.HealthPromotionServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OrganizationServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="organization-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.OrganizationServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SphygmometerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="sphygmometer-app" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.SphygmometerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SphygmometerManageApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="sphygmometer-manage" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.SphygmometerManageApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SphygmometerMqApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="sphygmometer-mp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.SphygmometerMqApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SphygmometerServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="sphygmometer-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.SphygmometerServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SphygmometerWxApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="sphygmometer-wxapp" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.SphygmometerWxApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SystemServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="system-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.bojun.SystemServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.27812.49" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.27812.49" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="009c5dc9-20d0-48f2-9dc2-84ccd5faf4c9" name="Default Changelist" comment="" />
      <created>1638751553164</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1638751553164</updated>
      <workItem from="1638751555882" duration="30566000" />
      <workItem from="1638846668994" duration="28992000" />
      <workItem from="1754366048282" duration="1734000" />
    </task>
    <task id="LOCAL-00021" summary="项目初始化">
      <created>1638774396129</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1638774396129</updated>
    </task>
    <task id="LOCAL-00022" summary="项目初始化">
      <created>1638775276376</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1638775276376</updated>
    </task>
    <task id="LOCAL-00023" summary="项目初始化">
      <created>1638776413273</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1638776413273</updated>
    </task>
    <task id="LOCAL-00024" summary="项目初始化">
      <created>1638776672806</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1638776672806</updated>
    </task>
    <task id="LOCAL-00025" summary="项目初始化">
      <created>1638777078984</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1638777078984</updated>
    </task>
    <task id="LOCAL-00026" summary="项目初始化">
      <created>1638777478652</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1638777478652</updated>
    </task>
    <task id="LOCAL-00027" summary="项目初始化">
      <created>1638778097315</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1638778097315</updated>
    </task>
    <task id="LOCAL-00028" summary="项目初始化">
      <created>1638778673317</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1638778673317</updated>
    </task>
    <task id="LOCAL-00029" summary="项目初始化">
      <created>1638779394041</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1638779394041</updated>
    </task>
    <task id="LOCAL-00030" summary="项目初始化">
      <created>1638779618969</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1638779618969</updated>
    </task>
    <task id="LOCAL-00031" summary="项目初始化">
      <created>1638780024021</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1638780024021</updated>
    </task>
    <task id="LOCAL-00032" summary="项目初始化">
      <created>1638780417685</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1638780417685</updated>
    </task>
    <task id="LOCAL-00033" summary="项目初始化">
      <created>1638780674123</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1638780674123</updated>
    </task>
    <task id="LOCAL-00034" summary="项目初始化">
      <created>1638780996743</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1638780996743</updated>
    </task>
    <task id="LOCAL-00035" summary="项目初始化">
      <created>1638781247015</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1638781247015</updated>
    </task>
    <task id="LOCAL-00036" summary="项目初始化">
      <created>1638781501968</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1638781501968</updated>
    </task>
    <task id="LOCAL-00037" summary="项目初始化">
      <created>1638781896247</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1638781896247</updated>
    </task>
    <task id="LOCAL-00038" summary="项目初始化">
      <created>1638782019150</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1638782019150</updated>
    </task>
    <task id="LOCAL-00039" summary="项目初始化">
      <created>1638782312616</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1638782312616</updated>
    </task>
    <task id="LOCAL-00040" summary="项目初始化">
      <created>1638782671406</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1638782671406</updated>
    </task>
    <task id="LOCAL-00041" summary="项目初始化">
      <created>1638783207676</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1638783207676</updated>
    </task>
    <task id="LOCAL-00042" summary="项目初始化">
      <created>1638783594543</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1638783594543</updated>
    </task>
    <task id="LOCAL-00043" summary="项目初始化">
      <created>1638839546627</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1638839546627</updated>
    </task>
    <task id="LOCAL-00044" summary="项目初始化">
      <created>1638840912849</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1638840912849</updated>
    </task>
    <task id="LOCAL-00045" summary="项目初始化">
      <created>1638841242484</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1638841242484</updated>
    </task>
    <task id="LOCAL-00046" summary="项目初始化">
      <created>1638841768181</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1638841768181</updated>
    </task>
    <task id="LOCAL-00047" summary="项目初始化">
      <created>1638842101448</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1638842101448</updated>
    </task>
    <task id="LOCAL-00048" summary="项目初始化">
      <created>1638842768744</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1638842768744</updated>
    </task>
    <task id="LOCAL-00049" summary="项目初始化">
      <created>1638845645844</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1638845645844</updated>
    </task>
    <task id="LOCAL-00050" summary="修改bug">
      <created>1638847724978</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1638847724978</updated>
    </task>
    <task id="LOCAL-00051" summary="修改bug">
      <created>1638858543227</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1638858543227</updated>
    </task>
    <task id="LOCAL-00052" summary="修改bug">
      <created>1638859376914</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1638859376914</updated>
    </task>
    <task id="LOCAL-00053" summary="修改bug">
      <created>1638860592137</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1638860592137</updated>
    </task>
    <task id="LOCAL-00054" summary="修改bug">
      <created>1638860634134</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1638860634139</updated>
    </task>
    <task id="LOCAL-00055" summary="修改bug">
      <created>1638860978668</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1638860978668</updated>
    </task>
    <task id="LOCAL-00056" summary="修改bug">
      <created>1638860994485</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1638860994485</updated>
    </task>
    <task id="LOCAL-00057" summary="修改bug">
      <created>1638861354612</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1638861354612</updated>
    </task>
    <task id="LOCAL-00058" summary="修改bug">
      <created>1638861750673</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1638861750673</updated>
    </task>
    <task id="LOCAL-00059" summary="修改bug">
      <created>1638864078181</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1638864078182</updated>
    </task>
    <task id="LOCAL-00060" summary="修改bug">
      <created>1638869227365</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1638869227365</updated>
    </task>
    <task id="LOCAL-00061" summary="修改bug">
      <created>1638870946310</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1638870946310</updated>
    </task>
    <task id="LOCAL-00062" summary="修改bug">
      <created>1638873572904</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1638873572904</updated>
    </task>
    <task id="LOCAL-00063" summary="修改bug">
      <created>1638873656716</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1638873656716</updated>
    </task>
    <task id="LOCAL-00064" summary="修改bug">
      <created>1638876311981</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1638876311981</updated>
    </task>
    <task id="LOCAL-00065" summary="修改bug">
      <created>1638887974498</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1638887974498</updated>
    </task>
    <task id="LOCAL-00066" summary="修改bug">
      <created>1638889851098</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1638889851099</updated>
    </task>
    <task id="LOCAL-00067" summary="修改bug">
      <created>1638891124257</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1638891124257</updated>
    </task>
    <task id="LOCAL-00068" summary="修改bug">
      <created>1638923322657</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1638923322657</updated>
    </task>
    <task id="LOCAL-00069" summary="修改bug">
      <created>1638928335949</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1638928335949</updated>
    </task>
    <option name="localTasksCounter" value="70" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="项目初始化" />
    <MESSAGE value="修改bug" />
    <option name="LAST_COMMIT_MESSAGE" value="修改bug" />
  </component>
</project>