/**
 * 
 */
package com.bojun.base.manage.controller.homePage.vo;

import java.io.Serializable;
import java.util.List;

import com.bojun.system.dto.MenuDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：主页管理 
 * Description：查询主页列表返回信息
 * Author：lj
 * created： 2020年4月27日
 */
@ApiModel(value = "主页列表", description = "查询主页产品列表返回信息")
public class HomeSystemDictInfoVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;

	@ApiModelProperty(value = "系统id")
	private String systemId;

	@ApiModelProperty(value = "系统名称")
	private String systemName;

	@ApiModelProperty(value = "系统类型id")
	private Integer systemTypeId;

	@ApiModelProperty(value = "是否是移动端(0:移动端，1：web端)")
	private Integer isMobile;

	@ApiModelProperty(value = "主页URL")
	private String homeUrl;



	@ApiModelProperty(value = "图标样式")
	private String iconCass;


	@ApiModelProperty(value = "系统类型名称")
	private String systemTypeName;
	
	
	private List<MenuDTO> treeList;
	
	
	
	

	public List<MenuDTO> getTreeList() {
		return treeList;
	}

	public void setTreeList(List<MenuDTO> treeList) {
		this.treeList = treeList;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public Integer getIsMobile() {
		return isMobile;
	}

	public void setIsMobile(Integer isMobile) {
		this.isMobile = isMobile;
	}

	public String getHomeUrl() {
		return homeUrl;
	}

	public void setHomeUrl(String homeUrl) {
		this.homeUrl = homeUrl;
	}



	public String getIconCass() {
		return iconCass;
	}

	public void setIconCass(String iconCass) {
		this.iconCass = iconCass;
	}

	

	public String getSystemTypeName() {
		return systemTypeName;
	}

	public void setSystemTypeName(String systemTypeName) {
		this.systemTypeName = systemTypeName;
	}
	
}
