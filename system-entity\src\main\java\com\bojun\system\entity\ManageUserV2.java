package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统管理员用户对象 t_manage_user
 * 
 * <AUTHOR>
 * @date 2021-06-05 16:05:04
 */
@ApiModel(value = "ManageUserV2对象" , description = "系统管理员用户")
@Data
@TableName("t_manage_user")
public class ManageUserV2 implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 用户id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "user_id", type = IdType.AUTO)
    private Integer userId;

    /** 登录账号 */
    @ApiModelProperty(value = "登录账号", example = "")
	@TableField("account_no")
    private String accountNo;

    /** 工号 */
    @ApiModelProperty(value = "工号", example = "")
	@TableField("work_number")
    private String workNumber;

    /** 父级ID */
    @ApiModelProperty(value = "父级ID", example = "")
	@TableField("parent_id")
    private Integer parentId;

    /** 机构id */
    @ApiModelProperty(value = "机构id", example = "")
	@TableField("organization_id")
    private Integer organizationId;

    /** 机构名称 */
    @ApiModelProperty(value = "机构名称", example = "")
	@TableField("organization_name")
    private String organizationName;

    /** 角色id */
    @ApiModelProperty(value = "角色id", example = "")
	@TableField("role_id")
    private String roleId;

    /** 部门（科室）id */
    @ApiModelProperty(value = "部门（科室）id", example = "")
	@TableField("dept_id")
    private Integer deptId;

    /** 区域（病区）id */
    @ApiModelProperty(value = "区域（病区）id", example = "")
	@TableField("ward_id")
    private Integer wardId;

    /** 姓名 */
    @ApiModelProperty(value = "姓名", example = "")
	@TableField("real_name")
    private String realName;

    /** 身份证号码 */
    @ApiModelProperty(value = "身份证号码", example = "")
	@TableField("id_no")
    private String idNo;

    /** 权限类型（0：超级管理员，1：普通管理员） */
    @ApiModelProperty(value = "权限类型（0：超级管理员，1：普通管理员）", example = "")
	@TableField("auth_type")
    private Integer authType;

    /** 用户类型  1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维  99 :其他 */
    @ApiModelProperty(value = "用户类型  1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维  99 :其他", example = "")
	@TableField("user_type")
    private Integer userType;

    /** 手机号 */
    @ApiModelProperty(value = "手机号", example = "")
	@TableField("mobile")
    private String mobile;

    /** 密码 */
    @ApiModelProperty(value = "密码", example = "")
	@TableField("passwords")
    private String passwords;

    /** 密码盐 */
    @ApiModelProperty(value = "密码盐", example = "")
	@TableField("salt")
    private String salt;

    /** 密码修改时间 */
    @ApiModelProperty(value = "密码修改时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("modify_password_time")
    private Date modifyPasswordTime;

    /** 0:停用  1:启用 */
    @ApiModelProperty(value = "0:停用  1:启用", example = "")
	@TableField("status")
    private Integer status;

    /** 登录身份令牌（token） */
    @ApiModelProperty(value = "登录身份令牌（token）", example = "")
	@TableField("token")
    private String token;

    /** 创建来源（初始创建的系统code） */
    @ApiModelProperty(value = "创建来源（初始创建的系统code）", example = "")
	@TableField("source_system_id")
    private String sourceSystemId;

    /** 最近登录时间 */
    @ApiModelProperty(value = "最近登录时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("lastest_login_time")
    private Date lastestLoginTime;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("create_time")
    private Date createTime;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "")
	@TableField("remark")
    private String remark;

    /** 头像 */
    @ApiModelProperty(value = "头像", example = "")
	@TableField("head_portrait")
    private String headPortrait;
}
