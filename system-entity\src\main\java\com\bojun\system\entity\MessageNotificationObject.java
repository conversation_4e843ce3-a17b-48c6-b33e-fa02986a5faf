package com.bojun.system.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 消息通知关联推送对象表对象 t_message_notification_object
 * 
 * <AUTHOR>
 * @date 2021-09-01 09:41:11
 */
@ApiModel(value = "MessageNotificationObject对象" , description = "消息通知关联推送对象表")
@Data
@TableName("t_message_notification_object")
public class MessageNotificationObject implements Serializable {
    private static final long serialVersionUID = 1L;


    /** id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 消息通知id */
    @ApiModelProperty(value = "消息通知id", example = "")
	@TableField("notice_id")
    private String noticeId;

    /** 机构id */
    @ApiModelProperty(value = "机构id", example = "")
	@TableField("organization_id")
    private Integer organizationId;

    /** 部门（科室）id */
    @ApiModelProperty(value = "部门（科室）id", example = "")
	@TableField("dept_id")
    private Integer deptId;

    /** 区域（病区）id */
    @ApiModelProperty(value = "区域（病区）id", example = "")
	@TableField("ward_id")
    private Integer wardId;
}
