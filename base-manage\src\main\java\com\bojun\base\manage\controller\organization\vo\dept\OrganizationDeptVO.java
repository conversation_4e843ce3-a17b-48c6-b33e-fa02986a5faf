package com.bojun.base.manage.controller.organization.vo.dept;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月5日
*/
@ApiModel(value = "机构--科室信息", description = "机构--科室输出信息")
public class OrganizationDeptVO implements Serializable{

	private static final long serialVersionUID = -3075888287785296208L;
	
	@ApiModelProperty(value="科室id", required = true, example = "18120405")
    private Integer deptId;
	
	@ApiModelProperty(value="父级科室编码", required = true, example = "18120405")
    private Integer parentDeptId;
	
	@ApiModelProperty(value="机构编码", required = true, example = "1812")
    private Integer organizationId;
	
	@ApiModelProperty(value="科室编码", required = true, example = "18120404")
    private String deptNumber;
    
	@ApiModelProperty(value="科室名称", required = true, example = "泌尿外科")
    private String deptName;
    
	@ApiModelProperty(value="科室属性", required = true, example = "临床")
    private Integer deptAttr;
    
	@ApiModelProperty(value="门诊住院类型", required = true, example = "门诊部")
    private Integer outpOrInp;
    
	@ApiModelProperty(value="科室类型", required = true, example = "内科")
    private Integer internalOrSergery;
    
	@ApiModelProperty(value="科室主任", required = true, example = "皮皮猪主任")
    private String directorName;
	
	@ApiModelProperty(value="科室护士长", required = true, example = "皮皮猪护士长")
    private String matronName;
	
	@ApiModelProperty(value="科室图片地址", required = true, example = "http://192.168.1.11/dept/233r22.jpg")
    private String deptImageUrl;
	
	@ApiModelProperty(value="科室图片", required = true, example = "233r22.jpg")
    private String deptImage;
    
    @ApiModelProperty(value="科室描述", required = true, example = "优秀内科科室")
    private String describes;
    
    @ApiModelProperty(value="是否启用", required = true, example = "1")
    private Integer isEnabled;
    
    @ApiModelProperty(value="科室电话", required = true, example = "88888888888")
    private String telephone;
    
    @ApiModelProperty(value="科室图片集合", required = true, example = "")
    private List<DepartmentImgParamVO> imgList;
    
    private List<OrganizationDeptVO> subDeptList;
    
	public List<OrganizationDeptVO> getSubDeptList() {
		return subDeptList;
	}

	public void setSubDeptList(List<OrganizationDeptVO> subDeptList) {
		this.subDeptList = subDeptList;
	}

	public String getDeptImage() {
		return deptImage;
	}

	public void setDeptImage(String deptImage) {
		this.deptImage = deptImage;
	}

	public List<DepartmentImgParamVO> getImgList() {
		return imgList;
	}

	public void setImgList(List<DepartmentImgParamVO> imgList) {
		this.imgList = imgList;
	}
    
	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public Integer getParentDeptId() {
		return parentDeptId;
	}

	public void setParentDeptId(Integer parentDeptId) {
		this.parentDeptId = parentDeptId;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getDeptNumber() {
		return deptNumber;
	}

	public void setDeptNumber(String deptNumber) {
		this.deptNumber = deptNumber;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getDeptAttr() {
		return deptAttr;
	}

	public void setDeptAttr(Integer deptAttr) {
		this.deptAttr = deptAttr;
	}

	public Integer getOutpOrInp() {
		return outpOrInp;
	}

	public void setOutpOrInp(Integer outpOrInp) {
		this.outpOrInp = outpOrInp;
	}

	public Integer getInternalOrSergery() {
		return internalOrSergery;
	}

	public void setInternalOrSergery(Integer internalOrSergery) {
		this.internalOrSergery = internalOrSergery;
	}

	public String getDirectorName() {
		return directorName;
	}

	public void setDirectorName(String directorName) {
		this.directorName = directorName;
	}

	public String getMatronName() {
		return matronName;
	}

	public void setMatronName(String matronName) {
		this.matronName = matronName;
	}

	public String getDeptImageUrl() {
		return deptImageUrl;
	}

	public void setDeptImageUrl(String deptImageUrl) {
		this.deptImageUrl = deptImageUrl;
	}

	public String getDescribes() {
		return describes;
	}

	public void setDescribes(String describes) {
		this.describes = describes;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

}

