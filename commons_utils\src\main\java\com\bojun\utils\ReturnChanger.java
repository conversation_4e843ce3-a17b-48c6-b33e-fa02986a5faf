package com.bojun.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.response.Results;
import com.bojun.vo.Page;

import java.util.List;

/**
 * @description
 * @author:
 * @create: 2020-11-16
 **/
public interface ReturnChanger<T> {
    /**
     * @Description 解析
     * <AUTHOR>
     * @param [result, clazz]
     * @return com.bojun.response.Results
     * @date：2020-11-13日
     */
    default Results resolveResultsPage(String result, Class<T> clazz, String listName) {
        JSONObject resultOjb = JSONObject.parseObject(result);
        if (200 != resultOjb.getInteger("code")) {
            return failResultss(resultOjb.getInteger("code"), resultOjb.getString("msg"));
        }
        String jsonStr = resultOjb.get("data").toString();
        JSONObject result2 = JSONObject.parseObject(jsonStr);
        Integer totalCount = result2.getInteger("totalCount");
        String jsonStr2 = result2.get(listName).toString();
        List<T> list = JSONArray.parseArray(jsonStr2, clazz);
        Page page = new Page();
        page.setTotalCount(totalCount);
        page.setDataList(list);
        return sucessResultss(page);
    }

    default Results resolveResultsList(String result, Class<T> clazz,String listName) {
        JSONObject resultOjb = JSONObject.parseObject(result);
        if (200 != resultOjb.getInteger("code")) {
            return failResultss(resultOjb.getInteger("code"), resultOjb.getString("msg"));
        }
        String jsonStr = resultOjb.get("data").toString();
        JSONObject result2 = JSONObject.parseObject(jsonStr);
        String jsonStr2 = result2.get(listName).toString();
        List<T> list = JSONArray.parseArray(jsonStr2, clazz);
        return sucessResultss(list);
    }

    /**
     * @Description 错误结果
     * <AUTHOR>
     * @param [code, msg]
     * @return com.bojun.response.Results
     * @date：2020-11-13日
     */
    default Results failResultss(Integer code, String msg) {
        Results results = new Results<>(code, msg, null);
        return results;
    }

    /**
     * @Description 正确结果
     * <AUTHOR>
     * @param [obj]
     * @return com.bojun.response.Results
     * @date：2020-11-13日
     */
    default Results sucessResultss(Object obj) {
        Results results = new Results<>(ResponseCodeEnum.SUCCESS_REQUEST.getCode(),
                ResponseCodeEnum.SUCCESS_REQUEST.getErrorDescr(), obj);
        return results;
    }
}
