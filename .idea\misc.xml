<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/base-manage/pom.xml" />
        <option value="$PROJECT_DIR$/eureka-server/pom.xml" />
        <option value="$PROJECT_DIR$/sphygmometer-app/pom.xml" />
        <option value="$PROJECT_DIR$/health-promotion-entity/pom.xml" />
        <option value="$PROJECT_DIR$/health-promotion-service/pom.xml" />
        <option value="$PROJECT_DIR$/health-promotion-service-api/pom.xml" />
        <option value="$PROJECT_DIR$/commons_encrypt/pom.xml" />
        <option value="$PROJECT_DIR$/commons_ip/pom.xml" />
        <option value="$PROJECT_DIR$/commons_redis/pom.xml" />
        <option value="$PROJECT_DIR$/commons_sms/pom.xml" />
        <option value="$PROJECT_DIR$/commons_utils/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>