package com.bojun.base.manage.api.system.hystrix;

import com.bojun.base.manage.api.system.NoticeService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.dto.MessageNotificationDTO;
import org.springframework.stereotype.Component;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 8:45
 */

@Component
public class NoticeServiceHystrix implements NoticeService {

    @Override
    public String getNotices(MessageNotificationDTO noticeVO) {
        throw new BaseRuntimeException(" getNotices 接口服务已断开");
    }

    @Override
    public String getNoticeById(String noticeId) {
        throw new BaseRuntimeException("getNoticeById 接口服务已断开");
    }

    @Override
    public String addNotice(MessageNotificationDTO messageNotificationDTO) {
        throw new BaseRuntimeException("addNotice 接口服务已断开");
    }

    @Override
    public String deleteNotice(MessageNotificationDTO messageNotificationDTO) {
        throw new BaseRuntimeException("deleteNotice 接口服务已断开");
    }


    @Override
    public String updateNotice(MessageNotificationDTO messageNotificationDTO) {
        throw new BaseRuntimeException("updateNotice 接口服务已断开");
    }

    @Override
    public String getNoticeType() {
        throw new BaseRuntimeException(" getNoticeType 接口服务已断开");
    }


}
