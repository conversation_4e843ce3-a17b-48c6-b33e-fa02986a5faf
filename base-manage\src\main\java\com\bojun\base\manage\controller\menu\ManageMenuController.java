/**
 * 
 */
package com.bojun.base.manage.controller.menu;

import java.util.List;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.IMenuService;
import com.bojun.base.manage.controller.menu.vo.AddMenuPramVO;
import com.bojun.base.manage.controller.menu.vo.GetMenuParamVO;
import com.bojun.base.manage.controller.menu.vo.MenuInfoVO;
import com.bojun.base.manage.controller.menu.vo.SaveButtonParamVO;
import com.bojun.base.manage.controller.menu.vo.TreeMenuListParamVO;
import com.bojun.base.manage.controller.menu.vo.TreeMenuParamVO;
import com.bojun.base.manage.controller.menu.vo.UpdateMenuPramVO;
import com.bojun.base.manage.controller.menu.vo.UpdateMenuStatusPramVO;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.MenuDTO;
import com.bojun.vo.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
 * Model：菜单管理模块
 * Description：菜单管理模块
 * Author：赖水秀
 * created： 2020年4月28日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("menuMange")
@Api(tags = {"菜单管理模块接口"})
@ApiSort(value = 4)
public class ManageMenuController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(ManageMenuController.class);
	
	@Autowired
	private IMenuService menuService;
		 
	/**
	 * @Description 新增菜单信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月28日
	 */
	@ApiOperation(value = "新增菜单", notes = "新增菜单信息（赖水秀）")
	@ApiOperationSupport(order = 1)
	@RequestMapping(value="/saveMenu", method = RequestMethod.POST)
	@SystemLog(action = "saveMenu", description = "新增菜单", operationType = Contants.ADD_REQUEST)
	@AuthAnnotation(action = "saveMenu")
	public Results saveMenu(HttpServletRequest request, @RequestBody @Valid AddMenuPramVO addMenuPramVO) {
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(addMenuPramVO, menuDTO);
			//生成ID
			menuDTO.setMenuId(UUID.randomUUID().toString());			
			if(addMenuPramVO.getParentId() != null) {
				menuDTO.setLevel(addMenuPramVO.getParentLevel() + 1);
			} else {
				menuDTO.setLevel(1);
			}
			String result = menuService.saveMenu(menuDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("saveMenu:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 编辑菜单信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月28日
	 */
	@ApiOperation(value = "编辑菜单", notes = "编辑菜单信息（赖水秀）")
	@ApiOperationSupport(order = 2)
	@RequestMapping(value="/updateMenu", method = RequestMethod.POST)
	@SystemLog(action = "updateMenu", description = "编辑菜单", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateMenu")
	public Results updateMenu(@RequestBody @Valid UpdateMenuPramVO updateMenuPramVO) {
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(updateMenuPramVO, menuDTO);
			if(updateMenuPramVO.getParentId() != null) {
				menuDTO.setLevel(updateMenuPramVO.getParentLevel() + 1);
			} else {
				menuDTO.setLevel(1);
			}
			String result = menuService.updateMenu(menuDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateMenu:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 查询单个菜单数据
	 * <AUTHOR>
	 * @return
	 * Results<MenuInfoVO>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询单个菜单", notes = "查询单个菜单数据（赖水秀）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value = "/getMenuById", method = RequestMethod.POST)
	@AuthAnnotation(action = "getMenuById")
	public Results<MenuInfoVO> getMenuById(@RequestParam(value="menuId") String menuId) {		
		try {			
			String result = menuService.getMenuById(menuId);
			return returnResults(result, MenuInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getMenuById:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 分页查询菜单信息
	 * <AUTHOR>
	 * @return
	 * Results<MenuInfoVO>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询菜单列表", notes = "查询菜单列表数据（赖水秀）")
	@ApiOperationSupport(order = 4)
	@RequestMapping(value="/getMenuPageList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getMenuPageList")
	public Results<Page<MenuInfoVO>> getMenuPageList(@RequestBody @Valid GetMenuParamVO getMenuParamVO) {		
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(getMenuParamVO, menuDTO);
			String result = menuService.getMenuPageList(menuDTO);
			return returnResultsPage(result, MenuInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getMenuPageList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	

	/**
	 * @Description 查询树形菜单列表
	 * <AUTHOR>
	 * @return
	 * Results<List<MenuInfoVO>>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询树形菜单列表", notes = "查询树形菜单列表数据（赖水秀）")
	@ApiOperationSupport(order = 5)
	@RequestMapping(value="/getMenuTreeList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getMenuTreeList")
	public Results<List<MenuInfoVO>> getMenuTreeList(@RequestBody @Valid TreeMenuListParamVO treeMenuListParamVO) {		
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(treeMenuListParamVO, menuDTO);
			String result = menuService.getMenuTreeList(menuDTO);
			return returnResultsList(result, MenuInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getMenuTreeList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 查询树形菜单下拉列表数据
	 * <AUTHOR>
	 * @return
	 * Results<List<MenuInfoVO>>
	 * 2020年4月30日
	 */
	@ApiOperation(value = "查询树形菜单下拉列表数据", notes = "查询树形菜单下拉列表（赖水秀）")
	@ApiOperationSupport(order = 6)
	@RequestMapping(value="/getPullDownMenuList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getPullDownMenuList")
	public Results<List<MenuInfoVO>> getPullDownMenuList(@RequestBody @Valid TreeMenuParamVO treeMenuParamVO) {		
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(treeMenuParamVO, menuDTO);
			String result = menuService.getMenuTreeList(menuDTO);
			return returnResultsList(result, MenuInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getMenuTreeList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}	
	
	
	/**
	 * @Description 删除菜单信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月29日
	 */
	@ApiOperation(value = "删除菜单信息", notes = "删除菜单信息（赖水秀）")
	@ApiOperationSupport(order = 7)
	@RequestMapping(value="/deleteMenuById", method = RequestMethod.POST)
	@SystemLog(action = "deleteMenuById", description = "删除菜单", operationType = Contants.DELETE_REQUEST)
	@AuthAnnotation(action = "deleteMenuById")
	public Results deleteMenuById(@RequestParam(value="menuId") String menuId) {
		try {			
			String result = menuService.deleteMenuById(menuId);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateMenu:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 批量删除菜单信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月29日
	 */
	@ApiOperation(value = "批量删除菜单", notes = "批量删除菜单信息（赖水秀）")
	@ApiOperationSupport(order = 8)
	@RequestMapping(value="/batchDeleteMenu", method = RequestMethod.POST)
	@SystemLog(action = "batchDeleteMenu", description = "批量删除菜单", operationType = Contants.DELETE_REQUEST)
	@AuthAnnotation(action = "batchDeleteMenu")
	public Results batchDeleteMenu(@RequestBody @Valid List<String> menuIds) {
		try {			
			String result = menuService.batchDeleteMenu(menuIds);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateMenu:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 禁用、启用菜单信息
	 * <AUTHOR>
	 * @param request
	 * @param updateMenuStatusPramVO
	 * @return
	 * Results
	 * 2020年4月30日
	 */
	@ApiOperation(value = "禁用、启用菜单", notes = "禁用、启用菜单信息（赖水秀）")
	@ApiOperationSupport(order = 9)
	@RequestMapping(value="/updateMenuStatus", method = RequestMethod.POST)
	@SystemLog(action = "updateMenuStatus", description = "禁用、启用菜单", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateMenuStatus")
	public Results updateMenuStatus(HttpServletRequest request, @RequestBody @Valid UpdateMenuStatusPramVO updateMenuStatusPramVO) {
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(updateMenuStatusPramVO, menuDTO);
			//String result = menuService.updateMenu(menuDTO);
			String result = menuService.updateMenuStatus(menuDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateSystemDictStatus:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	/**
	 * @Description 查询菜单下的按钮列表
	 * <AUTHOR>
	 * @param treeMenuListParamVO
	 * @return
	 * Results<List<MenuInfoVO>>
	 * 2020年5月12日
	 */
	@ApiOperation(value = "查询菜单下的按钮列表", notes = "查询菜单下的按钮列表数据（赖水秀）")
	@ApiOperationSupport(order = 10)
	@RequestMapping(value="/getButtonListByMenu", method = RequestMethod.POST)
	@AuthAnnotation(action = "getButtonListByMenu")
	public Results<List<MenuInfoVO>> getButtonListByMenu(@RequestParam(value="menuId") String menuId) {		
		try {			
			String result = menuService.getButtonListByMenu(menuId);
			return returnResultsList(result, MenuInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getButtonListByMenu:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	/**
	 * @Description 保存按钮信息
	 * <AUTHOR>
	 * @param request
	 * @param updateMenuStatusPramVO
	 * @return
	 * Results
	 * 2020年4月30日
	 */
	@ApiOperation(value = "保存按钮信息", notes = "保存按钮信息（赖水秀）")
	@ApiOperationSupport(order = 11)
	@RequestMapping(value="/saveButtonInfo", method = RequestMethod.POST)
	@SystemLog(action = "saveButtonInfo", description = "禁用、启用菜单", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "saveButtonInfo")
	public Results saveButtonInfo(@RequestBody @Valid SaveButtonParamVO saveButtonParamVO) {
		try {
			MenuDTO menuDTO = new MenuDTO();
			BeanUtils.copyProperties(saveButtonParamVO, menuDTO);
			String result = menuService.saveButtonInfo(menuDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("saveButtonInfo:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

}
