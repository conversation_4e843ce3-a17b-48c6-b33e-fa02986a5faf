package com.bojun.health.promotion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.common.entity.TopicInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
@Mapper
public interface TopicInfoMapper extends BaseMapper<TopicInfo> {
    int deleteByPrimaryKey(Integer topicId);

    int insert(TopicInfoDTO topicInfo);

    int insertSelective(TopicInfoDTO topicInfo);

    TopicInfoDTO selectByPrimaryKey(Integer topicId);

    List<TopicInfoDTO> getTopicInfo(TopicInfoDTO topicInfo);

    int updateByPrimaryKeySelective(TopicInfoDTO topicInfo);

    int updateByPrimaryKey(TopicInfoDTO topicInfo);

    List<TopicInfoDTO> getChildrenTopicByParentId(Integer parentId);
}