package com.bojun.utils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.*;
import org.springframework.lang.Nullable;
import org.springframework.util.ClassUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;

/**
 * Model：bean工具类
 * Description：bean工具类
 * Author:  毛国辉
 * created：2019年4月18日
 */
public class BeanUtil extends BeanUtils {

    private static final Log logger = LogFactory.getLog(BeanUtil.class);

    public static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<String>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null || srcValue.toString().length() < 1)
                emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }

    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        copyProperties(src, target, getNullPropertyNames(src));
    }

    public static <T> List<T> deepCopyProperties(List sourceList, Class<T> targetClass) {
        try {
            List<T> list = new ArrayList<>();
            for (Object o : sourceList) {
                T t =  deepCopyProperties(o, targetClass, (String[]) null);
                list.add(t);
            }
            return list;
        } catch (InstantiationException e) {
            logger.error(e.getMessage(), e);
        } catch (IllegalAccessException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 深度复制
     * 实现了 [实体嵌套实体] [实体嵌套List] 的深拷贝
     * 排除了实体常用的数据类型
     * 暂不支持 [实体嵌套Map] 的深拷贝，可自行实现
     * 20201208 严峡华
     *
     * @param source      复制源
     * @param targetClass 目标源Class
     * @throws Exception
     */
    public static <T> T deepCopyProperties(Object source, Class<T> targetClass) {
        try {
            return deepCopyProperties(source, targetClass, (String[]) null);
        } catch (InstantiationException e) {
            logger.error(e.getMessage(), e);
        } catch (IllegalAccessException e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 深度复制
     * 实现了 [实体嵌套实体] [实体嵌套List] 的深拷贝
     * 排除了实体常用的数据类型
     * 暂不支持 [实体嵌套Map] 的深拷贝，可自行实现
     * 20201208 严峡华
     *
     * @param source           复制源
     * @param targetClazz      目标源Class
     * @param ignoreProperties 忽略字段
     * @throws Exception
     */
    private static <T> T deepCopyProperties(Object source, Class targetClazz, @Nullable String... ignoreProperties) throws BeansException, InstantiationException, IllegalAccessException {
        if (source == null) return null;
        Object target = targetClazz.newInstance();
        PropertyDescriptor[] targetPds = getPropertyDescriptors(targetClazz);
        List<String> ignoreList = (ignoreProperties != null ? Arrays.asList(ignoreProperties) : null);

        for (PropertyDescriptor targetPd : targetPds) {
            //拿到目标字段的setter方法
            Method targetPdWriteMethod = targetPd.getWriteMethod();
            if (targetPdWriteMethod != null && (ignoreList == null || !ignoreList.contains(targetPd.getName()))) {
                PropertyDescriptor sourcePd = getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null) {
                    //拿到源字段的getter方法
                    Method sourcePdReadMethod = sourcePd.getReadMethod();
                    if (sourcePdReadMethod != null) {
                        try {
                            if (!Modifier.isPublic(sourcePdReadMethod.getDeclaringClass().getModifiers())) {
                                sourcePdReadMethod.setAccessible(true);
                            }
                            //执行getter拿到value
                            Object sourcePdValue = sourcePdReadMethod.invoke(source);
                            if (!Modifier.isPublic(targetPdWriteMethod.getDeclaringClass().getModifiers())) {
                                targetPdWriteMethod.setAccessible(true);
                            }
                            Method targetPdReadMethod = targetPd.getReadMethod();
                            if (!Modifier.isPublic(targetPdReadMethod.getDeclaringClass().getModifiers())) {
                                targetPdReadMethod.setAccessible(true);
                            }
                            /**
                             * ClassUtils.isAssignable：
                             * 判断两者是否是基本类型，两者是否是继承关系，两者是否为基础类型的包装类
                             */
                            if (ClassUtils.isAssignable(targetPdWriteMethod.getParameterTypes()[0], sourcePdReadMethod.getReturnType())) {
                                if (sourcePdValue instanceof Collection) {  //是否为集合类型字段
                                    Type type = targetPdReadMethod.getGenericReturnType();
                                    Class entityClazz = (Class) ((ParameterizedType) type).getActualTypeArguments()[0];
                                    if (isEntityDataType(entityClazz)) {
                                        //如果entityClazz为实体类常用的数据类型就直接设值到target中
                                        targetPdWriteMethod.invoke(target, sourcePdValue);
                                    } else {
                                        List list = new ArrayList();
                                        ((Collection) sourcePdValue).forEach(collectionItem -> {
                                            Object entity = deepCopyProperties(collectionItem, entityClazz);
                                            list.add(entity);
                                        });
                                        //将list设值到target中
                                        targetPdWriteMethod.invoke(target, list);
                                    }
                                } else {    //实体类常用的数据类型直接设值
                                    //将value设值到target中
                                    targetPdWriteMethod.invoke(target, sourcePdValue);
                                }
                            } else {//如果不符合ClassUtils.isAssignable的条件，说明其是其他类型，则进行递归复制
                                Object entity = deepCopyProperties(sourcePdValue, targetPdReadMethod.getReturnType());
                                targetPdWriteMethod.invoke(target, entity);
                            }
                        } catch (Throwable ex) {
                            throw new FatalBeanException(
                                    "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
                        }
                    }
                }
            }
        }
        return (T) target;
    }

    /**
     * 判断是否为实体类常用的数据类型
     * 严峡华
     *
     * @param clazz Class
     * @return
     */
    private static boolean isEntityDataType(Class clazz) {
        if (Byte.class == clazz ||
                Short.class == clazz ||
                Integer.class == clazz ||
                Long.class == clazz ||
                Float.class == clazz ||
                Double.class == clazz ||
                Boolean.class == clazz ||
                Character.class == clazz ||
                String.class == clazz ||
                BigDecimal.class == clazz ||
                java.util.Date.class == clazz ||
                java.sql.Date.class == clazz) {
            return true;
        }
        return false;
    }

}
