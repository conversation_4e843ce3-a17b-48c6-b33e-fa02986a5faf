package com.bojun.base.system.api.controller;

import com.bojun.base.system.api.RoleFeignClient;
import com.bojun.base.system.service.RoleService;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.Results;
import com.bojun.system.dto.RoleDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 角色表FeignController
 *
 * <AUTHOR>
 * @date 2021-06-15 11:01:35
 */
@RestController
public class RoleFeignController extends BaseFeignController implements RoleFeignClient {
    @Autowired
    private RoleService roleService;

//    /**
//     * 查询角色表分页列表
//     */
//    @PostMapping(PREFIX + "/page")
//    public Results<PageData<RoleDTO>> page(@RequestBody RoleDTO roleDTO) {
//        startPage(roleDTO.getPageNum(), roleDTO.getEveryPage());
//        List<RoleDTO> list = roleService.selectRoleList(roleDTO);
//        return Results.list(getPageData(list));
//    }
//
//    /**
//     * 查询角色表列表
//     */
//    @PostMapping(PREFIX + "/list")
//    public Results<List<RoleDTO>> list(@RequestBody RoleDTO roleDTO) {
//        List<RoleDTO> list = roleService.selectRoleList(roleDTO);
//        return Results.list(list);
//    }

    /**
     * 获取角色表详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<RoleDTO> getInfo(@RequestParam("roleId") String roleId) {
        RoleDTO roleDTO = roleService.selectRoleById(roleId);
        return Results.data(roleDTO);
    }

//    /**
//     * 新增角色表DTO
//     */
//    @PostMapping(PREFIX + "/addDTO")
//    public Results addDTO(@RequestBody RoleDTO roleDTO) {
//        Integer num = roleService.insertRole(roleDTO);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 修改角色表DTO
//     */
//    @PostMapping(PREFIX + "/editDTO")
//    public Results editDTO(@RequestBody RoleDTO roleDTO) {
//        Integer num = roleService.updateRole(roleDTO);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 新增角色表
//     */
//    @PostMapping(PREFIX + "/add")
//    public Results add(@RequestBody Role role) {
//        Integer num = roleService.insertRole(role);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 修改角色表
//     */
//    @PostMapping(PREFIX + "/edit")
//    public Results edit(@RequestBody Role role) {
//        Integer num = roleService.updateRole(role);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 删除角色表，多个以逗号分隔
//     */
//    @GetMapping(PREFIX + "/removeByIds")
//    public Results removeByIds(@RequestParam("ids") String ids) {
//        Integer num = roleService.deleteRoleByIds(ids);
//        return Results.opResult(num);
//    }
}
