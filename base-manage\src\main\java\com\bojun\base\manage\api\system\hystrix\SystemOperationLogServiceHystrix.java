package com.bojun.base.manage.api.system.hystrix;

import com.bojun.base.manage.api.system.ISystemOperationService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.dto.OperationLogDTO;
import com.bojun.system.entity.OperationLog;

import org.springframework.stereotype.Component;

@Component
public class SystemOperationLogServiceHystrix implements ISystemOperationService {
    @Override
    public String getSystemLogList(OperationLogDTO operationLogDTO) {
        throw new BaseRuntimeException("getSystemLogList 接口服务已断开");
    }

	@Override
	public String addSystemLog(OperationLog operationLog) {
		throw new BaseRuntimeException("addSystemLog 接口服务已断开");
	}
}
