/**
 * 
 */
package com.bojun.base.manage.controller.systemDict.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：产品管理
 * Description：查询产品分类下拉列表数据返回信息
 * Author：赖水秀
 * created： 2020年4月27日
 */
@ApiModel(value = "查询产品分类数据", description = "查询产品分类下拉列表数据返回信息")
public class SystemDictTypeVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;

	@ApiModelProperty(value="系统类型id")
	private Integer systemTypeId; 
	
	@ApiModelProperty(value="系统类型名称")
	private String systemTypeName;

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public String getSystemTypeName() {
		return systemTypeName;
	}

	public void setSystemTypeName(String systemTypeName) {
		this.systemTypeName = systemTypeName;
	}

}
