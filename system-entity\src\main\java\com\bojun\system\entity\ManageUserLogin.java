/**
 * 
 */
package com.bojun.system.entity;

import java.io.Serializable;
import java.util.Date;

/**
*Model：系统管理员登录信息实体类
*Description：系统管理员登录信息实体类
*Author: 段德鹏
*created：2019年1月13日
*/
public class ManageUserLogin implements Serializable {

	private static final long serialVersionUID = 7584093660532046590L;
	
	//id主键
	private Integer id;
	//用户id
	private Integer userId;
	//关联产品id
	private String systemId;
	//登录的ip地址
	private String ipAddress;
	//登录时间
	private Date loginTime;
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public Integer getUserId() {
		return userId;
	}
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	public String getIpAddress() {
		return ipAddress;
	}
	public void setIpAddress(String ipAddress) {
		this.ipAddress = ipAddress;
	}
	public Date getLoginTime() {
		return loginTime;
	}
	public void setLoginTime(Date loginTime) {
		this.loginTime = loginTime;
	}
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	
}
