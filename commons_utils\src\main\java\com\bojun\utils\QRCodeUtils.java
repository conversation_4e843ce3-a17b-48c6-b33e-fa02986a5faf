/**
 * 
 */
package com.bojun.utils;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import javax.imageio.ImageIO;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

/**
 * <AUTHOR>
 *
 */
public class QRCodeUtils {
	
	/**
	 * 生成二维码
	 * @param width
	 * @param height
	 * @param content
	 * @throws WriterException
	 * @throws IOException
	 */
    public static String createQRcode(int width, int height, String content) throws WriterException, IOException {  
        Map<EncodeHintType, Object> hints = new HashMap<EncodeHintType, Object>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        // 生成矩阵 
        BitMatrix bitMatrix = new MultiFormatWriter().encode(content,  
                BarcodeFormat.QR_CODE, width, height, hints);
        bitMatrix = deleteWhite(bitMatrix);//删除白边
        width = bitMatrix.getWidth();
        height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(bitMatrix.getWidth(), bitMatrix.getHeight(), BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        ByteArrayOutputStream bs = null;
        String imgBase64 = null;
       //将二维码输出到流  
        try {  
            bs = new ByteArrayOutputStream();
            ImageIO.write(image, "png", bs);
            imgBase64 = Base64.byteArrayToBase64(bs.toByteArray());
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
        	try {
				bs.close();
			} catch (IOException e) {
				e.printStackTrace();
			} finally{
				bs = null;
			}
        }    
        return imgBase64;
    }
    
    private static BitMatrix deleteWhite(BitMatrix matrix) {
        int[] rec = matrix.getEnclosingRectangle();
        int resWidth = rec[2] + 1;
        int resHeight = rec[3] + 1;

        BitMatrix resMatrix = new BitMatrix(resWidth, resHeight);
        resMatrix.clear();
        for (int i = 0; i < resWidth; i++) {
            for (int j = 0; j < resHeight; j++) {
                if (matrix.get(i + rec[0], j + rec[1]))
                    resMatrix.set(i, j);
            }
        }
        return resMatrix;
    }
    
    public static void main(String[] args) throws Exception {  
    	System.out.println(createQRcode(28,28,"110｜01"));  
    }  
}
