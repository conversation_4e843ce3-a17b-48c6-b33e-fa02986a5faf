/**
 * 
 */
package com.bojun.base.manage.api.system.hystrix;

import java.util.List;

import org.springframework.stereotype.Component;

import com.bojun.base.manage.api.system.IMenuService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.dto.MenuDTO;

/**
 * Model： 菜单管理接口熔断器
 * Description：菜单管理接口熔断器
 * Author：赖水秀
 * created： 2020年4月28日
 */
@Component
public class MenuServiceHystrix implements IMenuService {

	@Override
	public String saveMenu(MenuDTO menuDTO) {
		throw new BaseRuntimeException("saveMenu接口服务已断开");
	}

	@Override
	public String updateMenu(MenuDTO menuDTO) {
		throw new BaseRuntimeException("updateMenu接口服务已断开");
	}

	@Override
	public String getMenuById(String menuId) {
		throw new BaseRuntimeException("getMenuById接口服务已断开");
	}

	@Override
	public String getMenuPageList(MenuDTO menuDTO) {
		throw new BaseRuntimeException("getMenuList接口服务已断开");
	}

	@Override
	public String getMenuTreeList(MenuDTO menuDTO) {
		throw new BaseRuntimeException("getMenuTreeList接口服务已断开");
	}

	@Override
	public String deleteMenuById(String menuId) {
		throw new BaseRuntimeException("deleteMenuById接口服务已断开");
	}

	@Override
	public String batchDeleteMenu(List<String> menuIds) {
		throw new BaseRuntimeException("batchDeleteMenu接口服务已断开");
	}

	@Override
	public String getButtonListByMenu(String menuId) {
		throw new BaseRuntimeException("getButtonListByMenu接口服务已断开");
	}

	@Override
	public String saveButtonInfo(MenuDTO menuDTO) {
		throw new BaseRuntimeException("saveButtonInfo接口服务已断开");
	}

	@Override
	public String updateMenuStatus(MenuDTO menuDTO) {
		throw new BaseRuntimeException("updateMenuStatus接口服务已断开");
	}

	@Override
	public String getMenuButtonTree(MenuDTO menuDTO) {
		throw new BaseRuntimeException("getMenuButtonTree接口服务已断开");
	}	

}
