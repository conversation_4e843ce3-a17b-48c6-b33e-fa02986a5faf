package com.bojun.base.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.MessageNotificationSystemV2DTO;
import com.bojun.system.entity.MessageNotificationSystemV2;

import java.util.List;

/**
 * MessageNotificationSystemV2Service接口
 * 
 * <AUTHOR>
 * @date 2021-07-22 16:11:35
 */
public interface MessageNotificationSystemV2Service extends IService<MessageNotificationSystemV2> {
    /**
     * 查询消息通知关联产品表
     * 
     * @param id 消息通知关联产品表ID
     * @return 消息通知关联产品表
     */
    public MessageNotificationSystemV2DTO selectMessageNotificationSystemV2ById(Integer id);

    /**
     * 查询消息通知关联产品表列表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 消息通知关联产品表集合
     */
    public List<MessageNotificationSystemV2DTO> selectMessageNotificationSystemV2List(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO);

    /**
     * 新增消息通知关联产品表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 结果
     */
    public int insertMessageNotificationSystemV2(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO);

    /**
     * 修改消息通知关联产品表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 结果
     */
    public int updateMessageNotificationSystemV2(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO);
    
    /**
     * 新增消息通知关联产品表
     * 
     * @param messageNotificationSystemV2 消息通知关联产品表
     * @return 结果
     */
    public int insertMessageNotificationSystemV2(MessageNotificationSystemV2 messageNotificationSystemV2);

    /**
     * 修改消息通知关联产品表
     * 
     * @param messageNotificationSystemV2 消息通知关联产品表
     * @return 结果
     */
    public int updateMessageNotificationSystemV2(MessageNotificationSystemV2 messageNotificationSystemV2);

    /**
     * 批量删除消息通知关联产品表
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteMessageNotificationSystemV2ByIds(String ids);

    /**
     * 删除消息通知关联产品表信息
     * 
     * @param id 消息通知关联产品表ID
     * @return 结果
     */
    public int deleteMessageNotificationSystemV2ById(Integer id);
}
