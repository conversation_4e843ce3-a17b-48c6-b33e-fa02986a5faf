package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：禁用、启用机构信息
 * Author：赖水秀
 * created： 2020年5月7日
 */
@ApiModel(value = "禁用、启用机构信息", description = "禁用、启用机构入参信息")
public class UpdateOrganizationStatusPramVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6585776517296448821L;
	
	@NotNull(message = " 机构id不能为空")
	@ApiModelProperty(value = "机构id", required = true, example = "10001")
	private Integer organizationId; 
	
	@NotNull(message = "状态不能为空")
	@ApiModelProperty(value="是否启用  0：否  1:是 ", required = true, example = "1")
	private Integer isEnabled;

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}
	
}
