package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知实体类
 * @Description: 基础控台融云消息通知实体类
 * @since 2020-12-04
 */
@Data
@TableName("t_rong_notice")
public class RongNotice extends Model<RongNotice> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "notice_id")
    private String noticeId; //消息通知id(uuid)

    @TableField("title")
    private String title; //消息标题

    @TableField("notice_type_id")
    private String noticeTypeId; //消息通知类型id

    @TableField("push_products")
    private Integer pushProducts; //推送产品 1.全部 2.患者移动端，3医生移动端

    @TableField("is_immediately")
    private Integer isImmediately; //是否立即发送 1:是 0:否

    @TableField("timing_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timingTime; //定时发布时间

    @TableField("status")
    private Integer status; //状态：1：已发布 2：待发布  3：已撤回   4：重发

    @TableField("is_delete")
    private Integer isDelete; //是否删除 1:是 0:否

    @TableField("delete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime; //删除时间

    @TableField("publish_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime; //发布时间

    @TableField("create_user_id")
    private Integer createUserId; //创建（发布）人用户id

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime; //创建时间
    @TableField(value = "notice_content")
    private String noticeContent; //消息通知id(uuid)

    @Override
    protected Serializable pkVal() {
        return this.noticeId;
    }

}
