server.port=8707

#臂筒式设备Socket端口
device.netty.server.port=15002
#Websocket端口
websocket.netty.server.port=16002

#注册中心
eureka.client.service-url.defaultZone=http://localhost:1115/eureka

#redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379

#高血压智能网格筛查小程序的appId/appSecret
wx.open.ma.appId=
wx.open.ma.secret=
wx.open.ma.wx_app_queue_notice=
wx.open.ma.wx_app_get_member=
wx.open.ma.wx_app_blood_pressure=



wx.open.mp.appId=
wx.open.mp.secret=
wx.open.mp.device_offline=
# 文件预览前缀
file.prefix.url =

#小程序知识模块是否显示（1：显示，2：不显示）
news.is.enabled =2