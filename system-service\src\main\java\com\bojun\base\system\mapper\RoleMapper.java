package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.RoleDTO;
import com.bojun.system.entity.Role;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * RoleMapper接口
 *
 * <AUTHOR>
 * @date 2021-06-15 11:01:35
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 查询角色表
     *
     * @param roleId 角色表ID
     * @return 角色表
     */
    public RoleDTO selectRoleById(String roleId);

    /**
     * 查询角色表列表
     *
     * @param roleDTO 角色表
     * @return 角色表集合
     */
    public List<RoleDTO> selectRoleList(RoleDTO roleDTO);
}
