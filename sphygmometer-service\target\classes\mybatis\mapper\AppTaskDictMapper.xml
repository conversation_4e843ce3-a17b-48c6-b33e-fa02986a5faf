<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.AppTaskDictMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.AppTaskDictDTO" id="AppTaskDictDTOResult">
        <result property="taskId"    column="task_id"    />
        <result property="taskTitle"    column="task_title"    />
        <result property="content"    column="content"    />
        <result property="taskType"    column="task_type"    />
        <result property="measurePosture"    column="measure_posture"    />
        <result property="showIndex"    column="show_index"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="matchBeginTime"    column="match_begin_time"    />
        <result property="matchEndTime"    column="match_end_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectAppTaskDict">
    	select
	        task_id,
	        task_title,
	        content,
	        task_type,
	        measure_posture,
	        show_index,
	        is_enabled,
	        match_begin_time,
	        match_end_time,
	        update_user_id,
	        update_time,
	        create_user_id,
	        create_time,
	        DATE_SUB(curdate(),INTERVAL 0 DAY) matchEndTimeStr,
	        (select count(1) from t_user_task_record where user_id =#{userId} and task_id=tatd.task_id and  to_days( record_time) = to_days( now() ) ) isAccomplish
		from 
        	t_app_task_dict tatd
    </sql>

    <select id="selectAppTaskDictById" parameterType="int" resultMap="AppTaskDictDTOResult">
		<include refid="selectAppTaskDict"/>
		where 
        	task_id = #{taskId}
    </select>

    <select id="selectAppTaskDictList" parameterType="com.bojun.sphygmometer.dto.AppTaskDictDTO" resultMap="AppTaskDictDTOResult">
        <include refid="selectAppTaskDict"/>
        <where>  
		<if test="taskId != null "> and tatd.task_id = #{taskId}</if>
		<if test="taskTitle != null  and taskTitle != ''"> and task_title = #{taskTitle}</if>
		<if test="content != null  and content != ''"> and content = #{content}</if>
		<if test="taskType != null "> and task_type = #{taskType}</if>
		<if test="measurePosture != null "> and measure_posture = #{measurePosture}</if>
		<if test="showIndex != null "> and show_index = #{showIndex}</if>
		<if test="isEnabled != null "> and is_enabled = #{isEnabled}</if>
		<if test="matchBeginTime != null  and matchBeginTime != ''"> and match_begin_time = #{matchBeginTime}</if>
		<if test="matchEndTime != null  and matchEndTime != ''"> and match_end_time = #{matchEndTime}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>

</mapper>