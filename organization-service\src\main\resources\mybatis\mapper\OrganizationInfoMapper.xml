<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.organization.mapper.OrganizationInfoMapper" >

    <!-- 新增机构信息 -->
  <insert id="saveOrganizationInfo"
		  parameterType="com.bojun.organization.dto.OrganizationInfoDTO"
		  useGeneratedKeys="true" keyProperty="organizationId"
		  keyColumn="organization_id">
	  insert into t_organization_info
	  <trim prefix="(" suffix=")" suffixOverrides=",">
		  <if test="organizationName != null and organizationName != '' ">
			  organization_name,
		  </if>
		  <if test="socialCreditCode != null and socialCreditCode != '' ">
			  social_credit_code,
		  </if>
		  <if test="organizationProperty != null and organizationProperty != '' ">
			  organization_property,
		</if>
		<if test="organizationScale != null and organizationScale != '' ">
			organization_scale,
		</if>
		<if test="organizationClassCode != null and organizationClassCode != '' ">
			organization_class_code,
		</if>
		<if test="organizationTypeCode != null and organizationTypeCode != '' ">
			organization_type_code,
		</if>
		<if test="organizationDirector != null and organizationDirector != '' ">
			organization_director,
		</if>
		<if test="telephoneNumber != null and telephoneNumber != '' ">
			telephone_number,
		</if>
		<if test="provinceCode != null and provinceCode != '' ">
			province_code,
		</if>
		<if test="cityCode != null and cityCode != '' ">
			city_code,
		</if>
		<if test="countyCode != null and countyCode != '' ">
			county_code,
		</if>
		<if test="organizationAddress != null and organizationAddress != '' ">
			organization_address,
		</if>
		<if test="longitude != null and longitude != '' ">
			longitude,
		</if>
		<if test="latitude != null and latitude != '' ">
			latitude,
		</if>
		<if test="organizationIntroduction != null and organizationIntroduction != '' ">
			organization_introduction,
		</if>
		<if test="organizationHistory != null and organizationHistory != '' ">
			organization_history,
		</if>
		<if test="organizationHonor != null and organizationHonor != '' ">
			organization_honor,
		</if>
		<if test="frontBusinessLicense != null and frontBusinessLicense != '' ">
			front_business_license,
		</if>
		<if test="backBusinessLicense != null and backBusinessLicense != '' ">
			back_business_license,
		</if>
		<if test="createUserId != null and createUserId != '' ">
			create_user_id,
		</if>
		<if test="createTime != null">
			create_time,
		</if>
		<if	test="parentId != null">
			parent_id,
		</if>
		<if test="organizationCode != null ">
			organization_code,
		</if>
		<if	test="isEnabled != null">
			is_enabled,
		</if>
		<if test="townName != null and townName != ''">
			town_name,
		</if>
		<if test="villageName != null and villageName != ''">
			village_name,
		</if>
		<if test="townCode != null and townCode != ''">
			town_code,
		</if>
		<if test="villageCode != null and villageCode != ''">
			village_code,
		</if>
		<if test="weight != null ">
			weight,
		</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">	   
		<if test="organizationName != null and organizationName != '' ">
			#{organizationName,jdbcType=VARCHAR},
		</if>
		<if test="socialCreditCode != null and socialCreditCode != '' ">
			#{socialCreditCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationProperty != null and organizationProperty != '' ">
			#{organizationProperty,jdbcType=INTEGER},
		</if>
		<if test="organizationScale != null and organizationScale != '' ">
			#{organizationScale,jdbcType=VARCHAR},
		</if>
		<if
			test="organizationClassCode != null and organizationClassCode != '' ">
			#{organizationClassCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationTypeCode != null and organizationTypeCode != '' ">
			#{organizationTypeCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationDirector != null and organizationDirector != '' ">
			#{organizationDirector,jdbcType=VARCHAR},
		</if>
		<if test="telephoneNumber != null and telephoneNumber != '' ">
			#{telephoneNumber,jdbcType=VARCHAR},
		</if>
		<if test="provinceCode != null and provinceCode != '' ">
			#{provinceCode,jdbcType=VARCHAR},
		</if>
		<if test="cityCode != null and cityCode != '' ">
			#{cityCode,jdbcType=VARCHAR},
		</if>
		<if test="countyCode != null and countyCode != '' ">
			#{countyCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationAddress != null and organizationAddress != '' ">
			#{organizationAddress,jdbcType=VARCHAR},
		</if>
		<if test="longitude != null and longitude != '' ">
			#{longitude,jdbcType=VARCHAR},
		</if>
		<if test="latitude != null and latitude != '' ">
			#{latitude,jdbcType=VARCHAR},
		</if>
		<if test="organizationIntroduction != null and organizationIntroduction != '' ">
			#{organizationIntroduction,jdbcType=VARCHAR},
		</if>
		<if test="organizationHistory != null and organizationHistory != '' ">
			#{organizationHistory,jdbcType=VARCHAR},
		</if>
		<if test="organizationHonor != null and organizationHonor != '' ">
			#{organizationHonor,jdbcType=VARCHAR},
		</if>
		<if test="frontBusinessLicense != null and frontBusinessLicense != '' ">
			#{frontBusinessLicense,jdbcType=VARCHAR},
		</if>
		<if test="backBusinessLicense != null and backBusinessLicense != '' ">
			#{backBusinessLicense,jdbcType=VARCHAR},
		</if>
		<if test="createUserId != null ">
			#{createUserId,jdbcType=INTEGER},
		</if>
		<if test="createTime != null">
			#{createTime,jdbcType=TIMESTAMP},
		</if>
		<if	test="parentId != null">
			#{parentId,jdbcType=INTEGER},
		</if>
		<if test="organizationCode != null ">
			#{organizationCode,jdbcType=VARCHAR},
		</if> 
		<if	test="isEnabled != null">
			#{isEnabled,jdbcType=INTEGER},
		</if>
		<if test="townName != null and townName != ''">
			#{townName,jdbcType=VARCHAR},
		</if>
		<if test="villageName != null and villageName != ''">
			#{villageName,jdbcType=VARCHAR},
		</if>
		<if test="townCode != null and townCode != ''">
			#{townCode,jdbcType=VARCHAR},
		</if>
		<if test="villageCode != null and villageCode != ''">
			#{villageCode,jdbcType=VARCHAR},
		</if>
		<if test="weight != null ">
			#{weight,jdbcType=INTEGER},
		</if>
    </trim>
  </insert>
  
  <!-- 修改机构信息 -->
  <update id="updateOrganizationInfo" parameterType="com.bojun.organization.dto.OrganizationInfoDTO">
    update t_organization_info
    <set>      
		<if test="organizationName != null ">
			organization_name=#{organizationName,jdbcType=VARCHAR},
		</if>
		<if test="socialCreditCode != null ">
			social_credit_code=#{socialCreditCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationProperty != null ">
			organization_property=#{organizationProperty,jdbcType=INTEGER},
		</if>
		<if test="organizationScale != null ">
			organization_scale=#{organizationScale,jdbcType=VARCHAR},
		</if>
		<if test="organizationClassCode != null ">
			organization_class_code=#{organizationClassCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationTypeCode != null ">
			organization_type_code=#{organizationTypeCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationDirector != null ">
			organization_director=#{organizationDirector,jdbcType=VARCHAR},
		</if>
		<if test="telephoneNumber != null ">
			telephone_number=#{telephoneNumber,jdbcType=VARCHAR},
		</if>
		<if test="provinceCode != null ">
			province_code=#{provinceCode,jdbcType=VARCHAR},
		</if>
		<if test="cityCode != null ">
			city_code=#{cityCode,jdbcType=VARCHAR},
		</if>
		<if test="countyCode != null ">
			county_code=#{countyCode,jdbcType=VARCHAR},
		</if>
		<if test="organizationAddress != null ">
			organization_address=#{organizationAddress,jdbcType=VARCHAR},
		</if>
		<if test="longitude != null ">
			longitude=#{longitude,jdbcType=VARCHAR},
		</if>
		<if test="latitude != null ">
			latitude=#{latitude,jdbcType=VARCHAR},
		</if>
		<if test="organizationIntroduction != null ">
			organization_introduction=#{organizationIntroduction,jdbcType=VARCHAR},
		</if>
		<if test="organizationHistory != null ">
			organization_history=#{organizationHistory,jdbcType=VARCHAR},
		</if>
		<if test="organizationHonor != null ">
			organization_honor=#{organizationHonor,jdbcType=VARCHAR},
		</if>
		<if test="frontBusinessLicense != null ">
			front_business_license=#{frontBusinessLicense,jdbcType=VARCHAR},
		</if>
		<if test="backBusinessLicense != null ">
			back_business_license=#{backBusinessLicense,jdbcType=VARCHAR},
		</if>
		<if test="createUserId != null ">
			create_user_id=#{createUserId,jdbcType=INTEGER},
		</if>
		<if	test="parentId != null and parentId != '' ">
			parent_id=#{parentId,jdbcType=INTEGER},
		</if>
		<if test="organizationCode != null ">
			organization_code=#{organizationCode,jdbcType=VARCHAR},
		</if> 
		<if	test="isEnabled != null ">
			is_enabled=#{isEnabled,jdbcType=INTEGER},
		</if>
		<if test="townName != null and townName != ''">
			town_name=#{townName,jdbcType=VARCHAR},
		</if>
		<if test="villageName != null and villageName != ''">
			village_name=#{villageName,jdbcType=VARCHAR},
		</if>
		<if test="townCode != null and townCode != ''">
			town_code=#{townCode,jdbcType=VARCHAR},
		</if>
		<if test="villageCode != null and villageCode != ''">
			village_code=#{villageCode,jdbcType=VARCHAR},
		</if> 
		<if test="weight != null ">
			weight=#{weight,jdbcType=INTEGER},
		</if> 
    </set>
    where organization_id = #{organizationId,jdbcType=VARCHAR}
  </update>
   
   <!-- 查询单个机构数据 -->
   <!-- 
	<select id="getOrganizationInfoById" resultType="com.bojun.organization.dto.OrganizationInfoDTO" parameterType="Integer">
	 -->
	<select id="getOrganizationInfoById" resultMap="orgInfoMap" parameterType="Integer">	 
      SELECT 
		organization_id,		
		organization_name,		
		social_credit_code,		
		organization_property,		
		organization_scale,		
		organization_class_code,		
		organization_type_code,		
		organization_director,		
		telephone_number,		
		province_code,
		weight,		
		city_code,	
		county_code,		
		organization_address,	
		longitude,		
		latitude,		
		organization_introduction,		
		organization_history,	
		organization_honor,		
		front_business_license,		
		back_business_license,	
		create_user_id,				
		parent_id,	
		organization_code,
		is_enabled,
		t.town_name,
		t.village_name,
		t.town_code,
		t.village_code
	  FROM t_organization_info t where organization_id = #{organizationId}      
    </select>

	<select id="getOrganizationByName" resultType="com.bojun.organization.dto.OrganizationInfoDTO"
			parameterType="com.bojun.organization.dto.OrganizationInfoDTO">
	select organization_id,
		organization_name,
		social_credit_code,
		organization_property,
		organization_scale,
		organization_class_code,
		organization_type_code,
		organization_director,
		telephone_number,
		province_code,
		city_code,
		county_code,
		organization_address,
		longitude,
		latitude,
		organization_introduction,
		organization_history,
		organization_honor,
		front_business_license,
		back_business_license,
		create_user_id,
		parent_id,
		organization_code,
		is_enabled,
		t.town_name,
		t.village_name,
		t.town_code,
		t.village_code,
		weight
		from t_organization_info t where 1=1
		<if test="organizationName != null and organizationName != '' ">
			and organization_name =#{organizationName}
		</if>
	</select>

    <!-- 分页查询机构数数据列表 -->
	<select id="getOrganizationPageList" resultType="com.bojun.organization.dto.OrganizationInfoDTO" 
							parameterType="com.bojun.organization.dto.OrganizationInfoDTO">
      select organization_id,		
			organization_name,		
			social_credit_code,		
			organization_property,		
			organization_scale,		
			organization_class_code,		
			organization_type_code,		
			organization_director,		
			telephone_number,		
			province_code,		
			city_code,	
			county_code,		
			organization_address,	
			longitude,		
			latitude,		
			organization_introduction,		
			organization_history,	
			organization_honor,		
			front_business_license,		
			back_business_license,	
			create_user_id,				
			parent_id,	
			organization_code,
			is_enabled,
			t.town_name,
			t.village_name,
			t.town_code,
			t.village_code,
			weight,
		(SELECT count(1) from system.t_manage_user e where e.organization_id = t.organization_id and e.status=1
		<if test="userType != null and userType != ''">
			and e.user_type = #{userType}
		</if>
		) employeeCount,
      	(select organization_name from t_organization_info d  where d.organization_id = t.parent_id) parentName,
		(select organization_type_name from t_organization_type_dict d  where d.organization_type_code = t.organization_type_code) organizationTypeName,
		(select province_name from system.t_province_dict d  where d.province_code = t.province_code) province_name,
		(select city_name from system.t_city_dict d  where d.city_code = t.city_code) city_name,
		(select county_name from system.t_county_dict d  where d.county_code = t.county_code) county_name
		from t_organization_info t
		where 1=1
	   <if test="organizationClassCode != null ">
			and organization_class_code=#{organizationClassCode,jdbcType=VARCHAR}
	   </if>
       <if test="organizationId != null">
			and (parent_id=#{organizationId,jdbcType=INTEGER} or organization_id=#{organizationId,jdbcType=INTEGER})
	   </if>
	   <if test="organizationId == null or organizationId == '0' ">
			and (parent_id is null or parent_id ='0') 
	   </if>
	   <if test="organizationName != null and organizationName != '' ">
			and organization_name LIKE CONCAT('%',#{organizationName},'%')
	   </if>
	   
    </select>


	<!-- 分页查询机构数数据列表 -->
	<select id="getOrganizationListPage" resultType="com.bojun.organization.dto.OrganizationInfoDTO"
			parameterType="com.bojun.organization.entity.OrganizationInfo">
		select organization_id,
		organization_name,
		social_credit_code,
		organization_property,
		organization_scale,
		organization_class_code,
		organization_type_code,
		organization_director,
		telephone_number,
		province_code,
		city_code,
		county_code,
		organization_address,
		longitude,
		latitude,
		organization_introduction,
		organization_history,
		organization_honor,
		front_business_license,
		back_business_license,
		create_user_id,
		parent_id,
		organization_code,
		is_enabled,
		t.town_name,
		t.village_name,
		t.town_code,
		t.village_code,
		weight
		from t_organization_info t
	</select>
        
     <!-- 查询所有机构数数据列表 -->
	<select id="getOrganizationList" resultType="com.bojun.organization.dto.OrganizationInfoDTO" parameterType="com.bojun.organization.dto.OrganizationInfoDTO">
     select organization_id,		
			organization_name,		
			social_credit_code,		
			organization_property,		
			organization_scale,		
			organization_class_code,		
			organization_type_code,		
			organization_director,		
			telephone_number,		
			province_code,		
			city_code,	
			county_code,		
			organization_address,	
			longitude,		
			latitude,		
			organization_introduction,		
			organization_history,	
			organization_honor,		
			front_business_license,		
			back_business_license,	
			create_user_id,				
			parent_id,	
			organization_code,
			is_enabled,
			t.town_name,
			t.village_name,
			t.town_code,
			t.village_code,
			t.weight,
		(SELECT count(1) from system.t_manage_user e where e.organization_id = t.organization_id and e.status=1
		<if test="userType != null and userType != ''">
			and e.user_type = #{userType}
		</if>
		) employeeCount,
     	(select organization_name from t_organization_info d  where d.organization_id = t.parent_id) parentName,
		(select organization_type_name from t_organization_type_dict d  where d.organization_type_code = t.organization_type_code) organizationTypeName,
		(select province_name from system.t_province_dict d  where d.province_code = t.province_code) province_name,
		(select city_name from system.t_city_dict d  where d.city_code = t.city_code) city_name,
		(select county_name from system.t_county_dict d  where d.county_code = t.county_code) county_name
		from t_organization_info t
		where 1=1       
	   <if test="organizationClassCode != null and organizationClassCode != '' ">
			and organization_class_code=#{organizationClassCode,jdbcType=VARCHAR}
	   </if>
		<if test="isEnabled != null">
			and is_enabled=#{isEnabled}
		</if>
	    <if test="weight != null  ">
	     <if test="type == 3  ">
			and weight>#{weight}
		</if>
		 <if test="type == 1  ">
		 and weight=#{weight}
		 </if>
		  <if test="type == 2  ">
		 and weight=#{weight}
		 </if>
	   </if>
		
    </select>
    
    <!-- 根据id查询子级机构数据 -->
<!-- <select id="getSubOrganizationList" resultType="com.bojun.organization.dto.OrganizationInfoDTO" parameterType="Integer">  -->	
	<select id="getSubOrganizationList" resultMap="orgInfoMap" parameterType="Integer">
      SELECT 
		organization_id,		
		organization_name,		
		social_credit_code,		
		organization_property,		
		organization_scale,		
		organization_class_code,		
		organization_type_code,		
		organization_director,		
		telephone_number,		
		province_code,		
		city_code,	
		county_code,		
		organization_address,	
		longitude,		
		latitude,		
		organization_introduction,		
		organization_history,	
		organization_honor,		
		front_business_license,		
		back_business_license,	
		create_user_id,				
		parent_id,	
		organization_code,
		is_enabled,
		t.town_name,
		t.village_name,
		t.town_code,
		t.village_code,
		t.weight
	  FROM t_organization_info t where parent_id = #{organizationId}      
    </select>
    
     <!--机构图片结果集 -->
	<resultMap id="orgInfoMap" type="com.bojun.organization.dto.OrganizationInfoDTO">
		<result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
		<result column="town_name" jdbcType="VARCHAR" property="townName" />
		<result column="village_name" jdbcType="VARCHAR" property="villageName" />
		<collection column="organizationId = organization_id" property="imgList"
			ofType="com.bojun.organization.dto.OrganizationImgDTO" select="getOrganizationImgById"></collection>
	</resultMap>
	
	<!--根据根据机构id查询机构图片信息 -->
	<select id="getOrganizationImgById" parameterType="Map"
		resultType="com.bojun.organization.dto.OrganizationImgDTO">
		select id,
		organization_id,
		is_cover,
		organization_image from t_organization_img t
		<where>
			<if test="organizationId != null and organizationId != ''">
				and organization_id = #{organizationId}
			</if>
		</where>
		order by show_index
	</select>
        
    <!-- 删除机构 -->
    <delete id="deleteOrganizationById" parameterType="Integer">
	  delete from t_organization_info where organization_id = #{organizationId}
	</delete>
    
     <!-- 查询机构分类数据 -->
	<select id="getOrganizationTypeList" resultType="com.bojun.organization.dto.OrganizationTypeDTO" parameterType="String">
      select organization_type_code typeCode, organization_type_name typeName, organization_class_code classCode 
		from t_organization_type_dict t where is_enabled = '1' and organization_class_code = #{classCode}    
    </select>

	<select id="getPushObjectByOrgId"
			resultType="com.bojun.organization.dto.OrganizationInfoDTO">
		SELECT oi.organization_id as deptId,oi.organization_name as deptName,
		'org' as `type`,organization_introduction, is_enabled,organization_id,oi.town_name,oi.village_name,oi.town_code,
		oi.village_code,
		(select organization_image from t_organization_img where organization_id = oi.organization_id
		ORDER BY upload_time DESC LIMIT 1
		)organizationImage
		 FROM
		organization.t_organization_info oi
		where oi.organization_id=#{organizationId}
	</select>
	
	<!-- 获取医疗机构信息（二级机构、app端登录选择机构、切换机构） -->
	<select id="getOrgInfoForApp" parameterType="com.bojun.organization.dto.OrganizationInfoDTO"
			resultType="com.bojun.organization.dto.OrganizationInfoDTO">
		SELECT organization_id,organization_name FROM organization.t_organization_info
		where organization_class_code = 1 and  is_enabled = 1 and weight = 2
	</select>
	
	<select id="getPushObjectByOrgAndRoleId"
			resultType="com.bojun.organization.dto.OrganizationInfoDTO">
		SELECT oi.organization_id as deptId,oi.organization_name as  deptName,
				'org' as `type`,ro.role_id, is_enabled,ro.role_id,oi.town_name,oi.village_name,oi.town_code,
				oi.village_code
				FROM
				organization.t_organization_info oi LEFT JOIN system.t_role_organization ro
				ON oi.organization_id=ro.organization_id WHERE ro.role_id=#{roleId} AND
				oi.organization_id=#{organizationId}
	</select>
	<select id="getPushObject"
            resultMap="oneLevelOrg">
		SELECT oi.organization_id as deptId,oi.organization_name as deptName,
		'org' as `type`,longitude,latitude,organization_id,
		is_enabled,oi.organization_id,oi.town_name,oi.village_name,oi.town_code,
		oi.village_code
		FROM
		organization.t_organization_info oi
		where 1=1 and is_enabled=1 and parent_id IS NULL
		<if test="keyWord !=null and keyWord !=''">
			and oi.organization_name LIKE CONCAT('%',#{keyWord},'%')
		</if>
		<if test="organizationTypeCode !=null and organizationTypeCode !=''">
			and organization_class_code=#{organizationTypeCode}
		</if>
		<if test="organizationId !=null">
			and oi.organization_id =#{organizationId}
		</if>
	</select>
	
	<!--一级机构数据 -->
	<resultMap id="oneLevelOrg" type="com.bojun.organization.dto.OrganizationInfoDTO">
		<result column="deptId" jdbcType="VARCHAR" property="deptId" />
	</resultMap>
	

	<select id="getPushObjectByRoleId"
            resultType="com.bojun.organization.dto.OrganizationInfoDTO">
        SELECT oi.organization_id ,oi.organization_name as organizationName,
        'org' as `type` ,
		<if test="roleId !=null and roleId !='' ">
        ro.role_id,
        </if>
        is_enabled,oi.town_name,oi.village_name,oi.town_code,
        oi.village_code,longitude,latitude, oi.parent_id
        FROM
        organization.t_organization_info oi
		<if test="roleId !=null and roleId !='' ">
        LEFT JOIN system.t_role_organization ro ON
        oi.organization_id=ro.organization_id
		</if>
        WHERE is_enabled=1 and parent_id IS null
        <if test="roleId !=null and roleId !='' ">
            and ro.role_id=#{roleId}
        </if>
    </select>

	<select id="getOrgListByRoleId" resultType="com.bojun.organization.dto.OrganizationInfoDTO">
		SELECT
		<if test="roleId !=null and roleId !='' ">
			ro.role_id,
			ro.has_auth
		</if>
		oi.*
		FROM
		organization.t_organization_info oi
		<if test="roleId !=null and roleId !='' ">
			LEFT JOIN system.t_role_organization ro ON
			oi.organization_id=ro.organization_id
		</if>
		WHERE is_enabled=1
		<if test="roleId !=null and roleId !='' ">
			and ro.role_id=#{roleId}
		</if>
		<if test="organizationId !=null and organizationId !=''">
			and parent_id =#{organizationId}
		</if>
		<if test="organizationIds != null  and organizationIds.size() > 0">
			and parent_id in
			<foreach item="item" index="index"
					 collection="organizationIds" open="(" separator=","
					 close=")">
				#{item}
			</foreach>
		</if>
	</select>

	<!-- 批量 插入图片信息-->
	<insert id="batchSaveOrganizationImg" parameterType="java.util.List">
		insert into t_organization_img 
		( organization_id,
		 organization_image,
		 show_index,
		 is_cover,
		 upload_time)
		values
		<foreach collection="list" item="item" index="index" separator=",">
			(#{item.organizationId,jdbcType=INTEGER},
			#{item.organizationImage,jdbcType=VARCHAR},
			#{index},
			#{item.isCover},
			sysdate())
		</foreach>
	</insert>

	<!-- 批量删除机构机构图片 -->
    <delete id="deleteOrganizationImg" parameterType="Integer">
	  delete from t_organization_img where organization_id = #{organizationId}
	</delete>
	
	<!-- 单个删除机构机构图片 -->
    <delete id="deleteOrganizationImgById" parameterType="com.bojun.organization.dto.OrganizationImgDTO">
	  delete from t_organization_img where organization_id=#{organizationId}  and id=#{id}
	</delete>  
	
	 <!-- 查询单个机构数据 -->
	<select id="getOrganizationInfoByCode" resultType="com.bojun.organization.dto.OrganizationInfoDTO"  parameterType="String">
	      SELECT 
			organization_id,		
			organization_name,		
			social_credit_code,		
			organization_property,		
			organization_scale,		
			organization_class_code,		
			organization_type_code,		
			organization_director,		
			telephone_number,		
			province_code,		
			city_code,	
			county_code,		
			organization_address,	
			longitude,		
			latitude,		
			organization_introduction,		
			organization_history,	
			organization_honor,		
			front_business_license,		
			back_business_license,	
			create_user_id,				
			parent_id,	
			organization_code,
			is_enabled,
			t.town_name,
			t.village_name,
			t.town_code,
			t.village_code
		  FROM t_organization_info t 
		  where organization_code=#{organizationCode,jdbcType=VARCHAR}
    </select>
	<select id="getChilrenOrg"
			resultMap="nextLevelOrg">
		SELECT oi.organization_id as deptId,oi.organization_name as deptName,
				'org' as `type`,longitude,latitude,organization_id, is_enabled,oi.town_name,oi.village_name
				FROM
				organization.t_organization_info oi
				where 1=1 and is_enabled=1  and
				parent_id =#{organizationId}
	</select>
	
	
	<!--下级机构数据 -->
	<resultMap id="nextLevelOrg" type="com.bojun.organization.dto.OrganizationInfoDTO">
		<result column="deptId" jdbcType="VARCHAR" property="deptId" />
	</resultMap>

	
	<select id="getChilrenOrgByRoleId"
			resultType="com.bojun.organization.dto.OrganizationInfoDTO">
		SELECT oi.organization_id,oi.organization_name,
		'org' as `type`,ro.role_id,
		is_enabled,oi.town_name,oi.village_name,oi.town_code,oi.village_code, oi.parent_id
		FROM
		organization.t_organization_info oi
		LEFT JOIN system.t_role_organization ro ON
		oi.organization_id=ro.organization_id
		where 1=1 and is_enabled=1 and parent_id is not null
		<if test="roleId !=null and roleId !=''">
			and ro.role_id=#{roleId}
		</if>
		<if test="organizationId !=null and organizationId !=''">
			and parent_id =#{organizationId}
		</if>
	</select>

	<select id="getChildrenOrganizationCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            t_organization_info
        WHERE
            parent_id = #{parentId}
    </select>

	<select id="getStatisticOrganizationList" resultType="com.bojun.organization.entity.OrganizationInfo">
		SELECT
		oi.organization_id,
		oi.organization_name,
		oi.organization_code
		FROM
		t_organization_info oi
		WHERE
			is_enabled = 1
			<if test="organizationId != null and organizationId != '' ">
				and (oi.organization_id = #{organizationId} OR oi.parent_id =
				#{organizationId})
			</if>
	</select>




<!--	根据ClassCode获取其机构列表（目前绩效统计使用）-->
	<select id="getOrganizationsByClassCode" resultType="com.bojun.organization.entity.OrganizationInfo">
		SELECT
			oi.organization_id,
			oi.organization_name,
			oi.organization_code
		FROM
			t_organization_info oi
		WHERE
			is_enabled = 1 AND
			organization_class_code = #{classCode}
	</select>

</mapper>   
