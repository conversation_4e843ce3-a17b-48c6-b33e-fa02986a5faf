package com.bojun.utils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * cron表达式工具
 *
 * <AUTHOR>
 */
public class CronUtil {
    public static final String defaultDateFormat = "ss mm HH dd MM ? yyyy";

    public static String getCron(Date date) {
        return getCron(date, defaultDateFormat);
    }

    public static String getCron(Date date, String dateFormat) {
        SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
        String formatTimeStr = null;
        if (date != null) {
            formatTimeStr = sdf.format(date);
        }
        return formatTimeStr;
    }

    /**
     * 多个日期合并cron表达式
     *
     * @param date
     * @param dateFormat
     * @param count      次数
     * @param interval   间隔
     * @return
     */
    public static String getCron(Date date, String dateFormat, int count, int interval) {
        if (count <= 1) {
            return getCron(date);
        }
        List<Calendar> calendas = new ArrayList<>(10);
        List<String> crons = new ArrayList<>(10);
        crons.add(DateUtil.getFormatDate(date, dateFormat));//第一个时间
        for (int i = 1; i <= count; i++) {
            Calendar calendar = Calendar.getInstance(getTimeZone());
            //Date nextDate = new Date(date.getTime() + (interval * 1000 * i));
            calendar.setTime(date);
            calendar.add(Calendar.SECOND, interval * i);
            calendas.add(calendar);
            crons.add(DateUtil.getFormatDate(calendar.getTime(), dateFormat));
        }
        return packagCron(crons);
    }

    private static String packagCron(List<String> crons) {
        List<List<String>> allTimes = new ArrayList<>(10);
        crons.forEach(cron -> {
            List<String> times = Arrays.asList(cron.split(" "));
            allTimes.add(times);
        });
        return packagCronToString(allTimes);
    }

    private static String packagCronToString(List<List<String>> allTimes) {
        List<String> seconds = new ArrayList<>(10);
        List<String> minute = new ArrayList<>(10);
        List<String> hour = new ArrayList<>(10);
        List<String> day = new ArrayList<>(10);
        List<String> month = new ArrayList<>(10);
        Set<String> year = new HashSet<>(10);
        allTimes.forEach(allTime -> {
            seconds.add(allTime.get(0));//秒
            minute.add(allTime.get(1));//分钟
            hour.add(allTime.get(2));//小时
            day.add(allTime.get(3));//日
            month.add(allTime.get(4));//月
            year.add(allTime.get(6));//年
        });
        //Collections.sort(seconds);
        //Collections.sort(minute);
        StringBuilder result = new StringBuilder();
        result.append(StringUtils.join(seconds.toArray(), ","))
                .append(" ")
                .append(StringUtils.join(minute.toArray(), ","))
                .append(" ")
                .append(StringUtils.join(hour.toArray(), ","))
                .append(" ")
                .append(StringUtils.join(day.toArray(), ","))
                .append(" ")
                .append(StringUtils.join(month.toArray(), ","))
                .append(" ? ")
                .append(StringUtils.join(year.toArray(), ","))
                .append(" ");
        return result.toString();
    }

    public static TimeZone getTimeZone() {
        return TimeZone.getDefault();
    }

    public static void main(String[] args) {
        getCron(DateUtil.toDate("2021-06-09 17:17:45"), defaultDateFormat, 1, 5);
    }

}
