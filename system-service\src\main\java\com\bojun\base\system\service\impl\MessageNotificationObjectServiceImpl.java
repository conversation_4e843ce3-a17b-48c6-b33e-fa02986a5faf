package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.MessageNotificationObjectMapper;
import com.bojun.base.system.service.MessageNotificationObjectService;
import com.bojun.system.dto.MessageNotificationObjectDTO;
import com.bojun.system.entity.MessageNotificationObject;
import com.bojun.utils.Convert;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * MessageNotificationObjectService业务层处理
 * 
 * <AUTHOR>
 * @date 2021-09-01 09:41:11
 */
@Service
public class MessageNotificationObjectServiceImpl extends ServiceImpl<MessageNotificationObjectMapper, MessageNotificationObject> implements MessageNotificationObjectService {

    /**
     * 查询消息通知关联推送对象表
     * 
     * @param id 消息通知关联推送对象表ID
     * @return 消息通知关联推送对象表
     */
    @Override
    public MessageNotificationObjectDTO selectMessageNotificationObjectById(Integer id)
    {
        return this.baseMapper.selectMessageNotificationObjectById(id);
    }

    /**
     * 查询消息通知关联推送对象表列表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 消息通知关联推送对象表集合
     */
    @Override
    public List<MessageNotificationObjectDTO> selectMessageNotificationObjectList(MessageNotificationObjectDTO messageNotificationObjectDTO)
    {
        return this.baseMapper.selectMessageNotificationObjectList(messageNotificationObjectDTO);
    }

    /**
     * 新增消息通知关联推送对象表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 结果
     */
    @Override
    public int insertMessageNotificationObject(MessageNotificationObjectDTO messageNotificationObjectDTO)
    {
        return this.baseMapper.insert(messageNotificationObjectDTO);
    }

    /**
     * 修改消息通知关联推送对象表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 结果
     */
    @Override
    public int updateMessageNotificationObject(MessageNotificationObjectDTO messageNotificationObjectDTO)
    {
        return this.baseMapper.updateById(messageNotificationObjectDTO);
    }
    
    /**
     * 新增消息通知关联推送对象表
     * 
     * @param messageNotificationObject 消息通知关联推送对象表
     * @return 结果
     */
    @Override
    public int insertMessageNotificationObject(MessageNotificationObject messageNotificationObject)
    {
        return this.baseMapper.insert(messageNotificationObject);
    }

    /**
     * 修改消息通知关联推送对象表
     * 
     * @param messageNotificationObject 消息通知关联推送对象表
     * @return 结果
     */
    @Override
    public int updateMessageNotificationObject(MessageNotificationObject messageNotificationObject)
    {
        return this.baseMapper.updateById(messageNotificationObject);
    }

    /**
     * 删除消息通知关联推送对象表对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationObjectByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除消息通知关联推送对象表信息
     * 
     * @param id 消息通知关联推送对象表ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationObjectById(Integer id)
    {
        return this.removeById(id) ? 1 : 0;
    }
}
