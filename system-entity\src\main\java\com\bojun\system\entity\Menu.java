package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model： 菜单表
 * Description：菜单表实体
 * Author：赖水秀
 * created： 2020年4月28日
 */
public class Menu implements Serializable {

	private static final long serialVersionUID = -1344026328907379635L;

	private String menuId; // 菜单（按钮）ID（uuid）
	private String menuName; // 菜单（按钮）名称
	private String parentId; // 上级菜单ID（一级菜单为空）
	private String systemId; // 关联的系统id
	private Integer menuType; // 菜单类型 1：菜单 2：按钮
	private Integer level; // 菜单级别 1：一级 2：二级 ............类推
	private String menuUrl; // 菜单URL
	private String authorityUrl; // 权限URL
	private Integer showIndex; // 显示顺序
	private Integer isDisplay; // 显示状态 0：隐藏 1：显示
	private String icon; // 菜单图标
	private String remark; // 备注
	private String iconColor; //图标颜色
	
	public String getMenuId() {
		return menuId;
	}
	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}
	public String getMenuName() {
		return menuName;
	}
	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}
	public String getParentId() {
		return parentId;
	}
	public void setParentId(String parentId) {
		this.parentId = parentId;
	}
	public String getSystemId() {
		return systemId;
	}
	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}
	public Integer getMenuType() {
		return menuType;
	}
	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public String getMenuUrl() {
		return menuUrl;
	}
	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}
	public String getAuthorityUrl() {
		return authorityUrl;
	}
	public void setAuthorityUrl(String authorityUrl) {
		this.authorityUrl = authorityUrl;
	}
	public Integer getShowIndex() {
		return showIndex;
	}
	public void setShowIndex(Integer showIndex) {
		this.showIndex = showIndex;
	}
	public Integer getIsDisplay() {
		return isDisplay;
	}
	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getIconColor() {
		return iconColor;
	}
	public void setIconColor(String iconColor) {
		this.iconColor = iconColor;
	}
	
}
