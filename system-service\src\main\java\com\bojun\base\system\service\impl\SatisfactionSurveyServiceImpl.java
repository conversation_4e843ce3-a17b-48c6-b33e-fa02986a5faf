package com.bojun.base.system.service.impl;

import com.bojun.base.system.mapper.SatisfactionMapper;
import com.bojun.base.system.service.ISatisfactionSurveyService;
import com.bojun.system.dto.FormQuestionAnswerDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireResultDTO;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/12 18:07
 */
@Service
public class SatisfactionSurveyServiceImpl implements ISatisfactionSurveyService {
    @Autowired
    SatisfactionMapper satisfactionMapper;

    @Override
    public Page<List<SatisfactionQuestionnaireDTO>> getSatisfactionSurvey(SatisfactionQuestionnaireDTO questionnaireDTO) {

        Page<List<SatisfactionQuestionnaireDTO>> m = satisfactionMapper.getSatisfactionSurvey(questionnaireDTO);
        return m;
    }

    @Override
    public SatisfactionQuestionnaireDTO getSatisfactionSurveyById(Integer questionnaireId) {
        return satisfactionMapper.getSatisfactionSurveyById(questionnaireId);
    }

    @Override
    public int addQuestionnaireResult(SatisfactionQuestionnaireResultDTO questionnaireResult) {
        return satisfactionMapper.addQuestionnaireResult(questionnaireResult);
    }

    @Override
    public int addFormQuestionAnswer(List<FormQuestionAnswerDTO> formQuestionAnswerDTOS) {
        return satisfactionMapper.addFormQuestionAnswer(formQuestionAnswerDTOS);
    }
}
