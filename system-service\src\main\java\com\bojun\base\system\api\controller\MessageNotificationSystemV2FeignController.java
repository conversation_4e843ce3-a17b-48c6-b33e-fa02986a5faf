package com.bojun.base.system.api.controller;

import com.bojun.base.system.api.MessageNotificationSystemV2FeignClient;
import com.bojun.base.system.service.MessageNotificationSystemV2Service;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.system.dto.MessageNotificationSystemV2DTO;
import com.bojun.system.entity.MessageNotificationSystemV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息通知关联产品表FeignController
 * 
 * <AUTHOR>
 * @date 2021-07-22 16:11:35
 */
@RestController
public class MessageNotificationSystemV2FeignController extends BaseFeignController implements MessageNotificationSystemV2FeignClient {
    @Autowired
    private MessageNotificationSystemV2Service messageNotificationSystemV2Service;

	/**
     * 查询消息通知关联产品表分页列表
     */
    @PostMapping(PREFIX + "/page")
    public Results<PageData<MessageNotificationSystemV2DTO>> page(@RequestBody MessageNotificationSystemV2DTO messageNotificationSystemV2DTO){
        startPage(messageNotificationSystemV2DTO.getPageNum(), messageNotificationSystemV2DTO.getEveryPage());
        List<MessageNotificationSystemV2DTO> list = messageNotificationSystemV2Service.selectMessageNotificationSystemV2List(messageNotificationSystemV2DTO);
        return Results.list(getPageData(list));
    }

    /**
     * 查询消息通知关联产品表列表
     */
    @PostMapping(PREFIX + "/list")
    public Results<List<MessageNotificationSystemV2DTO>> list(@RequestBody MessageNotificationSystemV2DTO messageNotificationSystemV2DTO){
        List<MessageNotificationSystemV2DTO> list = messageNotificationSystemV2Service.selectMessageNotificationSystemV2List(messageNotificationSystemV2DTO);
        return Results.list(list);
    }

    /**
     * 获取消息通知关联产品表详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<MessageNotificationSystemV2DTO> getInfo(@RequestParam("id") Integer id){
        MessageNotificationSystemV2DTO messageNotificationSystemV2DTO = messageNotificationSystemV2Service.selectMessageNotificationSystemV2ById(id);
        return Results.data(messageNotificationSystemV2DTO);
    }

    /**
     * 新增消息通知关联产品表DTO
     */
    @PostMapping(PREFIX + "/addDTO")
    public Results addDTO(@RequestBody MessageNotificationSystemV2DTO messageNotificationSystemV2DTO){
    	Integer num = messageNotificationSystemV2Service.insertMessageNotificationSystemV2(messageNotificationSystemV2DTO);
        return Results.opResult(num);
    }

    /**
     * 修改消息通知关联产品表DTO
     */
    @PostMapping(PREFIX + "/editDTO")
    public Results editDTO(@RequestBody MessageNotificationSystemV2DTO messageNotificationSystemV2DTO){
        Integer num = messageNotificationSystemV2Service.updateMessageNotificationSystemV2(messageNotificationSystemV2DTO);
        return Results.opResult(num);
    }
    
    /**
     * 新增消息通知关联产品表
     */
    @PostMapping(PREFIX + "/add")
    public Results add(@RequestBody MessageNotificationSystemV2 messageNotificationSystemV2){
        Integer num = messageNotificationSystemV2Service.insertMessageNotificationSystemV2(messageNotificationSystemV2);
        return Results.opResult(num);
    }

    /**
     * 修改消息通知关联产品表
     */
    @PostMapping(PREFIX + "/edit")
    public Results edit(@RequestBody MessageNotificationSystemV2 messageNotificationSystemV2){
        Integer num = messageNotificationSystemV2Service.updateMessageNotificationSystemV2(messageNotificationSystemV2);
        return Results.opResult(num);
    }

    /**
     * 删除消息通知关联产品表，多个以逗号分隔
     */
    @GetMapping(PREFIX + "/removeByIds")
    public Results removeByIds(@RequestParam("ids") String ids) {
        Integer num = messageNotificationSystemV2Service.deleteMessageNotificationSystemV2ByIds(ids);
        return Results.opResult(num);
    }
}
