<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.SphygmometerUserRelativeMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.SphygmometerUserRelativeDTO" id="SphygmometerUserRelativeDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="relativeUserId"    column="relative_user_id"    />
        <result property="relationship"    column="relationship"    />
        <result property="mobile"    column="mobile"    />
        <result property="createTime"    column="create_time"    />
		<result property="nickName" column="nick_name"/>
		<result property="headPortrait" column="head_portrait"/>
    </resultMap>

    <sql id="selectSphygmometerUserRelative">
    	select
			tsur.id,
			tsur.user_id,
			tsur.relative_user_id,
			tsur.relationship,
			tsur.mobile,
			tsur.create_time,
    	    (select tsu.head_portrait from t_sphygmometer_user tsu where tsu.user_id = tsur.relative_user_id) head_portrait,
			(select tsu.nick_name from t_sphygmometer_user tsu where tsu.user_id = tsur.relative_user_id) nick_name
		from
        	t_sphygmometer_user_relative tsur
    </sql>

    <select id="selectSphygmometerUserRelativeById" parameterType="int" resultMap="SphygmometerUserRelativeDTOResult">
		<include refid="selectSphygmometerUserRelative"/>
		where 
        	id = #{id}
    </select>

    <select id="selectSphygmometerUserRelativeList" parameterType="com.bojun.sphygmometer.dto.SphygmometerUserRelativeDTO" resultMap="SphygmometerUserRelativeDTOResult">
        <include refid="selectSphygmometerUserRelative"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="relativeUserId != null "> and relative_user_id = #{relativeUserId}</if>
		<if test="relationship != null  and relationship != ''"> and relationship = #{relationship}</if>
		<if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="wxOpenId != null  and wxOpenId != ''"> and wx_open_id = #{wxOpenId}</if>
        </where>
    </select>

</mapper>