package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：机构管理
*Description：机构图片信息
*Author: 赖水秀
*created：2020年5月8日
*/
@ApiModel(value = "新增机构图片信息", description = "新增机构图片信息入参")
public class AddOrganizationImgParamVO implements Serializable{
	
	private static final long serialVersionUID = 109958065299895997L;
		
	@NotEmpty(message = "图片名称")
	@ApiModelProperty(value="图片名称", required = true, example = "aaa.jpg")
	private String organizationImage;
	
	@NotNull(message = "是否为封面")
	@ApiModelProperty(value="是否为封面", required = true, example = "0")
	private Integer isCover;//是否封面
    
	public Integer getIsCover() {
		return isCover;
	}

	public void setIsCover(Integer isCover) {
		this.isCover = isCover;
	}
	
	public String getOrganizationImage() {
		return organizationImage;
	}

	public void setOrganizationImage(String organizationImage) {
		this.organizationImage = organizationImage;
	}

	
}

