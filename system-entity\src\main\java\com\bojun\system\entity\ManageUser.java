package com.bojun.system.entity;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 
 * Model：系统管理员用户表 Description：系统管理员用户表实体类 Author:刘俊 created：2020年4月28日
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class ManageUser implements Serializable {

	private static final long serialVersionUID = -1344026328907379635L;
	private Integer userId; //
	private String accountNo; // 登录账号
	private Integer organizationId; // 机构id
	private String organizationName; // 机构名称
	private String roleId;// 角色ID
	private String workNumber; // 工号
	private Integer deptId;// 部门id
	private String deptName;// 部门名称
	private String deptCode;// 部门code
	private String realName; // 姓名
	private String idNo; // 身份证号
	private Integer authType; // 权限类型（0：超级管理员，1：普通管理员）
	private Integer userType; // 用户类型 1：医疗机构人员 2：养老机构人员 3：监管人员 4：其他
	private String passwords; // 密码
	private String salt; // 密码盐
	private Date modifyPasswordTime; // 密码修改时间
	private Integer status; // 0:停用 1:启用
	private String token; // 登录身份令牌（token）
	private Date createTime; // 创建时间
	private Date lastestLoginTime;// 最近登陆时间
	private String remark; // 备注
	private Integer wardId; // 区域（病区）id
	private String mobile; // 手机号
	private String sourceSystemId; // 创建来源（初始创建的系统code）
	private Integer parentId; // 上级用户id
	private String townName;// 乡镇名
	private String villageName;// 村名
	private String townCode;// 乡镇code
	private String villageCode;// 村code
	private String  headPortrait;//头像
}
