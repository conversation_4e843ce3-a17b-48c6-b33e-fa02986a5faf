package com.bojun.commons.sms;

/**
*Model：阿里云短信模板枚举类
*Description：阿里云短信模板枚举类
*Author:段德鹏
*created：2020年11月9日
 */
public enum AliyunSMSTemplateEnum {
	
	REGISTER_TEMPLATE_CODE("SMS_136388335", "用户注册模板"),
	LOGIN_TEMPLATE_CODE("SMS_136393388", "用户登录模板"),
	BIND_MOBILE_TEMPLATE_CODE("SMS_136383443", "绑定手机号模板"),
	UNBIND_MOBILE_TEMPLATE_CODE("SMS_219745778", "解绑手机号模板"),
	FORGET_PASSWORD_TEMPLATE_CODE("SMS_205438516", "重置登录密码模板"),

	//验证码为：${code}，10分钟内有效，您正在进行登录操作，任何人索取验证码均为诈骗，切勿泄露！
	HYPERTENSION_LOGIN_REGISTER_TEMPLATE_CODE("SMS_221735277", "慢病登录/注册模板"),

	//验证码为：${code}，10分钟内有效，您正在进行绑定手机号操作，任何人索取验证码均为诈骗，切勿泄露！
	CHRONIC_DISEASE_BIND_MOBILE("SMS_221730702", "慢病绑定手机号模板"),

	//验证码为：${code}，10分钟内有效，您正在通过手机号重置登录密码，任何人索取验证码均为诈骗，切勿泄露！
	CHRONIC_DISEASE_FORGET_PASSWORD("SMS_222340913", "慢病重置登录密码模板"),

	//验证码为：${code}，10分钟内有效，您正在进行解绑当前手机号操作，任何人索取验证码均为诈骗，切勿泄露！_TEMPLATE_CODE后缀是统一的，干脆去掉- -
	CHRONIC_DISEASE_UNBIND_MOBILE("SMS_221730287", "慢病解绑手机号模板")
	;
	
	/**
	 * 短信模板代码
	 */
	private String templateCode;
	
	/**
	 * 短信模板名称
	 */
	private String 	templateName;
	
	private AliyunSMSTemplateEnum(String templateCode, String templateName) {
		this.templateCode = templateCode;
		this.templateName = templateName;
	}

	public static String getTemplateName(String templateCode) {
		String templateName = "";
		for (AliyunSMSTemplateEnum aliyunSMSTemplateEnum : AliyunSMSTemplateEnum.values()) {
			if (templateCode.equals(aliyunSMSTemplateEnum.getTemplateCode())) {
				templateName = aliyunSMSTemplateEnum.getTemplateName();
				break;
			}
		}
		return templateName;
	}

	public String getTemplateCode() {
		return templateCode;
	}

	public String getTemplateName() {
		return templateName;
	}

}
