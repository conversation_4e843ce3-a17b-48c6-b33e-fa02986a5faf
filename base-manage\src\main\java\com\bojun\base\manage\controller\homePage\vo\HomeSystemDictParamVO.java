/**
 * 
 */
package com.bojun.base.manage.controller.homePage.vo;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：主页管理
 * Description：
 * Author：lj
 * created： 2020年4月27日
 */
@ApiModel(value = "主页接口参数", description = "主页接口参数")
public class HomeSystemDictParamVO  implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;
	
	@NotNull(message = "角色不能为空")
	@ApiModelProperty(value="角色ID", required = true, example = "1")
	private String roleId;
	
	@NotNull(message = "权限类型不能为空 ")
	@ApiModelProperty(value = "（0：超级管理员，1：普通管理员）", required = true, example = "1")
	private Integer authType;
	
	@ApiModelProperty(value="产品名称", required = true, example = "1")
	private String searchKey;
	
	

	
	
	
	public String getSearchKey() {
		return searchKey;
	}

	public void setSearchKey(String searchKey) {
		this.searchKey = searchKey;
	}

	public Integer getAuthType() {
		return authType;
	}

	public void setAuthType(Integer authType) {
		this.authType = authType;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	
	

	
	
	
}
