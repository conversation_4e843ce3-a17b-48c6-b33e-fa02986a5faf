/**
 * 
 */
package com.bojun.base.manage.controller.organization;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.organization.IOrganizationService;
import com.bojun.base.manage.api.system.ICommonDictService;
import com.bojun.base.manage.controller.notice.vo.ObjectVO;
import com.bojun.base.manage.controller.organization.vo.*;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.response.Results;
import com.bojun.utils.StringUtil;
import com.bojun.vo.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;

/**
 * Model：机构管理模块
 * Description：机构管理模块
 * Author：赖水秀
 * created： 2020年5月4日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("OrgMange")
@Api(tags = {"机构管理模块接口"})
@ApiSort(value = 5)
public class ManageOrganizationController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(ManageOrganizationController.class);
	
	@Autowired
	private IOrganizationService organizationService;
	
	@Autowired
	private ICommonDictService commonDictService;
		 
	/**
	 * @Description 新增机构信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月28日
	 */
	@ApiOperation(value = "新增机构", notes = "新增机构信息（赖水秀）")
	@ApiOperationSupport(order = 1)
	@RequestMapping(value="/saveOrganizationInfo", method = RequestMethod.POST)
	@SystemLog(action = "saveOrganizationInfo", description = "新增机构", operationType = Contants.ADD_REQUEST)
	@AuthAnnotation(action = "saveOrganizationInfo")
	public Results saveOrganizationInfo(HttpServletRequest request, @RequestBody @Valid AddOrganizationPramVO addOrganizationPramVO) {
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(addOrganizationPramVO, organizationInfoDTO);
			
			organizationInfoDTO.setCreateTime(new Date());		
			String result = organizationService.saveOrganizationInfo(organizationInfoDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("saveOrganizationInfo:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 编辑机构信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月28日
	 */
	@ApiOperation(value = "编辑机构", notes = "编辑机构信息（赖水秀）")
	@ApiOperationSupport(order = 2)
	@RequestMapping(value="/updateOrganizationInfo", method = RequestMethod.POST)
	@SystemLog(action = "updateOrganizationInfo", description = "编辑机构", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateOrganizationInfo")
	public Results updateOrganizationInfo(@RequestBody @Valid UpdateOrganizationPramVO updateOrganizationPramVO) {
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(updateOrganizationPramVO, organizationInfoDTO);			
			String result = organizationService.updateOrganizationInfo(organizationInfoDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateOrganizationInfo:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 查询单个机构数据
	 * <AUTHOR>
	 * @return
	 * Results<MenuInfoVO>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询单个机构", notes = "查询单个机构数据（赖水秀）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value = "/getOrganizationInfoById", method = RequestMethod.POST)
	@AuthAnnotation(action = "getOrganizationInfoById")
	public Results<OrganizationInfoVO> getOrganizationInfoById(@RequestParam(value="organizationId") Integer organizationId) {		
		try {			
			String result = organizationService.getOrganizationInfoById(organizationId);
			return returnResults(result, OrganizationInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getOrganizationInfoById:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 分页查询机构信息
	 * <AUTHOR>
	 * @return
	 * Results<MenuInfoVO>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询机构列表", notes = "查询机构列表数据（赖水秀）")
	@ApiOperationSupport(order = 4)
	@RequestMapping(value="/getOrganizationPageList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getOrganizationPageList")
	public Results<Page<OrganizationInfoVO>> getOrganizationPageList(@RequestBody @Valid GetOrganizationParamVO getOrganizationParamVO) {		
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(getOrganizationParamVO, organizationInfoDTO);	
			String result = organizationService.getOrganizationPageList(organizationInfoDTO);
			return returnResultsPage(result, OrganizationInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getOrganizationPageList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	

	/**
	 * @Description 查询树形机构列表
	 * <AUTHOR>
	 * @return
	 * Results<List<MenuInfoVO>>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询树形机构列表", notes = "查询树形机构列表数据（赖水秀）")
	@ApiOperationSupport(order = 5)
	@RequestMapping(value="/getOrganizationTreeList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getOrganizationTreeList")
	public Results<List<OrganizationInfoVO>> getOrganizationTreeList(@RequestBody @Valid GetOrganizationTreeParamVO getOrganizationTreeParamVO) {		
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(getOrganizationTreeParamVO, organizationInfoDTO);	
			String result = organizationService.getOrganizationTreeList(organizationInfoDTO);
			return returnResultsList(result, OrganizationInfoVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getOrganizationTreeList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

	/**
	 * @Description 查询树形机构列表
	 * <AUTHOR>
	 * @return
	 * Results<List<MenuInfoVO>>
	 * 2020年4月28日
	 */
	@ApiOperation(value = "查询所有机构列表", notes = "查询所有机构列表（黄卫平）")
	@RequestMapping(value="/getOrganizationList", method = RequestMethod.GET)
	public Results<List<OrganizationSimpleVO>> getOrganizationList() {
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			String result = organizationService.getOrganizationTreeList(organizationInfoDTO);
			return returnResultsList(result, OrganizationSimpleVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getOrganizationList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 删除机构信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月29日
	 */
	@ApiOperation(value = "删除机构信息", notes = "删除机构信息（赖水秀）")
	@ApiOperationSupport(order = 7)
	@RequestMapping(value="/deleteOrganizationById", method = RequestMethod.POST)
	@SystemLog(action = "deleteOrganizationById", description = "删除机构", operationType = Contants.DELETE_REQUEST)
	@AuthAnnotation(action = "deleteOrganizationById")
	public Results deleteOrganizationById(@RequestParam(value="organizationId") Integer organizationId) {
		try {			
			String result = organizationService.deleteOrganizationById(organizationId);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("deleteOrganizationById:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 批量删除菜单信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年4月29日
	 */
	@ApiOperation(value = "批量删除机构", notes = "批量删除机构信息（赖水秀）")
	@ApiOperationSupport(order = 8)
	@RequestMapping(value="/batchDeleteOrganization", method = RequestMethod.POST)
	@SystemLog(action = "batchDeleteOrganization", description = "批量删除机构", operationType = Contants.DELETE_REQUEST)
	@AuthAnnotation(action = "batchDeleteOrganization")
	public Results batchDeleteOrganization(@RequestBody @Valid List<String> organizationIds) {
		try {			
			String result = organizationService.batchDeleteOrganization(organizationIds);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("batchDeleteOrganization:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 查询机构分类列表
	 * <AUTHOR>
	 * @return
	 * Results<List<OrganizationTypeVO>>
	 * 2020年5月6日
	 */
	@ApiOperation(value = "查询机构类型列表", notes = "查询机构类型列表数据（赖水秀）")
	@ApiOperationSupport(order = 9)
	@RequestMapping(value="/getOrganizationTypeList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getOrganizationTypeList")
	public Results<List<OrganizationTypeVO>> getOrganizationTypeList(@RequestParam(value="classCode") String classCode) {		
		try {
			String result = organizationService.getOrganizationTypeList(classCode);
			return returnResultsList(result, OrganizationTypeVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getOrganizationTypeList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	


	@ApiOperation(value = "获取指定机构下的所有科室病区", notes = "获取推送对象（赖允翔）")
	@ApiOperationSupport(order = 9)
	@PostMapping(value = "/getPushObjectByOrgId")
	@SystemLog(action = "getPushObjectByOrgId", description = "获取推送对象", operationType = 6)
	@AuthAnnotation(action = "getPushObjectByOrgId")
	public Results getPushObjectByOrgId(@RequestBody ObjectVO objectVO) {
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(objectVO, organizationInfoDTO);
			String result = organizationService.getPushObjectByOrgId(organizationInfoDTO);
			JSONObject resultOjb = JSONObject.parseObject(result);
			resultOjb = changeJsonKey(resultOjb);
			Map<String, String> keyMap = new HashMap<>();
			keyMap.put("children", "subDeptList");
			result = changeJsonObj(resultOjb, keyMap).toString();
			return returnResults(result, com.bojun.base.manage.controller.notice.vo.OrganizationInfoVO.class);
		} catch (BeansException e) {
			logger.error("/getPushObjectByOrgId", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

	/**
	 * jsonkey进行对换
	 *
	 * <AUTHOR>
	public static JSONObject changeJsonKey(JSONObject jsonObj) {
		Set<String> keySet = jsonObj.keySet();
		for (String key : keySet) {
			try {
				JSONObject jsonobj1 = jsonObj.getJSONObject(key);
				changeJsonKey(jsonobj1);
			} catch (Exception e) {
				if (key.equals("children")) {
					JSONArray jsonArr = jsonObj.getJSONArray(key);
					if (jsonArr.isEmpty()) {
						jsonObj.remove("children");
					}
				} else if (key.equals("subDeptList")) {
					JSONArray jsonArr = jsonObj.getJSONArray(key);
					if (jsonArr.isEmpty()) {
						jsonObj.remove("subDeptList");
					}
				}

			}
		}
		return jsonObj;
	}

	@ApiOperation(value = "获取指定机构与角色ID下的所有科室病区", notes = "获取指定机构与角色ID下的所有科室病区（赖允翔）")
	@ApiOperationSupport(order = 9)
	@PostMapping(value = "/getPushObjectByOrgAndRoleId")
	@SystemLog(action = "getPushObjectByOrgAndRoleId", description = "获取指定机构与角色ID下的所有科室病区", operationType = 6)
	@AuthAnnotation(action = "getPushObjectByOrgAndRoleId")
	public Results<com.bojun.base.manage.controller.notice.vo.OrganizationInfoVO> getPushObjectByOrgAndRoleId(@RequestBody ObjectVO objectVO) {
		try {
			String result = null;
			if (!StringUtil.isEmpty(objectVO.getRoleId()) && objectVO.getOrganizationId() > 0) {
				OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
				BeanUtils.copyProperties(objectVO, organizationInfoDTO);
				result = organizationService.getPushObjectByOrgAndRoleId(organizationInfoDTO);

			}
			return returnResults(result, com.bojun.base.manage.controller.notice.vo.OrganizationInfoVO.class);
		} catch (BeansException e) {
			logger.error("/getPushObjectByOrgId", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

	/**
	 * 对json数据key进行替换
	 *
	 * <AUTHOR>
	 */
	public static JSONObject changeJsonObj(JSONObject jsonObj, Map<String, String> keyMap) {
		JSONObject resJson = new JSONObject();
		Set<String> keySet = jsonObj.keySet();
		for (String key : keySet) {
			String resKey = keyMap.get(key) == null ? key : keyMap.get(key);
			try {
				JSONObject jsonobj1 = jsonObj.getJSONObject(key);
				resJson.put(resKey, changeJsonObj(jsonobj1, keyMap));
			} catch (Exception e) {
				try {
					JSONArray jsonArr = jsonObj.getJSONArray(key);
					resJson.put(resKey, changeJsonArr(jsonArr, keyMap));
				} catch (Exception x) {
					resJson.put(resKey, jsonObj.get(key));
				}
			}
		}
		return resJson;
	}

	public static JSONArray changeJsonArr(JSONArray jsonArr, Map<String, String> keyMap) {
		JSONArray resJson = new JSONArray();
		for (int i = 0; i < jsonArr.size(); i++) {
			JSONObject jsonObj = jsonArr.getJSONObject(i);
			resJson.add(changeJsonObj(jsonObj, keyMap));
		}
		return resJson;
	}

	@ApiOperation(value = "获取所有机构下的所有科室病区", notes = "获取推送对象（赖允翔）")
	@ApiOperationSupport(order = 9)
	@PostMapping(value = "/getPushObject")
	@SystemLog(action = "getPushObject", description = "获取推送对象", operationType = 6)
	@AuthAnnotation(action = "getPushObject")
	public Results getPushObject(@RequestBody ObjectVO objectVO) {
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(objectVO, organizationInfoDTO);
			String result = organizationService.getPushObject(organizationInfoDTO);
			JSONObject resultOjb = JSONObject.parseObject(result);
			resultOjb = changeJsonKey(resultOjb);
			Map<String, String> keyMap = new HashMap<>();
			keyMap.put("children", "subDeptList");
			result = changeJsonObj(resultOjb, keyMap).toString();
			return returnResultsList(result, com.bojun.base.manage.controller.notice.vo.OrganizationInfoVO.class);
		} catch (BeansException e) {
			logger.error("/getPushObject", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

	@ApiOperation(value = "根据角色ID获取所有机构下的所有科室病区", notes = "获取推送对象（赖允翔）")
	@ApiOperationSupport(order = 9)
	@PostMapping(value = "/getPushObjectByRoleId")
	@AuthAnnotation(action = "getPushObjectByRoleId")
	public Results<List<com.bojun.base.manage.controller.notice.vo.OrganizationInfoVO>> getPushObjectByRoleId(@RequestBody ObjectVO objectVO) {
		try {
			String result = null;
			if (!StringUtil.isEmpty(objectVO.getRoleId())) {
				OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
				BeanUtils.copyProperties(objectVO, organizationInfoDTO);
				result = organizationService.getPushObjectByRoleId(organizationInfoDTO);
			}
			return returnResultsList(result, com.bojun.base.manage.controller.notice.vo.OrganizationInfoVO.class);
		} catch (BeansException e) {
			logger.error("/getPushObject", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

	/**
	 * @return Results<List < ProvinceDictVO>>
	 * 2020年5月7日
	 * @Description 查询省份列表
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查询省份列表", notes = "查询查询省份列表数据（赖水秀）")
	@ApiOperationSupport(order = 10)
	@RequestMapping(value="/getProvinceList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getProvinceList")
	public Results<List<ProvinceDictVO>> getProvinceList() {		
		try {
			String result = commonDictService.getProvinceList();
			return returnResultsList(result, ProvinceDictVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getProvinceList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	/**
	 * @Description 查询市区列表
	 * <AUTHOR>
	 * @return
	 * Results<List<CityDictVO>>
	 * 2020年5月7日
	 */
	@ApiOperation(value = "查询市区列表", notes = "查询市区列表数据（赖水秀）")
	@ApiOperationSupport(order = 11)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "provinceCode", value = "省份id", dataType = "String", paramType = "query") })
	@RequestMapping(value="/getCityList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getCityList")
	public Results<List<CityDictVO>> getCityList(@RequestParam(value="provinceCode") String provinceCode) {		
		try {				
			String result = commonDictService.getCityList(provinceCode);
			return returnResultsList(result, CityDictVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getCityList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	

	/**
	 * @Description 查询县区列表
	 * <AUTHOR>
	 * @return
	 * Results<List<CountyDictVO>>
	 * 2020年5月7日
	 */
	@ApiOperation(value = "查询县区列表", notes = "查询县区列表数据（赖水秀）")
	@ApiOperationSupport(order = 12)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "cityCode", value = "市id", dataType = "String", paramType = "query") })
	@RequestMapping(value="/getCountyList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getCountyList")
	public Results<List<CountyDictVO>> getCountyList(@RequestParam(value="cityCode") String cityCode) {		
		try {				
			String result = commonDictService.getCountyList(cityCode);
			return returnResultsList(result, CountyDictVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getCountyList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 禁用、启用机构信息
	 * <AUTHOR>
	 * @return
	 * Results
	 * 2020年5月7日
	 */
	@ApiOperation(value = "禁用、启用机构", notes = "禁用、启用机构信息（赖水秀）")
	@ApiOperationSupport(order = 13)
	@RequestMapping(value="/updateOrganizationStatus", method = RequestMethod.POST)
	@SystemLog(action = "updateOrganizationStatus", description = "禁用、启用机构", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateOrganizationStatus")
	public Results updateOrganizationStatus(@RequestBody @Valid UpdateOrganizationStatusPramVO updateOrganizationStatusPramVO) {
		try {
			OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
			BeanUtils.copyProperties(updateOrganizationStatusPramVO, organizationInfoDTO);			
			String result = organizationService.updateOrganizationInfo(organizationInfoDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateOrganizationStatus:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	/**
	 * @Description 删除机构图片
	 * <AUTHOR>
	 * @param organizationImgParamVO
	 * @return
	 * Results
	 * 2020年5月8日
	 */
	@ApiOperation(value = "单个删除机构图片", notes = "删除机构图片（赖水秀）")
	@ApiOperationSupport(order = 14)
	@RequestMapping(value="/deleteOrganizationImgById", method = RequestMethod.POST)
	@SystemLog(action = "deleteOrganizationImgById", description = "单个删除机构图片", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "deleteOrganizationImgById")
	public Results deleteOrganizationImgById(@RequestBody @Valid DeleteOrganizationImgParamVO deleteOrganizationImgParamVO) {
		try {
			OrganizationImgDTO organizationImgDTO = new OrganizationImgDTO();
			BeanUtils.copyProperties(deleteOrganizationImgParamVO, organizationImgDTO);			
			String result = organizationService.deleteOrganizationImgById(organizationImgDTO);
			return returnResults(result);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("deleteOrganizationImgById:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	/**
	 * @Description 查询乡镇列表
	 * <AUTHOR>
	 * @param countyCode
	 * @return
	 * Results<List<TownDictVO>>
	 * 2020年7月2日
	 */
	@ApiOperation(value = "查询乡镇列表", notes = "查询乡镇列表数据（赖水秀）")
	@ApiOperationSupport(order = 15)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "countyCode", value = "县id", dataType = "String", paramType = "query") })
	@RequestMapping(value = "/getTownList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getTownList")
	public Results<List<TownDictVO>> getTownList(@RequestParam(value = "countyCode") String countyCode) {
		try {
			String result = commonDictService.getTownList(countyCode);
			return returnResultsList(result, TownDictVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getTownList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}


	/**
	 * @Description 查询村居委会信息列表
	 * <AUTHOR>
	 * @param townCode
	 * @return
	 * Results<List<VillageDictVO>>
	 * 2020年7月2日
	 */
	@ApiOperation(value = "查询村居委会信息列表", notes = "查询村居委会信息列表（赖水秀）")
	@ApiOperationSupport(order = 16)
	@ApiImplicitParams({
		@ApiImplicitParam(name = "townCode", value = "乡镇id", dataType = "String", paramType = "query") })
	@RequestMapping(value = "/getVillageList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getVillageList")
	public Results<List<VillageDictVO>> getVillageList(@RequestParam(value = "townCode") String townCode) {
		try {
			String result = commonDictService.getVillageList(townCode);
			return returnResultsList(result, VillageDictVO.class);
		} catch (RuntimeException e) {
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getVillageList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}

}
