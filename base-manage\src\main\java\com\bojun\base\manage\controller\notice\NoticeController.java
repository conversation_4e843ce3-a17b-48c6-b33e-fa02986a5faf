package com.bojun.base.manage.controller.notice;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.NoticeService;
import com.bojun.base.manage.controller.notice.vo.*;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.dto.MessageNotificationDTO;
import com.bojun.vo.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

/**
 * Model： 通知公告
 * Description：
 * Author: 赖允翔
 * created：2020/4/24 17:15
 */
@RestController
@RequestMapping("notice")
@Api(tags = {"通知公告接口"})
@ApiSort(value = 1)
public class NoticeController extends BaseController {
    private static Logger logger = LoggerFactory.getLogger(NoticeController.class);
    
    @Autowired
    NoticeService noticeService;


    @ApiOperation(value = "通知公告查询（全查）", notes = "通知公告查询（赖允翔）")
    @ApiOperationSupport(order = 1)
    @PostMapping(value = "/getNotices")
    @AuthAnnotation(action = "/getNotices")
    public Results<Page<NoticeVO>> getNotices(@RequestBody GetNoticeVO noticeVO) {
        try {
            MessageNotificationDTO messageNotificationDTO = new MessageNotificationDTO();
            BeanUtils.copyProperties(noticeVO, messageNotificationDTO);
            String result = noticeService.getNotices(messageNotificationDTO);
            return returnResultsPage(result, NoticeVO.class);
        } catch (BeansException e) {
            logger.error("/getNotices", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    @ApiOperation(value = "通知公告查询（单查）", notes = "通知公告查询（赖允翔）")
    @ApiOperationSupport(order = 2)
    @PostMapping(value = "/getNoticeById")
    @AuthAnnotation(action = "/getNoticeById")
    public Results<NoticeVO> getNoticeById(@RequestBody NoticeByIdVO noticeVO) {
        try {
            String result = noticeService.getNoticeById(noticeVO.getNoticeId());
            return returnResults(result, NoticeVO.class);
        } catch (BeansException e) {
            logger.error("getNoticeById", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    @ApiOperation(value = "添加公告（再次编辑同一个接口）", notes = "添加通知公告（赖允翔）")
    @ApiOperationSupport(order = 3)
    @PostMapping(value = "/addNotice")
    @SystemLog(action = "addNotice", description = "添加公告", operationType = Contants.ADD_REQUEST)
    @AuthAnnotation(action = "/addNotice")
    public Results<String> addNotice(@RequestBody AddNoticeVO addNoticeVO, HttpServletRequest request) {
        try {
            MessageNotificationDTO messageNotificationDTO = new MessageNotificationDTO();
            BeanUtils.copyProperties(addNoticeVO,messageNotificationDTO);
            ManageUserDTO manageUser = (ManageUserDTO) request.getAttribute("manageUser");
            messageNotificationDTO.setNoticeId(UUID.randomUUID().toString());
            messageNotificationDTO.setIsDelete(0);
            messageNotificationDTO.setCreateUserId(manageUser.getUserId());
            String result  = noticeService.addNotice(messageNotificationDTO);
            return returnResults(result);
        } catch (BeansException e) {
            logger.error("getNoticeById", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    @ApiOperation(value = "删除公告（删除与批量删除同一个接口）", notes = "删除公告（赖允翔）")
    @ApiOperationSupport(order = 4)
    @PostMapping(value = "/deleteNotices")
    @SystemLog(action = "deleteNotices", description = "删除公告查询", operationType = Contants.DELETE_REQUEST)
    @AuthAnnotation(action = "/deleteNotices")
    public Results<String> deleteNotice(@RequestBody DelNoticeVO noticeVO) {
        try {
            MessageNotificationDTO messageNotificationDTO = new MessageNotificationDTO();
            BeanUtils.copyProperties(noticeVO, messageNotificationDTO);
            String result = noticeService.deleteNotice(messageNotificationDTO);
            return returnResults(result);
        } catch (BeansException e) {
            logger.error("/deleteNotices", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }
    @ApiOperation(value = "修改公告(立即推送同一个接口)", notes = "修改公告（赖允翔）")
    @ApiOperationSupport(order = 5)
    @PostMapping(value = "/updateNotice")
    @SystemLog(action = "updateNotice", description = "修改公告", operationType = Contants.UPDATE_REQUEST)
    @AuthAnnotation(action = "/updateNotice")
    public Results<String> updateNotice(@RequestBody UpdateNoticeVO updateNoticeVO) {
        try {
            MessageNotificationDTO messageNotificationDTO = new MessageNotificationDTO();
            BeanUtils.copyProperties(updateNoticeVO, messageNotificationDTO);
            if (updateNoticeVO.getIsImmediately() == 1) {
                messageNotificationDTO.setStatus(1);
            }
            String result = noticeService.updateNotice(messageNotificationDTO);
            return returnResults(result);
        } catch (BeansException e) {
            logger.error("/updateNotice", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

    @ApiOperation(value = "获取通知类型", notes = "获取通知类型（赖允翔）")
    @ApiOperationSupport(order = 6)
    @PostMapping(value = "/getNoticeType")
    @AuthAnnotation(action = "/getNoticeType")
    public Results<List<NoticeTypeVO>> getNoticeType() {
        try {
            String result = noticeService.getNoticeType();
            return returnResultsList(result, NoticeTypeVO.class);
        } catch (BeansException e) {
            logger.error("getNoticeType", e);
            return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
        }
    }

}
