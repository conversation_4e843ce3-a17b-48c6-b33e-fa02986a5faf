package com.bojun.sphygmometer.wxapp.controller.device;

import com.alibaba.fastjson.JSONObject;
import com.bojun.author.AuthAnnotation;
import com.bojun.common.controller.BaseController;
import com.bojun.common.util.LoginUserThreadLocal;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.page.Results;
import com.bojun.sphygmometer.api.ScreenDeviceLogFeignClient;
import com.bojun.sphygmometer.api.SphygmometerDeviceFeignClient;
import com.bojun.sphygmometer.api.SphygmometerUserFeignClient;
import com.bojun.sphygmometer.dto.MeasuringUserDTO;
import com.bojun.sphygmometer.dto.PreQueueDTO;
import com.bojun.sphygmometer.dto.SphygmometerDeviceDTO;
import com.bojun.sphygmometer.dto.SphygmometerUserDTO;
import com.bojun.sphygmometer.entity.ScreenDeviceLog;
import com.bojun.sphygmometer.entity.SphygmometerDevice;
import com.bojun.sphygmometer.queue.common.MeasureQueue;
import com.bojun.sphygmometer.queue.utils.MeasureQueueUtils;
import com.bojun.sphygmometer.wxapp.common.QueueUtils;
import com.bojun.sphygmometer.wxapp.common.WxAppNoticeSendUtils;
import com.bojun.sphygmometer.websocket.netty.handle.WebsocketChannelHandler;
import com.bojun.sphygmometer.wxapp.common.Constants;
import com.bojun.sphygmometer.wxapp.controller.device.vo.DeviceStatusVo;
import com.bojun.sphygmometer.wxapp.controller.device.vo.SphygmometerDeviceDetailVo;
import com.bojun.sphygmometer.wxapp.controller.user.SphygmometerRecordController;
import com.bojun.sphygmometer.wxapp.controller.user.SphygmometerUserController;
import com.bojun.sphygmometer.wxapp.controller.user.vo.MiniAppLoginResultVO;
import com.bojun.sphygmometer.wxapp.handler.SphygmometerDeviceSocketHandler;
import com.bojun.utils.BeanUtil;
import com.bojun.utils.SpringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 血压记录信息表Controller
 *
 * <AUTHOR>
 * @date 2021-03-20 10:22:09
 */
@Slf4j
@Api(tags = {"高血压设备(lihong)"})
@RestController
@RequestMapping(SphygmometerDeviceController.BASE_URL)
public class SphygmometerDeviceController extends BaseController
{
    public static final String BASE_URL = "/device";

    @Autowired
    private SphygmometerDeviceFeignClient sphygmometerDeviceFeignClient;

    @Autowired
    private SphygmometerDeviceSocketHandler sphygmometerDeviceSocketHandler;

    @Autowired
    private ScreenDeviceLogFeignClient screenDeviceLogFeignClient;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private MeasureQueue measureQueue;

    @Value("${wx.open.ma.wx_app_get_member}")
    private String WX_APP_GET_MEMBER;

    /**
     * 获取血压记录信息表详细信息
     */
    @ApiOperation("获取设备信息")
    @GetMapping(value = "/getInfo")
    @AuthAnnotation(action = SphygmometerRecordController.BASE_URL + "/getInfo")
    public Results<SphygmometerDeviceDetailVo> getInfo(@RequestParam(value = "deviceNo") String deviceNo){
        SphygmometerDevice sphygmometerDevice = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
        SphygmometerDeviceDetailVo sphygmometerDeviceDetailVo = BeanUtil.deepCopyProperties(sphygmometerDevice, SphygmometerDeviceDetailVo.class);
        if(ObjectUtils.isEmpty(sphygmometerDeviceDetailVo)){
            return Results.fail("设备不存在");
        }
        MeasureQueue measureQueue = SpringUtils.getBean(MeasureQueue.class);
        LinkedHashMap<String, Long> queue = measureQueue.getQueue(sphygmometerDevice.getDeviceNo());
        if(!CollectionUtils.isEmpty(queue)){
            sphygmometerDeviceDetailVo.setCurrentQueueSize(queue.size());
        }else{
            sphygmometerDeviceDetailVo.setCurrentQueueSize(0);
        }
        return Results.data(sphygmometerDeviceDetailVo);
    }

    /**
     * 开始测量
     */
    @ApiOperation("开始排队")
    @GetMapping(value = "/startQueue")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/startQueue")
    public Results<String> startQueue(@RequestParam("deviceNo") String deviceNo) throws InterruptedException {
        synchronized (this){
            MiniAppLoginResultVO loginUser = LoginUserThreadLocal.getUserInfo();
            SphygmometerDevice sphygmometerDevice = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
            if (sphygmometerDevice == null) {
                return Results.fail("设备不存在");
            }
            if(null == sphygmometerDevice.getIsEnabled() || sphygmometerDevice.getIsEnabled().intValue() == 0){ //判断设备是否禁用
                return Results.fail("设备已禁用，请更换设备再试");
            }
            log.info("用户" + loginUser.getWxappOpenId() + "已加入预备队列");
            startQueue(deviceNo,loginUser.getWxappOpenId(),loginUser.getUserId());
            Thread.sleep(200);
            //发送订阅消息
            MeasureQueue measureQueue = SpringUtils.getBean(MeasureQueue.class);
            LinkedHashMap<String, Long> queue =  measureQueue.getQueue(deviceNo);
            if(!CollectionUtils.isEmpty(queue)){
                WxAppNoticeSendUtils.sendGetNumberSuccess(loginUser.getWxappOpenId(),WX_APP_GET_MEMBER,queue.size()+"");
            }
            return Results.success();
        }
    }

    //开始排队
    private void startQueue(String deviceNo,String openId,Integer userId){
        //加入预备队列
        MeasureQueue.preQueue.offer(new PreQueueDTO(deviceNo, openId));
        addScreenDeviceLog(deviceNo,userId,1,8);
        //保存设备状态
        String statusKey = Constants.MEASURE_STATUS+":"+deviceNo+":"+openId;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("status",1);
        jsonObject.put("createTime",new Date());
        redisUtil.set(statusKey,jsonObject.toJSONString(),300);
    }




    /**
     * 取消排队
     */
    @ApiOperation("取消排队")
    @GetMapping(value = "/cancelQueue")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/cancelQueue")
    public Results<String> cancelQueue(@RequestParam("deviceNo") String deviceNo){
        synchronized (this){
            SphygmometerDevice sphygmometerDevice = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
            if (sphygmometerDevice == null) {
                return Results.fail("设备不存在");
            }
            if(null == sphygmometerDevice.getIsEnabled() || sphygmometerDevice.getIsEnabled().intValue() == 0){ //判断设备是否禁用
                return Results.fail("设备已禁用，请更换设备再试");
            }
            MiniAppLoginResultVO loginUser = LoginUserThreadLocal.getUserInfo();
            log.info("用户" + loginUser.getWxappOpenId() + "取消排队");
            MeasureQueue measureQueue = SpringUtils.getBean(MeasureQueue.class);
            measureQueue.remove(deviceNo,loginUser.getWxappOpenId());
            //删除正在测量的设备状态
            if(redisUtil.hasKey(MeasureQueueUtils.MEASURE_STATUS+":"+deviceNo+":"+loginUser.getWxappOpenId())){
                redisUtil.del(MeasureQueueUtils.MEASURE_STATUS+":"+deviceNo+":"+loginUser.getWxappOpenId());
            }
            //重新通知用户排队情况
            LinkedHashMap<String, Long> queue =  measureQueue.getQueue(deviceNo);
            if(!CollectionUtils.isEmpty(queue)){
                Iterator<String> iterator = queue.keySet().iterator();
                MeasuringUserDTO measuringUserDTO = measureQueue.getMeasuringUser(deviceNo);
                int index = 1;
                while (iterator.hasNext()) {
                    String nextUserOpenId = iterator.next();
                    //测量中用户不需通知排队情况
                    if(!nextUserOpenId.equals(measuringUserDTO.getOpenId())){
                        SphygmometerUserFeignClient sphygmometerRecordFeignClient = SpringUtils.getBean(SphygmometerUserFeignClient.class);
                        SphygmometerUserDTO sphygmometerUserDTO = sphygmometerRecordFeignClient.getInfoByWxAppOpenId(nextUserOpenId);
                        String wsToken = sphygmometerUserDTO.getWsToken();
                        JSONObject param = new JSONObject();
                        param.put("type","2");
                        param.put("currentQueueSize",index);
                        WebsocketChannelHandler.sendMessage(wsToken, param.toJSONString());
                        log.info("通知小程序排队情况: wsToken:"+wsToken+",param:"+param);
                        index++;
                    }
                    long expiredTime = System.currentTimeMillis()+MeasureQueue.QUEUE_TIMEOUT*index;
                    queue.put(nextUserOpenId,expiredTime);
                }
               this.measureQueue.updateQueue(deviceNo,queue);
            }
            return Results.success();
        }
    }


    /**
     * 重新排队
     */
    @ApiOperation("重新排队")
    @GetMapping(value = "/queueUpAgain")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/queueUpAgain")
    public Results<String> queueUpAgain(@RequestParam("deviceNo") String deviceNo){
        synchronized (this){
            MiniAppLoginResultVO loginUser = LoginUserThreadLocal.getUserInfo();
            SphygmometerDevice sphygmometerDevice = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
            if (sphygmometerDevice == null) {
                return Results.fail("设备不存在");
            }
            if(null == sphygmometerDevice.getIsEnabled() || sphygmometerDevice.getIsEnabled().intValue() == 0){ //判断设备是否禁用
                return Results.fail("设备已禁用，请更换设备再试");
            }
            //重新排队
            MeasureQueue.preQueue.offer(new PreQueueDTO(deviceNo, loginUser.getWxappOpenId()));
            //保存设备状态
            String statusKey = Constants.MEASURE_STATUS+":"+deviceNo+":"+loginUser.getWxappOpenId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("status",1);
            jsonObject.put("createTime",new Date());
            redisUtil.set(statusKey,jsonObject.toJSONString(),300);
            //发送订阅消息
            MeasureQueue measureQueue = SpringUtils.getBean(MeasureQueue.class);
            LinkedHashMap<String, Long> queue =  measureQueue.getQueue(deviceNo);
            if(!CollectionUtils.isEmpty(queue)){
                WxAppNoticeSendUtils.sendGetNumberSuccess(loginUser.getWxappOpenId(),WX_APP_GET_MEMBER,queue.size()+"");
            }
            return Results.success();
        }
    }

    @ApiOperation(value = "解锁", notes = "解锁")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceNo", value = "设备编号", dataType = "String", paramType = "query"),
            @ApiImplicitParam(name = "userId", value = "", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "解锁类型（1：直接解锁，2：排队后解锁）", dataType = "Integer", paramType = "query")
    })
    @GetMapping("/unLock")
    public Results unLock(@RequestParam("deviceNo") String deviceNo,@RequestParam("type") Integer type) throws InterruptedException {
        synchronized(this) {
            MiniAppLoginResultVO loginUser = LoginUserThreadLocal.getUserInfo();
            //判断用户同时点击解锁时
            if(redisUtil.hasKey("unLock")){
                return Results.fail("前方已有人解锁，请重新开始测量");
            }
            redisUtil.set("unLock","unLock",3);
            if (!measureQueue.containsMeasureUser(deviceNo, loginUser.getWxappOpenId())) {
                MeasureQueue.preQueue.offer(new PreQueueDTO(deviceNo, loginUser.getWxappOpenId()));
            } else if ( !measureQueue.isFirst(deviceNo, loginUser.getWxappOpenId()) ){
                return Results.fail("请稍后点击开始检测");
            }
            Thread.sleep(1000);
            //保存设备状态
            String statusKey = Constants.MEASURE_STATUS+":"+deviceNo+":"+loginUser.getWxappOpenId();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("status",3);
            jsonObject.put("createTime",new Date());
            redisUtil.set(statusKey,jsonObject.toJSONString(),300);
            //删除用户排队后的待解锁任务
            if(redisUtil.hasKey(MeasureQueueUtils.TO_START_UNLOCKED+":"+deviceNo)){
                redisUtil.del(MeasureQueueUtils.TO_START_UNLOCKED+":"+deviceNo);
            }
            //保存待开锁任务
            redisUtil.set(MeasureQueueUtils.TO_BE_UNLOCKED+":"+deviceNo,null,MeasureQueueUtils.expiredTime);
            sphygmometerDeviceSocketHandler.sendUnlockCmdToDevice(deviceNo, loginUser.getWxappOpenId());
            return Results.success();
        }
    }



    /**
     * 获取设备状态
     */
    @ApiOperation("获取设备状态")
    @GetMapping(value = "/getStatus")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getStatus")
    public Results<DeviceStatusVo> getStatus(@RequestParam("deviceNo") String deviceNo){
        String wxAppOpenId = LoginUserThreadLocal.getUserInfo().getWxappOpenId();
        String key = Constants.MEASURE_STATUS+":"+deviceNo+":"+wxAppOpenId;
        if(!redisUtil.hasKey(key)){
          return Results.success();
        }
        String data = (String) redisUtil.get(key);
        JSONObject jsonObject = JSONObject.parseObject(data);
//        DeviceStatusVo deviceStatusVo = BeanUtil.deepCopyProperties(jsonObject.toJSONString(),DeviceStatusVo.class);
        Integer status = jsonObject.getInteger("status");
        DeviceStatusVo deviceStatusVo = new DeviceStatusVo();
        deviceStatusVo.setStatus(jsonObject.getInteger("status"));
        deviceStatusVo.setCreateTime(jsonObject.getDate("createTime"));
        //判断用户未排队中时，返回当前排队状态
        if(status.equals(2) || status.equals(3)|| status.equals(4)){
            //计算剩余时间
            long currentTime = System.currentTimeMillis();
            long createTime  = deviceStatusVo.getCreateTime().getTime();
            Integer countDown = 0;
            if(currentTime>createTime){
                Integer time = Math.toIntExact((currentTime - createTime) / 1000);
                countDown = 30-time;
                if(countDown<=0){
                    countDown = 0;
                }
            }
            deviceStatusVo.setCountDown(countDown);
        }
        if(status.equals(1)){
            //找出当前排队状态
            MeasureQueue measureQueue = SpringUtils.getBean(MeasureQueue.class);
            LinkedHashMap<String, Long>  queue = measureQueue.getQueue(deviceNo);
            List <String> lst = new ArrayList<>(queue.keySet());
            Integer index= lst.indexOf(LoginUserThreadLocal.getUserInfo().getWxappOpenId())<1?0:lst.indexOf(LoginUserThreadLocal.getUserInfo().getWxappOpenId());
            deviceStatusVo.setCurrentQueueSize(index);
        }
        return Results.data(deviceStatusVo);
    }


    /**
     * 获取设备状态
     */
    @ApiOperation("根据微信二维码查询设备")
    @GetMapping(value = "/getByWxQrCodeUrl")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getByWxQrCodeUrl")
    public Results<SphygmometerDeviceDetailVo> getByWxQrCodeUrl(@RequestParam("wxQrCodeUrl") String wxQrCodeUrl){
        SphygmometerDeviceDTO sphygmometerDeviceDTO = sphygmometerDeviceFeignClient.getByWxQrCodeUrl(wxQrCodeUrl);
        SphygmometerDeviceDetailVo sphygmometerDeviceDetailVo = BeanUtil.deepCopyProperties(sphygmometerDeviceDTO, SphygmometerDeviceDetailVo.class);
        return Results.data(sphygmometerDeviceDetailVo);
    }


    /**
     * 保存筛查端筒式血压计日志
     * <AUTHOR>
     * @date 2021/7/19
     * @param deviceNo 设备编号
     * @param statusType 状态类型
     * @param status 状态值
     * @return void
     */
    private void addScreenDeviceLog(String deviceNo, Integer userId, Integer statusType, Integer status) {
        ScreenDeviceLog screenDeviceLog = new ScreenDeviceLog();
        screenDeviceLog.setDeviceNo(deviceNo);
        screenDeviceLog.setUserId(userId);
        SphygmometerDeviceDTO sphygmometerDevice = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
        screenDeviceLog.setOrganizationId(sphygmometerDevice.getOrganizationId());
        screenDeviceLog.setStatusType(statusType);
        screenDeviceLog.setStatus(status);
        screenDeviceLog.setCreateTime(new Date());
        this.screenDeviceLogFeignClient.add(screenDeviceLog);
    }

}
