<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@**************">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>exact</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root||ALTER|G
|root||root|127.0.0.1|ALTER|G
|root||root|*************|ALTER|G
|root||root|::1|ALTER|G
|root||root|izwz96awf0ghl7azwop5j7z|ALTER|G
|root||root|localhost|ALTER|G
|root||root||ALTER ROUTINE|G
|root||root|127.0.0.1|ALTER ROUTINE|G
|root||root|*************|ALTER ROUTINE|G
|root||root|::1|ALTER ROUTINE|G
|root||root|izwz96awf0ghl7azwop5j7z|ALTER ROUTINE|G
|root||root|localhost|ALTER ROUTINE|G
|root||root||CREATE|G
|root||root|127.0.0.1|CREATE|G
|root||root|*************|CREATE|G
|root||root|::1|CREATE|G
|root||root|izwz96awf0ghl7azwop5j7z|CREATE|G
|root||root|localhost|CREATE|G
|root||root||CREATE ROUTINE|G
|root||root|127.0.0.1|CREATE ROUTINE|G
|root||root|*************|CREATE ROUTINE|G
|root||root|::1|CREATE ROUTINE|G
|root||root|izwz96awf0ghl7azwop5j7z|CREATE ROUTINE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root||CREATE TABLESPACE|G
|root||root|127.0.0.1|CREATE TABLESPACE|G
|root||root|*************|CREATE TABLESPACE|G
|root||root|::1|CREATE TABLESPACE|G
|root||root|izwz96awf0ghl7azwop5j7z|CREATE TABLESPACE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root||CREATE TEMPORARY TABLES|G
|root||root|127.0.0.1|CREATE TEMPORARY TABLES|G
|root||root|*************|CREATE TEMPORARY TABLES|G
|root||root|::1|CREATE TEMPORARY TABLES|G
|root||root|izwz96awf0ghl7azwop5j7z|CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root||CREATE USER|G
|root||root|127.0.0.1|CREATE USER|G
|root||root|*************|CREATE USER|G
|root||root|::1|CREATE USER|G
|root||root|izwz96awf0ghl7azwop5j7z|CREATE USER|G
|root||root|localhost|CREATE USER|G
|root||root||CREATE VIEW|G
|root||root|127.0.0.1|CREATE VIEW|G
|root||root|*************|CREATE VIEW|G
|root||root|::1|CREATE VIEW|G
|root||root|izwz96awf0ghl7azwop5j7z|CREATE VIEW|G
|root||root|localhost|CREATE VIEW|G
|root||root||DELETE|G
|root||root|127.0.0.1|DELETE|G
|root||root|*************|DELETE|G
|root||root|::1|DELETE|G
|root||root|izwz96awf0ghl7azwop5j7z|DELETE|G
|root||root|localhost|DELETE|G
|root||root||DROP|G
|root||root|127.0.0.1|DROP|G
|root||root|*************|DROP|G
|root||root|::1|DROP|G
|root||root|izwz96awf0ghl7azwop5j7z|DROP|G
|root||root|localhost|DROP|G
|root||root||EVENT|G
|root||root|127.0.0.1|EVENT|G
|root||root|*************|EVENT|G
|root||root|::1|EVENT|G
|root||root|izwz96awf0ghl7azwop5j7z|EVENT|G
|root||root|localhost|EVENT|G
|root||root||EXECUTE|G
|root||root|127.0.0.1|EXECUTE|G
|root||root|*************|EXECUTE|G
|root||root|::1|EXECUTE|G
|root||root|izwz96awf0ghl7azwop5j7z|EXECUTE|G
|root||root|localhost|EXECUTE|G
|root||root||FILE|G
|root||root|127.0.0.1|FILE|G
|root||root|*************|FILE|G
|root||root|::1|FILE|G
|root||root|izwz96awf0ghl7azwop5j7z|FILE|G
|root||root|localhost|FILE|G
|root||root||INDEX|G
|root||root|127.0.0.1|INDEX|G
|root||root|*************|INDEX|G
|root||root|::1|INDEX|G
|root||root|izwz96awf0ghl7azwop5j7z|INDEX|G
|root||root|localhost|INDEX|G
|root||root||INSERT|G
|root||root|127.0.0.1|INSERT|G
|root||root|*************|INSERT|G
|root||root|::1|INSERT|G
|root||root|izwz96awf0ghl7azwop5j7z|INSERT|G
|root||root|localhost|INSERT|G
|root||root||LOCK TABLES|G
|root||root|127.0.0.1|LOCK TABLES|G
|root||root|*************|LOCK TABLES|G
|root||root|::1|LOCK TABLES|G
|root||root|izwz96awf0ghl7azwop5j7z|LOCK TABLES|G
|root||root|localhost|LOCK TABLES|G
|root||root||PROCESS|G
|root||root|127.0.0.1|PROCESS|G
|root||root|*************|PROCESS|G
|root||root|::1|PROCESS|G
|root||root|izwz96awf0ghl7azwop5j7z|PROCESS|G
|root||root|localhost|PROCESS|G
|root||root||REFERENCES|G
|root||root|127.0.0.1|REFERENCES|G
|root||root|*************|REFERENCES|G
|root||root|::1|REFERENCES|G
|root||root|izwz96awf0ghl7azwop5j7z|REFERENCES|G
|root||root|localhost|REFERENCES|G
|root||root||RELOAD|G
|root||root|127.0.0.1|RELOAD|G
|root||root|*************|RELOAD|G
|root||root|::1|RELOAD|G
|root||root|izwz96awf0ghl7azwop5j7z|RELOAD|G
|root||root|localhost|RELOAD|G
|root||root||REPLICATION CLIENT|G
|root||root|127.0.0.1|REPLICATION CLIENT|G
|root||root|*************|REPLICATION CLIENT|G
|root||root|::1|REPLICATION CLIENT|G
|root||root|izwz96awf0ghl7azwop5j7z|REPLICATION CLIENT|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root||REPLICATION SLAVE|G
|root||root|127.0.0.1|REPLICATION SLAVE|G
|root||root|*************|REPLICATION SLAVE|G
|root||root|::1|REPLICATION SLAVE|G
|root||root|izwz96awf0ghl7azwop5j7z|REPLICATION SLAVE|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root||SELECT|G
|root||root|127.0.0.1|SELECT|G
|root||root|*************|SELECT|G
|root||root|::1|SELECT|G
|root||root|izwz96awf0ghl7azwop5j7z|SELECT|G
|root||root|localhost|SELECT|G
|root||root||SHOW DATABASES|G
|root||root|127.0.0.1|SHOW DATABASES|G
|root||root|*************|SHOW DATABASES|G
|root||root|::1|SHOW DATABASES|G
|root||root|izwz96awf0ghl7azwop5j7z|SHOW DATABASES|G
|root||root|localhost|SHOW DATABASES|G
|root||root||SHOW VIEW|G
|root||root|127.0.0.1|SHOW VIEW|G
|root||root|*************|SHOW VIEW|G
|root||root|::1|SHOW VIEW|G
|root||root|izwz96awf0ghl7azwop5j7z|SHOW VIEW|G
|root||root|localhost|SHOW VIEW|G
|root||root||SHUTDOWN|G
|root||root|127.0.0.1|SHUTDOWN|G
|root||root|*************|SHUTDOWN|G
|root||root|::1|SHUTDOWN|G
|root||root|izwz96awf0ghl7azwop5j7z|SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||root||SUPER|G
|root||root|127.0.0.1|SUPER|G
|root||root|*************|SUPER|G
|root||root|::1|SUPER|G
|root||root|izwz96awf0ghl7azwop5j7z|SUPER|G
|root||root|localhost|SUPER|G
|root||root||TRIGGER|G
|root||root|127.0.0.1|TRIGGER|G
|root||root|*************|TRIGGER|G
|root||root|::1|TRIGGER|G
|root||root|izwz96awf0ghl7azwop5j7z|TRIGGER|G
|root||root|localhost|TRIGGER|G
|root||root||UPDATE|G
|root||root|127.0.0.1|UPDATE|G
|root||root|*************|UPDATE|G
|root||root|::1|UPDATE|G
|root||root|izwz96awf0ghl7azwop5j7z|UPDATE|G
|root||root|localhost|UPDATE|G
|root||root|127.0.0.1|grant option|G
|root||root|::1|grant option|G
|root||root|izwz96awf0ghl7azwop5j7z|grant option|G
|root||root|localhost|grant option|G
test|schema||&apos;&apos;||ALTER|G
test|schema||&apos;&apos;||CREATE|G
test|schema||&apos;&apos;||CREATE ROUTINE|G
test|schema||&apos;&apos;||CREATE TEMPORARY TABLES|G
test|schema||&apos;&apos;||CREATE VIEW|G
test|schema||&apos;&apos;||DELETE|G
test|schema||&apos;&apos;||DROP|G
test|schema||&apos;&apos;||EVENT|G
test|schema||&apos;&apos;||INDEX|G
test|schema||&apos;&apos;||INSERT|G
test|schema||&apos;&apos;||LOCK TABLES|G
test|schema||&apos;&apos;||REFERENCES|G
test|schema||&apos;&apos;||SELECT|G
test|schema||&apos;&apos;||SHOW VIEW|G
test|schema||&apos;&apos;||TRIGGER|G
test|schema||&apos;&apos;||UPDATE|G
test\\_%|schema||&apos;&apos;||ALTER|G
test\\_%|schema||&apos;&apos;||CREATE|G
test\\_%|schema||&apos;&apos;||CREATE ROUTINE|G
test\\_%|schema||&apos;&apos;||CREATE TEMPORARY TABLES|G
test\\_%|schema||&apos;&apos;||CREATE VIEW|G
test\\_%|schema||&apos;&apos;||DELETE|G
test\\_%|schema||&apos;&apos;||DROP|G
test\\_%|schema||&apos;&apos;||EVENT|G
test\\_%|schema||&apos;&apos;||INDEX|G
test\\_%|schema||&apos;&apos;||INSERT|G
test\\_%|schema||&apos;&apos;||LOCK TABLES|G
test\\_%|schema||&apos;&apos;||REFERENCES|G
test\\_%|schema||&apos;&apos;||SELECT|G
test\\_%|schema||&apos;&apos;||SHOW VIEW|G
test\\_%|schema||&apos;&apos;||TRIGGER|G
test\\_%|schema||&apos;&apos;||UPDATE|G</Grants>
      <ServerVersion>5.6.21</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="39" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="41" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="43" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="45" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="47" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="49" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="51" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="53" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="57" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="58" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="64" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="65" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="66" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="68" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="70" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="72" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="73" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="76" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="78" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="80" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="82" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="84" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="86" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="87" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="92" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="113" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="114" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="115" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="116" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="121" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="142" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="143" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="144" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="149" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf8_bin">
      <Charset>utf8</Charset>
    </collation>
    <collation id="169" parent="1" name="utf8_croatian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="170" parent="1" name="utf8_czech_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8_danish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8_esperanto_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8_estonian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8_general_ci">
      <Charset>utf8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="175" parent="1" name="utf8_general_mysql500_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8_german2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8_hungarian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8_icelandic_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8_latvian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8_lithuanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8_persian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8_polish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8_roman_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8_romanian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8_sinhala_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8_slovak_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8_slovenian_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8_spanish2_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8_spanish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8_swedish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8_turkish_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8_unicode_520_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8_unicode_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8_vietnamese_ci">
      <Charset>utf8</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="221" parent="1" name="RECOVER_YOUR_DATA">
      <CollationName>latin1_swedish_ci</CollationName>
    </schema>
    <schema id="222" parent="1" name="common_resident">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-08-05.04:05:17</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="223" parent="1" name="health_promotion">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-08-05.04:05:17</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <schema id="224" parent="1" name="information_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="225" parent="1" name="mysql">
      <CollationName>latin1_swedish_ci</CollationName>
    </schema>
    <schema id="226" parent="1" name="performance_schema">
      <CollationName>utf8_general_ci</CollationName>
    </schema>
    <schema id="227" parent="1" name="sphygmometer">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-08-05.04:24:28</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="228" parent="1" name="root"/>
    <user id="229" parent="1" name="root">
      <Host>127.0.0.1</Host>
    </user>
    <user id="230" parent="1" name="root">
      <Host>*************</Host>
    </user>
    <user id="231" parent="1" name="root">
      <Host>::1</Host>
    </user>
    <user id="232" parent="1" name="root">
      <Host>izwz96awf0ghl7azwop5j7z</Host>
    </user>
    <user id="233" parent="1" name="root">
      <Host>localhost</Host>
    </user>
    <table id="234" parent="222" name="t_resident_basic_info">
      <Comment>居民基本信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="235" parent="222" name="t_resident_health_info">
      <Comment>居民健康信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="236" parent="223" name="t_news_info">
      <Comment>资讯信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="237" parent="223" name="t_news_topic">
      <Comment>新闻话题关系表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="238" parent="223" name="t_topic_info">
      <Comment>话题信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="239" parent="227" name="t_app_banner">
      <Comment>banner信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="240" parent="227" name="t_app_message_notification">
      <Comment>消息通知</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="241" parent="227" name="t_app_message_read">
      <Comment>APP用户消息已读记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
DYNAMIC</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="242" parent="227" name="t_app_task_dict">
      <Comment>高血压APP任务字典信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="243" parent="227" name="t_device_bind_record">
      <Comment>设备绑定记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="244" parent="227" name="t_hypertension_health_info">
      <Comment>高血压健康信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="245" parent="227" name="t_news_person_tag">
      <Comment>资讯人员标签表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="246" parent="227" name="t_news_push_record">
      <Comment>文章推送记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="247" parent="227" name="t_organization_manage_hypertension">
      <Comment>辖区高血压人数表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="248" parent="227" name="t_person_tag">
      <Comment>人员标签表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="249" parent="227" name="t_product_msg">
      <Comment>产品消息管理表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="250" parent="227" name="t_resident_basic_info">
      <Comment>居民基本信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="251" parent="227" name="t_resident_health_info">
      <Comment>居民健康信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="252" parent="227" name="t_screen_device_log">
      <Comment>臂筒式筛查设备测量日志表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <Options>row_format
COMPACT</Options>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="253" parent="227" name="t_sphygmometer_device">
      <Comment>血压计设备信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="254" parent="227" name="t_sphygmometer_device_transfer">
      <Comment>血压计设备迁移记录信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="255" parent="227" name="t_sphygmometer_record">
      <Comment>血压记录信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="256" parent="227" name="t_sphygmometer_user">
      <Comment>高血压用户信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="257" parent="227" name="t_sphygmometer_user_health_info">
      <Comment>高血压用户健康信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="258" parent="227" name="t_sphygmometer_user_relative">
      <Comment>高血压用户亲属关系表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="259" parent="227" name="t_test_plan_tpl">
      <Comment>用户检测计划模板表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="260" parent="227" name="t_test_plan_tpl_time">
      <Comment>检测计划模板时段表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="261" parent="227" name="t_tips_template">
      <Comment>温馨提示模板</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>latin1_swedish_ci</CollationName>
    </table>
    <table id="262" parent="227" name="t_user_lineup_record">
      <Comment>用户排队记录</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="263" parent="227" name="t_user_medication_remind">
      <Comment>用户用药提醒表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="264" parent="227" name="t_user_medication_remind_record">
      <Comment>用户实际用药时间表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="265" parent="227" name="t_user_medication_remind_time">
      <Comment>用户用药提醒时段表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="266" parent="227" name="t_user_news_favorite">
      <Comment>用户资讯收藏表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="267" parent="227" name="t_user_org_log">
      <Comment>用户机构变更日志</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="268" parent="227" name="t_user_task_record">
      <Comment>APP用户任务完成记录信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8_general_ci</CollationName>
    </table>
    <table id="269" parent="227" name="t_user_test_plan">
      <Comment>用户检测计划</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="270" parent="227" name="t_user_test_plan_record">
      <Comment>用户实际检测时间表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="271" parent="227" name="t_user_test_plan_time">
      <Comment>用户检测计划时段表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <column id="272" parent="234" name="id">
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="273" parent="234" name="real_name">
      <Comment>真实姓名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="274" parent="234" name="id_no">
      <Comment>身份证号码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="275" parent="234" name="gender">
      <Comment>性别 0：女 1：男  2:保密</Comment>
      <DefaultExpression>2</DefaultExpression>
      <Position>4</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="276" parent="234" name="birthday">
      <Comment>出生日期</Comment>
      <DefaultExpression>&apos;0000-00-00&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="277" parent="234" name="is_married">
      <Comment>是否已婚  0：否  1：是  2：未知</Comment>
      <DefaultExpression>2</DefaultExpression>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="278" parent="234" name="nation_code">
      <Comment>民族code</Comment>
      <Position>7</Position>
      <StoredType>varchar(60)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="279" parent="234" name="province_code">
      <Comment>省份code(现住址)</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="280" parent="234" name="city_code">
      <Comment>城市code(现住址)</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="281" parent="234" name="county_code">
      <Comment>县code(现住址)</Comment>
      <Position>10</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="282" parent="234" name="town_code">
      <Comment>乡镇code(现住址）</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="283" parent="234" name="village_code">
      <Comment>村区code(现住址)</Comment>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="284" parent="234" name="live_address">
      <Comment>详细地址（现住址）</Comment>
      <Position>13</Position>
      <StoredType>varchar(200)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="285" parent="234" name="profession_type">
      <Comment>职业类型</Comment>
      <Position>14</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="286" parent="234" name="create_time">
      <Comment>登记时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="287" parent="234" name="health_code">
      <Comment>健康码</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="288" parent="234" name="has_document">
      <Comment>是否建档, 0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="289" parent="234" name="create_user_id">
      <Comment>登记人用户id</Comment>
      <Position>18</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="290" parent="234" name="create_user_name">
      <Comment>登记人用户姓名</Comment>
      <Position>19</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="291" parent="234" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="292" parent="234" name="update_user_id">
      <Comment>更新用户id</Comment>
      <Position>21</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="293" parent="234" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>22</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <index id="294" parent="234" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="295" parent="234" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="296" parent="235" name="id">
      <AutoIncrement>100</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="297" parent="235" name="resident_id">
      <Comment>基本信息ID，对应t_resident_basic_info表</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="298" parent="235" name="height">
      <Comment>身高（cm）</Comment>
      <Position>3</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="299" parent="235" name="weight">
      <Comment>体重（kg）</Comment>
      <Position>4</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="300" parent="235" name="bmi">
      <Comment>体脂指数</Comment>
      <Position>5</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="301" parent="235" name="waistline">
      <Comment>腰围</Comment>
      <Position>6</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="302" parent="235" name="blood_type">
      <Comment>血型  1：A ，2：B , 3: AB  4: O</Comment>
      <Position>7</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="303" parent="235" name="past_history">
      <Comment>既往史</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="304" parent="235" name="hpi">
      <Comment>现病史</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="305" parent="235" name="allergic_history">
      <Comment>过敏史</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="306" parent="235" name="family_history">
      <Comment>家族病史</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="307" parent="235" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="308" parent="235" name="create_user_id">
      <Comment>创建用户id</Comment>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="309" parent="235" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>14</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="310" parent="235" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="311" parent="235" name="update_user_id">
      <Comment>更新用户id</Comment>
      <Position>16</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="312" parent="235" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>17</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <index id="313" parent="235" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="314" parent="235" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="315" parent="236" name="news_id">
      <AutoIncrement>247</AutoIncrement>
      <Comment>资讯id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="316" parent="236" name="organization_id">
      <Comment>机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="317" parent="236" name="title">
      <Comment>文章标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="318" parent="236" name="dept_id">
      <Comment>所属科室</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="319" parent="236" name="cover_image">
      <Comment>文章封面</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="320" parent="236" name="file_name">
      <Comment>文章附件</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="321" parent="236" name="show_index">
      <Comment>排序下标</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="322" parent="236" name="is_home">
      <Comment>是否在健康首页  0： 否  1：是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="323" parent="236" name="home_index">
      <Comment>首页排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="324" parent="236" name="content">
      <Comment>文章内容</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="325" parent="236" name="read_number">
      <Comment>阅读数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="326" parent="236" name="publish_type">
      <Comment>发布类型  1:新发   2:更新</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="327" parent="236" name="status">
      <Comment>状态  1:已发布  2:待发布  3：已下架 </Comment>
      <DefaultExpression>2</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="328" parent="236" name="auth_status">
      <Comment>审核状态  1：审核通过  2：待审核  3：审核未通过</Comment>
      <Position>14</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="329" parent="236" name="auth_user_id">
      <Comment>审核人用户id</Comment>
      <Position>15</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="330" parent="236" name="auth_time">
      <Comment>审核时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="331" parent="236" name="publish_time">
      <Comment>发布时间</Comment>
      <Position>17</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="332" parent="236" name="update_time">
      <Comment>更新时间</Comment>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="333" parent="236" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="334" parent="236" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="335" parent="236" name="is_delete">
      <Comment>是否删除 0：否 1：是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>21</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="336" parent="236" name="favorite_num">
      <Comment>收藏数量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>22</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="337" parent="236" name="accurate_push">
      <Comment>精准推送  0 ：否   1：是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>23</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="338" parent="236" name="person_tag">
      <Comment>推送人员标签</Comment>
      <Position>24</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="339" parent="236" name="publish_user_name">
      <Comment>发布人名称</Comment>
      <Position>25</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="340" parent="236" name="publish_user_id">
      <Comment>发布人id</Comment>
      <Position>26</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="341" parent="236" name="push_type">
      <Comment>推送类型 ：0：立即推送 1：定时推送</Comment>
      <Position>27</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="342" parent="236" name="push_time">
      <Comment>推送时间</Comment>
      <Position>28</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="343" parent="236" name="system_id">
      <Comment>系统id</Comment>
      <Position>29</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="344" parent="236" name="PRIMARY">
      <ColNames>news_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="345" parent="236" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="346" parent="237" name="id">
      <AutoIncrement>357</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="347" parent="237" name="news_id">
      <Comment>资讯id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="348" parent="237" name="topic_id">
      <Comment>话题id</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="349" parent="237" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="350" parent="237" name="create_user_id">
      <Comment>创建用户ID</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="351" parent="237" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
      <CollationName>utf8mb4_general_ci</CollationName>
    </column>
    <column id="352" parent="237" name="is_top">
      <Comment>是否置顶</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="353" parent="237" name="news_show_index">
      <Comment>文章在栏目中排序下标</Comment>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="354" parent="237" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="355" parent="237" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="356" parent="238" name="topic_id">
      <AutoIncrement>134</AutoIncrement>
      <Comment>话题id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="357" parent="238" name="parent_topic_id">
      <Comment>父级话题id</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="358" parent="238" name="topic_name">
      <Comment>话题名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="359" parent="238" name="show_index">
      <Comment>排序下标</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="360" parent="238" name="is_enabled">
      <Comment>是否启动  0：否 1：是</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="361" parent="238" name="is_delete">
      <Comment>是否删除 0：否 1：是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="362" parent="238" name="update_user_id">
      <Comment>编辑人用户id</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="363" parent="238" name="update_time">
      <Comment>编辑时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="364" parent="238" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="365" parent="238" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="366" parent="238" name="top">
      <Comment>置顶</Comment>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="367" parent="238" name="system_id">
      <Comment>系统id</Comment>
      <Position>12</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="368" parent="238" name="PRIMARY">
      <ColNames>topic_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="369" parent="238" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="370" parent="239" name="banner_id">
      <AutoIncrement>36</AutoIncrement>
      <Comment>自增主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="371" parent="239" name="title">
      <Comment>标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="372" parent="239" name="banner_img">
      <Comment>banner图片</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="373" parent="239" name="show_index">
      <Comment>排序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="374" parent="239" name="redirect_url">
      <Comment>跳转链接地址</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="375" parent="239" name="is_enabled">
      <Comment>是否启用  0：否  1：是</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="376" parent="239" name="remark">
      <Comment>描述</Comment>
      <Position>7</Position>
      <StoredType>longtext|0s</StoredType>
    </column>
    <column id="377" parent="239" name="update_user_id">
      <Comment>更新人用户id</Comment>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="378" parent="239" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="379" parent="239" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="380" parent="239" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="381" parent="239" name="PRIMARY">
      <ColNames>banner_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="382" parent="239" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="383" parent="240" name="notice_id">
      <Comment>消息通知id(uuid)</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="384" parent="240" name="organization_id">
      <Comment>机构ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="385" parent="240" name="title">
      <Comment>消息标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="386" parent="240" name="notice_content">
      <Comment>消息内容</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="387" parent="240" name="status">
      <Comment>状态：1：已发布 2：待发布  </Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="388" parent="240" name="is_delete">
      <Comment>是否删除 1:是 0:否</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="389" parent="240" name="delete_time">
      <Comment>删除时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="390" parent="240" name="publish_time">
      <Comment>发布时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="391" parent="240" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;1979-01-01 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="392" parent="240" name="notice_picture">
      <Comment>消息图片</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="393" parent="240" name="msg_type">
      <Comment>消息类型，1系统消息2用户消息</Comment>
      <Position>11</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="394" parent="240" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="395" parent="240" name="push_product">
      <Comment>推送产品，1高血压管理控台2糖尿病管理控台3高血压智能管家4糖尿病智能管家5微信小程序</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="396" parent="240" name="product_msg_id">
      <Comment>推送产品消息表ID</Comment>
      <Position>14</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="397" parent="240" name="PRIMARY">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="398" parent="240" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="399" parent="241" name="id">
      <AutoIncrement>101</AutoIncrement>
      <Comment>记录id(自增主键id)</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="400" parent="241" name="create_date">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;1979-01-01 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="401" parent="241" name="notice_id">
      <Comment>消息通知id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(60)|0s</StoredType>
    </column>
    <column id="402" parent="241" name="is_read">
      <Comment>是否已读 0:否 1:是</Comment>
      <Position>4</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="403" parent="241" name="user_id">
      <Comment>高血压用户id</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="404" parent="241" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="405" parent="241" name="notice_id">
      <ColNames>notice_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="406" parent="241" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="407" parent="242" name="task_id">
      <AutoIncrement>3</AutoIncrement>
      <Comment>任务id（自增主键id）</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="408" parent="242" name="task_title">
      <Comment>任务标题</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="409" parent="242" name="content">
      <Comment>任务内容</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="410" parent="242" name="task_type">
      <Comment>任务类型  1：血压测量</Comment>
      <Position>4</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="411" parent="242" name="measure_posture">
      <Comment>测量姿势  1：躺卧位  2：坐立位</Comment>
      <Position>5</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="412" parent="242" name="show_index">
      <Comment>排序下标</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="413" parent="242" name="is_enabled">
      <Comment>是否启用 0：否  1：是</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="414" parent="242" name="match_begin_time">
      <Comment>匹配开始时间</Comment>
      <Position>8</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="415" parent="242" name="match_end_time">
      <Comment>匹配结束时间</Comment>
      <Position>9</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="416" parent="242" name="update_user_id">
      <Comment>更新人用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="417" parent="242" name="update_time">
      <Comment>更新时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="418" parent="242" name="create_user_id">
      <Comment>创建人用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int(6)|0s</StoredType>
    </column>
    <column id="419" parent="242" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>13</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="420" parent="242" name="PRIMARY">
      <ColNames>task_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="421" parent="242" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="422" parent="243" name="id">
      <AutoIncrement>71</AutoIncrement>
      <Comment>记录id(自增主键id)</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="423" parent="243" name="create_date">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;1979-01-01 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="424" parent="243" name="user_id">
      <Comment>高血压用户id</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="425" parent="243" name="device_id">
      <Comment>血压设备id</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="426" parent="243" name="bind_status">
      <Comment>绑定状态：  1：绑定  2：解绑</Comment>
      <Position>5</Position>
      <StoredType>int(3)|0s</StoredType>
    </column>
    <column id="427" parent="243" name="bind_device_user_type">
      <Comment>绑定的血压计用户类型，1：用户1，2：用户2</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="428" parent="243" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="429" parent="243" name="user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="430" parent="243" name="device_id">
      <ColNames>device_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="431" parent="243" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="432" parent="244" name="id">
      <AutoIncrement>51</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="433" parent="244" name="user_id">
      <Comment>用户id</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="434" parent="244" name="hypertension_type">
      <Comment>高血压类型 1级 2级 3级</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(2)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="435" parent="244" name="remark">
      <Comment>备注</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="436" parent="244" name="dynamic_blood_pressure_img">
      <Comment>24小时动态血压图</Comment>
      <Position>5</Position>
      <StoredType>varchar(1000)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="437" parent="244" name="compliance">
      <Comment>依从性，1良好2一般3差</Comment>
      <Position>6</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="438" parent="244" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="439" parent="244" name="create_user_id">
      <Comment>创建用户id</Comment>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="440" parent="244" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="441" parent="244" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="442" parent="244" name="update_user_id">
      <Comment>更新用户id</Comment>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="443" parent="244" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <index id="444" parent="244" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="445" parent="244" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="446" parent="245" name="id">
      <AutoIncrement>43</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="447" parent="245" name="news_id">
      <Comment>资讯ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="448" parent="245" name="person_tag_code">
      <Comment>人员标签CODE</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="449" parent="245" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="450" parent="245" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="451" parent="245" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="452" parent="246" name="id">
      <AutoIncrement>107</AutoIncrement>
      <Comment>消息推送记录id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="453" parent="246" name="news_id">
      <Comment>文章id</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="454" parent="246" name="push_type">
      <Comment>推送类型 ：0：立即推送 1：定时推送</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="455" parent="246" name="push_time">
      <Comment>推送时间</Comment>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="456" parent="246" name="status">
      <Comment>状态：1：已推送 2：待推送 </Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="457" parent="246" name="person_tag_code">
      <Comment>人员标签code</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf32_croatian_ci</CollationName>
    </column>
    <column id="458" parent="246" name="create_time">
      <Comment>创建时间</Comment>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="459" parent="246" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="460" parent="246" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="461" parent="247" name="organization_id">
      <Comment>主键ID 机构id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="462" parent="247" name="manage_hypertension_count">
      <Comment>辖区高血压管理人数</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <index id="463" parent="247" name="PRIMARY">
      <ColNames>organization_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="464" parent="247" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="465" parent="248" name="id">
      <AutoIncrement>5</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="466" parent="248" name="code">
      <Comment>标签code</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="467" parent="248" name="name">
      <Comment>标签名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="468" parent="248" name="table_name">
      <Comment>表名</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="469" parent="248" name="column_name">
      <Comment>列名</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="470" parent="248" name="value">
      <Comment>标签值</Comment>
      <Position>6</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="471" parent="248" name="tag_type">
      <Comment>标签类型：1 高血压</Comment>
      <Position>7</Position>
      <StoredType>int(4)|0s</StoredType>
    </column>
    <index id="472" parent="248" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="473" parent="248" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="474" parent="249" name="id">
      <AutoIncrement>171</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="475" parent="249" name="msg_code">
      <Comment>消息ID</Comment>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="476" parent="249" name="msg_type">
      <Comment>消息类型，1系统通知</Comment>
      <Position>3</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="477" parent="249" name="push_product">
      <Comment>推送产品，1高血压管理控台2糖尿病管理控台3高血压智能管家4糖尿病智能管家</Comment>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="478" parent="249" name="title">
      <Comment>标题</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="479" parent="249" name="content">
      <Comment>内容</Comment>
      <Position>6</Position>
      <StoredType>text|0s</StoredType>
    </column>
    <column id="480" parent="249" name="send_time">
      <Comment>发送时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="481" parent="249" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="482" parent="249" name="create_user_id">
      <Comment>创建用户ID</Comment>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="483" parent="249" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="484" parent="249" name="update_time">
      <Comment>更新时间</Comment>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="485" parent="249" name="update_user_id">
      <Comment>更新用户ID</Comment>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="486" parent="249" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="487" parent="249" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="488" parent="249" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="489" parent="249" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="490" parent="250" name="id">
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="491" parent="250" name="real_name">
      <Comment>真实姓名</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>2</Position>
      <StoredType>varchar(30)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="492" parent="250" name="id_no">
      <Comment>身份证号码</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="493" parent="250" name="gender">
      <Comment>性别 0：女 1：男  2:保密</Comment>
      <DefaultExpression>2</DefaultExpression>
      <Position>4</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="494" parent="250" name="birthday">
      <Comment>出生日期</Comment>
      <DefaultExpression>&apos;0000-00-00&apos;</DefaultExpression>
      <Position>5</Position>
      <StoredType>date|0s</StoredType>
    </column>
    <column id="495" parent="250" name="is_married">
      <Comment>是否已婚  0：否  1：是  2：未知</Comment>
      <DefaultExpression>2</DefaultExpression>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="496" parent="250" name="nation_code">
      <Comment>民族code</Comment>
      <Position>7</Position>
      <StoredType>varchar(60)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="497" parent="250" name="province_code">
      <Comment>省份code(现住址)</Comment>
      <Position>8</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="498" parent="250" name="city_code">
      <Comment>城市code(现住址)</Comment>
      <Position>9</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="499" parent="250" name="county_code">
      <Comment>县code(现住址)</Comment>
      <Position>10</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="500" parent="250" name="town_code">
      <Comment>乡镇code(现住址）</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="501" parent="250" name="village_code">
      <Comment>村区code(现住址)</Comment>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="502" parent="250" name="live_address">
      <Comment>详细地址（现住址）</Comment>
      <Position>13</Position>
      <StoredType>varchar(200)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="503" parent="250" name="profession_type">
      <Comment>职业类型</Comment>
      <Position>14</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="504" parent="250" name="create_time">
      <Comment>登记时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="505" parent="250" name="health_code">
      <Comment>健康码</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="506" parent="250" name="has_document">
      <Comment>是否建档, 0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>17</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="507" parent="250" name="create_user_id">
      <Comment>登记人用户id</Comment>
      <Position>18</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="508" parent="250" name="create_user_name">
      <Comment>登记人用户姓名</Comment>
      <Position>19</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="509" parent="250" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="510" parent="250" name="update_user_id">
      <Comment>更新用户id</Comment>
      <Position>21</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="511" parent="250" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>22</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <index id="512" parent="250" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="513" parent="250" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="514" parent="251" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="515" parent="251" name="resident_id">
      <Comment>基本信息ID，对应t_resident_basic_info表</Comment>
      <Position>2</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="516" parent="251" name="height">
      <Comment>身高（cm）</Comment>
      <Position>3</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="517" parent="251" name="weight">
      <Comment>体重（kg）</Comment>
      <Position>4</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="518" parent="251" name="bmi">
      <Comment>体脂指数</Comment>
      <Position>5</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="519" parent="251" name="waistline">
      <Comment>腰围</Comment>
      <Position>6</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="520" parent="251" name="blood_type">
      <Comment>血型  1：A ，2：B , 3: AB  4: O</Comment>
      <Position>7</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="521" parent="251" name="past_history">
      <Comment>既往史</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="522" parent="251" name="hpi">
      <Comment>现病史</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="523" parent="251" name="allergic_history">
      <Comment>过敏史</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="524" parent="251" name="family_history">
      <Comment>家族病史</Comment>
      <Position>11</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="525" parent="251" name="create_time">
      <Comment>创建时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="526" parent="251" name="create_user_id">
      <Comment>创建用户id</Comment>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="527" parent="251" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>14</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="528" parent="251" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="529" parent="251" name="update_user_id">
      <Comment>更新用户id</Comment>
      <Position>16</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="530" parent="251" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>17</Position>
      <StoredType>varchar(20)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <index id="531" parent="251" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="532" parent="251" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="533" parent="252" name="id">
      <AutoIncrement>9220</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="534" parent="252" name="user_id">
      <Comment>用户id</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="535" parent="252" name="device_no">
      <Comment>设备编号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>3</Position>
      <StoredType>varchar(64)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="536" parent="252" name="organization_id">
      <Comment>机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="537" parent="252" name="status">
      <Comment>状态，1下发解锁2解锁成功3启动成功(测量中)4测量结束5设备锁定6测量错误7结果上传8排队中</Comment>
      <Position>5</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="538" parent="252" name="status_type">
      <Comment>状态类型，1用户操作2设备响应</Comment>
      <Position>6</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="539" parent="252" name="remark">
      <Comment>备注</Comment>
      <Position>7</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="540" parent="252" name="create_time">
      <Comment>创建时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="541" parent="252" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="542" parent="252" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="543" parent="253" name="device_id">
      <AutoIncrement>312</AutoIncrement>
      <Comment>设备id(自增主键id)</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="544" parent="253" name="device_no">
      <Comment>设备编号</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="545" parent="253" name="organization_id">
      <Comment>机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="546" parent="253" name="relation_org_id">
      <Comment>关联社康/医院机构ID</Comment>
      <Position>4</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="547" parent="253" name="device_name">
      <Comment>设备名称</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="548" parent="253" name="device_type">
      <Comment>设备类型  1-臂筒式血压计  2-家庭式血压计</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="549" parent="253" name="device_model">
      <Comment>设备型号</Comment>
      <Position>7</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="550" parent="253" name="is_online">
      <Comment>是否在线 0-否，1-是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="551" parent="253" name="is_enabled">
      <Comment>是否启用  0:否  1：是</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="552" parent="253" name="location">
      <Comment>所在地点</Comment>
      <Position>10</Position>
      <StoredType>varchar(200)|0s</StoredType>
    </column>
    <column id="553" parent="253" name="lastest_ip_address">
      <Comment>最近登录ip地址</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="554" parent="253" name="lastest_login_time">
      <Comment>最近登录时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>12</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="555" parent="253" name="update_user_id">
      <Comment>更新设备用户id</Comment>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="556" parent="253" name="update_user_name">
      <Comment>更新设备用户名称</Comment>
      <Position>14</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="557" parent="253" name="update_time">
      <Comment>更新设备时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="558" parent="253" name="add_user_id">
      <Comment>新增设备用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>16</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="559" parent="253" name="add_user_name">
      <Comment>新增设备用户名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>17</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="560" parent="253" name="add_time">
      <Comment>新增设备时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="561" parent="253" name="device_imei">
      <Comment>设备IMEI</Comment>
      <Position>19</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="562" parent="253" name="heartbeat">
      <Comment>设备心跳时间</Comment>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="563" parent="253" name="remark">
      <Comment>备注</Comment>
      <Position>21</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="564" parent="253" name="wx_qr_code_url">
      <Comment>设备微信二维码地址</Comment>
      <Position>22</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="565" parent="253" name="offline_reminder">
      <Comment>离线提醒 （1：启用，2：禁用）</Comment>
      <Position>23</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="566" parent="253" name="offline_reminder_start_time">
      <Comment>提醒时段开始时间</Comment>
      <Position>24</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="567" parent="253" name="offline_reminder_end_time">
      <Comment>提醒时段结束时间</Comment>
      <Position>25</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="568" parent="253" name="offline_reminder_time">
      <Comment>离线提醒时间</Comment>
      <Position>26</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="569" parent="253" name="offline_time">
      <Comment>离线时间</Comment>
      <Position>27</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="570" parent="253" name="online_time">
      <Comment>上线时间</Comment>
      <Position>28</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="571" parent="253" name="PRIMARY">
      <ColNames>device_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="572" parent="253" name="device_no">
      <ColNames>device_no</ColNames>
      <Type>btree</Type>
    </index>
    <index id="573" parent="253" name="organization_id">
      <ColNames>organization_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="574" parent="253" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="575" parent="254" name="id">
      <AutoIncrement>30</AutoIncrement>
      <Comment>自增主键id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="576" parent="254" name="device_id">
      <Comment>设备id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="577" parent="254" name="from_relation_org_id">
      <Comment>迁出社康/医院机构ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="578" parent="254" name="from_organization_id">
      <Comment>迁出机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="579" parent="254" name="from_place">
      <Comment>迁出地点</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="580" parent="254" name="to_relation_org_id">
      <Comment>迁入社康/医院机构ID</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="581" parent="254" name="to_organization_id">
      <Comment>迁入机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="582" parent="254" name="to_place">
      <Comment>迁入地点</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="583" parent="254" name="transfer_user_id">
      <Comment>迁移人用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="584" parent="254" name="transfer_user_name">
      <Comment>迁移人用户名称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="585" parent="254" name="transfer_time">
      <Comment>迁移时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="586" parent="254" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="587" parent="254" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="588" parent="255" name="id">
      <AutoIncrement>5285</AutoIncrement>
      <Comment>记录id(自增主键id)</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="589" parent="255" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="590" parent="255" name="organization_id">
      <Comment>机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="591" parent="255" name="serial_number">
      <Comment>检测序列号</Comment>
      <Position>4</Position>
      <StoredType>varchar(64)|0s</StoredType>
    </column>
    <column id="592" parent="255" name="device_id">
      <Comment>检测设备id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="593" parent="255" name="device_type">
      <Comment>设备类型  1-臂筒式血压计  2-家庭式血压计</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="594" parent="255" name="record_type">
      <Comment>记录类型 1：用户1  2：用户2 （家庭式血压计记录不为空）</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="595" parent="255" name="systolic_pressure">
      <Comment>收缩压（高压）mmHg</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="596" parent="255" name="diastolic_pressure">
      <Comment>舒张压（低压）mmHg</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="597" parent="255" name="heartbeat">
      <Comment>心率（心跳）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="598" parent="255" name="pressure_result">
      <Comment>血压结果   1:正常  2：疑似  3：危重 4：控制达标</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="599" parent="255" name="heartbeat_result">
      <Comment>心率结果  1：正常  2：异常</Comment>
      <DefaultExpression>1</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>12</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="600" parent="255" name="measure_place">
      <Comment>测量地点</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>13</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="601" parent="255" name="measure_time">
      <Comment>测量时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>14</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="602" parent="255" name="is_read">
      <Comment>是否已读 1:是 0:否</Comment>
      <Position>15</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <column id="603" parent="255" name="decrypt_value">
      <Comment>家庭式设备解密数据</Comment>
      <Position>16</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="604" parent="255" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="605" parent="255" name="user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="606" parent="255" name="organization_id">
      <ColNames>organization_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="607" parent="255" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="608" parent="256" name="user_id">
      <AutoIncrement>2154</AutoIncrement>
      <Comment>用户id(自增主键id)</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="609" parent="256" name="nick_name">
      <Comment>昵称</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="610" parent="256" name="gender">
      <Comment>性别  0：女  1：男 2：未知</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="611" parent="256" name="head_portrait">
      <Comment>头像</Comment>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="612" parent="256" name="wx_open_id">
      <Comment>公众号应用唯一标识id</Comment>
      <Position>5</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="613" parent="256" name="app_open_id">
      <Comment>app应用唯一标识id</Comment>
      <Position>6</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="614" parent="256" name="wxapp_open_id">
      <Comment>微信小程序唯一标识id</Comment>
      <Position>7</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="615" parent="256" name="union_id">
      <Comment>同一用户对应同一主体的唯一标识</Comment>
      <Position>8</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="616" parent="256" name="rong_token">
      <Comment>融云token(APP注册用户)</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="617" parent="256" name="mobile">
      <Comment>手机号</Comment>
      <Position>10</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="618" parent="256" name="country">
      <Comment>国家</Comment>
      <Position>11</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="619" parent="256" name="province">
      <Comment>省份</Comment>
      <Position>12</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="620" parent="256" name="city">
      <Comment>城市</Comment>
      <Position>13</Position>
      <StoredType>varchar(20)|0s</StoredType>
    </column>
    <column id="621" parent="256" name="bind_device_id">
      <Comment>绑定的血压计设备id(家庭式)</Comment>
      <Position>14</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="622" parent="256" name="bind_device_user_type">
      <Comment>绑定的血压计用户类型，1：用户1，2：用户2</Comment>
      <Position>15</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="623" parent="256" name="bind_time">
      <Comment>绑定血压计时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="624" parent="256" name="manage_organization_id">
      <Comment>管理机构id</Comment>
      <Position>17</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="625" parent="256" name="register_organization_id">
      <Comment>注册机构id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>18</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="626" parent="256" name="register_source">
      <Comment>注册源： 1-公众号 , 2-APP, 3-小程序</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>19</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="627" parent="256" name="register_time">
      <Comment>注册时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>20</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="628" parent="256" name="password">
      <Comment>密码</Comment>
      <Position>21</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="629" parent="256" name="salt">
      <Comment>密码盐</Comment>
      <Position>22</Position>
      <StoredType>varchar(10)|0s</StoredType>
    </column>
    <column id="630" parent="256" name="ws_token">
      <Comment>websocketToken</Comment>
      <Position>23</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="631" parent="256" name="resident_id">
      <Comment>基本信息ID，对应t_resident_basic_info表</Comment>
      <Position>24</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="632" parent="256" name="is_sign">
      <Comment>是否签约，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>25</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="633" parent="256" name="sign_time">
      <Comment>签约时间</Comment>
      <Position>26</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="634" parent="256" name="ge_tui_client_id">
      <Comment>个推CID</Comment>
      <Position>27</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="635" parent="256" name="region_user">
      <Comment>区域用户，1城区，2乡镇</Comment>
      <Position>28</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="636" parent="256" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="637" parent="256" name="user_id">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="638" parent="256" name="union_id">
      <ColNames>union_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="639" parent="256" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="640" parent="257" name="user_id">
      <AutoIncrement>2087</AutoIncrement>
      <Comment>用户id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="641" parent="257" name="height">
      <Comment>身高（cm）</Comment>
      <Position>2</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="642" parent="257" name="weight">
      <Comment>体重（kg）</Comment>
      <Position>3</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="643" parent="257" name="bmi">
      <Comment>体脂指数</Comment>
      <Position>4</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="644" parent="257" name="waistline">
      <Comment>腰围</Comment>
      <Position>5</Position>
      <StoredType>float(11,1 digit)|0s</StoredType>
    </column>
    <column id="645" parent="257" name="blood_type">
      <Comment>血型  1：A ，2：B , 3: AB  4: O</Comment>
      <Position>6</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="646" parent="257" name="past_history">
      <Comment>既往史</Comment>
      <Position>7</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="647" parent="257" name="hpi">
      <Comment>现病史</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="648" parent="257" name="allergic_history">
      <Comment>过敏史</Comment>
      <Position>9</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="649" parent="257" name="family_history">
      <Comment>家族病史</Comment>
      <Position>10</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="650" parent="257" name="update_time">
      <Comment>更新时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <Position>11</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="651" parent="257" name="hypertension_type">
      <Comment>高血压类型 1级 2级 3级</Comment>
      <Position>12</Position>
      <StoredType>varchar(2)|0s</StoredType>
    </column>
    <index id="652" parent="257" name="PRIMARY">
      <ColNames>user_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="653" parent="257" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="654" parent="258" name="id">
      <AutoIncrement>75</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="655" parent="258" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="656" parent="258" name="relative_user_id">
      <Comment>关联亲属用户ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="657" parent="258" name="relationship">
      <Comment>亲属关系说明</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="658" parent="258" name="mobile">
      <Comment>亲属电话</Comment>
      <Position>5</Position>
      <StoredType>varchar(11)|0s</StoredType>
    </column>
    <column id="659" parent="258" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="660" parent="258" name="wx_open_id">
      <Comment>亲属OpenId</Comment>
      <NotNull>1</NotNull>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="661" parent="258" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="662" parent="258" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="663" parent="259" name="id">
      <AutoIncrement>224</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="664" parent="259" name="tpl_name">
      <Comment>模板名称</Comment>
      <Position>2</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="665" parent="259" name="test_frequency">
      <Comment>检测频率，值为1-7，1-7代表周一到周日，例：周一/周三/周六，就存1,3,6</Comment>
      <Position>3</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="666" parent="259" name="enable_status">
      <Comment>启用状态，1启用2禁用</Comment>
      <Position>4</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="667" parent="259" name="remark">
      <Comment>备注</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="668" parent="259" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="669" parent="259" name="create_user_id">
      <Comment>创建用户ID</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="670" parent="259" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="671" parent="259" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="672" parent="259" name="update_user_id">
      <Comment>更新用户ID</Comment>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="673" parent="259" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="674" parent="259" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="675" parent="259" name="tag_code">
      <Comment>选择的人员标签</Comment>
      <Position>13</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <index id="676" parent="259" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="677" parent="259" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="678" parent="260" name="id">
      <AutoIncrement>468</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="679" parent="260" name="test_plan_tpl_id">
      <Comment>检测计划模板ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="680" parent="260" name="start_time">
      <Comment>开始时间</Comment>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="681" parent="260" name="end_time">
      <Comment>结束时间</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="682" parent="260" name="create_time">
      <Comment>创建时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="683" parent="260" name="create_user_id">
      <Comment>创建用户ID</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="684" parent="260" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="685" parent="260" name="update_time">
      <Comment>更新时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="686" parent="260" name="update_user_id">
      <Comment>更新用户ID</Comment>
      <Position>9</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="687" parent="260" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>10</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="688" parent="260" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>11</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="689" parent="260" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="690" parent="260" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="691" parent="261" name="id">
      <AutoIncrement>26</AutoIncrement>
      <Comment>id</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="692" parent="261" name="organization_id">
      <Comment>机构id</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="693" parent="261" name="product_type">
      <Comment>产品类型：1.筛查小程序 2.高血压智能管家</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="694" parent="261" name="prompt_content">
      <Comment>提示内容</Comment>
      <Position>4</Position>
      <StoredType>varchar(255)|0s</StoredType>
      <CollationName>utf8_general_ci</CollationName>
    </column>
    <column id="695" parent="261" name="pressure_results">
      <Comment>血压结果：1.筛查小程序：（1：正常 2：异常 3：高风险 ）、2.智能管家：（ 1：控制良好 2：控制达标 3：异常 4：高风险）</Comment>
      <Position>5</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="696" parent="261" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="697" parent="261" name="update_time">
      <Comment>更新时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="698" parent="261" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="699" parent="261" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="700" parent="262" name="id">
      <AutoIncrement>712</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="701" parent="262" name="wx_open_id">
      <Comment>公众号应用唯一标识id</Comment>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="702" parent="262" name="line_up_size">
      <Comment>前面排队人数</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="703" parent="262" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="704" parent="262" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="705" parent="262" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="706" parent="263" name="id">
      <AutoIncrement>97</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="707" parent="263" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="708" parent="263" name="name">
      <Comment>用药提醒名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="709" parent="263" name="test_frequency">
      <Comment>检测频率，值为1-7，1-7代表周一到周日，例：周一/周三/周六，就存1,3,6</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="710" parent="263" name="alarm_clock_remind">
      <Comment>闹钟提醒，1提醒2不提醒</Comment>
      <Position>5</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="711" parent="263" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="712" parent="263" name="update_time">
      <Comment>更新时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="713" parent="263" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="714" parent="263" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="715" parent="263" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="716" parent="264" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="717" parent="264" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="718" parent="264" name="user_medication_remind_id">
      <Comment>用户检测计划ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="719" parent="264" name="user_medication_remind_time_id">
      <Comment>用户检测计划时段ID</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="720" parent="264" name="medication_time">
      <Comment>实际用药时间</Comment>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="721" parent="264" name="medication_num">
      <Comment>用药数量</Comment>
      <Position>6</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="722" parent="264" name="medication_unit">
      <Comment>用药单位</Comment>
      <Position>7</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="723" parent="264" name="test_date">
      <Comment>检测时间，YYYYMMDD格式</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="724" parent="264" name="create_time">
      <Comment>创建时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="725" parent="264" name="update_time">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="726" parent="264" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="727" parent="264" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="728" parent="265" name="id">
      <AutoIncrement>2604</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="729" parent="265" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="730" parent="265" name="user_medication_remind_id">
      <Comment>检测计划模板ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="731" parent="265" name="medication_time">
      <Comment>用药时间，例：18:00</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="732" parent="265" name="medication_num">
      <Comment>用药数量</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="733" parent="265" name="medication_unit">
      <Comment>用药单位</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="734" parent="265" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="735" parent="265" name="update_time">
      <Comment>更新时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="736" parent="265" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="737" parent="265" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="738" parent="265" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="739" parent="266" name="id">
      <AutoIncrement>403</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="740" parent="266" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="741" parent="266" name="news_id">
      <Comment>资讯ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="742" parent="266" name="create_time">
      <Comment>创建时间</Comment>
      <Position>4</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="743" parent="266" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="744" parent="266" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="745" parent="267" name="id">
      <AutoIncrement>28</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="746" parent="267" name="from_org_id">
      <Comment>迁出机构</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="747" parent="267" name="to_org_id">
      <Comment>迁入机构</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="748" parent="267" name="user_id">
      <Comment>用户ID</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="749" parent="267" name="change_method">
      <Comment>变更方式，1初次筛查2绑定设备3APP更换社康4控台乡镇签约5控台编辑更换</Comment>
      <Position>5</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="750" parent="267" name="create_time">
      <Comment>操作时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="751" parent="267" name="create_user_id">
      <Comment>创建用户id</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="752" parent="267" name="create_user_name">
      <Comment>创建用户名称</Comment>
      <Position>8</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <index id="753" parent="267" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="754" parent="267" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="755" parent="268" name="id">
      <AutoIncrement>68</AutoIncrement>
      <Comment>记录id(自增主键id)</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="756" parent="268" name="user_id">
      <Comment>用户id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="757" parent="268" name="task_id">
      <Comment>任务id</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="758" parent="268" name="record_id">
      <Comment>测量记录id</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="759" parent="268" name="record_time">
      <Comment>记录时间</Comment>
      <DefaultExpression>&apos;0000-00-00 00:00:00&apos;</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="760" parent="268" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="761" parent="268" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="762" parent="269" name="id">
      <AutoIncrement>279</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="763" parent="269" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="764" parent="269" name="name">
      <Comment>计划名称</Comment>
      <Position>3</Position>
      <StoredType>varchar(255)|0s</StoredType>
    </column>
    <column id="765" parent="269" name="test_frequency">
      <Comment>检测频率，值为1-7，1-7代表周一到周日，例：周一/周三/周六，就存1,3,6</Comment>
      <Position>4</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="766" parent="269" name="remark">
      <Comment>备注</Comment>
      <Position>5</Position>
      <StoredType>varchar(500)|0s</StoredType>
    </column>
    <column id="767" parent="269" name="plan_status">
      <Comment>计划状态,1生效2失效</Comment>
      <Position>6</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <column id="768" parent="269" name="create_time">
      <Comment>创建时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="769" parent="269" name="create_user_id">
      <Comment>创建用户ID</Comment>
      <Position>8</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="770" parent="269" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>9</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="771" parent="269" name="update_time">
      <Comment>更新时间</Comment>
      <Position>10</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="772" parent="269" name="update_user_id">
      <Comment>更新用户ID</Comment>
      <Position>11</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="773" parent="269" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>12</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="774" parent="269" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="775" parent="269" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="776" parent="269" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="777" parent="270" name="id">
      <AutoIncrement>22738</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="778" parent="270" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="779" parent="270" name="user_test_plan_id">
      <Comment>用户检测计划ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="780" parent="270" name="user_test_plan_time_id">
      <Comment>用户检测计划时段ID</Comment>
      <Position>4</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="781" parent="270" name="sphygmometer_record_id">
      <Comment>血压记录ID</Comment>
      <Position>5</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="782" parent="270" name="test_date">
      <Comment>检测时间，YYYYMMDD格式</Comment>
      <Position>6</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="783" parent="270" name="start_time">
      <Comment>开始时间</Comment>
      <Position>7</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="784" parent="270" name="end_time">
      <Comment>结束时间</Comment>
      <Position>8</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="785" parent="270" name="pressure_result">
      <Comment>血压结果   1:正常  2：疑似  3：危重 4：控制达标 0：待测量 -1：未测量</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="786" parent="270" name="heartbeat_result">
      <Comment>心率结果  1：正常  2：异常</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="787" parent="270" name="send_msg">
      <Comment>是否发送app消息 0未发送 1已发送 2发送失败  3已检测无需发送</Comment>
      <DefaultExpression>0</DefaultExpression>
      <NotNull>1</NotNull>
      <Position>11</Position>
      <StoredType>int(1)|0s</StoredType>
    </column>
    <column id="788" parent="270" name="systolic_pressure">
      <Comment>收缩压（高压）mmHg</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="789" parent="270" name="diastolic_pressure">
      <Comment>舒张压（低压）mmHg</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="790" parent="270" name="heartbeat">
      <Comment>心率（心跳）</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="791" parent="270" name="create_time">
      <Comment>创建时间</Comment>
      <Position>15</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="792" parent="270" name="update_time">
      <Comment>更新时间</Comment>
      <Position>16</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <index id="793" parent="270" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="794" parent="270" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="795" parent="271" name="id">
      <AutoIncrement>837</AutoIncrement>
      <Comment>主键ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="796" parent="271" name="user_id">
      <Comment>高血压用户ID</Comment>
      <Position>2</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="797" parent="271" name="user_test_plan_id">
      <Comment>检测计划模板ID</Comment>
      <Position>3</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="798" parent="271" name="start_time">
      <Comment>开始时间</Comment>
      <Position>4</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="799" parent="271" name="end_time">
      <Comment>结束时间</Comment>
      <Position>5</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="800" parent="271" name="create_time">
      <Comment>创建时间</Comment>
      <Position>6</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="801" parent="271" name="create_user_id">
      <Comment>创建用户ID</Comment>
      <Position>7</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="802" parent="271" name="create_user_name">
      <Comment>创建用户姓名</Comment>
      <Position>8</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="803" parent="271" name="update_time">
      <Comment>更新时间</Comment>
      <Position>9</Position>
      <StoredType>datetime|0s</StoredType>
    </column>
    <column id="804" parent="271" name="update_user_id">
      <Comment>更新用户ID</Comment>
      <Position>10</Position>
      <StoredType>int(11)|0s</StoredType>
    </column>
    <column id="805" parent="271" name="update_user_name">
      <Comment>更新用户姓名</Comment>
      <Position>11</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="806" parent="271" name="is_delete">
      <Comment>是否删除，0否1是</Comment>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
      <StoredType>int(2)|0s</StoredType>
    </column>
    <index id="807" parent="271" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="808" parent="271" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
  </database-model>
</dataSource>