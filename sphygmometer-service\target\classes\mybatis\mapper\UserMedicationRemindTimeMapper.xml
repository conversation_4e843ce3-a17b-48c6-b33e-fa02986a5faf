<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserMedicationRemindTimeMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.UserMedicationRemindTimeDTO" id="UserMedicationRemindTimeDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userMedicationRemindId"    column="user_medication_remind_id"    />
        <result property="medicationTime"    column="medication_time"    />
        <result property="medicationNum"    column="medication_num"    />
        <result property="medicationUnit"    column="medication_unit"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectUserMedicationRemindTime">
    	select
	        id,
	        user_id,
	        user_medication_remind_id,
	        medication_time,
	        medication_num,
	        medication_unit,
	        create_time,
	        update_time,
	        is_delete
		from 
        	t_user_medication_remind_time
    </sql>

    <select id="selectUserMedicationRemindTimeById" parameterType="int" resultMap="UserMedicationRemindTimeDTOResult">
		<include refid="selectUserMedicationRemindTime"/>
		where 
        	id = #{id}
    </select>

    <select id="selectUserMedicationRemindTimeList" parameterType="com.bojun.sphygmometer.dto.UserMedicationRemindTimeDTO" resultMap="UserMedicationRemindTimeDTOResult">
        <include refid="selectUserMedicationRemindTime"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="userMedicationRemindId != null "> and user_medication_remind_id = #{userMedicationRemindId}</if>
		<if test="medicationTime != null  and medicationTime != ''"> and medication_time = #{medicationTime}</if>
		<if test="medicationNum != null "> and medication_num = #{medicationNum}</if>
		<if test="medicationUnit != null  and medicationUnit != ''"> and medication_unit = #{medicationUnit}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>

</mapper>