package com.bojun.base.system.api.controller;

import com.bojun.base.system.api.ManageUserV2FeignClient;
import com.bojun.base.system.service.ManageUserV2Service;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.system.dto.ManageUserV2DTO;
import com.bojun.system.entity.ManageUserLogin;
import com.bojun.system.entity.ManageUserV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统管理员用户FeignController
 * 
 * <AUTHOR>
 * @date 2021-06-05 16:05:04
 */
@RestController
public class ManageUserV2FeignController extends BaseFeignController implements ManageUserV2FeignClient
{
    @Autowired
    private ManageUserV2Service ManageUserV2Service;

	/**
     * 查询系统管理员用户分页列表
     */
    @PostMapping(PREFIX + "/page")
    public Results<PageData<ManageUserV2DTO>> page(@RequestBody ManageUserV2DTO ManageUserV2DTO){
        startPage(ManageUserV2DTO.getPageNum(), ManageUserV2DTO.getEveryPage());
        List<ManageUserV2DTO> list = ManageUserV2Service.selectManageUserList(ManageUserV2DTO);
        return Results.list(getPageData(list));
    }

    /**
     * 查询系统管理员用户列表
     */
    @PostMapping(PREFIX + "/list")
    public Results<List<ManageUserV2DTO>> list(@RequestBody ManageUserV2DTO ManageUserV2DTO){
        List<ManageUserV2DTO> list = ManageUserV2Service.selectManageUserList(ManageUserV2DTO);
        return Results.list(list);
    }

    /**
     * 获取系统管理员用户详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<ManageUserV2DTO> getInfo(@RequestParam("userId") Integer userId){
        ManageUserV2DTO ManageUserV2DTO = ManageUserV2Service.selectManageUserById(userId);
        return Results.data(ManageUserV2DTO);
    }

    /**
     * 新增系统管理员用户DTO
     */
    @PostMapping(PREFIX + "/addDTO")
    public Results addDTO(@RequestBody ManageUserV2DTO ManageUserV2DTO){
    	Integer num = ManageUserV2Service.insertManageUser(ManageUserV2DTO);
        return Results.opResult(num);
    }

    /**
     * 修改系统管理员用户DTO
     */
    @PostMapping(PREFIX + "/editDTO")
    public Results editDTO(@RequestBody ManageUserV2DTO ManageUserV2DTO){
        Integer num = ManageUserV2Service.updateManageUser(ManageUserV2DTO);
        return Results.opResult(num);
    }
    
    /**
     * 新增系统管理员用户
     */
    @PostMapping(PREFIX + "/add")
    public Results add(@RequestBody ManageUserV2 ManageUserV2){
        Integer num = ManageUserV2Service.insertManageUser(ManageUserV2);
        return Results.opResult(num);
    }

    /**
     * 修改系统管理员用户
     */
    @PostMapping(PREFIX + "/edit")
    public Results edit(@RequestBody ManageUserV2 ManageUserV2){
        Integer num = ManageUserV2Service.updateManageUser(ManageUserV2);
        return Results.opResult(num);
    }

    /**
     * 删除系统管理员用户，多个以逗号分隔
     */
    @GetMapping(PREFIX + "/removeByIds")
    public Results removeByIds(@RequestParam("ids") String ids) {
        Integer num = ManageUserV2Service.deleteManageUserByIds(ids);
        return Results.opResult(num);
    }

    @Override
    public Results<ManageUserLogin> selectLastUserLoginById(@RequestParam("userId")  Integer userId, @RequestParam("systemId") String systemId) {
        return Results.data(ManageUserV2Service.selectLastUserLoginById(userId, systemId));
    }
}
