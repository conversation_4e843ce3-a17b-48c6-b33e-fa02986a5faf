package com.bojun.sphygmometer.manage.controller.user;

import com.bojun.author.AuthAnnotation;
import com.bojun.common.controller.BaseController;
import com.bojun.common.util.LoginUserThreadLocal;
import com.bojun.contants.Contants;
import com.bojun.log.SystemLog;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.sphygmometer.api.ResidentBasicInfoFeignClient;
import com.bojun.sphygmometer.api.SphygmometerUserFeignClient;
import com.bojun.sphygmometer.dto.*;
import com.bojun.sphygmometer.manage.controller.user.vo.*;
import com.bojun.sphygmometer.manage.controller.user.vo.params.*;
import com.bojun.utils.BeanUtil;
import com.vdurmont.emoji.EmojiParser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 高血压用户信息表Controller
 * 
 * <AUTHOR>
 * @date 2021-03-19 14:15:25
 */
@Api(tags = {"高血压用户信息表相关接口"})
@RestController
@RequestMapping(SphygmometerUserController.BASE_URL)
public class SphygmometerUserController extends BaseController
{
	public static final String BASE_URL = "/user/user";

    @Autowired
    private SphygmometerUserFeignClient sphygmometerUserFeignClient;
    @Autowired
    private ResidentBasicInfoFeignClient residentBasicInfoFeignClient;
    @Autowired
    private WxMpService wxMpService;

    @ApiOperation("完善unoinid为空的用户信息")
    @GetMapping("/processUnoinIdBeNotNull")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/processUnoinIdBeNotNull")
    public Results processUnoinIdBeNotNull() {
        List<SphygmometerUserDTO> list = this.sphygmometerUserFeignClient.getUserListByUnoinIdNull();
        for (SphygmometerUserDTO sphygmometerUser : list) {
            String wxOpenId = sphygmometerUser.getWxOpenId();
            try {
                WxMpUser wxMpUser = this.wxMpService.getUserService().userInfo(wxOpenId);
                sphygmometerUser.setWxOpenId(wxMpUser.getOpenId());
                String nickName = wxMpUser.getNickname();
                if (StringUtils.isNotBlank(nickName)) {
                    nickName = EmojiParser.removeAllEmojis(wxMpUser.getNickname());
                }
                sphygmometerUser.setNickName(nickName);//微信昵称
                Integer gender = 2;//未知
                if (wxMpUser.getSex() != null) {
                    //性别--微信端(1时是男性，值为2时是女性，值为0时是未知)--bojun端(性别  0：女  1：男 2：未知)
                    gender = wxMpUser.getSex() == 1 ? 1 : (wxMpUser.getSex() == 2 ? 0 : 2);
                }
                sphygmometerUser.setGender(gender);
                sphygmometerUser.setCountry(wxMpUser.getCountry());//国家，如中国为CN
                sphygmometerUser.setProvince(wxMpUser.getProvince());//省份
                sphygmometerUser.setCity(wxMpUser.getCity());//城市
                sphygmometerUser.setHeadPortrait(wxMpUser.getHeadImgUrl());//头像
                sphygmometerUser.setUnionId(wxMpUser.getUnionId());//同一用户对应同一主体的唯一标识
                this.sphygmometerUserFeignClient.edit(sphygmometerUser);
            } catch (Exception e) {

            }

        }
        return Results.success();
    }

    /**
     * 筛查用户列表
     */
    @ApiOperation("筛查用户列表")
    @GetMapping("/getScreenUserList")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getScreenUserList")
    public Results<PageData<ScreenUserListVO>> getScreenUserList(@Valid ScreenUserListParamVO screenUserListParamVO){
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(screenUserListParamVO, SphygmometerUserDTO.class);
        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<OrganizationInfoV2DTO> orgList = this.getFullAuthOrgList(screenUserListParamVO.getManageOrganizationId());
        if (orgList.size() == 0) {
            return Results.list(null);
        }
        sphygmometerUserDTO.setRegisterOrganizationList(orgList);
        Results<PageData<SphygmometerUserDTO>> results1 = sphygmometerUserFeignClient.getScreenUserList(sphygmometerUserDTO);
        return Results.list(getPageDataVo(results1.getData(), ScreenUserListVO.class));
    }

    /**
     * 用户信息刷新
     */
    @ApiOperation("根据身份证获取用户信息")
    @GetMapping(value = "/getUserInfoByIdNo")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getUserInfoByIdNo")
    public Results<ScreenUserInfoVO> getUserInfoByIdNo(String idNo){
        if(StringUtils.isBlank(idNo)){
            return Results.fail("身份证号码为空");
        }
        Results<SphygmometerUserDTO> results = sphygmometerUserFeignClient.getUserInfoByIdNo(idNo);
        ScreenUserInfoVO screenUserInfoVO = BeanUtil.deepCopyProperties(results.getData(), ScreenUserInfoVO.class);
        return Results.data(screenUserInfoVO);
    }

    /**
     * 筛查用户基本信息
     */
    @ApiOperation("筛查用户基本信息")
    @GetMapping(value = "/getScreenUserInfo")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getScreenUserInfo")
    public Results<ScreenUserInfoVO> getScreenUserInfo(@RequestParam("userId") Integer userId){
        Results<SphygmometerUserDTO> results = sphygmometerUserFeignClient.getScreenUserInfo(userId);
        ScreenUserInfoVO screenUserInfoVO = BeanUtil.deepCopyProperties(results.getData(), ScreenUserInfoVO.class);
        return Results.data(screenUserInfoVO);
    }

    /**
     * 修改筛查用户的信息
     */
    @ApiOperation("修改筛查用户的信息")
    @PostMapping("/editScreenUserInfo")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/editScreenUserInfo")
    @SystemLog(action = SphygmometerUserController.BASE_URL + "/editScreenUserInfo", operationType = Contants.UPDATE_REQUEST, description = "修改筛查用户的信息")
    public Results editScreenUserInfo(@RequestBody @Valid ScreenUserInfoEditParamVO screenUserInfoEditParamVO){
        ResidentBasicInfoDTO residentBaseInfoDTO = BeanUtil.deepCopyProperties(screenUserInfoEditParamVO, ResidentBasicInfoDTO.class);
        residentBaseInfoDTO.setCreateUserId(LoginUserThreadLocal.getUserInfo().getUserId());
        residentBaseInfoDTO.setCreateUserName(LoginUserThreadLocal.getUserInfo().getRealName());
        Results results = this.residentBasicInfoFeignClient.editScreenUserInfo(residentBaseInfoDTO);
        return results;
    }

    /**
     * 签约用户列表
     */
    @ApiOperation("签约用户列表")
    @GetMapping("/getSignContractUserList")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getSignContractUserList")
    public Results<PageData<SignContractUserListVO>> getSignContractUserList(@Valid SignContractUserListParamVO signContractUserListParamVO){
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(signContractUserListParamVO, SphygmometerUserDTO.class);

        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<OrganizationInfoV2DTO> orgList = this.getFullAuthOrgList(signContractUserListParamVO.getManageOrganizationId());
        if (orgList.size() == 0) {
            return Results.list(null);
        }
        sphygmometerUserDTO.setManageOrganizationList(orgList);
        Results<PageData<SphygmometerUserDTO>> results1 = sphygmometerUserFeignClient.getSignContractUserList(sphygmometerUserDTO);
        return Results.list(getPageDataVo(results1.getData(), SignContractUserListVO.class));
    }

    /**
     * 获签约用户详细信息
     */
    @ApiOperation("获取签约用户详细信息")
    @GetMapping(value = "/getSignContractUserInfo")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getSignContractUserInfo")
    public Results<SphygmometerUserDetailVO> getInfo(@RequestParam("userId") Integer userId){
        SphygmometerUserDTO sphygmometerUserDTO = sphygmometerUserFeignClient.getInfo(userId);
        SphygmometerUserDetailVO sphygmometerUserDetailVO = BeanUtil.deepCopyProperties(sphygmometerUserDTO, SphygmometerUserDetailVO.class);
        return Results.data(sphygmometerUserDetailVO);
    }

    /**
     * 修改签约用户的信息
     */
    @ApiOperation("修改签约用户的信息")
    @PostMapping("/editSignContractUserInfo")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/editSignContractUserInfo")
    @SystemLog(action = SphygmometerUserController.BASE_URL + "/editSignContractUserInfo", operationType = Contants.UPDATE_REQUEST, description = "修改签约用户的信息")
    public Results editSignContractUserInfo(@RequestBody @Valid SphygmometerUserEditParamVO sphygmometerUserEditParamVO){
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(sphygmometerUserEditParamVO, SphygmometerUserDTO.class);
        sphygmometerUserDTO.setOperatorId(LoginUserThreadLocal.getUserInfo().getUserId());
        sphygmometerUserDTO.setOperatorName(LoginUserThreadLocal.getUserInfo().getRealName());
        return Results.opResult(sphygmometerUserFeignClient.editUserInfoAndBaseInfoAndHealthInfo(sphygmometerUserDTO));
    }

    /**
     * 未绑定设备用户列表
     */
    @ApiOperation("未绑定设备用户列表")
    @GetMapping("/getNoBindDeviceUserList")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getNoBindDeviceUserList")
    public Results<PageData<NoBindDeviceUserListVO>> getNoBindDeviceUserList(@Valid NoBindDeviceUserListParamVO noBindDeviceUserListParamVO){
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(noBindDeviceUserListParamVO, SphygmometerUserDTO.class);
        Results<PageData<SphygmometerUserDTO>> results1 = sphygmometerUserFeignClient.getNoBindDeviceUserList(sphygmometerUserDTO);
        return Results.list(getPageDataVo(results1.getData(), NoBindDeviceUserListVO.class));
    }

    @ApiOperation("工作台-筛查用户统计")
    @GetMapping("/getScreenUserStatistic")
    public Results<ScreenUserStatisticVO> getScreenUserStatistic() {
        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<Integer> orgIdList = this.getFullAuthOrgIdList(null);
        if (orgIdList.size() == 0) {
            return Results.success("您无权限查看此数据");
        }
        Results<ScreenUserStatisticDTO> results = sphygmometerUserFeignClient.getScreenUserStatistic(orgIdList);
        ScreenUserStatisticVO screenUserStatisticVO = BeanUtil.deepCopyProperties(results.getData(), ScreenUserStatisticVO.class);
        return Results.data(screenUserStatisticVO);
    }

    @ApiOperation("工作台-签约用户统计")
    @GetMapping("/getSignContractUserStatistic")
    public Results<SignContractUserStatisticVO> getSignContractUserStatistic() {
        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<Integer> orgIdList = this.getFullAuthOrgIdList(null);
        if (orgIdList.size() == 0) {
            return Results.success("您无权限查看此数据");
        }
        Results<SignContractUserStatisticDTO> results = sphygmometerUserFeignClient.getSignContractUserStatistic(orgIdList);
        SignContractUserStatisticVO signContractUserStatisticVO = BeanUtil.deepCopyProperties(results.getData(), SignContractUserStatisticVO.class);
        return Results.data(signContractUserStatisticVO);
    }

    @ApiOperation("工作台-乡镇筛查用户统计")
    @GetMapping("/getTownshipScreenUserStatistic")
    public Results<TownshipScreenUserStatisticVO> getTownshipScreenUserStatistic() {
        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<Integer> orgIdList = this.getFullAuthOrgIdList(null);
        if (orgIdList.size() == 0) {
            return Results.success("您无权限查看此数据");
        }
        Results<TownshipScreenUserStatisticDTO> results = sphygmometerUserFeignClient.getTownshipScreenUserStatistic(orgIdList);
        TownshipScreenUserStatisticVO townshipScreenUserStatisticVO = BeanUtil.deepCopyProperties(results.getData(), TownshipScreenUserStatisticVO.class);
        return Results.data(townshipScreenUserStatisticVO);
    }

    /**
     * 获取用户绑定的设备列表
     */
    @ApiOperation("获取用户绑定的设备列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", dataType = "Integer", paramType = "query"),
    })
    @GetMapping("/getUserBindDeviceList")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/getUserBindDeviceList")
    public Results<List<UserBindDeviceListVO>> getUserBindDeviceList(@RequestParam("userId") Integer userId){
        Results<List<UserBindDeviceListDTO>> results1 = this.sphygmometerUserFeignClient.getUserBindDeviceList(userId);
        return Results.list(getListVo(results1.getData(), UserBindDeviceListVO.class));
    }

    /**
     * 修改签约用户的信息
     */
    @ApiOperation("签约用户")
    @PostMapping("/signContract")
    @AuthAnnotation(action = SphygmometerUserController.BASE_URL + "/signContract")
    public Results signContract(@RequestBody @Valid SignContractParamVO signContractParamVO){
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(signContractParamVO, SphygmometerUserDTO.class);
        sphygmometerUserDTO.setManageOrganizationId(LoginUserThreadLocal.getUserInfo().getOrganizationId());
        sphygmometerUserDTO.setRegisterOrganizationId(LoginUserThreadLocal.getUserInfo().getOrganizationId());
        sphygmometerUserDTO.setOperatorId(LoginUserThreadLocal.getUserInfo().getUserId());
        sphygmometerUserDTO.setBindDeviceId(signContractParamVO.getDeviceId());
        return this.sphygmometerUserFeignClient.signContract(sphygmometerUserDTO);
    }

}
