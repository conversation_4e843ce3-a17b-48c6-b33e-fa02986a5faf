/**
 *
 */
package com.bojun.base.manage.api.organization.hystrix;

import com.bojun.base.manage.api.organization.IOrganizationService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Model： 机构管理接口熔断器
 * Description：机构管理接口熔断器
 * Author：赖水秀
 * created： 2020年5月4日
 */
@Component
public class OrganizationServiceHystrix implements IOrganizationService {

	@Override
	public String saveOrganizationInfo(OrganizationInfoDTO organizationInfoDTO) {
		throw new BaseRuntimeException("saveOrganizationInfo接口服务已断开");
	}

	@Override
	public String updateOrganizationInfo(OrganizationInfoDTO organizationId) {
		throw new BaseRuntimeException("updateOrganizationInfo接口服务已断开");
	}

	@Override
	public String getOrganizationInfoById(Integer organizationId) {
		throw new BaseRuntimeException("getOrganizationInfoById接口服务已断开");
	}

	@Override
	public String getOrganizationPageList(OrganizationInfoDTO organizationInfoDTO) {
		throw new BaseRuntimeException("getOrganizationPageList接口服务已断开");
	}

	@Override
	public String getOrganizationTreeList(OrganizationInfoDTO organizationInfoDTO) {
		throw new BaseRuntimeException("getOrganizationTreeList接口服务已断开");
	}

	@Override
	public String deleteOrganizationById(Integer organizationId) {
		throw new BaseRuntimeException("deleteOrganizationById接口服务已断开");
	}

	@Override
	public String batchDeleteOrganization(List<String> organizationIds) {
		throw new BaseRuntimeException("batchDeleteOrganization接口服务已断开");
	}

	@Override
	public String getOrganizationTypeList(String classCode) {
		throw new BaseRuntimeException("getOrganizationTypeList接口服务已断开");
	}

	@Override
	public String getPushObject(OrganizationInfoDTO organizationInfoDTO) {
		throw new BaseRuntimeException("getPushObject接口服务已断开");
	}

    @Override
    public String getPushObjectByOrgId(OrganizationInfoDTO organizationInfoDTO) {
		throw new BaseRuntimeException("getPushObjectByOrgId接口服务已断开");
	}

    @Override
    public String deleteOrganizationImgById(OrganizationImgDTO organizationImgDTO) {
        throw new BaseRuntimeException("deleteOrganizationImgById接口服务已断开");
    }

    @Override
    public String getPushObjectByOrgAndRoleId(OrganizationInfoDTO organizationInfoDTO) {
        throw new BaseRuntimeException("getPushObjectByOrgAndRoleId接口服务已断开");
    }

    @Override
    public String getPushObjectByRoleId(OrganizationInfoDTO organizationInfoDTO) {
        throw new BaseRuntimeException("getPushObjectByRoleId接口服务已断开");
    }
}
