/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;

import com.bojun.vo.BaseQueryInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：菜单管理
 * Description：查询菜单信息
 * Author：赖水秀
 * created： 2020年4月28日
 */
@ApiModel(value = "查询菜单条件", description = "查询菜单列表传入的查询条件")
public class GetMenuParamVO extends BaseQueryInfoVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;
	
	@NotEmpty(message = "系统id不能为空")
	@ApiModelProperty(value="系统id", required = true, example = "1")
	private String systemId;	
	
	@ApiModelProperty(value="菜单名称")
	private String menuName;

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getMenuName() {
		return menuName;
	}

	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}	
	
}
