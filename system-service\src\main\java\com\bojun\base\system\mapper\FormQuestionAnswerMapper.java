
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.FormQuestionAnswerDTO;



/**
 * 
*Model：满意度问卷答题信息表
*Description：满意度问卷答题信息表
*Author:李欣颖
*created：2020年5月7日
 */
@Mapper
public interface FormQuestionAnswerMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	int queryFormQuestionAnswerCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询满意度问卷答题信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionAnswerDTO>
     * created：2020年5月7日
	 */
	List<FormQuestionAnswerDTO> getFormQuestionAnswer(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer addFormQuestionAnswer(FormQuestionAnswerDTO formQuestionAnswerDTO);

	/**
	 * 
	 * @Description 删除满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer deleteFormQuestionAnswer(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer updateFormQuestionAnswer(FormQuestionAnswerDTO formQuestionAnswerDTO);

	/**
	 * 
	 * @Description 查询单个满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionAnswerDTO
	 * created：2020年5月7日
	 */
	FormQuestionAnswerDTO getFormQuestionAnswerById(Map<String, Object> mapPara);


	
}
