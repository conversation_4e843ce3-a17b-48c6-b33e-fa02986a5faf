package com.bojun.sphygmometer.manage.controller.device;

import com.bojun.author.AuthAnnotation;
import com.bojun.common.controller.BaseController;
import com.bojun.common.util.DeviceImeiUtils;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.organization.dto.GetAuthOrganizationV2DTO;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;
import com.bojun.organization.service.api.OrganizationInfoV2FeignClient;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.sphygmometer.api.DeviceBindRecordFeignClient;
import com.bojun.sphygmometer.api.SphygmometerDeviceFeignClient;
import com.bojun.sphygmometer.api.SphygmometerUserFeignClient;
import com.bojun.sphygmometer.dto.DeviceBindRecordDTO;
import com.bojun.sphygmometer.dto.SphygmometerDeviceDTO;
import com.bojun.sphygmometer.dto.SphygmometerUserDTO;
import com.bojun.sphygmometer.entity.SphygmometerDevice;
import com.bojun.sphygmometer.entity.SphygmometerUser;
import com.bojun.sphygmometer.manage.controller.device.vo.SphygmometerDeviceCountVO;
import com.bojun.sphygmometer.manage.controller.device.vo.SphygmometerDeviceDetailVO;
import com.bojun.sphygmometer.manage.controller.device.vo.SphygmometerDeviceListVO;
import com.bojun.sphygmometer.manage.controller.device.vo.params.SphygmometerDeviceAddParamVO;
import com.bojun.sphygmometer.manage.controller.device.vo.params.SphygmometerDeviceCountParamVO;
import com.bojun.sphygmometer.manage.controller.device.vo.params.SphygmometerDeviceEditParamVO;
import com.bojun.sphygmometer.manage.controller.device.vo.params.SphygmometerDeviceListParamVO;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.utils.BeanUtil;
import com.google.common.base.Joiner;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatReader;
import com.google.zxing.Result;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.common.HybridBinarizer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

/**
 * 血压计设备信息表Controller
 *
 * <AUTHOR>
 * @date 2021-03-19 12:03:46
 */
@Slf4j
@Api(tags = {"血压计设备信息表相关接口(严峡华)"})
@RestController
@RequestMapping(SphygmometerDeviceController.BASE_URL)
public class SphygmometerDeviceController extends BaseController
{
    public static final String BASE_URL = "/device";

    @Autowired
    private SphygmometerDeviceFeignClient sphygmometerDeviceFeignClient;
    @Autowired
    private OrganizationInfoV2FeignClient organizationInfoV2FeignClient;
    @Autowired
    private DeviceBindRecordFeignClient deviceBindRecordFeignClient;
    @Autowired
    private WxMpService wxMpService;
    @Autowired
    private SphygmometerUserFeignClient sphygmometerUserFeignClient;

//    private static final String  devicePattern = "[a-zA-Z0-9]{1,50}";

    /**
     * 查询血压计设备信息表列表
     */
    @ApiOperation("查询血压计设备信息表列表")
    @GetMapping("/list")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/list")
    public Results<PageData<SphygmometerDeviceListVO>> list(HttpServletRequest request,@Valid SphygmometerDeviceListParamVO sphygmometerDeviceListParamVO){
        if(sphygmometerDeviceListParamVO.getOrganizationId() == null || sphygmometerDeviceListParamVO.getOrganizationId().equals("")){
            return Results.fail("机构ID为空");
        }
        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<Integer> orgIdList = this.getFullAuthOrgIdList(sphygmometerDeviceListParamVO.getOrganizationId());
        if (orgIdList.size() == 0) {
            return Results.success("您无权限查看此数据");
        }
        SphygmometerDeviceDTO sphygmometerDeviceDTO = BeanUtil.deepCopyProperties(sphygmometerDeviceListParamVO, SphygmometerDeviceDTO.class);
        sphygmometerDeviceDTO.setOrganizationIds(Joiner.on(",").join(orgIdList));
        PageData<SphygmometerDeviceDTO> pageData = sphygmometerDeviceFeignClient.list(sphygmometerDeviceDTO);
        return Results.list(getPageDataVo(pageData, SphygmometerDeviceListVO.class));
    }

    /**
     * 获取血压计设备信息表详细信息
     */
    @ApiOperation("获取血压计设备信息表详细信息")
    @GetMapping(value = "/getInfo")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/getInfo")
    public Results<SphygmometerDeviceDetailVO> getInfo(@RequestParam("deviceId") Integer deviceId){
        SphygmometerDeviceDTO sphygmometerDeviceDTO = sphygmometerDeviceFeignClient.getInfo(deviceId);
        SphygmometerDeviceDetailVO sphygmometerDeviceDetailVO = BeanUtil.deepCopyProperties(sphygmometerDeviceDTO, SphygmometerDeviceDetailVO.class);
        return Results.data(sphygmometerDeviceDetailVO);
    }


    /**
     * 获取血压计设备信息表详细信息
     */
    @ApiOperation("根据设备编号获取血压计设备信息表详细信息")
    @GetMapping(value = "/getInfoByDeviceNo")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/getInfoByDeviceNo")
    public Results<SphygmometerDeviceDetailVO> getInfoByDeviceNo(@RequestParam("deviceNo") String deviceNo){
        SphygmometerDeviceDTO sphygmometerDeviceDTO = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
        SphygmometerDeviceDetailVO sphygmometerDeviceDetailVO = BeanUtil.deepCopyProperties(sphygmometerDeviceDTO, SphygmometerDeviceDetailVO.class);
        return Results.data(sphygmometerDeviceDetailVO);
    }

    /**
     * 新增血压计设备信息表
     */
    @ApiOperation("新增血压计设备信息表")
    @PostMapping("/add")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/add")
    @SystemLog(action = SphygmometerDeviceController.BASE_URL + "/add", operationType = Contants.ADD_REQUEST, description = "新增血压计设备信息表")
    public Results add(HttpServletRequest request,@RequestBody @Valid SphygmometerDeviceAddParamVO sphygmometerDeviceAddParamVO){
        SphygmometerDeviceDTO sphygmometerDeviceDTO = BeanUtil.deepCopyProperties(sphygmometerDeviceAddParamVO, SphygmometerDeviceDTO.class);
        sphygmometerDeviceDTO.setRelationOrgId(sphygmometerDeviceAddParamVO.getRelationOrgId());
        //校验IMEI号合法性
        log.info("=========校验imei===========imei:"+sphygmometerDeviceDTO.getDeviceImei()+",deviceType:"+sphygmometerDeviceDTO.getDeviceType());
        if (sphygmometerDeviceDTO.getDeviceType().intValue() == 2
                && !DeviceImeiUtils.isCorrectImei(sphygmometerDeviceDTO.getDeviceImei())) {
            return Results.fail("imei:" + sphygmometerDeviceDTO.getDeviceImei() + "--不符合规则");
        }
        ManageUserDTO manageUserDTO = (ManageUserDTO) request.getAttribute("manageUser");
        sphygmometerDeviceDTO.setAddTime(new Date());
        sphygmometerDeviceDTO.setAddUserId(manageUserDTO.getUserId());
        String result =sphygmometerDeviceFeignClient.add(sphygmometerDeviceDTO);
        if (result.contains("成功")) {
            return Results.success(result);
        }
        return Results.fail(result);

    }

    /**
     * 修改血压计设备信息表
     */
    @ApiOperation("修改血压计设备信息表")
    @PostMapping("/edit")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/edit")
    @SystemLog(action = SphygmometerDeviceController.BASE_URL + "/edit", operationType = Contants.UPDATE_REQUEST, description = "修改血压计设备信息表")
    public Results edit(HttpServletRequest request,@RequestBody @Valid SphygmometerDeviceEditParamVO sphygmometerDeviceEditParamVO){
        SphygmometerDeviceDTO sphygmometerDeviceDTO = BeanUtil.deepCopyProperties(sphygmometerDeviceEditParamVO, SphygmometerDeviceDTO.class);
        ManageUserDTO manageUserDTO = (ManageUserDTO) request.getAttribute("manageUser");
        sphygmometerDeviceDTO.setUpdateTime(new Date());
        sphygmometerDeviceDTO.setUpdateUserId(manageUserDTO.getUserId());
//        sphygmometerDeviceDTO.setUpdateUserName(manageUserDTO.getRealName());

        SphygmometerDevice sphygmometerDevice = this.sphygmometerDeviceFeignClient.getInfo(sphygmometerDeviceDTO.getDeviceId());
        //如果为家庭式设备, 与库中org_id不一样
        if (sphygmometerDeviceDTO.getDeviceType() == 2 &&
                sphygmometerDevice.getRelationOrgId() != null &&
                sphygmometerDeviceDTO.getRelationOrgId() != null &&
                sphygmometerDevice.getRelationOrgId().intValue() != sphygmometerDeviceDTO.getRelationOrgId().intValue()) {
            //查询设备是否绑定用户
            SphygmometerUserDTO sphygmometerUserDTO = new SphygmometerUserDTO();
            sphygmometerUserDTO.setBindDeviceId(sphygmometerDevice.getDeviceId());
            List<SphygmometerUser> sphygmometerUserList = sphygmometerUserFeignClient.listByBindDeviceId(sphygmometerUserDTO);
            if (sphygmometerUserList != null && !sphygmometerUserList.isEmpty()) {
                return Results.fail("设备已绑定用户, 不可修改其机构信息");
            }
        }
        int num = sphygmometerDeviceFeignClient.edit(sphygmometerDeviceDTO);
        return Results.opResult(num);
    }

    /**
     * 删除血压计设备信息表，多个以逗号分隔
     */
    @ApiOperation("删除血压计设备信息表，多个以逗号分隔")
    @GetMapping("/removeByIds")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/removeByIds")
    @SystemLog(action = SphygmometerDeviceController.BASE_URL + "/removeByIds", operationType = Contants.DELETE_REQUEST, description = "删除血压计设备信息表")
    public Results removeByIds(@RequestParam("ids") String ids) {
        return Results.opResult(sphygmometerDeviceFeignClient.removeByIds(ids));
    }

    /**
     * 获取血压计设备信息表详细信息
     */
    @ApiOperation("统计血压数据")
    @GetMapping(value = "/countDevice")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/countDevice")
    public Results<SphygmometerDeviceCountVO> countDevice(HttpServletRequest request,@Valid SphygmometerDeviceCountParamVO sphygmometerDeviceCountParamVO){
        if(sphygmometerDeviceCountParamVO.getOrganizationId() == null || sphygmometerDeviceCountParamVO.getOrganizationId().equals("")){
            return Results.fail("机构ID为空");
        }
        //如果权限机构返回为空，说明该用户没有任何机构的权限，则返回空列表
        List<Integer> orgIdList = this.getFullAuthOrgIdList(sphygmometerDeviceCountParamVO.getOrganizationId());
        if (orgIdList.size() == 0) {
            return Results.success("您无权限查看此数据");
        }
        SphygmometerDeviceDTO sphygmometerDeviceDTO = BeanUtil.deepCopyProperties(sphygmometerDeviceCountParamVO, SphygmometerDeviceDTO.class);
        sphygmometerDeviceDTO.setOrganizationIds(Joiner.on(",").join(orgIdList));
        SphygmometerDeviceDTO resultDto = sphygmometerDeviceFeignClient.countDevice(sphygmometerDeviceDTO);
        SphygmometerDeviceCountVO sphygmometerDeviceCountVO = BeanUtil.deepCopyProperties(resultDto, SphygmometerDeviceCountVO.class);
        return Results.data(sphygmometerDeviceCountVO);
    }

    /**
     * 血压计启用禁用接口
     */
    @ApiOperation("血压计启用禁用接口")
    @GetMapping("/setEnableStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deviceId", value = "设备ID", dataType = "Integer", paramType = "query"),
            @ApiImplicitParam(name = "isEnabled", value = "是否启用  0:否  1：是", dataType = "Integer", paramType = "query"),
    })
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/setEnableStatus")
    public Results setEnableStatus(@RequestParam Integer deviceId, @RequestParam Integer isEnabled){
        SphygmometerDeviceDTO sphygmometerDeviceDTO = new SphygmometerDeviceDTO();
        sphygmometerDeviceDTO.setDeviceId(deviceId);
        sphygmometerDeviceDTO.setIsEnabled(isEnabled);
        return Results.opResult(this.sphygmometerDeviceFeignClient.edit(sphygmometerDeviceDTO));
    }

    /**
     * 批量添加
     */
    @ApiOperation("批量导入")
    @PostMapping("/batchAdd")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/batchAdd")
    public Results batchAdd(HttpServletRequest request,@RequestParam("files") MultipartFile files,Integer deviceType) throws IOException {
        BufferedInputStream bis = new BufferedInputStream(files.getInputStream());
        if(POIFSFileSystem.hasPOIFSHeader(bis)) {
            return Results.fail("文件格式不对，请使用2007以上版本");
        }
        XSSFWorkbook workbook = new XSSFWorkbook(files.getInputStream());
        int numberOfSheets = workbook.getNumberOfSheets();
        List<SphygmometerDeviceDTO> sphygmometerDeviceDTOS = new ArrayList<>();
        ManageUserDTO manageUserDTO = (ManageUserDTO) request.getAttribute("manageUser");
        String repeatDeviceNos = StringUtils.EMPTY;
        String repeatDeviceImeis = StringUtils.EMPTY;
        //解析Excel数据
        for (int i = 0; i < numberOfSheets; i++) {
            XSSFSheet sheet = workbook.getSheetAt(i);
            if(null != sheet && !sheet.toString().isEmpty()){
                XSSFRow titleRow =sheet.getRow(0);
                if(null == titleRow ||  titleRow.toString().isEmpty()){
                    continue;
                }
                XSSFCell titleCell = titleRow.getCell(0);
                if(null == titleCell ||  titleCell.toString().isEmpty()){
                    continue;
                }
                String title = getValue(titleCell);
                if((deviceType == 1 && title.equals("臂式血压计批量新增模板")) || (deviceType ==2 && title.equals("筒式血压计批量新增模板"))){
                    return Results.fail("表格格式有误，请检查是否正确");
                }
                int physicalNumberOfRows = sheet.getPhysicalNumberOfRows();
                for(int j =2;j<physicalNumberOfRows;j++){
                    XSSFRow row = sheet.getRow(j);
                    if(null != row && !row.toString().isEmpty()){
                        SphygmometerDeviceDTO sphygmometerDeviceDTO = new SphygmometerDeviceDTO();
                        //设备编号
                        XSSFCell cell = row.getCell(0);
                        if(null != cell && !cell.toString().isEmpty()){
                            String deviceNo = getValue(cell);
                            if(deviceNo.contains(".")){
                                String[] deviceNoArr = deviceNo.split("\\.");
                                deviceNo =deviceNoArr[0];
                            }
                            sphygmometerDeviceDTO.setDeviceNo(deviceNo);
                            if (deviceType == 2) {
                                // IMEI
                                cell = row.getCell(1);
                                String imei = getValue(cell);
                                //校验IMEI号合法性
                                if (!DeviceImeiUtils.isCorrectImei(imei)) {
                                	return Results.fail("imei:" + imei + "---不符合规则");
                                }
                                sphygmometerDeviceDTO.setDeviceImei(imei);
                            }
                            //设备型号
                            cell = row.getCell(deviceType == 1 ? 1 : 2);
                            sphygmometerDeviceDTO.setDeviceModel(getValue(cell));
                            //关联社康
                            cell = row.getCell(deviceType == 1 ? 2 : 3);
                            String relationOrgName = getValue(cell);
                            Results<OrganizationInfoV2> relationOrgResult = organizationInfoV2FeignClient.getInfoByName(relationOrgName);
                            if(relationOrgResult.getCode() == ResponseCodeEnum.SUCCESS_REQUEST.getCode() &&
                                    relationOrgResult.getData() == null){
                                return Results.fail("【" + relationOrgName + "】机构不存在");
                            }
                            sphygmometerDeviceDTO.setRelationOrgId(relationOrgResult.getData().getOrganizationId());
                            if (deviceType == 2) {
                                sphygmometerDeviceDTO.setOrganizationId(relationOrgResult.getData().getOrganizationId());
                            }
                            if (deviceType == 1) {
                                //投放机构
                                cell = row.getCell(3);
                                String organizationName = getValue(cell);
                                Results<OrganizationInfoV2> orgResult = organizationInfoV2FeignClient.getInfoByName(organizationName);
                                if(orgResult.getCode() == ResponseCodeEnum.SUCCESS_REQUEST.getCode() &&
                                        orgResult.getData() == null){
                                    return Results.fail("投放机构【" + organizationName + "】不存在");
                                }
                                //查出关联机构及其子集
                                GetAuthOrganizationV2DTO getAuthOrganizationV2DTO = new GetAuthOrganizationV2DTO();
                                getAuthOrganizationV2DTO.setAuthType(0);
                                Results<List<OrganizationInfoV2DTO>> results = organizationInfoV2FeignClient.getAuthOrgTreeList(getAuthOrganizationV2DTO);
                                List<OrganizationInfoV2DTO> orgList = results.getData();
                                OrganizationInfoV2DTO relativeOrgAndChildren = this.findOrgByTreeList(orgList, relationOrgResult.getData().getOrganizationId());
                                //验证投放机构是否关联机构本身或其子集
                                if (relativeOrgAndChildren.getOrganizationId().intValue() != orgResult.getData().getOrganizationId().intValue()) {
                                    OrganizationInfoV2DTO org = this.findOrgByTreeList(relativeOrgAndChildren.getChildren(), orgResult.getData().getOrganizationId());
                                    if (org == null) {
                                        return Results.fail("投放机构【" + organizationName + "】机构不是关联社康【" + relationOrgName + "】或其子机构");
                                    }
                                }
                                sphygmometerDeviceDTO.setOrganizationId(orgResult.getData().getOrganizationId());
                            }

                            //设备状态
                            cell = row.getCell(4);
                            Integer isEnable = 0;
                            String cellValue = getValue(cell);
                            if("禁用".equals(cellValue)){
                                isEnable = 0;
                            }else if("启用".equals(cellValue)){
                                isEnable = 1;
                            }
                            sphygmometerDeviceDTO.setIsEnabled(isEnable);
                            sphygmometerDeviceDTO.setIsOnline(0);
                            sphygmometerDeviceDTO.setDeviceType(deviceType);
                            sphygmometerDeviceDTO.setAddTime(new Date());
                            sphygmometerDeviceDTO.setAddUserId(manageUserDTO.getUserId());
                            sphygmometerDeviceDTOS.add(sphygmometerDeviceDTO);
                        }
                    }
                }
            }
        }
        // 1.检验IMEI是否有重复 --家庭式血压仪才需要检验是否重复
        if(deviceType ==2){
            String errMessage = checkDeviceImei(sphygmometerDeviceDTOS);
            // 2.如果有重复的IMEI,则重复的信息提示出去
            if(StringUtils.isNotBlank(errMessage)){
                return Results.fail(errMessage);
            }
        }

        String unCorrectDeviceMode = StringUtils.EMPTY;
        String unCorrectDeviceNo = StringUtils.EMPTY;
        List<SphygmometerDeviceDTO> addList = new ArrayList<>();
        for (SphygmometerDeviceDTO sphygmometerDeviceDTO : sphygmometerDeviceDTOS) {
            if(sphygmometerDeviceDTO.getDeviceModel().length()>50){
                unCorrectDeviceMode = sphygmometerDeviceDTO.getDeviceModel()+",";
            }
            if(sphygmometerDeviceDTO.getDeviceNo().length()>50){
                unCorrectDeviceNo = sphygmometerDeviceDTO.getDeviceNo()+",";
            }
            //判断设备编号
            SphygmometerDeviceDTO check1 = this.sphygmometerDeviceFeignClient.getInfoByDeviceNo(sphygmometerDeviceDTO.getDeviceNo());
            //判断设备IMEI
            SphygmometerDeviceDTO check2 = this.sphygmometerDeviceFeignClient.getInfoByDeviceImei(sphygmometerDeviceDTO.getDeviceImei());
            if (check1 == null && check2 == null) {
                addList.add(sphygmometerDeviceDTO);
            } else {
                if (check1 != null && StringUtils.isNotBlank(check1.getDeviceNo())) {
                    repeatDeviceNos += check1.getDeviceNo() + ",";
                } else if (check2 != null && StringUtils.isNotBlank(check2.getDeviceImei())) {
                    repeatDeviceImeis += check2.getDeviceImei() + ",";
                }
            }
        }
        String retValue = StringUtils.EMPTY;
        if(StringUtils.isNotBlank(unCorrectDeviceMode)){
            retValue += "设备型号: " + unCorrectDeviceMode + "错误;";
        }
        if(StringUtils.isNotBlank(unCorrectDeviceNo)){
            retValue += "设备编号: " + unCorrectDeviceNo + "错误;";
        }
        if (StringUtils.isNotBlank(repeatDeviceNos)) {
            retValue += "设备编号: " + repeatDeviceNos + "重复;";
        }
        if (StringUtils.isNotBlank(repeatDeviceImeis)) {
            retValue += "设备IMEI: " + repeatDeviceImeis + "重复;";
        }
        if (StringUtils.isNotBlank(retValue)) {
            return Results.fail(retValue);
        } else {
            String result = sphygmometerDeviceFeignClient.batchAdd(addList);
            return Results.success(result);
        }
    }

    private OrganizationInfoV2DTO findOrgByTreeList(List<OrganizationInfoV2DTO> orgList, Integer organizationId) {
        for (OrganizationInfoV2DTO org : orgList) {
            if (org.getOrganizationId().intValue() == organizationId.intValue()) {
                return org;
            } else {
                OrganizationInfoV2DTO orgChildren = this.findOrgByTreeList(org.getChildren(), organizationId);
                if (orgChildren != null && orgChildren.getOrganizationId() != null) {
                    return orgChildren;
                }
            }
        }
        return null;
    }


    /**
     * 获取二维码
     */
    @ApiOperation("获取二维码")
    @GetMapping(value = "/getQrcode")
    @AuthAnnotation(action = SphygmometerDeviceController.BASE_URL + "/getQrcode")
    public Results<String> getQrcode(@RequestParam("deviceNo") String deviceNo) {
        try {
            WxMpQrCodeTicket wxMpQrCodeTicket = this.wxMpService.getQrcodeService().qrCodeCreateLastTicket("qrscene_wxapp_" + deviceNo);
            String url = this.wxMpService.getQrcodeService().qrCodePictureUrl(wxMpQrCodeTicket.getTicket(), true);
            SphygmometerDeviceDTO sphygmometerDeviceDTO = sphygmometerDeviceFeignClient.getInfoByDeviceNo(deviceNo);
            String realUrl = getQrCodeInfo(url);
            sphygmometerDeviceDTO.setWxQrCodeUrl(realUrl);
            sphygmometerDeviceFeignClient.edit(sphygmometerDeviceDTO);
            return Results.data(url);
        } catch (WxErrorException e) {
            log.error(e.getMessage(), e);
        }
        return Results.fail("获取二维码失败");
    }


    private String getQrCodeInfo(String url){
        MultiFormatReader multiFormatReader=new MultiFormatReader();
        Map hints=new HashMap();
        hints.put(EncodeHintType.CHARACTER_SET,"GBK");
        try{
            BufferedImage source= getRemoteBufferedImage(url);
            BinaryBitmap binaryImg=new BinaryBitmap(new HybridBinarizer(new BufferedImageLuminanceSource(source)));
            Result result=multiFormatReader.decode(binaryImg,hints);
            return result.getText();
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 获取远程网络图片信息
     * @param imageURL
     * @return
     */
    public static BufferedImage getRemoteBufferedImage(String imageURL) {
        URL url = null;
        InputStream is = null;
        BufferedImage bufferedImage = null;
        try {
            url = new URL(imageURL);
            is = url.openStream();
            bufferedImage = ImageIO.read(is);
        } catch (MalformedURLException e) {
            e.printStackTrace();
            System.out.println("imageURL: " + imageURL + ",无效!");
            return null;
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("imageURL: " + imageURL + ",读取失败!");
            return null;
        } finally {
            try {
                if (is!=null) {
                    is.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
                System.out.println("imageURL: " + imageURL + ",流关闭异常!");
                return null;
            }
        }
        return bufferedImage;
    }


    /**
     * 不同类型对应不同的取值范围
     * @param cell
     * @return
     */
    private String getValue(XSSFCell cell) {
        if (cell == null) {
            return "";
        }
        String strCell = "";
        switch (cell.getCellType()) {
            case HSSFCell.CELL_TYPE_STRING:
                strCell = cell.getStringCellValue();
                break;
            case HSSFCell.CELL_TYPE_NUMERIC:
                strCell = String.valueOf(cell.getNumericCellValue());
                break;
            case HSSFCell.CELL_TYPE_BOOLEAN:
                strCell = String.valueOf(cell.getBooleanCellValue());
                break;
            case HSSFCell.CELL_TYPE_BLANK:
                strCell = "";
                break;
            default:
                strCell = "";
                break;
        }
        if (strCell.equals("") || strCell == null) {
            return "";
        }
        return strCell;
    }


    /**
     * 检验IMEI是否有重复
     * @param sphygmometerDeviceDTOS
     * @return
     */
    private String checkDeviceImei(List<SphygmometerDeviceDTO> sphygmometerDeviceDTOS) {
        StringBuffer stringBuffer = new StringBuffer();
        int row = 2;
        HashSet set1 = new HashSet();
        HashSet set2 = new HashSet();
        for (SphygmometerDeviceDTO sphygmometerDeviceDTO : sphygmometerDeviceDTOS) {
            row++;
            // 添加成功返回true 添加失败返回false
            if (sphygmometerDeviceDTO.getDeviceImei().length() != 15) {
                stringBuffer.append("设备的IMEI: ")
                        .append(sphygmometerDeviceDTO.getDeviceImei())
                        .append(" 第 ")
                        .append(row)
                        .append(" 行不能低于15位数字格式;");
            }
            if (!set2.add(sphygmometerDeviceDTO.getDeviceNo())) {
                stringBuffer.append("设备的设备编号: ")
                        .append(sphygmometerDeviceDTO.getDeviceNo())
                        .append(" 与上传文件的第 ")
                        .append(row)
                        .append(" 行重复;");
            }
            if (!set1.add(sphygmometerDeviceDTO.getDeviceImei())) {
                stringBuffer.append("设备的IMEI: ")
                        .append(sphygmometerDeviceDTO.getDeviceImei())
                        .append(" 与上传文件的第 ")
                        .append(row)
                        .append(" 行重复;");
            }
        }
        return stringBuffer.toString();
    }

//    public Result print
}
