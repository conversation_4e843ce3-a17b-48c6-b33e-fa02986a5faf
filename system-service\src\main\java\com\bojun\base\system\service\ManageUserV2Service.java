package com.bojun.base.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.ManageUserV2DTO;
import com.bojun.system.entity.ManageUserLogin;
import com.bojun.system.entity.ManageUserV2;

import java.util.List;

/**
 * ManageUserV2Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-05 16:05:04
 */
public interface ManageUserV2Service extends IService<ManageUserV2>
{
    /**
     * 查询系统管理员用户
     * 
     * @param userId 系统管理员用户ID
     * @return 系统管理员用户
     */
    public ManageUserV2DTO selectManageUserById(Integer userId);

    /**
     * 查询系统管理员用户列表
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 系统管理员用户集合
     */
    public List<ManageUserV2DTO> selectManageUserList(ManageUserV2DTO manageUserDTO);

    /**
     * 新增系统管理员用户
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 结果
     */
    public int insertManageUser(ManageUserV2DTO manageUserDTO);

    /**
     * 修改系统管理员用户
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 结果
     */
    public int updateManageUser(ManageUserV2DTO manageUserDTO);
    
    /**
     * 新增系统管理员用户
     * 
     * @param manageUser 系统管理员用户
     * @return 结果
     */
    public int insertManageUser(ManageUserV2 manageUser);

    /**
     * 修改系统管理员用户
     * 
     * @param manageUser 系统管理员用户
     * @return 结果
     */
    public int updateManageUser(ManageUserV2 manageUser);

    /**
     * 批量删除系统管理员用户
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteManageUserByIds(String ids);

    /**
     * 删除系统管理员用户信息
     * 
     * @param userId 系统管理员用户ID
     * @return 结果
     */
    public int deleteManageUserById(Integer userId);

    public ManageUserLogin selectLastUserLoginById(Integer userId, String systemId);

}
