/**
 * 
 */
package com.bojun.base.manage.controller.systemDict.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：产品管理
 * Description：禁用、启动产品接口参数
 * Author：赖水秀
 * created： 2020年4月29日
 */
@ApiModel(value = "禁用、启动产品接口参数", description = "禁用、启动产品接口参数传入信息")
public class UpdateSystemDictStatusPramVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8107667132631956342L;

	@NotEmpty(message = "系统ID不能为空")
	@ApiModelProperty(value="系统id", required = true, example = "S001")
	private String systemId;	
	
	@NotNull(message = "状态不能为空")
	@ApiModelProperty(value="是否启用  0：否  1:是  ", required = true, example = "1")
	private Integer isEnabled;

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}
	
}
