package com.bojun.sphygmometer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.exceptions.ApiException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.common.controller.exception.ServerException;
import com.bojun.push.GeTuiPushUtils;
import com.bojun.sphygmometer.common.PushTypeEnum;
import com.bojun.sphygmometer.dto.AppMessageNotificationDTO;
import com.bojun.sphygmometer.dto.UserTestPlanDTO;
import com.bojun.sphygmometer.dto.UserTestPlanTimeDTO;
import com.bojun.sphygmometer.entity.SphygmometerUser;
import com.bojun.sphygmometer.entity.UserTestPlan;
import com.bojun.sphygmometer.entity.UserTestPlanTime;
import com.bojun.sphygmometer.mapper.UserTestPlanMapper;
import com.bojun.sphygmometer.service.AppMessageNotificationService;
import com.bojun.sphygmometer.service.SphygmometerUserService;
import com.bojun.sphygmometer.service.UserTestPlanService;
import com.bojun.sphygmometer.service.UserTestPlanTimeService;
import com.bojun.utils.BeanUtil;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import com.bojun.utils.SpringUtils;
import com.gexin.rp.sdk.base.IPushResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * UserTestPlanService业务层处理
 *
 * <AUTHOR>
 * @date 2021-05-27 16:25:30
 */
@Service
@Slf4j
public class UserTestPlanServiceImpl extends ServiceImpl<UserTestPlanMapper, UserTestPlan> implements UserTestPlanService {

    @Autowired
    private UserTestPlanTimeService userTestPlanTimeService;

    @Autowired
    private SphygmometerUserService sphygmometerUserService;

    /**
     * 查询用户检测计划
     *
     * @param id 用户检测计划ID
     * @return 用户检测计划
     */
    @Override
    public UserTestPlanDTO selectUserTestPlanById(Integer id) {
        return setPlanDetailList(BeanUtil.deepCopyProperties(this.getById(id), UserTestPlanDTO.class));
    }

    @Override
    public UserTestPlanDTO selectUserTestPlanByUserId(Integer userId) {
        return setPlanDetailList(BeanUtil.deepCopyProperties(this.getOne(Wrappers.<UserTestPlan>lambdaQuery()
                .eq(UserTestPlan::getUserId, userId).eq(UserTestPlan::getIsDelete, 0)), UserTestPlanDTO.class));
    }

    private UserTestPlanDTO setPlanDetailList(UserTestPlanDTO testPlanDTO) {
        if (testPlanDTO == null) {
            return null;
        }
        if (testPlanDTO.getIsDelete() == 1) {
            throw new ApiException("该计划已删除");
        }
        UserTestPlanTimeDTO userTestPlanTimeDTO = new UserTestPlanTimeDTO();
        userTestPlanTimeDTO.setUserTestPlanId(testPlanDTO.getId());
        userTestPlanTimeDTO.setIsDelete(0);
        testPlanDTO.setTestPlanTimeList(BeanUtil.deepCopyProperties(userTestPlanTimeService.selectUserTestPlanTimeList(userTestPlanTimeDTO),
                UserTestPlanTimeDTO.class));
        return testPlanDTO;
    }

    /**
     * 查询用户检测计划列表
     *
     * @param userTestPlanDTO 用户检测计划
     * @return 用户检测计划集合
     */
    @Override
    public List<UserTestPlanDTO> selectUserTestPlanList(UserTestPlanDTO userTestPlanDTO) {
        userTestPlanDTO.setIsDelete(0);
        List<UserTestPlanDTO> userTestPlanDTOList = this.baseMapper.selectUserTestPlanList(userTestPlanDTO);
        userTestPlanDTOList.forEach(this::setPlanDetailList);
        return userTestPlanDTOList;
    }

    /**
     * 新增用户检测计划
     *
     * @param userTestPlanDTO 用户检测计划
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserTestPlanDTO insertUserTestPlan(UserTestPlanDTO userTestPlanDTO) throws ServerException {
        int count = count(Wrappers.<UserTestPlan>lambdaQuery()
                .eq(UserTestPlan::getUserId, userTestPlanDTO.getUserId()).eq(UserTestPlan::getIsDelete, 0));
        if (count > 0) {
            throw new ServerException("该用户已存在检测计划");
        }
        userTestPlanDTO.setPlanStatus(1);
        int num = this.baseMapper.insert(userTestPlanDTO);
        List<UserTestPlanTimeDTO> testPlanTimeList = userTestPlanDTO.getTestPlanTimeList();
        List<UserTestPlanTime> timeList = testPlanTimeList.stream()
                .map(time -> {
                    time.setUserId(userTestPlanDTO.getUserId());
                    time.setUserTestPlanId(userTestPlanDTO.getId());
                    time.setIsDelete(0);
                    return BeanUtil.deepCopyProperties(time, UserTestPlanTime.class);
                }).collect(Collectors.toList());
        userTestPlanTimeService.saveBatch(timeList);
        return userTestPlanDTO;
    }

    /**
     * 修改用户检测计划
     *
     * @param userTestPlanDTO 用户检测计划
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserTestPlanDTO updateUserTestPlan(UserTestPlanDTO userTestPlanDTO) throws ServerException {
        // 把原先的删除
        update(Wrappers.<UserTestPlan>lambdaUpdate().eq(UserTestPlan::getId, userTestPlanDTO.getId()).set(UserTestPlan::getIsDelete, 1)
                .set(UserTestPlan::getUpdateUserId, userTestPlanDTO.getUpdateUserId())
                .set(UserTestPlan::getUpdateUserName, userTestPlanDTO.getUpdateUserName())
                .set(UserTestPlan::getUpdateTime, userTestPlanDTO.getUpdateTime()));
        userTestPlanTimeService.update(Wrappers.<UserTestPlanTime>lambdaUpdate().eq(UserTestPlanTime::getUserTestPlanId, userTestPlanDTO.getId())
                .set(UserTestPlanTime::getIsDelete, 1)
                .set(UserTestPlanTime::getUserTestPlanId, userTestPlanDTO.getId())
                .set(UserTestPlanTime::getUpdateUserName, userTestPlanDTO.getUpdateUserName())
                .set(UserTestPlanTime::getUpdateTime, userTestPlanDTO.getUpdateTime()));
        userTestPlanDTO.setId(null);
        userTestPlanDTO.setCreateUserId(userTestPlanDTO.getUpdateUserId());
        userTestPlanDTO.setCreateUserName(userTestPlanDTO.getUpdateUserName());
        userTestPlanDTO.setCreateTime(userTestPlanDTO.getUpdateTime());
        List<UserTestPlanTimeDTO> testPlanTimeList = userTestPlanDTO.getTestPlanTimeList();
        StringBuilder sb = new StringBuilder();
        testPlanTimeList.forEach(time -> {
            sb.append("[").append(time.getStartTime()).append(" - ").append(time.getEndTime()).append("] 、");
            time.setId(null);
            time.setUserTestPlanId(null);
            time.setCreateUserId(userTestPlanDTO.getUpdateUserId());
            time.setCreateUserName(userTestPlanDTO.getUpdateUserName());
            time.setCreateTime(userTestPlanDTO.getUpdateTime());
        });
        UserTestPlanDTO testPlanDTO = insertUserTestPlan(userTestPlanDTO);
        // 发送健康计划修改通知
        String msg = String.format(CHANGE_PLAN_MESSAGE_TEMP, userTestPlanDTO.getOrganizationName(), userTestPlanDTO.getUpdateUserName(), userTestPlanDTO.getRoleName(),
                testPlanTimeList.size(), sb.substring(0, sb.length() - 1));
        SphygmometerUser user = sphygmometerUserService.getById(userTestPlanDTO.getUserId());
        if (StringUtils.isBlank(user.getGeTuiClientId())) {
            log.info("CHANGE_PLAN_MESSAGE user.getGeTuiClientId() is blank, with userId =" + user.getUserId());
            return testPlanDTO;
        }
        Integer mgType = PushTypeEnum.HEALTH_PLAN_MODIFY.getMgType();
        String comment = PushTypeEnum.HEALTH_PLAN_MODIFY.getComment();
        //将消息保存至数据库
        AppMessageNotificationDTO appMessageNotification = new AppMessageNotificationDTO();
        appMessageNotification.setIsDelete(0);
        appMessageNotification.setStatus(1);
        appMessageNotification.setMsgType(mgType);
        Date currentDate = new Date();
        appMessageNotification.setPublishTime(currentDate);
        appMessageNotification.setCreateTime(currentDate);
        appMessageNotification.setUserId(user.getUserId());
        appMessageNotification.setTitle(comment);
        appMessageNotification.setNoticeContent(msg);
        AppMessageNotificationService appMessageNotificationService = SpringUtils.getBean(AppMessageNotificationService.class);
        appMessageNotificationService.insertAppMessageNotification(appMessageNotification);
        JSONObject paramJson = new JSONObject();
        paramJson.put("msgType",mgType);
        paramJson.put("noticeId",appMessageNotification.getNoticeId());
        boolean pushResult = GeTuiPushUtils.pushMsgToSingle(user.getGeTuiClientId(), comment, msg, paramJson.toJSONString());
        if (pushResult) {
            // 发送成功
            log.info("send CHANGE_PLAN_MESSAGE ok, with planId = [{}] content = {}",userTestPlanDTO.getId() , msg);
        } else {
            // 发送失败
            log.info("send CHANGE_PLAN_MESSAGE ex, with planId = [{}] content = {}",userTestPlanDTO.getId() , msg);
        }
        return testPlanDTO;
    }

    private final static String CHANGE_PLAN_MESSAGE_TEMP = "[%s-%s-%s]已将您的血压检测计划进行了调整，根据最新的健康管理计划，" +
            "建议您每天检测[%s]次，检测时间段为：%s，请记得按时测量血压哦。";

    /**
     * 新增用户检测计划
     *
     * @param userTestPlan 用户检测计划
     * @return 结果
     */
    @Override
    public int insertUserTestPlan(UserTestPlan userTestPlan) {
        userTestPlan.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(userTestPlan);
    }

    /**
     * 修改用户检测计划
     *
     * @param userTestPlan 用户检测计划
     * @return 结果
     */
    @Override
    public int updateUserTestPlan(UserTestPlan userTestPlan) {
        userTestPlan.setUpdateTime(DateUtils.getNowDate());
        return this.baseMapper.updateById(userTestPlan);
    }

    /**
     * 删除用户检测计划对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteUserTestPlanByIds(String ids) {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除用户检测计划信息
     *
     * @param id 用户检测计划ID
     * @return 结果
     */
    @Override
    public int deleteUserTestPlanById(Integer id) {
        return this.removeById(id) ? 1 : 0;
    }

    @Override
    public int getManageDTOCount(List<Integer> orgIdList, String startTime, String endTime) {
        return this.baseMapper.getManageDTOCount(orgIdList, startTime, endTime);
    }
}
