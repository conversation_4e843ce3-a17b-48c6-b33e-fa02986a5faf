package com.bojun.organization.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.organization.dto.GetAuthOrganizationV2DTO;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;

import java.util.List;

/**
 * OrganizationInfoService接口
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
public interface OrganizationInfoV2Service extends IService<OrganizationInfoV2>
{
    /**
     * 查询机构信息表
     * 
     * @param organizationId 机构信息表ID
     * @return 机构信息表
     */
    public OrganizationInfoV2DTO selectOrganizationInfoById(Integer organizationId);

    /**
     * 查询机构信息表列表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 机构信息表集合
     */
    public List<OrganizationInfoV2DTO> selectOrganizationInfoList(OrganizationInfoV2DTO organizationInfoDTO);

    /**
     * 新增机构信息表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 结果
     */
    public int insertOrganizationInfo(OrganizationInfoV2DTO organizationInfoDTO);

    /**
     * 修改机构信息表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 结果
     */
    public int updateOrganizationInfo(OrganizationInfoV2DTO organizationInfoDTO);
    
    /**
     * 新增机构信息表
     * 
     * @param organizationInfo 机构信息表
     * @return 结果
     */
    public int insertOrganizationInfo(OrganizationInfoV2 organizationInfo);

    /**
     * 修改机构信息表
     * 
     * @param organizationInfo 机构信息表
     * @return 结果
     */
    public int updateOrganizationInfo(OrganizationInfoV2 organizationInfo);

    /**
     * 批量删除机构信息表
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteOrganizationInfoByIds(String ids);

    /**
     * 删除机构信息表信息
     * 
     * @param organizationId 机构信息表ID
     * @return 结果
     */
    public int deleteOrganizationInfoById(Integer organizationId);

    /**
     * 获取有权限(不含半选)的机构列表, 非树形, 主要供后端使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    List<OrganizationInfoV2DTO> getFullAuthOrgList(GetAuthOrganizationV2DTO getAuthOrganizationV2DTO);

    /**
     * 获取有权限(含半选)的机构树形列表, 供前端树形列表使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    List<OrganizationInfoV2DTO> getAuthOrgTreeList(GetAuthOrganizationV2DTO getAuthOrganizationV2DTO);
}
