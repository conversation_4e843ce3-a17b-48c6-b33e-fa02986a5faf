<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.health.promotion.mapper.TopicInfoMapper">
  <resultMap id="BaseResultMap" type="com.bojun.health.promotion.common.dto.TopicInfoDTO">
    <id column="topic_id" jdbcType="INTEGER" property="topicId" />
    <result column="parent_topic_id" jdbcType="INTEGER" property="parentTopicId" />
    <result column="topic_name" jdbcType="VARCHAR" property="topicName" />
    <result column="show_index" jdbcType="INTEGER" property="showIndex" />
    <result column="is_enabled" jdbcType="INTEGER" property="isEnabled" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="update_user_id" jdbcType="INTEGER" property="updateUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="system_id" jdbcType="VARCHAR" property="systemId" />
  </resultMap>
  <sql id="Base_Column_List">
    topic_id, parent_topic_id, topic_name, show_index, is_enabled, is_delete, update_user_id,
    update_time, create_user_id, create_time,system_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_topic_info
    where topic_id = #{topicId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_topic_info
    where topic_id = #{topicId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.bojun.health.promotion.common.dto.TopicInfoDTO">
    insert into t_topic_info (topic_id, parent_topic_id, topic_name,
      show_index, is_enabled, is_delete,
      update_user_id, update_time, create_user_id,
      create_time,system_id)
    values (#{topicId,jdbcType=INTEGER}, #{parentTopicId,jdbcType=INTEGER}, #{topicName,jdbcType=VARCHAR},
      #{showIndex,jdbcType=INTEGER}, #{isEnabled,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER},
      #{updateUserId,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER},
      #{createTime,jdbcType=TIMESTAMP},#{systemId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bojun.health.promotion.common.dto.TopicInfoDTO">
    insert into t_topic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="topicId != null">
        topic_id,
      </if>
      <if test="parentTopicId != null">
        parent_topic_id,
      </if>
      <if test="topicName != null">
        topic_name,
      </if>
      <if test="showIndex != null">
        show_index,
      </if>
      <if test="isEnabled != null">
        is_enabled,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUserId != null">
        create_user_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="topicId != null">
        #{topicId,jdbcType=INTEGER},
      </if>
      <if test="parentTopicId != null">
        #{parentTopicId,jdbcType=INTEGER},
      </if>
      <if test="topicName != null">
        #{topicName,jdbcType=VARCHAR},
      </if>
      <if test="showIndex != null">
        #{showIndex,jdbcType=INTEGER},
      </if>
      <if test="isEnabled != null">
        #{isEnabled,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.bojun.health.promotion.common.dto.TopicInfoDTO">
    update t_topic_info
    <set>
      <if test="parentTopicId != null">
        parent_topic_id = #{parentTopicId,jdbcType=INTEGER},
      </if>
      <if test="topicName != null">
        topic_name = #{topicName,jdbcType=VARCHAR},
      </if>
      <if test="showIndex != null">
        show_index = #{showIndex,jdbcType=INTEGER},
      </if>
      <if test="isEnabled != null">
        is_enabled = #{isEnabled,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null">
        create_user_id = #{createUserId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where topic_id = #{topicId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bojun.health.promotion.common.dto.TopicInfoDTO">
    update t_topic_info
    set parent_topic_id = #{parentTopicId,jdbcType=INTEGER},
      topic_name = #{topicName,jdbcType=VARCHAR},
      show_index = #{showIndex,jdbcType=INTEGER},
      is_enabled = #{isEnabled,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=INTEGER},
      update_user_id = #{updateUserId,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user_id = #{createUserId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where topic_id = #{topicId,jdbcType=INTEGER}
  </update>

  <select id="getTopicInfo" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_topic_info
    where is_delete= 0
    <if test="isEnabled != null">
      and  is_enabled = #{isEnabled,jdbcType=INTEGER}
    </if>
    and system_id = #{systemId}
    order by show_index asc
  </select>

  <select id="getChildrenTopicByParentId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_topic_info
    where is_delete= 0
    <if test="parentId != null">
      and  parent_topic_id = #{parentId,jdbcType=INTEGER}
    </if>
    and system_id = #{systemId}
  </select>
</mapper>