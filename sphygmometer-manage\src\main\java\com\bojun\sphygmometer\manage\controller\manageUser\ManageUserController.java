/**
 * 
 */
package com.bojun.sphygmometer.manage.controller.manageUser;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.sphygmometer.manage.api.system.IManageUserService;
import com.bojun.sphygmometer.manage.controller.manageUser.vo.UpdateManageUserVO;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.entity.ManageUser;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
 * 
 * Model：用户管理
 * Description：用户管理
 * Author：lj
 * created： 2020年4月27日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("manageUser")
@Api(tags = {"用户管理模块接口"})
@ApiSort(value = 3)
public class ManageUserController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(ManageUserController.class);
	
	@Autowired
	private IManageUserService manageUserService;
	
	 
	/**
	  * 
	  * @Description 修改密码
	  * <AUTHOR>
	  * @param request
	  * @param paramsMap
	  * void
	  * 2020年6月2日
	  */
	 @RequestMapping(value="/updateUserPasswords", method = RequestMethod.POST)
	 @AuthAnnotation(action = "updateUserPasswords")
	 @ApiOperation(value = "修改管理员密码", notes = "修改密码（lj）")
	 @ApiImplicitParams({
	        @ApiImplicitParam(name = "newPwd",value = "新密码", dataType = "String", paramType = "query"),
	        @ApiImplicitParam(name = "oldPwd",value = "旧密码", dataType = "String", paramType = "query")
	 })
	 @ApiOperationSupport(order = 3)
	 @SystemLog(action = "updateUserPasswords", description = "修改管理员密码", operationType = Contants.UPDATE_REQUEST)
	 public Results updateUserPasswords(HttpServletRequest request,
	   @RequestParam(value = "newPwd", required = true) String newPwd,
	   @RequestParam(value = "oldPwd", required = true) String oldPwd) {
	  try {
	   String token = request.getHeader("token");
	   ManageUserDTO manageUser = new ManageUserDTO();
	   manageUser.setOldPwd(oldPwd);
	   manageUser.setNewPwd(newPwd);
	   manageUser.setHanderToken(token);
	   // 请求system-service服务修改用户信息
	   String result = manageUserService.updateUserPasswords(manageUser);
	   return returnResults(result);
	  } catch (RuntimeException e) {
	   logger.error("updateUserPasswords", e);
	   return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
	  } catch (Exception e) {
	   logger.error("updateUserPasswords:", e);
	   return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
	  }
	 }

	 /**
		 * @Description 编辑用户信息
		 * <AUTHOR>
		 * @return
		 * Results<updateManageUser>
		 * 2020年4月27日
		 */
		@ApiOperation(value = "编辑用户", notes = "编辑用户信息（lj）")
		@ApiOperationSupport(order = 3)
		@RequestMapping(value="/updateManageUser", method = RequestMethod.POST)
		@SystemLog(action = "updateManageUser", description = "新增用户", operationType = Contants.UPDATE_REQUEST)
		@AuthAnnotation(action = "updateManageUser")
		public Results updateManageUser(HttpServletRequest request, @RequestBody @Valid UpdateManageUserVO addManageUser) {
			try {
				ManageUser manageUser = new ManageUser();
				BeanUtils.copyProperties(addManageUser, manageUser);
				String result = manageUserService.updateManageUser(manageUser);
				return returnResults(result);
			} catch (RuntimeException e) {
				logger.error("updateManageUser:", e);
				return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
			} catch (Exception e) {
				logger.error("updateManageUser:", e);
				return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
			}
		}

	/**
	 *
	 */
	@ApiOperation(value = "查询运维权限和角色权限用户", notes = "林伟")
	@ApiOperationSupport(order = 4)
	@SystemLog(action = "getManageRoleUser", description = "查询运维权限和角色权限用户", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "getManageRoleUser")
	@RequestMapping(value = "/getManageRoleUser", method = RequestMethod.POST)
	public Results<List<ManageUserDTO>>  getManageRoleUser() {
		try {
			String result = manageUserService.getManageRoleUser();
			// 解析结果
//			String orgFullAuthDeptJsonStr = getJsonStr(result);
//			List<ManageUserDTO> deptDTOList = JSONArray.parseArray(orgFullAuthDeptJsonStr, ManageUserDTO.class);
			return returnResults(result);
		} catch (Exception e) {
			logger.error("getManageRoleUser:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
}
