package com.bojun.base.manage.controller.rongnotice.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知vo类
 * @Description: 基础控台融云消息通知vo类
 * @since 2020-12-04
 */
@Data
@TableName("t_rong_notice")
@ApiModel(value = "添加RongNoticeVo对象", description = "基础控台融云消息通知")
public class AddRongNoticeParamVo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "消息标题", example = " ")
    private String title; //消息标题

    @ApiModelProperty(value = "消息通知类型id", example = " ")
    private String noticeTypeId; //消息通知类型id

    @ApiModelProperty(value = "推送产品 1.全部 2.患者移动端，3医生移动端", example = " ")
    private Integer pushProducts; //推送产品 1.全部 2.患者移动端，3医生移动端

    @ApiModelProperty(value = "是否立即发送 1:是 0:否", example = " ")
    private Integer isImmediately; //是否立即发送 1:是 0:否

    @ApiModelProperty(value = "定时发布时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timingTime; //定时发布时间

    @ApiModelProperty(value = "状态：1：已发布 2：待发布  3：已撤回   4：重发", example = " ")
    private Integer status; //状态：1：已发布 2：待发布  3：已撤回   4：重发

    @ApiModelProperty(value = "是否删除 1:是 0:否", example = " ")
    private Integer isDelete; //是否删除 1:是 0:否

    @ApiModelProperty(value = "删除时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime; //删除时间

    @ApiModelProperty(value = "发布时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime; //发布时间

    @ApiModelProperty(value = "创建（发布）人用户id", example = " ")
    private Integer createUserId; //创建（发布）人用户id

    @ApiModelProperty(value = "创建时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime; //创建时间

    private String noticeContent; //消息通知id(uuid)
}
