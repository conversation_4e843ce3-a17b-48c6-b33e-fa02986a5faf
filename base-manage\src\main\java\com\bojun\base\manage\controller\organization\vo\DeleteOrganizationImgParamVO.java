package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：机构管理
*Description：机构图片信息
*Author: 赖水秀
*created：2020年5月8日
*/
@ApiModel(value = "删除机构图片信息", description = "删除机构图片信息入参")
public class DeleteOrganizationImgParamVO implements Serializable{
	
	private static final long serialVersionUID = 109958065299895997L;
	
	@NotNull(message = "机构id")
	@ApiModelProperty(value="机构id", required = true, example = "10003")
	private Integer organizationId;
	
	@NotNull(message = "图片id")
	@ApiModelProperty(value="图片id", required = true, example = "10000")	
	private Integer id;

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}	
	
}

