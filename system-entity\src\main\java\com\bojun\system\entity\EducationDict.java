/**
 * 
 */
package com.bojun.system.entity;

import java.io.Serializable;

/**
*Model：学历信息表
*Description：EnducationDict.java
*Author: 曾玲玲
*created：2020年1月9日
*/
public class EducationDict implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 3697277025967969456L;
	
	private Integer id;
	
	//学历code
	private String educationCode;
	
	//学历名称
	private String educationName;
	
	//学历权重 （数字越大，对应的学历级别越高
	private Integer weight;
	
	//是否启用 0：否 1：是
	private Integer isEnabled;
	
	private String color;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getColor() {
		return color;
	}

	public void setColor(String color) {
		this.color = color;
	}

	public String getEducationCode() {
		return educationCode;
	}

	public void setEducationCode(String educationCode) {
		this.educationCode = educationCode;
	}

	public String getEducationName() {
		return educationName;
	}

	public void setEducationName(String educationName) {
		this.educationName = educationName;
	}

	
}
