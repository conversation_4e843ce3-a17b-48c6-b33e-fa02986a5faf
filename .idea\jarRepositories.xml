<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="maven-public" />
      <option name="name" value="maven-public" />
      <option name="url" value="http://bj-ihealthcare.com:56787/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="getui-nexus" />
      <option name="name" value="getui-nexus" />
      <option name="url" value="http://mvn.gt.getui.com/nexus/content/repositories/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://repo.maven.apache.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-releases" />
      <option name="name" value="Spring Releases" />
      <option name="url" value="https://repo.spring.io/libs-release" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="getui-nexus" />
      <option name="name" value="Getui Maven Repository" />
      <option name="url" value="http://mvn.gt.getui.com/nexus/content/repositories/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-aliyun" />
      <option name="name" value="Nexus aliyun" />
      <option name="url" value="http://maven.aliyun.com/nexus/content/groups/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://bj-ihealthcare.com:56787/repository/maven-public/" />
    </remote-repository>
  </component>
</project>