package com.bojun.base.manage.api.system;

import com.bojun.base.manage.api.system.hystrix.SystemOperationLogServiceHystrix;
import com.bojun.system.dto.OperationLogDTO;
import com.bojun.system.entity.OperationLog;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name="system-service", fallback = SystemOperationLogServiceHystrix.class)
public interface ISystemOperationService {
    /**
     * @Description 获取日志记录列表11
     * <AUTHOR>
     * @return
     * List<OperationLogDTO>
     * 2020年4月27日
     */
    @RequestMapping(value = "/getSystemLogList", method = RequestMethod.POST)
    String getSystemLogList(OperationLogDTO operationLogDTO);
    
    /**
     * @Description 新增系统日志信息
     * <AUTHOR>
     * @param operationLog
     * @return
     * @return int
     * created：2020年6月4日
     */
    @RequestMapping(value = "/addSystemLog", method = RequestMethod.POST)
    String addSystemLog(OperationLog operationLog);
}
