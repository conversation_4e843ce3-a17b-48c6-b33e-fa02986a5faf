package com.bojun.system.entity;

import java.io.Serializable;

/**
 * 
*Model：消息通知接收人信息表
*Description：消息通知接收人信息表实体类
*Author:李欣颖
*created：2020年1月7日
 */
public class MessageNotificationReceiver implements Serializable {

   private static final long serialVersionUID = -1344026328907379635L;


      private Integer id; // 
      private String noticeId; // 消息通知id
      private Integer receiveUserId; // 接收人用户id
      private java.sql.Date receiveTime; // 接收时间
      private Integer isRead; // 是否已读  0：未读  1：已读
      private java.sql.Date readTime; // 读取时间
      private Integer isDelete; // 是否删除  0：否  1：是
      private java.sql.Date deleteTime; // 删除时间
    
      
    
    public Integer getId()
    {
        return id;
    }
    
    public void setId(Integer id)
    {
        this.id = id;
    }   	
      
    
    public String getNoticeId()
    {
        return noticeId;
    }
    
    public void setNoticeId(String noticeId)
    {
        this.noticeId = noticeId;
    }   	
      
    
    public Integer getReceiveUserId()
    {
        return receiveUserId;
    }
    
    public void setReceiveUserId(Integer receiveUserId)
    {
        this.receiveUserId = receiveUserId;
    }   	
      
    
    public java.sql.Date getReceiveTime()
    {
        return receiveTime;
    }
    
    public void setReceiveTime(java.sql.Date receiveTime)
    {
        this.receiveTime = receiveTime;
    }   	
      
    
    public Integer getIsRead()
    {
        return isRead;
    }
    
    public void setIsRead(Integer isRead)
    {
        this.isRead = isRead;
    }   	
      
    
    public java.sql.Date getReadTime()
    {
        return readTime;
    }
    
    public void setReadTime(java.sql.Date readTime)
    {
        this.readTime = readTime;
    }   	
      
    
    public Integer getIsDelete()
    {
        return isDelete;
    }
    
    public void setIsDelete(Integer isDelete)
    {
        this.isDelete = isDelete;
    }   	
      
    
    public java.sql.Date getDeleteTime()
    {
        return deleteTime;
    }
    
    public void setDeleteTime(java.sql.Date deleteTime)
    {
        this.deleteTime = deleteTime;
    }   	
    

}
