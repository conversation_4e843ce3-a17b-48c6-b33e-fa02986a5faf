package com.bojun.base.system.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.ISatisfactionSurveyService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireResultDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 11:24
 */
@RestController
public class SatisfactionSurveyController extends BoJunBaseController {
    private static Logger logger = LoggerFactory.getLogger(SatisfactionSurveyController.class);
    @Autowired
    private ISatisfactionSurveyService satisfactionSurveyService;

    /**
     * @description: 添加答题信息
     * @author: 赖允翔
     * @date: 2020/5/11
     * @Param:
     * @return:
     */
    @RequestMapping(value = "/addQuestionnaireResult", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public void addQuestionnaireResult(@RequestBody SatisfactionQuestionnaireResultDTO questionnaireResult) {
        try {
            int count = satisfactionSurveyService.addQuestionnaireResult(questionnaireResult);
            if (null != questionnaireResult.getFormQuestionAnswerDTOS() && !questionnaireResult.getFormQuestionAnswerDTOS().isEmpty()) {
                count = satisfactionSurveyService.addFormQuestionAnswer(questionnaireResult.getFormQuestionAnswerDTOS());
            }
            if (count <= 0) {
                outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
                return;
            }
            outJson(successInfo());
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("/addQuestionnaireResult:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @description: 根据ID满意度调查数据
     * @author: 赖允翔
     * @date: 2020/5/11
     * @Param:
     * @return:
     */
    @RequestMapping(value = "/getSatisfactionSurveyById", method = RequestMethod.POST)
    public void getSatisfactionSurveyById(@RequestParam("questionnaireId") Integer questionnaireId) {
        try {
            SatisfactionQuestionnaireDTO doctor = satisfactionSurveyService.getSatisfactionSurveyById(questionnaireId);
            if (doctor == null) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }

            outJson(successInfo(doctor));
        } catch (Exception e) {
            logger.error("/getSatisfactionSurveyById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @description: 获得满意度调查所有数据
     * @author: 赖允翔
     * @date: 2020/5/11
     * @Param:
     * @return:
     */
    @RequestMapping(value = "/getSatisfactionSurvey", method = RequestMethod.POST)
    public void getSatisfactionSurvey(@RequestBody SatisfactionQuestionnaireDTO questionnaireDTO) {
        try {
            int pageNum = (null == questionnaireDTO.getPageNum() ? 1 : questionnaireDTO.getPageNum());
            int everyPage = (null == questionnaireDTO.getEveryPage() ? 10 : questionnaireDTO.getEveryPage());
            PageHelper.startPage(pageNum, everyPage);
            Page<List<SatisfactionQuestionnaireDTO>> page = satisfactionSurveyService.getSatisfactionSurvey(questionnaireDTO);
            if (page == null || page.getTotal() == 0) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            outJson(successPageInfo(page.getResult(), page.getTotal()));
        } catch (Exception e) {
            logger.error("/getSatisfactionSurvey:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }


}
