package com.bojun.utils;

import java.util.UUID;

/**
 * uuid生成器
 * <AUTHOR>
 *
 */
public class UuidGenerator {
    /**
     * 生成唯一标示uuid
     * 
     * @return
     */
    public static String getUuidGenerator() {
        String uuid = UUID.randomUUID().toString();
        return uuid.replaceAll("-", "");
    }
    
    public static void main(String[] args) {
        String uuid=UuidGenerator.getUuidGenerator();
        System.out.println(uuid);
    }

}
