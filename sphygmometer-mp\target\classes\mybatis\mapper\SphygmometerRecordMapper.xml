<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mq.mapper.SphygmometerRecordMapper">

	<!-- 保存臂筒式血压仪测量记录  -->
    <insert id="addSphygmometerRecord" useGeneratedKeys="true" keyProperty="id" parameterType="com.bojun.sphygmometer.mq.entity.SphygmometerRecord">
		INSERT INTO t_sphygmometer_record(
			user_id,
			organization_id,
			serial_number,
			device_id,
			device_type,
			systolic_pressure,
			diastolic_pressure,
			heartbeat,
			pressure_result,
			heartbeat_result,
			measure_place,
			measure_time,
			wx_open_id
		)
		VALUES(
			#{userId},
			#{organizationId},
			#{serialNumber},
			#{deviceId},
			#{deviceType},
			#{systolicPressure},
		 	#{diastolicPressure},
		 	#{heartbeat},
		 	#{pressureResult},
		 	#{heartbeatResult},
		 	#{measurePlace},
		 	#{measureTime},
		 	#{wxOpenId}
		)
	</insert>

	<resultMap type="com.bojun.sphygmometer.dto.SphygmometerRecordDTO" id="SphygmometerRecordDTOResult">
		<result property="id"    column="id"    />
		<result property="userId"    column="user_id"    />
		<result property="organizationId"    column="organization_id"    />
		<result property="serialNumber"    column="serial_number"    />
		<result property="deviceId"    column="device_id"    />
		<result property="deviceType"    column="device_type"    />
		<result property="recordType"    column="record_type"    />
		<result property="systolicPressure"    column="systolic_pressure"    />
		<result property="diastolicPressure"    column="diastolic_pressure"    />
		<result property="heartbeat"    column="heartbeat"    />
		<result property="pressureResult"    column="pressure_result"    />
		<result property="heartbeatResult"    column="heartbeat_result"    />
		<result property="measurePlace"    column="measure_place"    />
		<result property="measureTime"    column="measure_time"    />
		<result property="isRead"    column="is_read"    />
		<result property="decryptValue"    column="decrypt_value"    />
		<result property="wxOpenId"    column="wx_open_id"    />
	</resultMap>

	<sql id="selectSphygmometerRecord">
		select
			tsr.id,
			tsr.user_id,
			tsr.organization_id,
			tsr.serial_number,
			tsr.device_id,
			tsr.device_type,
			tsr.record_type,
			tsr.systolic_pressure,
			tsr.diastolic_pressure,
			tsr.heartbeat,
			tsr.pressure_result,
			tsr.heartbeat_result,
			tsr.measure_place,
			tsr.measure_time,
			DATE_FORMAT(tsr.measure_time,'%Y-%m-%d')  measureTimeFormat,
			tsr.is_read,
			tsr.decrypt_value,
			tsr.wx_open_id
		from
			t_sphygmometer_record tsr left join t_sphygmometer_user tsu on tsr.user_id = tsu.user_id
	</sql>

	<select id="selectSphygmometerRecordList" parameterType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO" resultMap="SphygmometerRecordDTOResult">
		<include refid="selectSphygmometerRecord"/>
		<where>
			<if test="id != null "> and tsr.id = #{id}</if>
			<if test="userId != null "> and tsr.user_id = #{userId}</if>
			<if test="organizationId != null "> and tsr.organization_id = #{organizationId}</if>
			<if test="serialNumber != null  and serialNumber != ''"> and tsr.serial_number = #{serialNumber}</if>
			<if test="deviceId != null "> and tsr.device_id = #{deviceId}</if>
			<if test="deviceType != null "> and tsr.device_type = #{deviceType}</if>
			<if test="recordType != null "> and tsr.record_type = #{recordType}</if>
			<if test="systolicPressure != null "> and tsr.systolic_pressure = #{systolicPressure}</if>
			<if test="diastolicPressure != null "> and tsr.diastolic_pressure = #{diastolicPressure}</if>
			<if test="heartbeat != null "> and tsr.heartbeat = #{heartbeat}</if>
			<if test="pressureResult != null "> and tsr.pressure_result = #{pressureResult}</if>
			<if test="heartbeatResult != null "> and tsr.heartbeat_result = #{heartbeatResult}</if>
			<if test="measurePlace != null  and measurePlace != ''"> and tsr.measure_place = #{measurePlace}</if>
			<if test="measureTime != null "> and tsr.measure_time = #{measureTime}</if>
			<if test="isRead != null  and isRead != ''"> and tsr.is_read = #{isRead}</if>
			<if test="decryptValue != null  and decryptValue != ''"> and tsr.decrypt_value = #{decryptValue}</if>
			<if test="wxOpenId != null  and wxOpenId != ''"> and tsu.wx_open_id = #{wxOpenId}</if>
			<if test="measureStartTime != null  and measureStartTime != ''">
				AND tsr.measure_time >= #{measureStartTime} AND tsr.measure_time &lt;= #{measureEndTime}
			</if>
		</where>
	</select>
</mapper>