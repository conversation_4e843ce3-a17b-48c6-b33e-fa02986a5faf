package com.bojun.base.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.MessageNotificationReceiverV2DTO;
import com.bojun.system.entity.MessageNotificationReceiverV2;

import java.util.List;

/**
 * MessageNotificationReceiverService接口
 * 
 * <AUTHOR>
 * @date 2021-06-07 15:53:16
 */
public interface MessageNotificationReceiverV2Service extends IService<MessageNotificationReceiverV2>
{
    /**
     * 查询消息通知接收人信息表
     * 
     * @param id 消息通知接收人信息表ID
     * @return 消息通知接收人信息表
     */
    public MessageNotificationReceiverV2DTO selectMessageNotificationReceiverById(Integer id);

    /**
     * 查询消息通知接收人信息表列表
     * 
     * @param MessageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 消息通知接收人信息表集合
     */
    public List<MessageNotificationReceiverV2DTO> selectMessageNotificationReceiverList(MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO);

    /**
     * 新增消息通知接收人信息表
     * 
     * @param MessageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 结果
     */
    public int insertMessageNotificationReceiver(MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO);

    /**
     * 修改消息通知接收人信息表
     * 
     * @param MessageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 结果
     */
    public Integer updateMessageNotificationReceiver(MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO) ;
    
    /**
     * 新增消息通知接收人信息表
     * 
     * @param messageNotificationReceiver 消息通知接收人信息表
     * @return 结果
     */
    public int insertMessageNotificationReceiver(MessageNotificationReceiverV2 messageNotificationReceiver);

    /**
     * 修改消息通知接收人信息表
     * 
     * @param messageNotificationReceiver 消息通知接收人信息表
     * @return 结果
     */
    public int updateMessageNotificationReceiver(MessageNotificationReceiverV2 messageNotificationReceiver);

    /**
     * 批量删除消息通知接收人信息表
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteMessageNotificationReceiverByIds(String ids);

    /**
     * 删除消息通知接收人信息表信息
     * 
     * @param id 消息通知接收人信息表ID
     * @return 结果
     */
    public int deleteMessageNotificationReceiverById(Integer id);
}
