/**
 *
 */
package com.bojun.organization.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.contants.Contants;
import com.bojun.contants.FilePathConstants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.file.FileUtils;
import com.bojun.organization.config.CommonConfig;
import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.organization.dto.OrganizationTypeDTO;
import com.bojun.organization.entity.OrganizationInfo;
import com.bojun.organization.enums.OrgServiceCacheKeyEnums;
import com.bojun.organization.service.IOrganizationService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Model： 机构管理模块
 * Description： 机构管理模块
 * Author：赖水秀
 * created： 2020年5月4日
 */
@RestController
public class OrganizationController extends BoJunBaseController {


    private static Log log = LogFactory.getLog(OrganizationController.class);

    @Autowired
    private IOrganizationService organizationService;

    @Autowired
    private RedisUtil redisUtil;
    
    @Autowired
	private CommonConfig  commonConfig;

    /**
     * @param sourceList
     * @return List
     * 2020年5月4日
     * @Description 列表转换成树形机构
     * <AUTHOR>
     */
    private static List<OrganizationInfoDTO> toTree(List<OrganizationInfoDTO> sourceList) {
        List<OrganizationInfoDTO> rest = new ArrayList<OrganizationInfoDTO>();
        Map map = (Map) sourceList.stream().collect(Collectors.toMap(OrganizationInfoDTO::getOrganizationId, Function.identity()));

        sourceList.forEach(data -> {
            OrganizationInfoDTO item = (OrganizationInfoDTO) data;

            if (item.getParentId() == null || item.getParentId() <= 0) {
            	item.setType("1");
                rest.add(item);
                
                item.setChildren(new ArrayList<>());
            } 
        });
        sourceList.forEach(data -> {
            OrganizationInfoDTO item = (OrganizationInfoDTO) data;

            if (item.getParentId() != null && item.getParentId() > 0) {
                OrganizationInfoDTO parent = (OrganizationInfoDTO) map.get(item.getParentId());
                if (null==parent||parent.getChildren() == null) {
                	if(null==parent)
                	{
                		parent=new OrganizationInfoDTO();
                	}
                	
                	
                    parent.setChildren(new ArrayList<>());
                    parent.setType("1");
                } else {
                	 parent.setType("2");
                }
                parent.getChildren().add(item);
            }
        });

        return rest;
    }

    /**
     * @param pathname
     * @return boolean
     * 2020年5月8日
     * @Description 删除文件
     * <AUTHOR>
     */
    public static boolean deleteFile(String pathname) {
        boolean result = false;
        File file = new File(pathname);
        if (file.exists()) {
            result = file.delete();
        }
        return result;
    }

    /**
     * @param
     *
     * @Description 保存机构信息
     * <AUTHOR>
     */
    @RequestMapping(value = "/saveOrganizationInfo", method = RequestMethod.POST)
    public void saveOrganizationInfo(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            //验证机构编码是否已经存在
            String organizationCode = organizationInfoDTO.getOrganizationCode();
            OrganizationInfoDTO organizationInfo = null;
            if (null != organizationCode && !"".equals(organizationCode)) {
                organizationInfo = organizationService.getOrganizationInfoByCode(organizationCode);
            }
            if (null != organizationInfo) {
                outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "机构编码已存在，请重新输入！"));
                return;
            }

            int addNumber = organizationService.saveOrganizationInfo(organizationInfoDTO);
            if (addNumber <= 0) {
                outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
                return;
            }
            //删除缓存中的超管、角色权限机构缓存
            this.redisUtil.deleteByPrefix(Contants.RK_SUPERADMIN_AUTH_PREFIX);
            this.redisUtil.deleteByPrefix(Contants.RK_ROLE_AUTH_PREFIX);
            outJson(successInfo());
        } catch (Exception e) {
            log.error("saveOrganizationInfo:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @param
     * @Description 修改机构信息
     * <AUTHOR>
     */
    @RequestMapping(value = "/updateOrganizationInfo", method = RequestMethod.POST)
    public void updateOrganizationInfo(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            int addNumber = organizationService.updateOrganizationInfo(organizationInfoDTO);
            if (addNumber <= 0) {
                outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
                return;
            }
            //删除缓存中的超管、角色权限机构缓存
            this.redisUtil.deleteByPrefix(Contants.RK_SUPERADMIN_AUTH_PREFIX);
            this.redisUtil.deleteByPrefix(Contants.RK_ROLE_AUTH_PREFIX);
            outJson(successInfo());
        } catch (Exception e) {
            log.error("updateOrganizationInfo:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @Description 查询单个机构信息
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationInfoById", method = RequestMethod.POST)
    public void getOrganizationInfoById(@RequestParam(value = "organizationId") Integer organizationId) {
        try {
            OrganizationInfoDTO organizationInfoDTO = organizationService.getOrganizationInfoById(organizationId);
            if (organizationInfoDTO == null) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            //处理机构图片
            List<OrganizationImgDTO> imgList = organizationInfoDTO.getImgList();
            if (imgList != null && imgList.size() > 0) {
                //图片查看路径
                String showFilePath = commonConfig.getBaseHttpUrl() + FilePathConstants.ORGANIZATION_IMG_PATH;

                for (OrganizationImgDTO organizationImgDTO : imgList) {
                    organizationImgDTO.setOrganizationImageUrl(showFilePath + organizationImgDTO.getOrganizationImage());
                }
            }
            outJson(successInfo(organizationInfoDTO));
        } catch (Exception e) {
            log.error("getOrganizationInfoById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }


    /**
     * @Description 分页查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationByName", method = RequestMethod.POST)
    public void getOrganizationByName(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try{
            List<OrganizationInfoDTO> result = organizationService.getOrganizationByName(organizationInfoDTO);
            outJson(successInfo(result));
        }catch (Exception e){
            log.error("getOrganizationList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @Description 分页查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationPageList", method = RequestMethod.POST)
    public void getOrganizationPageList(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            int pageNum = (null == organizationInfoDTO.getPageNum() ? 1 : organizationInfoDTO.getPageNum());
            int everyPage = (null == organizationInfoDTO.getEveryPage() ? 10 : organizationInfoDTO.getEveryPage());
            PageHelper.startPage(pageNum, everyPage);
            Page<OrganizationInfoDTO> page = organizationService.getOrganizationPageList(organizationInfoDTO);
            if (page == null || page.getTotal() == 0) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            //处理机构图片
            List<OrganizationImgDTO> imgList = organizationInfoDTO.getImgList();
            if (imgList != null && imgList.size() > 0) {
                //图片查看路径
                String showFilePath = commonConfig.getBaseHttpUrl() + FilePathConstants.ORGANIZATION_IMG_PATH;
                for (OrganizationImgDTO organizationImgDTO : imgList) {
                    organizationImgDTO.setOrganizationImage(organizationImgDTO.getOrganizationImage());
                    organizationImgDTO.setOrganizationImageUrl(showFilePath + organizationImgDTO.getOrganizationImage());
                }
            }
            outJson(successPageInfo(page.getResult(), page.getTotal()));
        } catch (Exception e) {
            log.error("getOrganizationPageList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }


    /**
     * @Description 分页查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationListPage", method = RequestMethod.POST)
    public void getOrganizationListPage(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try{
            int pageNum = (null == organizationInfoDTO.getPageNum() ? 1 : organizationInfoDTO.getPageNum());
            int everyPage = (null == organizationInfoDTO.getEveryPage() ? 10 : organizationInfoDTO.getEveryPage());
            PageHelper.startPage(pageNum, everyPage);
            Page<OrganizationInfo> page = organizationService.getOrganizationListPage(organizationInfoDTO);
            outJson(successPageInfo(page.getResult(), page.getTotal()));
        }catch (Exception e){
            log.error("getOrganizationList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }


    /**
     * @Description 根据父机构ID查询子机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getSubOrganizationList", method = RequestMethod.POST)
    public void getSubOrganizationList(@RequestParam(value = "organizationId") Integer organizationId) {
        try{
            List<OrganizationInfoDTO> list = organizationService.getSubOrganizationList(organizationId);
            outJson(successInfo(list));
        }catch (Exception e){
            log.error("getOrganizationList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @Description 查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationTreeList", method = RequestMethod.POST)
    public void getOrganizationTreeList(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            List<OrganizationInfoDTO> list = organizationService.getOrganizationList(organizationInfoDTO);
            if (list == null || list.isEmpty()) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            List<OrganizationInfoDTO> treeList = toTree(list);
            outJson(successInfo(treeList));
        } catch (Exception e) {
            log.error("getOrganizationTreeList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }
    
    /**
     * @Description 查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationTreeSubList", method = RequestMethod.POST)
    public void getOrganizationTreeSubList(@RequestBody Map<String, Object> map) {
        try {
        	OrganizationInfoDTO organizationInfoDTO = new OrganizationInfoDTO();
        	organizationInfoDTO.setOrganizationClassCode((String) map.get("organizationClassCode"));
            List<OrganizationInfoDTO> list = organizationService.getOrganizationList(organizationInfoDTO);
            if (list == null || list.isEmpty()) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            List<OrganizationInfoDTO> treeList = toTree(list);
            @SuppressWarnings("unchecked")
			List<Integer> ids=  (List<Integer>) map.get("ids"); 
              String type =(String) map.get("type"); 
            List<OrganizationInfoDTO> treeList1=   toSubTree(treeList,ids,type);
            outJson(successInfo(treeList1));
        } catch (Exception e) {
            log.error("getOrganizationTreeSubList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    } 
    
    private List<OrganizationInfoDTO> toSubTree(List<OrganizationInfoDTO> treeList,List<Integer> ids, String type) {
  List<OrganizationInfoDTO> delList=new ArrayList<OrganizationInfoDTO>();
		for (int j = 0; j < treeList.size(); j++) {
			OrganizationInfoDTO organizationInfoDTO=treeList.get(j);
		  int	i = toSubTreeObj(organizationInfoDTO, ids);
			
			if (0==i) {

				delList.add(organizationInfoDTO);
			}	
			else {	
				 if(null!=organizationInfoDTO.getChildren() && organizationInfoDTO.getChildren().size()>0)
				 {
					 List<OrganizationInfoDTO> subTree = toSubTree(organizationInfoDTO.getChildren(), ids,type); 
					 if (null!=type && "1".equals(type)) {
						 OrganizationInfoDTO organizationInfo=new  OrganizationInfoDTO();
						 organizationInfo.setOrganizationId(organizationInfoDTO.getOrganizationId());
						 organizationInfo.setOrganizationName(organizationInfoDTO.getOrganizationName());
						 organizationInfo.setOrganizations("yz01");
						 subTree.add(organizationInfo);
					}
					 if (null==subTree || subTree.size()==0) {
						 organizationInfoDTO.setChildren(null);
					}
					 else {
						 organizationInfoDTO.setChildren(subTree);	
					}
					 	
				 }
					
			}	
			}
		treeList.removeAll(delList);
		return treeList;
		
	}
	private int toSubTreeObj(OrganizationInfoDTO organizationInfoDTO,List<Integer> ids) {
    
       int i=0;   	
    	if (ids.contains(organizationInfoDTO.getOrganizationId())) {
		    i=1;
		    organizationInfoDTO.setOrganizations("yz01");
		    return i;
	   }
	List<OrganizationInfoDTO> children = organizationInfoDTO.getChildren();
	    if(null!=children && children.size()>0)
	      {
	    	for (OrganizationInfoDTO organizationInfoDTO2 : children) {
	    		int  l = toSubTreeObj(organizationInfoDTO2, ids);
	    		 if (1==l) {
	    			 return l;
				}
			}
		      
	      }
	    return i; 
               
    }
	/**
     * @Description 查询机构列表(添加本级)
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationTreeListByPerson", method = RequestMethod.POST)
    public void getOrganizationTreeListByPerson(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            List<OrganizationInfoDTO> list = organizationService.getOrganizationList(organizationInfoDTO);
            if (list == null || list.isEmpty()) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            List<OrganizationInfoDTO> treeList = toTree(list);
            String organizations = "";
            //遍历机构将下级机构列表拼接成字符串放入上级机构 在子集机构添加上级机构数据且数据无下级信息
            for (OrganizationInfoDTO organizationInfoDTO2 : treeList) {
            	//
            	organizations = organizationInfoDTO2.getOrganizationId()+"";
            	if (null != organizationInfoDTO2.getChildren() && !organizationInfoDTO2.getChildren().isEmpty()) {
            		 OrganizationInfoDTO organizationInfoDTO5 = new OrganizationInfoDTO();
					organizationInfoDTO5 = (OrganizationInfoDTO) organizationInfoDTO2.clone();
					organizationInfoDTO5.setChildren(new ArrayList());
					organizationInfoDTO2.getChildren().add(organizationInfoDTO5);
	            	for (OrganizationInfoDTO organizationInfoDTO3 : organizationInfoDTO2.getChildren()) {
	            			String organizations2 = "";
	            			organizations2 += organizationInfoDTO3.getOrganizationId();
							organizations  += ","+organizationInfoDTO3.getOrganizationId();
							
							if (null != organizationInfoDTO3.getChildren() && !organizationInfoDTO3.getChildren().isEmpty()) {
								OrganizationInfoDTO organizationInfoDTO6 = new OrganizationInfoDTO();
								organizationInfoDTO6 = (OrganizationInfoDTO) organizationInfoDTO3.clone();
								organizationInfoDTO6.setChildren(new ArrayList());
								organizationInfoDTO3.getChildren().add(organizationInfoDTO6);
								for (OrganizationInfoDTO organizationInfoDTO4 : organizationInfoDTO3.getChildren()) {
									organizations  += ","+organizationInfoDTO4.getOrganizationId();
									organizations2 += ","+organizationInfoDTO4.getOrganizationId();
									organizationInfoDTO4.setOrganizations(organizationInfoDTO4.getOrganizationId()+"");
								}
									
							}
							organizationInfoDTO3.setOrganizations(organizations2);	
	            	}
            	}
            	organizationInfoDTO2.setOrganizations(organizations);
			}
            
            outJson(successInfo(treeList));
        } catch (Exception e) {
            log.error("getOrganizationTreeList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }
    
    /**
     * @Description 查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    public List<OrganizationInfoDTO> getTreeList(@RequestBody List<OrganizationInfoDTO> treeList) {
    	if (treeList != null &&  !treeList.isEmpty()) {   
//    		for (int i = 0; i <= treeList.size(); i++ ) {
//    			if (!treeList.get(i).getChildren().isEmpty() ) {
//					OrganizationInfoDTO organizationInfoDTO3 = treeList.get(i);
//					treeList.get(i).getChildren().add(organizationInfoDTO3);
//					getTreeList(treeList.get(i).getChildren());
//				}
//    		}
	    	for (OrganizationInfoDTO organizationInfoDTO2 : treeList) {
					if (null != organizationInfoDTO2.getChildren() 
							&& !organizationInfoDTO2.getChildren().isEmpty()) {
						OrganizationInfoDTO organizationInfoDTO3 = organizationInfoDTO2;
						List<OrganizationInfoDTO> childrens = new ArrayList<OrganizationInfoDTO>();
						childrens.clear();
						organizationInfoDTO3.setChildren(childrens);
						organizationInfoDTO2.getChildren().add(organizationInfoDTO3);
					  	for (OrganizationInfoDTO organizationInfoDTO4 : treeList) {
							if (null != organizationInfoDTO4.getChildren() 
									&& !organizationInfoDTO4.getChildren().isEmpty()) {
								OrganizationInfoDTO organizationInfoDTO5 = organizationInfoDTO4;
								organizationInfoDTO5.setChildren(childrens);
								organizationInfoDTO4.getChildren().add(organizationInfoDTO5);
							}
					}	}
			}
    	}
            return treeList;
    }
    
    /**
     * @Description 查询机构列表
     * <AUTHOR>
     * void
     * 2020年5月4日
     */
    @RequestMapping(value = "/getOrganizationListByType", method = RequestMethod.POST)
    public void getOrganizationListByType(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
        	String type = organizationInfoDTO.getType();
        	//上级
        	if (null!=type&&"1".equals(type)) {
        		organizationInfoDTO.setWeight(organizationInfoDTO.getWeight()-1);
			}
        	//平级
        	if (null!=type&&"2".equals(type)) {
        		organizationInfoDTO.setWeight(organizationInfoDTO.getWeight());
			}
        	//下级
//        	if (null!=type&&"3".equals(type)) {
//        		organizationInfoDTO.setWeight(organizationInfoDTO.getWeight());
//			}
            List<OrganizationInfoDTO> list = organizationService.getOrganizationList(organizationInfoDTO);
            if (list == null || list.isEmpty()) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }

//            List<OrganizationInfoDTO> treeList = toTree(list);
            outJson(successInfo(list));
        } catch (Exception e) {
            log.error("getOrganizationTreeList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }
    /**
     * @param organizationId void
     *                       2020年5月4日
     * @Description 单个删除机构信息
     * <AUTHOR>
     */
    @RequestMapping(value = "/deleteOrganizationById", method = RequestMethod.POST)
    public void deleteOrganizationById(@RequestParam(value = "organizationId") Integer organizationId) {
        try {
            int deleteNumber = organizationService.deleteOrganizationById(organizationId);
            if (deleteNumber <= 0) {
                outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
                return;
            }
            //删除缓存中的超管、角色权限机构缓存
            this.redisUtil.deleteByPrefix(Contants.RK_SUPERADMIN_AUTH_PREFIX);
            this.redisUtil.deleteByPrefix(Contants.RK_ROLE_AUTH_PREFIX);
            outJson(successInfo());
        } catch (Exception e) {
            log.error("deleteOrganizationById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * 2020年5月4日
     * @Description 批量删除机构信息
     * <AUTHOR>
     */
    @RequestMapping(value = "/batchDeleteOrganization", method = RequestMethod.POST)
    public void batchDeleteOrganization(@RequestBody List<String> organizationIds) {
        try {
            for (String organizationId : organizationIds) {
                deletebyid(Integer.parseInt(organizationId));
            }
            outJson(successInfo());
        } catch (Exception e) {
            log.error("batchDeleteOrganization:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }


    /**
     * 2020年5月4日
     * @Description 递归删除机构
     * <AUTHOR>
     */
    private void deletebyid(Integer organizationId) {
        List<OrganizationInfoDTO> subOrganizationList = organizationService.getSubOrganizationList(organizationId);
        if (null != subOrganizationList && !subOrganizationList.isEmpty() && subOrganizationList.size() > 0) {
            for (OrganizationInfoDTO menuDTO : subOrganizationList) {
                deletebyid(menuDTO.getOrganizationId());
            }
        }
        int deleteNumber = organizationService.deleteOrganizationById(organizationId);
        if (deleteNumber <= 0) {
            outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
            return;
        }
    }

    /**
     * @param classCode void
     *                  2020年5月6日
     * @Description 查询机构类型列表
     * <AUTHOR>
     */
    @RequestMapping(value = "/getOrganizationTypeList", method = RequestMethod.POST)
    public void getOrganizationTypeList(@RequestParam(value = "classCode") String classCode) {
        try {
            List<OrganizationTypeDTO> list = organizationService.getOrganizationTypeList(classCode);
            if (list == null || list.isEmpty()) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            outJson(successInfo(list));
        } catch (Exception e) {
            log.error("getOrganizationTypeList:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @param
     * @description: 获取推送对象
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getPushObject")
    public void getPushObject(@RequestBody OrganizationInfoDTO organizationInfo) {
		try {
			//所有机构
			List<OrganizationInfoDTO> result = (List<OrganizationInfoDTO>) redisUtil.get(OrgServiceCacheKeyEnums.ORG_AND_DEPT_LAYERED.getKey());
			//医疗机构
			if("1".equals(organizationInfo.getOrganizationTypeCode())){
				result = (List<OrganizationInfoDTO>) redisUtil.get(OrgServiceCacheKeyEnums.YILIAO_ORG_AND_DEPT_LAYERED.getKey());
			}
			if (result == null || result.isEmpty()) {
				log.info("机构缓存数据为空，从数据库查询数据.....");
				//获取一级机构
				List<OrganizationInfoDTO> organizationInfoDTO = organizationService.getPushObject(organizationInfo);
				if (organizationInfoDTO == null || organizationInfoDTO.isEmpty()) {
					outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
					return;
				}
				// 异步获取子机构相关数据
				List<OrganizationInfoDTO> organizationInfoDTOFuture = getChildOrgAndDept(organizationInfoDTO);
				outJson(successInfo(organizationInfoDTOFuture));
				return;
			}
			log.info("返回机构缓存数据.....");
			outJson(successInfo(result));
		} catch (Exception e) {
            log.error("/getPushObject:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    
    
    
    /**
     * @param organizationImgDTO void
     *                           2020年5月8日
     * @Description 删除机构图片
     * <AUTHOR>
     */
    @RequestMapping(value = "/deleteOrganizationImgById", method = RequestMethod.POST)
    public void deleteOrganizationImgById(@RequestBody OrganizationImgDTO organizationImgDTO) {
        try {
            int delImgCount = organizationService.deleteOrganizationImgById(organizationImgDTO);
            if (delImgCount <= 0) {
                outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "删除图片失败"));
                return;
            }
            //图片上传根路径
            String uploadFilePath = commonConfig.getBaseUploadPath()
                    + FilePathConstants.ORGANIZATION_IMG_PATH;
            String fileName = uploadFilePath + organizationImgDTO.getOrganizationImage();
            //deleteFile(fileName);
            FileUtils.deleteFile(fileName);
            outJson(successInfo());
        } catch (Exception e) {
            log.error("deleteOrganizationImgById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @description:获取医疗机构信息（二级机构、app端登录选择机构、切换机构）
     * @author: 肖泽权
     * @date: 2020/11/26
     * @Param:
     * @return:
     */
    @RequestMapping(value = "/getOrgInfoForApp", method = RequestMethod.POST)
    public void getOrgInfoForApp(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            List<OrganizationInfoDTO> orgs = organizationService.getOrgInfoForApp(organizationInfoDTO);
            if(orgs == null || orgs.isEmpty()){
            	outJson(info(ResponseCodeEnum.NO_DATA.getCode(),"暂无机构数据"));
            	return;
            }
            outJson(successInfo(orgs));
        } catch (Exception e) {
            log.error("getOrgInfoForApp:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    //树形
    @RequestMapping(value = "/getOrgByRoleId", method = RequestMethod.POST)
    public void getOrgByRoleId(@RequestBody OrganizationInfoDTO org) {
        try {
            List<OrganizationInfoDTO> parentorgs = organizationService.getPushObjectByRoleId(org);
            if (null == parentorgs || parentorgs.isEmpty()) {
                outJson(info(ResponseCodeEnum.NO_DATA.getCode()));
            } else {
                Future<List<OrganizationInfoDTO>> orgs = getChildOrgByRoleId(parentorgs);
                outJson(successInfo(orgs.get()));
            }
        } catch (Exception e) {
            log.error("getOrgByRoleId:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @description: 根据条件获取机构列表，返回平级集合，非树形
     * @author: 严峡华
     * @date: 2021/3/31
     */
    @RequestMapping(value = "/getOrgListByRoleId", method = RequestMethod.POST)
    public List<OrganizationInfoDTO> getOrgListByRoleId(@RequestBody OrganizationInfoDTO organizationInfoDTO) {
        try {
            List<OrganizationInfoDTO> list = this.organizationService.getOrgListByRoleId(organizationInfoDTO);
            return list;
        } catch (Exception e) {
            log.error("getOrgListByRoleId:", e);
        }
        return new ArrayList<>();
    }

    /**
     * @Description 查询单个机构信息
     * <AUTHOR>
     * OrganizationInfoDTO
     * 2021年3月31日
     */
    @RequestMapping(value = "/getOrgById", method = RequestMethod.POST)
    public OrganizationInfoDTO getOrgById(@RequestParam(value = "organizationId") Integer organizationId) {
        try {
            OrganizationInfoDTO organizationInfoDTO = organizationService.getOrganizationInfoById(organizationId);
            return organizationInfoDTO;
        } catch (Exception e) {
            log.error("getOrganizationInfoById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
        return null;
    }

    /**
     * @description: 获取机构的下级数量（目前绩效统计使用）
     * @author: 严峡华
     * @date: 2020/7/23
     */
    @RequestMapping(value = "/getChildrenOrganizationCount", method = RequestMethod.GET)
    public Integer getChildrenOrganizationCount(@RequestParam Integer organizationId) {
        try {
            Integer count = this.organizationService.getChildrenOrganizationCount(organizationId);
            return count;
        } catch (Exception e) {
            log.error("getChildrenOrganizationCount:", e);
        }
        return null;
    }

    /**
     * @description: 获取传参机构及其下级机构（目前绩效统计使用）
     * @author: 严峡华
     * @date: 2020/7/23
     */
    @RequestMapping(value = "/getStatisticOrganizationList", method = RequestMethod.GET)
    public List<OrganizationInfo> getStatisticOrganizationList(@RequestParam Integer organizationId) {
        try {
            List<OrganizationInfo> list = this.organizationService.getStatisticOrganizationList(organizationId);
            return list;
        } catch (Exception e) {
            log.error("getStatisticOrganizationList:", e);
        }
        return new ArrayList<>();
    }

    /**
     * @description: 根据ClassCode获取其机构列表（目前绩效统计使用）
     * @author: 严峡华
     * @date: 2020/08/01
     */
    @RequestMapping(value = "/getOrganizationsByClassCode", method = RequestMethod.GET)
    public List<OrganizationInfo> getOrganizationsByClassCode(@RequestParam String classCode) {
        try {
            List<OrganizationInfo> list = this.organizationService.getOrganizationsByClassCode(classCode);
            return list;
        } catch (Exception e) {
            log.error("getOrganizationsByClassCode:", e);
        }
        return new ArrayList<>();
    }



    //异步向下递归出所有org(根据角色)
    @Async
    public Future<List<OrganizationInfoDTO>> getChildOrgByRoleId(List<OrganizationInfoDTO> orgs) {
        for (OrganizationInfoDTO org : orgs) {
            List<OrganizationInfoDTO> chilrenOrg = organizationService.getChilrenOrgByRoleId(org);
            if (null != chilrenOrg && !chilrenOrg.isEmpty()) {
                org.setChildren(chilrenOrg);
                this.getChildOrgByRoleId(chilrenOrg);
            }
        }
        return new AsyncResult<>(orgs);
    }




    
    @Async
  //递归出所有org
    public List<OrganizationInfoDTO> getChildOrgAndDept(List<OrganizationInfoDTO> orgs) throws ExecutionException,
            InterruptedException {
        for (OrganizationInfoDTO org : orgs) {
        	if("本机构".equals(org.getDeptName())){
        		continue;
        	}
            //获取下级机构
            List<OrganizationInfoDTO> chilrenOrg = organizationService.getChilrenOrg(org);
            org.setChildren(chilrenOrg);
        }
        return orgs;
    }

    //异步向下递归出所有dept
    @Async
    public Future<List<OrganizationInfoDTO>> getChilddept1(List<OrganizationInfoDTO> depts) {
        for (OrganizationInfoDTO dept : depts) {
            //查询部门子部门
            List<OrganizationInfoDTO> childDepts = organizationService.getChildDeptByHigerDeptTransOrg(dept);
            if (null != childDepts && !childDepts.isEmpty()) {
                if (null == dept.getChildren()) {
                    dept.setChildren(childDepts);
                } else {
                    dept.getChildren().addAll(childDepts);
                }
                this.getChilddept1(childDepts);
            } else {
                List<OrganizationInfoDTO> wardDTOS = organizationService.getAllWardByDeptIdTransOrg(dept.getDeptId());
                if (null == dept.getChildren()) {
                    dept.setChildren(wardDTOS);
                } else {
                    dept.getChildren().addAll(wardDTOS);
                }
            }
        }
        return new AsyncResult<>(depts);
    }

}
