package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.MessageNotificationReceiverV2Mapper;
import com.bojun.base.system.service.MessageNotificationReceiverV2Service;
import com.bojun.system.dto.MessageNotificationReceiverV2DTO;
import com.bojun.system.entity.MessageNotificationReceiverV2;
import com.bojun.utils.Convert;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * MessageNotificationReceiverService业务层处理
 * 
 * <AUTHOR>
 * @date 2021-06-07 15:53:16
 */
@Service
public class MessageNotificationReceiverV2ServiceImpl extends ServiceImpl<MessageNotificationReceiverV2Mapper, MessageNotificationReceiverV2> implements MessageNotificationReceiverV2Service
{

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    /**
     * 查询消息通知接收人信息表
     * 
     * @param id 消息通知接收人信息表ID
     * @return 消息通知接收人信息表
     */
    @Override
    public MessageNotificationReceiverV2DTO selectMessageNotificationReceiverById(Integer id)
    {
        return this.baseMapper.selectMessageNotificationReceiverById(id);
    }

    /**
     * 查询消息通知接收人信息表列表
     * 
     * @param MessageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 消息通知接收人信息表集合
     */
    @Override
    public List<MessageNotificationReceiverV2DTO> selectMessageNotificationReceiverList(MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO)
    {
        return this.baseMapper.selectMessageNotificationReceiverList(MessageNotificationReceiverV2DTO);
    }

    /**
     * 新增消息通知接收人信息表
     * 
     * @param MessageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 结果
     */
    @Override
    public int insertMessageNotificationReceiver(MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO)
    {
        return this.baseMapper.insert(MessageNotificationReceiverV2DTO);
    }

    /**
     * 修改消息通知接收人信息表
     * 
     * @param messageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 结果
     */
    @Override
    public Integer updateMessageNotificationReceiver(MessageNotificationReceiverV2DTO messageNotificationReceiverV2DTO)  {
        List<String> noticeIdList = messageNotificationReceiverV2DTO.getNoticeIdList();
        if (noticeIdList.size() > 0) {
            List<MessageNotificationReceiverV2> entityList = new ArrayList<>();
            Date current = new Date();
            for (String noticeId : noticeIdList) {
                int count = count(Wrappers.<MessageNotificationReceiverV2>lambdaQuery()
                        .eq(MessageNotificationReceiverV2::getReceiveUserId, messageNotificationReceiverV2DTO.getReceiveUserId())
                        .eq(MessageNotificationReceiverV2::getIsRead, 1).eq(MessageNotificationReceiverV2::getNoticeId, noticeId));
                if (count > 0) {
                    continue;
                }
                MessageNotificationReceiverV2 receiverV2 = new MessageNotificationReceiverV2();
                receiverV2.setNoticeId(noticeId);
                receiverV2.setIsRead(1);
                receiverV2.setReceiveTime(current);
                receiverV2.setReceiveUserId(messageNotificationReceiverV2DTO.getReceiveUserId());
                entityList.add(receiverV2);
            }
            boolean b = this.saveBatch(entityList);
            return b ? entityList.size() : 0;
        }
        return 0;
    }
    
    /**
     * 新增消息通知接收人信息表
     * 
     * @param messageNotificationReceiver 消息通知接收人信息表
     * @return 结果
     */
    @Override
    public int insertMessageNotificationReceiver(MessageNotificationReceiverV2 messageNotificationReceiver)
    {
        return this.baseMapper.insert(messageNotificationReceiver);
    }

    /**
     * 修改消息通知接收人信息表
     * 
     * @param messageNotificationReceiver 消息通知接收人信息表
     * @return 结果
     */
    @Override
    public int updateMessageNotificationReceiver(MessageNotificationReceiverV2 messageNotificationReceiver)
    {
        return this.baseMapper.updateById(messageNotificationReceiver);
    }

    /**
     * 删除消息通知接收人信息表对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationReceiverByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除消息通知接收人信息表信息
     * 
     * @param id 消息通知接收人信息表ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationReceiverById(Integer id)
    {
        return this.removeById(id) ? 1 : 0;
    }
}
