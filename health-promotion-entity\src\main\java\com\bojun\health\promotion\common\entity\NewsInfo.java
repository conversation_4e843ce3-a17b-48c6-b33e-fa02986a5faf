package com.bojun.health.promotion.common.entity;

import lombok.Data;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 资讯信息表对象 t_news_info
 * 
 * <AUTHOR>
 * @date 2021-08-26 10:07:05
 */
@ApiModel(value = "NewsInfo对象" , description = "资讯信息表")
@Data
@TableName("t_news_info")
public class NewsInfo implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 资讯id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "news_id", type = IdType.AUTO)
    private Integer newsId;

    /** 机构id */
    @ApiModelProperty(value = "机构id", example = "")
	@TableField("organization_id")
    private Integer organizationId;

    /** 文章标题 */
    @ApiModelProperty(value = "文章标题", example = "")
	@TableField("title")
    private String title;

    /** 所属科室 */
    @ApiModelProperty(value = "所属科室", example = "")
	@TableField("dept_id")
    private Integer deptId;

    /** 文章封面 */
    @ApiModelProperty(value = "文章封面", example = "")
	@TableField("cover_image")
    private String coverImage;

    /** 文章附件 */
    @ApiModelProperty(value = "文章附件", example = "")
	@TableField("file_name")
    private String fileName;

    /** 排序下标 */
    @ApiModelProperty(value = "排序下标", example = "")
	@TableField("show_index")
    private Integer showIndex;

    /** 是否在健康首页  0： 否  1：是 */
    @ApiModelProperty(value = "是否在健康首页  0： 否  1：是", example = "")
	@TableField("is_home")
    private Integer isHome;

    /** 首页排序 */
    @ApiModelProperty(value = "首页排序", example = "")
	@TableField("home_index")
    private Integer homeIndex;

    /** 文章内容 */
    @ApiModelProperty(value = "文章内容", example = "")
	@TableField("content")
    private String content;

    /** 阅读数 */
    @ApiModelProperty(value = "阅读数", example = "")
	@TableField("read_number")
    private Integer readNumber;

    /** 发布类型  1:新发   2:更新 */
    @ApiModelProperty(value = "发布类型  1:新发   2:更新", example = "")
	@TableField("publish_type")
    private Integer publishType;

    /** 状态  1:已发布  2:待发布  3：已下架  */
    @ApiModelProperty(value = "状态  1:已发布  2:待发布  3：已下架 ", example = "")
	@TableField("status")
    private Integer status;

    /** 审核状态  1：审核通过  2：待审核  3：审核未通过 */
    @ApiModelProperty(value = "审核状态  1：审核通过  2：待审核  3：审核未通过", example = "")
	@TableField("auth_status")
    private Integer authStatus;

    /** 审核人用户id */
    @ApiModelProperty(value = "审核人用户id", example = "")
	@TableField("auth_user_id")
    private Integer authUserId;

    /** 审核时间 */
    @ApiModelProperty(value = "审核时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("auth_time")
    private Date authTime;

    /** 发布时间 */
    @ApiModelProperty(value = "发布时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("publish_time")
    private Date publishTime;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("update_time")
    private Date updateTime;

    /** 创建人用户id */
    @ApiModelProperty(value = "创建人用户id", example = "")
	@TableField("create_user_id")
    private Integer createUserId;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("create_time")
    private Date createTime;

    /** 是否删除 0：否 1：是 */
    @ApiModelProperty(value = "是否删除 0：否 1：是", example = "")
	@TableField("is_delete")
    private Integer isDelete;

    /** 收藏数量 */
    @ApiModelProperty(value = "收藏数量", example = "")
	@TableField("favorite_num")
    private Integer favoriteNum;

    /** 精准推送  0 ：否   1：是 */
    @ApiModelProperty(value = "精准推送  0 ：否   1：是", example = "")
	@TableField("accurate_push")
    private Integer accuratePush;

    /** 发布人名称 */
    @ApiModelProperty(value = "发布人名称", example = "")
	@TableField("publish_user_name")
    private String publishUserName;

    /** 发布人id */
    @ApiModelProperty(value = "发布人id", example = "")
	@TableField("publish_user_id")
    private Integer publishUserId;

    /** 推送类型 ：0：立即推送 1：定时推送 */
    @ApiModelProperty(value = "推送类型 ：0：立即推送 1：定时推送", example = "")
	@TableField("push_type")
    private Integer pushType;

    /** 推送时间 */
    @ApiModelProperty(value = "推送时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("push_time")
    private Date pushTime;

    /** 系统id */
    @ApiModelProperty(value = "系统id", example = "")
	@TableField("system_id")
    private String systemId;
}
