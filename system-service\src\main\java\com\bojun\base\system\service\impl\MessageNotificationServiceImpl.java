package com.bojun.base.system.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.bojun.base.system.mapper.MessageNotificationMapper;
import com.bojun.base.system.service.MessageNotificationService;
import com.bojun.system.dto.MessageNotificationDTO;
import com.github.pagehelper.PageHelper;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月30日
*/
@Service
public class MessageNotificationServiceImpl implements MessageNotificationService{
	
	@Autowired
	MessageNotificationMapper messageNotificationMapper;
	
	
	/**
	 * 
	 * @Description 查询消息通知（站内）信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	@RequestMapping(value = "/getMessageNotification", method = RequestMethod.POST)
	public List<MessageNotificationDTO> getMessageNotification(@RequestBody Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<MessageNotificationDTO> resList = messageNotificationMapper.getMessageNotification(mapPara);
		return resList;
	}
	
	
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	@RequestMapping(value = "/queryMessageNotificationCount", method = RequestMethod.POST)
	public Integer queryMessageNotificationCount(@RequestBody Map<String, Object> paramsMap) {
		return (int) messageNotificationMapper.queryMessageNotificationCount(paramsMap);
	}
	
	
	/**
	 * 
	 * @Description 新增消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	@RequestMapping(value = "/addMessageNotification", method = RequestMethod.POST)
	public Integer addMessageNotification(@RequestBody MessageNotificationDTO messageNotificationDTO) {
		return messageNotificationMapper.addMessageNotification(messageNotificationDTO);
	}
	/**
	 * 
	 * @Description 删除消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	@RequestMapping(value = "/deleteMessageNotification", method = RequestMethod.POST)
	public int deleteMessageNotification(@RequestBody Map<String, Object> paramsMap) {

		return messageNotificationMapper.deleteMessageNotification(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	@RequestMapping(value = "/updateMessageNotification", method = RequestMethod.POST)
	public int updateMessageNotification(@RequestBody MessageNotificationDTO messageNotificationDTO) {

		return messageNotificationMapper.updateMessageNotification(messageNotificationDTO);
	}
	/**
	 * 
	 * @Description 查询单个消息通知（站内）信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return MessageNotificationDTO
	 */
	@RequestMapping(value = "/getMessageNotificationById", method = RequestMethod.POST)
	public MessageNotificationDTO getMessageNotificationById(@RequestBody Map<String, Object> paramsMap) {

		return messageNotificationMapper.getMessageNotificationById(paramsMap);
	}

	
	/**
	 * 
	 * @Description 查询推送人
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	@RequestMapping(value = "/findListUser", method = RequestMethod.POST)
	public List<MessageNotificationDTO> findListUser(@RequestBody Map<String, Object> paramsMap){
		return messageNotificationMapper.findListUser(paramsMap);
	}
	/**
	 * 
	 * @Description 查询推送科室
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationDTO>
	 */
	@RequestMapping(value = "/findListDept", method = RequestMethod.POST)
	public List<MessageNotificationDTO> findListDept(@RequestBody Map<String, Object> paramsMap){
	return messageNotificationMapper.findListDept(paramsMap);
	}
	
	
	@RequestMapping(value = "/getMessageNotificationType", method = RequestMethod.POST)
	public List<MessageNotificationDTO> getMessageNotificationType(){
		return messageNotificationMapper.getMessageNotificationType();
	}

}

