/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.bojun.vo.BaseQueryInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：查询机构列表
 * Author：赖水秀
 * created： 2020年5月4日
 */
@ApiModel(value = "查询机构条件", description = "查询机构表传入的查询条件")
public class GetOrganizationParamVO extends BaseQueryInfoVO implements Serializable {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 5576296508270960485L;
	
	@NotNull(message = "机构分类不能为空")
	@ApiModelProperty(value="机构分类", required = true, example = "1")
	private String organizationClassCode;

	@ApiModelProperty(value="机构id", required = false, example = "10001")
	private Integer organizationId;
	
	@ApiModelProperty(value="机构名称", required = false, example = "测试")
	private String organizationName;
	
	@ApiModelProperty(value="用户类型", required = false, example = "1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维")
	private Integer userType;

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getOrganizationClassCode() {
		return organizationClassCode;
	}

	public void setOrganizationClassCode(String organizationClassCode) {
		this.organizationClassCode = organizationClassCode;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}
	
}
