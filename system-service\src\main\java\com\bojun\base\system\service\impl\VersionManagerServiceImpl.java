package com.bojun.base.system.service.impl;

import com.bojun.base.system.mapper.VersionManagerMapper;
import com.bojun.base.system.service.IVersionManagerService;
import com.bojun.system.dto.AppVersionDTO;
import com.bojun.system.entity.AppVersion;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 16:20
 */
@Service
public class VersionManagerServiceImpl implements IVersionManagerService {
    @Autowired
    VersionManagerMapper versionManagerMapper;
    /**
     * @description: 添加版本
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @Override
    public int addVersion(AppVersion appVersion) {
        return versionManagerMapper.addVersion(appVersion);
    }

    @Override
    public int deleteVersion(AppVersion appVersion) {
        return versionManagerMapper.deleteVersion(appVersion);
    }

    @Override
    public Page<AppVersion> getVersionManager(AppVersion appVersion) {
        return versionManagerMapper.getVersionManager(appVersion);
    }

    @Override
    public AppVersionDTO getAppVersion(AppVersionDTO appVersionDTO) {
        return versionManagerMapper.getAppVersion(appVersionDTO);
    }

    @Override
    public AppVersionDTO getAppVersionbyCreateTime(AppVersionDTO appVersion1) {
        return versionManagerMapper.getAppVersionbyCreateTime(appVersion1);
    }
}
