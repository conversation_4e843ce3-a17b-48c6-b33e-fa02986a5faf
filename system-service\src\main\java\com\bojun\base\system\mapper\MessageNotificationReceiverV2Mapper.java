package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.MessageNotificationReceiverV2DTO;
import com.bojun.system.entity.MessageNotificationReceiverV2;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * MessageNotificationReceiverMapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-07 15:53:16
 */
@Mapper
public interface MessageNotificationReceiverV2Mapper extends BaseMapper<MessageNotificationReceiverV2>
{

    /**
     * 查询消息通知接收人信息表
     *
     * @param id 消息通知接收人信息表ID
     * @return 消息通知接收人信息表
     */
    public MessageNotificationReceiverV2DTO selectMessageNotificationReceiverById(Integer id);

    /**
     * 查询消息通知接收人信息表列表
     * 
     * @param MessageNotificationReceiverV2DTO 消息通知接收人信息表
     * @return 消息通知接收人信息表集合
     */
    public List<MessageNotificationReceiverV2DTO> selectMessageNotificationReceiverList(MessageNotificationReceiverV2DTO MessageNotificationReceiverV2DTO);
}
