package com.bojun.base.system.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.config.CommonConfig;
import com.bojun.base.system.service.ISystemDictService;
import com.bojun.base.system.service.IVersionManagerService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.file.FileUtils;
import com.bojun.system.constant.FileUploadPathConstant;
import com.bojun.system.dto.AppVersionDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.entity.AppVersion;
import com.bojun.utils.StringUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 11:24
 */
@RestController
public class VersionManagerController extends BoJunBaseController {
	
    private static Logger logger = LoggerFactory.getLogger(VersionManagerController.class);
    
    @Autowired
    private IVersionManagerService versionManagerService;
    
    @Autowired
    private ISystemDictService systemDictService;

    @Autowired
    private CommonConfig  commonConfig;
    
    /**
     * @description: 添加版本
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @PostMapping(value = "/addVersion")
    public void addVersion(@RequestBody AppVersion appVersion) {
        try {
            //判断版本号是否存在
            AppVersionDTO appVersionDTO = new AppVersionDTO();
            BeanUtils.copyProperties(appVersion, appVersionDTO);
            AppVersionDTO appVersion1 = versionManagerService.getAppVersion(appVersionDTO);
            if (appVersion1 != null) {
                outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "版本号已存在，请更换版本号"));
                return;
            }
            //apk包fileath
            String filePath = appVersion.getMobileType() == 1 ? FileUploadPathConstant.android_apk_update : FileUploadPathConstant.ios_apk_update;
            String fileSize = FileUtils.fileSize(appVersion.getFileName(), commonConfig.getBaseUploadPath() + filePath);
            /*if ((commonConfig.getBaseUploadPath() + filePath).startsWith("http")) {
                fileSize = FileUtils.fileSizeForUrl(commonConfig.getBaseUploadPath() + filePath + appVersion.getFileName());
            } else {
                fileSize = FileUtils.fileSize(appVersion.getFileName(), commonConfig.getBaseUploadPath() + filePath);
            }*/
            if (!StringUtil.isEmpty(fileSize)) {
                appVersion.setAppSize(Double.parseDouble(fileSize));
            } else {
                outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "所上传的文件没找到，请再次上传"));
                return;
            }
            //获取问价大小
            versionManagerService.addVersion(appVersion);
            outJson(successInfo());
        } catch (Exception e) {
            logger.error("/addVersion:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @description: 版本管理（全查）
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getVersionManager")
    public void getVersionManager(@RequestBody AppVersion appVersion) {
        try {
            int pageNum = (0 == appVersion.getPageNum() ? 1 : appVersion.getPageNum());
            int everyPage = (0 == appVersion.getEveryPage() ? 10 : appVersion.getEveryPage());
            PageHelper.startPage(pageNum, everyPage);
            Page<AppVersion> page = versionManagerService.getVersionManager(appVersion);
            if (page == null || page.getTotal() == 0) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            outJson(successPageInfo(page.getResult(), page.getTotal()));
        } catch (Exception e) {
            logger.error("getVersionManager:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @description: 查询产品名称
     * @author: 赖允翔
     * @date: 2020/4/27
     * @Param:
     * @return:
     */
    @PostMapping(value = "/getSystemDict")
    public void getSystemDict() {
        try {
            SystemDictDTO systemDictDTO = new SystemDictDTO();
            //查询移动端1 web端  0 移动端
            systemDictDTO.setIsMobile(0);
            List<SystemDictDTO> list = systemDictService.getSystemDicts(systemDictDTO);
            if (list == null || list.isEmpty()) {
                outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
                return;
            }
            outJson(successInfo(list));
        } catch (Exception e) {
            logger.error("getSystemDict", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    @PostMapping(value = "/getAppVersion")
    public void getAppVersion(@RequestBody AppVersionDTO appVersionDTO) {
        try {
//            AppVersionDTO appVersion1 = new AppVersionDTO();
//            appVersion1.setSystemId(appVersionDTO.getSystemId());
//            appVersion1.setMobileType(1);
            AppVersionDTO NewsAppVersion = versionManagerService.getAppVersionbyCreateTime(appVersionDTO);
            if(NewsAppVersion == null){
            	NewsAppVersion = new AppVersionDTO();
                NewsAppVersion.setIsNew("0");//标识当前版本不是最新的
                NewsAppVersion.setMobileType(appVersionDTO.getMobileType());
                outJson(successInfo(NewsAppVersion));
            	return;
            }
            if (appVersionDTO.getMobileType() == 1 && !appVersionDTO.getVersionNumber().equals(NewsAppVersion.getVersionNumber())) {//安卓
                NewsAppVersion.setIsNew("1");// 标识当前版本是最新的
                NewsAppVersion.setDownloadUrl(commonConfig.getBaseHttpUrl() + FileUploadPathConstant.android_apk_update + 
                		"/" + NewsAppVersion.getFileName());
                NewsAppVersion.setFilePath("/" + FileUploadPathConstant.android_apk_update + "/" + NewsAppVersion.getFileName());
                outJson(successInfo(NewsAppVersion));
            } else if (appVersionDTO.getMobileType() == 2 && !appVersionDTO.getVersionNumber().equals(NewsAppVersion.getVersionNumber())) {//ios
                NewsAppVersion.setIsNew("1");// 标识当前版本是最新的
                NewsAppVersion.setDownloadUrl(commonConfig.getBaseHttpUrl() + FileUploadPathConstant.ios_apk_update +
                        "/" + NewsAppVersion.getFileName());
                NewsAppVersion.setFilePath("/" + FileUploadPathConstant.ios_apk_update + "/" + NewsAppVersion.getFileName());
                outJson(successInfo(NewsAppVersion));
            } else {
                NewsAppVersion.setIsNew("0");//标识当前版本不是最新的
                NewsAppVersion.setMobileType(appVersionDTO.getMobileType());
                outJson(successInfo(NewsAppVersion));
              }
        } catch (Exception e) {
            logger.error("getAppVersion", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }


}
