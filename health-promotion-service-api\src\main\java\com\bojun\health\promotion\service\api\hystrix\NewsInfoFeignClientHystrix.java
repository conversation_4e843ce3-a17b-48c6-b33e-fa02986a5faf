package com.bojun.health.promotion.service.api.hystrix;

import com.bojun.exception.BaseRuntimeException;

import com.bojun.health.promotion.common.dto.NewsInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoParamDTO;
import com.bojun.health.promotion.service.api.NewsInfoFeignClient;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import org.springframework.stereotype.Component;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 18:14
 */
@Component
public class NewsInfoFeignClientHystrix implements NewsInfoFeignClient {

    @Override
    public Results<PageData<NewsInfoDTO>> getNewsInfoList(TopicInfoParamDTO topicInfo) {
        throw new BaseRuntimeException("getNewsTopic   接口服务已断开");
    }

    @Override
    public Integer addNewsInfo(NewsInfoDTO newsInfo) {
        throw new BaseRuntimeException("addNewsInfo   接口服务已断开");
    }

    @Override
    public Results updateNewsInfo(NewsInfoDTO newsInfo) {
        throw new BaseRuntimeException("updateNewsInfo   接口服务已断开");
    }

    @Override
    public Results newsPutonOrOffShelf(NewsInfoDTO newsInfo) throws Exception {
        throw new BaseRuntimeException("newsPutonOrOffShelf   接口服务已断开");
    }

    @Override
    public Integer deleteNewsInfo(NewsInfoDTO newsInfo) {
        throw new BaseRuntimeException("deleteNewsInfo   接口服务已断开");
    }

    @Override
    public NewsInfoDTO getNewsInfo(NewsInfoDTO newsInfo,boolean isLookOver) {
        throw new BaseRuntimeException("getNewsInfo   接口服务已断开");
    }
}
