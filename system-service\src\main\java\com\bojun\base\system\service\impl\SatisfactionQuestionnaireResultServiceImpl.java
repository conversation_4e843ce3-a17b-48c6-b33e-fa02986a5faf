package com.bojun.base.system.service.impl;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.bojun.base.system.mapper.FormQuestionAnswerMapper;
import com.bojun.base.system.mapper.SatisfactionQuestionnaireResultMapper;
import com.bojun.base.system.service.ISatisfactionQuestionnaireResultService;
import com.bojun.system.dto.FormQuestionAnswerDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireResultDTO;
import com.github.pagehelper.Page;

/**
 * 
*Model：满意度问卷答题结果信息表
*Description：满意度问卷答题结果信息表service
*Author:李欣颖
*created：2020年5月7日
 */
@Service
public class SatisfactionQuestionnaireResultServiceImpl implements ISatisfactionQuestionnaireResultService {

	@Autowired
	SatisfactionQuestionnaireResultMapper satisfactionQuestionnaireResultMapper;
	@Autowired
	FormQuestionAnswerMapper formQuestionAnswerMapper;
	
	/**
	 * 
	 * @Description 查询满意度问卷答题结果信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<SatisfactionQuestionnaireResultDTO>
	 * created：2020年5月7日
	 */
	public Page<List<SatisfactionQuestionnaireResultDTO>> getSatisfactionQuestionnaireResult( SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO) {
		return satisfactionQuestionnaireResultMapper.getSatisfactionQuestionnaireResult(satisfactionQuestionnaireResultDTO);
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer querySatisfactionQuestionnaireResultCount( Map<String, Object> paramsMap) {
		return (Integer) satisfactionQuestionnaireResultMapper.querySatisfactionQuestionnaireResultCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	@Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 36000, rollbackFor = Exception.class)
	public Integer addSatisfactionQuestionnaireResult(SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO) {
		 satisfactionQuestionnaireResultMapper.addSatisfactionQuestionnaireResult(satisfactionQuestionnaireResultDTO);
		 List<FormQuestionAnswerDTO> formQuestionAnswerDTOList = satisfactionQuestionnaireResultDTO.getFormQuestionAnswerDTOList();
		 if (null!=formQuestionAnswerDTOList && formQuestionAnswerDTOList.size()>0) {
			 for (FormQuestionAnswerDTO formQuestionAnswerDTO : formQuestionAnswerDTOList) {
				 formQuestionAnswerDTO.setQuestionnaireId(satisfactionQuestionnaireResultDTO.getQuestionnaireId());
				 formQuestionAnswerDTO.setAnswerUserId(satisfactionQuestionnaireResultDTO.getAnswerUserId());
				 formQuestionAnswerMapper.addFormQuestionAnswer(formQuestionAnswerDTO);
			}
		}
		
		
		return 1;
	}
	/**
	 * 
	 * @Description 删除满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer deleteSatisfactionQuestionnaireResult(Map<String, Object> paramsMap) {

		return satisfactionQuestionnaireResultMapper.deleteSatisfactionQuestionnaireResult(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer updateSatisfactionQuestionnaireResult(SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO) {

		return satisfactionQuestionnaireResultMapper.updateSatisfactionQuestionnaireResult(satisfactionQuestionnaireResultDTO);
	}
	/**
	 * 
	 * @Description 查询单个满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return SatisfactionQuestionnaireResultDTO
	 * created：2020年5月7日
	 */
	public SatisfactionQuestionnaireResultDTO getSatisfactionQuestionnaireResultById(Map<String, Object> paramsMap) {

		return satisfactionQuestionnaireResultMapper.getSatisfactionQuestionnaireResultById(paramsMap);
	}
	@Override
	public List<Map<String, Object>> getNumberStatistics(Map<String, Object> paramsMap) {
		return satisfactionQuestionnaireResultMapper.getNumberStatistics(paramsMap);
	}
	@Override
	public Integer queryTotalScore(Map<String, Object> paramsMap) {
		return satisfactionQuestionnaireResultMapper.queryTotalScore(paramsMap);

	}

	
	

}