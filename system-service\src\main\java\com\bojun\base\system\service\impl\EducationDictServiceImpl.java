package com.bojun.base.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import com.bojun.base.system.mapper.EducationDictMapper;
import com.bojun.base.system.service.EducationDictService;
import com.bojun.system.dto.EducationDictDto;
import com.github.pagehelper.PageHelper;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月26日
*/
@Service
public class EducationDictServiceImpl implements EducationDictService{
	
	@Autowired
	EducationDictMapper educationDictMapper;

	/**
	 * @Description: 查询学历字典列表总数
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日 Integer
	 */
	@Override
	public Integer getEducationDictListCount(Map<String, Object> map) {
		return educationDictMapper.getEducationDictListCount(map);
	}

	/**
	 * @Description: 查询学历字典列表信息
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	@Override
	public List<EducationDictDto> getEducationDictList(@RequestBody Map<String, Object> map) {
		if (null != map.get("pageNum") && null != map.get("pageSize")) {
			int pageNum = Integer.parseInt(map.get("pageNum").toString());
			int pageSize = Integer.parseInt(map.get("pageSize").toString());
			PageHelper.startPage(pageNum, pageSize);
		}
		return educationDictMapper.getEducationDictList(map);
	}
	
	/**
	 * @Description: 查询学历字典信息
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	@Override
	public List<Map<String,Object>> getMapEducationDict(@RequestBody Map<String, Object> map){
		return educationDictMapper.getMapEducationDict(map);
	}

	/**
	 * @Description: 新增学历
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 *
	 */
	@Override
	public int addEducation(@RequestBody EducationDictDto educationDictDto) {
		Map<String, Object> map = new HashMap<String, Object>();
		String educationCode = educationDictMapper.queryMaximumEducationCode(map);
		educationDictDto.setEducationCode(educationCode);
		return educationDictMapper.addEducation(educationDictDto);
	}

	/**
	 * @Description: 校验学历名称是否合规
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	@Override
	public int checkEducationName(@RequestBody EducationDictDto educationDictDto) {
		// 判断学历名称是否重复
		return educationDictMapper.checkEducationName(educationDictDto);
	}

	/**
	 * @Description: 查询出最大学历code
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	@Override
	public String queryMaximumEducationCode(@RequestBody Map<String, Object> map) {
		return educationDictMapper.queryMaximumEducationCode(map);
	}

	/**
	 * @Description: 编辑学历
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	@Override
	public int updateEducation(@RequestBody EducationDictDto educationDictDto) {
		return educationDictMapper.updateEducation(educationDictDto);
	}

	/**
	 * @Description: 根据id查询学历字典
	 * @Author: 曾玲玲
	 * @create: 2019年12月30日
	 * 
	 */
	@Override
	public EducationDictDto selectEducationDictById(@RequestBody Map<String, Object> map) {
		return educationDictMapper.selectEducationDictById(map);
	}

	/**
	 * @Description: 根据id编辑学历是否启用
	 * @Author: 曾玲玲
	 * @create: 2019/11/22 15:42
	 * 
	 */
	@Override
	public int updateEducationStatus(@RequestBody EducationDictDto educationDictDto) {
		return educationDictMapper.updateEducationStatus(educationDictDto);
	}

}

