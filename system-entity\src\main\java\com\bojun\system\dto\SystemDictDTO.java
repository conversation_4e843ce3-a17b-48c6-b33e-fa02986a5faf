package com.bojun.system.dto;

import com.bojun.system.entity.SystemDict;
import lombok.Data;

import java.util.List;

/**
 * Model：产品管理
 * Description：系统(产品)DTO
 * Author：赖水秀
 * created： 2020年4月27日
 */
@Data
public class SystemDictDTO extends SystemDict {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7975738844245197408L;
	
	private String parentId;	//父id
	
	private String systemTypeName;		//系统类型名称
	
	private String searchKey;	//查询关键字
	
	private Integer everyPage; 	//每页显示数
	
	private Integer pageNum;   //页面数
	
	private Integer totalCount;  //总记录数
	
	private String roleId;//角色ID
		
	private List<MenuDTO> treeList;
	
	private Integer authType;//权限类型（0：超级管理员，1：普通管理员）
	
	private Integer isDisplay=1;//显示1 隐藏0
	
	/**
	 * 子级菜单
	 */
	private List<SystemDictDTO> children;
}
