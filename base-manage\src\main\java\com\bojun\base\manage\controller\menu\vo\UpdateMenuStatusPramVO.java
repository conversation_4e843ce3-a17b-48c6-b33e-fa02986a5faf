/**
 * 
 */
package com.bojun.base.manage.controller.menu.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：菜单管理
 * Description：禁用、启用菜单信息
 * Author：赖水秀
 * created： 2020年4月30日
 */
@ApiModel(value = "禁用、启用菜单信息", description = "禁用、启用菜单入参信息")
public class UpdateMenuStatusPramVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -766355775036922136L;

	@NotEmpty(message = "菜单ID不能为空")
	@ApiModelProperty(value="菜单ID", required = true, example = "m12323")
	private String menuId;
	
	@NotNull(message = "显示状态不能为空")
	@ApiModelProperty(value="显示状态 0：隐藏 1：显示 ", required = true, example = "1")
	private Integer isDisplay;
	

	public String getMenuId() {
		return menuId;
	}

	public void setMenuId(String menuId) {
		this.menuId = menuId;
	}	

	public Integer getIsDisplay() {
		return isDisplay;
	}

	public void setIsDisplay(Integer isDisplay) {
		this.isDisplay = isDisplay;
	}

	
}
