package com.bojun.organization.enums;

/**
 * 机构分类
 * 20200801
 * 严峡华
 */
public enum OrganizationClassEnum {

    MEDICAL("1", "医疗机构"),
    PENSION("2", "养老机构"),
    REGULATOR("3", "监管机构"),
    OPERATION("4", "运维机构");

    private String code;

    private String name;

    OrganizationClassEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
