package com.bojun.base.system.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.MainDictMapper;
import com.bojun.base.system.service.MainDictService;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.system.entity.OrganizationlLeader;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月26日
*/
@Service
public class MainDictServiceImpl implements MainDictService{
	
	@Autowired
	private MainDictMapper mainDictMapper;

	@Override
	public OrganizationInfoDTO getMainDictInfoByKey(Map<String, Object> map) {
		return mainDictMapper.getMainDictInfoByKey(map);
	}

	@Override
	public Integer getPersonnelCountOfOnJob(Map<String, Object> map) {
		return mainDictMapper.getPersonnelCountOfOnJob(map);
	}

	@Override
	public Integer addMainDictInfo(OrganizationInfoDTO organizationInfoDTO) {
		return mainDictMapper.addMainDictInfo(organizationInfoDTO);
	}

	@Override
	public Integer updateMainDictInfo(OrganizationInfoDTO organizationInfoDTO) {
		return mainDictMapper.updateMainDictInfo(organizationInfoDTO);
	}

	@Override
	public Integer addMainDictOfHospitalLeaderInfo(Map<String, Object> map) {
		List<String> empIds = (List<String>)map.get("empIds");
		return mainDictMapper.addMainDictOfHospitalLeaderInfo(empIds);
	}

	@Override
	public Integer checkHospitalLeaderInfo(Map<String, Object> map) {
		List<String> empIds = (List<String>)map.get("empIds");
		return mainDictMapper.checkHospitalLeaderInfo(empIds);
	}

	@Override
	public List<OrganizationlLeader> getMainDictOfHospitalLeaderInfoList(Map<String, Object> map) {
		return mainDictMapper.getMainDictOfHospitalLeaderInfoList(map);
	}

	@Override
	public Integer deleteMainDictOfHospitalLeaderInfoById(Map<String, Object> map) {
		return mainDictMapper.deleteMainDictOfHospitalLeaderInfoById(map);
	}

	/* (non-Javadoc)
	 * @see com.bojun.base.system.service.MainDictService#queryEmployeeCount(java.util.Map)
	 */
	@Override
	public Integer queryEmployeeCount(Map<String, Object> map) {
		return mainDictMapper.queryEmployeeCount(map);
	}

}

