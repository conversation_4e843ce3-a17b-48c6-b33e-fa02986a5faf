package com.bojun.organization.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 机构信息表对象 t_organization_info
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@ApiModel(value = "OrganizationInfo对象" , description = "机构信息表")
@Data
@TableName("t_organization_info")
public class OrganizationInfoV2 implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 机构id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "organization_id", type = IdType.AUTO)
    private Integer organizationId;

    /** 上级机构id */
    @ApiModelProperty(value = "上级机构id", example = "")
	@TableField("parent_id")
    private Integer parentId;

    /** 机构代码 */
    @ApiModelProperty(value = "机构代码", example = "")
	@TableField("organization_code")
    private String organizationCode;

    /** 机构名称 */
    @ApiModelProperty(value = "机构名称", example = "")
	@TableField("organization_name")
    private String organizationName;

    /** 机构权重值,机构级别越高,权重值越小，顶级机构权重是1 */
    @ApiModelProperty(value = "机构权重值,机构级别越高,权重值越小，顶级机构权重是1", example = "")
	@TableField("weight")
    private Integer weight;

    /** 医院logo */
    @ApiModelProperty(value = "医院logo", example = "")
	@TableField("logo_img")
    private String logoImg;

    /** 社会信用代码证号 */
    @ApiModelProperty(value = "社会信用代码证号", example = "")
	@TableField("social_credit_code")
    private String socialCreditCode;

    /** 单位性质 1:企业  2：事业单位（国家行政机关） 3：政府 */
    @ApiModelProperty(value = "单位性质 1:企业  2：事业单位（国家行政机关） 3：政府", example = "")
	@TableField("organization_property")
    private Integer organizationProperty;

    /** 机构规模 */
    @ApiModelProperty(value = "机构规模", example = "")
	@TableField("organization_scale")
    private String organizationScale;

    /** 机构分类code(1:医疗，2：养老，3：监管) */
    @ApiModelProperty(value = "机构分类code(1医疗，2养老，3监管，4运维，5疾控)", example = "")
	@TableField("organization_class_code")
    private String organizationClassCode;

    /** 机构类型code */
    @ApiModelProperty(value = "机构类型code", example = "")
	@TableField("organization_type_code")
    private String organizationTypeCode;

    /** 单位负责人 */
    @ApiModelProperty(value = "单位负责人", example = "")
	@TableField("organization_director")
    private String organizationDirector;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话", example = "")
	@TableField("telephone_number")
    private String telephoneNumber;

    /** 省份code */
    @ApiModelProperty(value = "省份code", example = "")
	@TableField("province_code")
    private String provinceCode;

    /** 城市code */
    @ApiModelProperty(value = "城市code", example = "")
	@TableField("city_code")
    private String cityCode;

    /** 县code */
    @ApiModelProperty(value = "县code", example = "")
	@TableField("county_code")
    private String countyCode;

    /** 单位地址 */
    @ApiModelProperty(value = "单位地址", example = "")
	@TableField("organization_address")
    private String organizationAddress;

    /** 经度 */
    @ApiModelProperty(value = "经度", example = "")
	@TableField("longitude")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty(value = "纬度", example = "")
	@TableField("latitude")
    private String latitude;

    /** 机构介绍 */
    @ApiModelProperty(value = "机构介绍", example = "")
	@TableField("organization_introduction")
    private String organizationIntroduction;

    /** 机构历史 */
    @ApiModelProperty(value = "机构历史", example = "")
	@TableField("organization_history")
    private String organizationHistory;

    /** 机构荣誉 */
    @ApiModelProperty(value = "机构荣誉", example = "")
	@TableField("organization_honor")
    private String organizationHonor;

    /** 营业执照正面 */
    @ApiModelProperty(value = "营业执照正面", example = "")
	@TableField("front_business_license")
    private String frontBusinessLicense;

    /** 营业执照背面 */
    @ApiModelProperty(value = "营业执照背面", example = "")
	@TableField("back_business_license")
    private String backBusinessLicense;

    /** 是否启用  0：否  1:是 */
    @ApiModelProperty(value = "是否启用  0：否  1:是", example = "")
	@TableField("is_enabled")
    private Integer isEnabled;

    /** 核定编制人数 */
    @ApiModelProperty(value = "核定编制人数", example = "")
	@TableField("decide_authorize")
    private Integer decideAuthorize;

    /** 实际编制人数 */
    @ApiModelProperty(value = "实际编制人数", example = "")
	@TableField("actual_authorize")
    private Integer actualAuthorize;

    /** 空编制人数 */
    @ApiModelProperty(value = "空编制人数", example = "")
	@TableField("empty_authorize")
    private Integer emptyAuthorize;

    /** 核定院长人数 */
    @ApiModelProperty(value = "核定院长人数", example = "")
	@TableField("decide_director")
    private Integer decideDirector;

    /** 实际院长人数 */
    @ApiModelProperty(value = "实际院长人数", example = "")
	@TableField("actual_director")
    private Integer actualDirector;

    /** 核定副院长人数 */
    @ApiModelProperty(value = "核定副院长人数", example = "")
	@TableField("decide_vice_director")
    private Integer decideViceDirector;

    /** 实际副院长人数 */
    @ApiModelProperty(value = "实际副院长人数", example = "")
	@TableField("actual_vice_director")
    private Integer actualViceDirector;

    /** 创建人用户id */
    @ApiModelProperty(value = "创建人用户id", example = "")
	@TableField("create_user_id")
    private Integer createUserId;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("create_time")
    private Date createTime;

    /** 乡镇code */
    @ApiModelProperty(value = "乡镇code", example = "")
	@TableField("town_code")
    private String townCode;

    /** 村code */
    @ApiModelProperty(value = "村code", example = "")
	@TableField("village_code")
    private String villageCode;

    /** 乡镇名 */
    @ApiModelProperty(value = "乡镇名", example = "")
	@TableField("town_name")
    private String townName;

    /** 村名 */
    @ApiModelProperty(value = "村名", example = "")
	@TableField("village_name")
    private String villageName;
}
