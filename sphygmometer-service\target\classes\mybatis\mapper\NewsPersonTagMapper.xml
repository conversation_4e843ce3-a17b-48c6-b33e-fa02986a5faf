<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.NewsPersonTagMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.NewsPersonTagDTO" id="NewsPersonTagDTOResult">
        <result property="id"    column="id"    />
        <result property="newsId"    column="news_id"    />
        <result property="personTagCode"    column="person_tag_code"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNewsPersonTag">
    	select
	        id,
	        news_id,
	        person_tag_code,
	        create_time
		from 
        	t_news_person_tag
    </sql>

    <select id="selectNewsPersonTagById" parameterType="int" resultMap="NewsPersonTagDTOResult">
		<include refid="selectNewsPersonTag"/>
		where
        news_id = #{newsId}
    </select>

    <select id="selectNewsPersonTagList" parameterType="com.bojun.sphygmometer.dto.NewsPersonTagDTO" resultMap="NewsPersonTagDTOResult">
        <include refid="selectNewsPersonTag"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="newsId != null "> and news_id = #{newsId}</if>
		<if test="personTagCode != null  and personTagCode != ''"> and person_tag_code = #{personTagCode}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>

    <update id="updateNewsPersonTag" parameterType="com.bojun.sphygmometer.dto.NewsPersonTagDTO" >
        update t_news_person_tag set person_tag_code = #{personTagCode} where news_id = #{newsId}
    </update>

</mapper>