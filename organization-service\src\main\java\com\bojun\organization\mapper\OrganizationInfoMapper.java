package com.bojun.organization.mapper;

import com.bojun.organization.dto.OrganizationImgDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.organization.dto.OrganizationTypeDTO;
import com.bojun.organization.entity.OrganizationInfo;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * Model： 机构管理
 * Description：机构管理DAO
 * Author：赖水秀
 * created： 2020年4月29日
 */
@Mapper
public interface OrganizationInfoMapper {

	/**
	 * @Description 新增机构信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * int
	 * 2020年5月4日
	 */
	int saveOrganizationInfo(OrganizationInfoDTO organizationInfoDTO);

	/**
	 * @Description 修改机构信息信息
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * int
	 * 2020年5月4日
	 */
	int updateOrganizationInfo(OrganizationInfoDTO organizationInfoDTO);


	/**
	 * @Description 查询单个机构信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * OrganizationInfoDTO
	 * 2020年5月4日
	 */
	OrganizationInfoDTO getOrganizationInfoById(Integer organizationId);
	
	/**
	 * <AUTHOR>
	 * @Description //TODO 根据名称获取机构列表
	 * @Date 2021/3/29 17:55
	 * @Param [organizationInfoDTO]
	 * @return java.util.List<com.bojun.organization.dto.OrganizationInfoDTO>
	**/
	List<OrganizationInfoDTO> getOrganizationByName(OrganizationInfoDTO organizationInfoDTO);

    /**
	 * @Description 分页查询
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * Page<List<OrganizationInfoDTO>>
	 * 2020年5月4日
	 */
	Page<OrganizationInfoDTO> getOrganizationPageList(OrganizationInfoDTO organizationInfoDTO);


	Page<OrganizationInfo> getOrganizationListPage(OrganizationInfoDTO organizationInfoDTO);

    /**
	 * @Description 查询机构信息列表
	 * <AUTHOR>
	 * @param organizationInfoDTO
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年5月4日
	 */
	List<OrganizationInfoDTO> getOrganizationList(OrganizationInfoDTO organizationInfoDTO);

    /**
	 * @Description 根据id查询子机构列表
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * List<OrganizationInfoDTO>
	 * 2020年5月4日
	 */
	List<OrganizationInfoDTO> getSubOrganizationList(Integer organizationId);

    /**
	 * @Description  删除机构信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * int
	 * 2020年5月4日
	 */
	int deleteOrganizationById(Integer organizationId);

    /**
	 * @Description 加载类型列表
	 * <AUTHOR>
	 * @param classCode
	 * @return
	 * List<OrganizationTypeDTO>
	 * 2020年5月6日
	 */
	List<OrganizationTypeDTO> getOrganizationTypeList(String classCode);


    /**
	 * @param
	 * @param organizationInfo
	 * @description: 获取通知类型
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	List<OrganizationInfoDTO> getPushObject(OrganizationInfoDTO organizationInfo);

	/**
	 * @param
	 * @description: 根据机构ID递归
	 * @author: 赖允翔
	 * @date: 2020/4/26
	 * @Param:
	 * @return:
	 */
	OrganizationInfoDTO getPushObjectByOrgId(OrganizationInfoDTO organizationInfo);


    /**
	 * @Description  批量添加机构图片信息
	 * <AUTHOR>
	 * @return
	 * int
	 * 2020年5月7日
	 */
	int batchSaveOrganizationImg(List<OrganizationImgDTO> list);

    /**
	 * @Description  批量删除机构图片信息
	 * <AUTHOR>
	 * @param organizationId
	 * @return
	 * int
	 * 2020年5月8日
	 */
	int deleteOrganizationImg(Integer organizationId);


	/**
	 * @return int
	 * 2020年5月8日
	 * @Description 单个删除机构图片信息
	 * <AUTHOR>
	 */
	int deleteOrganizationImgById(OrganizationImgDTO organizationImgDTO);

	OrganizationInfoDTO getPushObjectByOrgAndRoleId(OrganizationInfoDTO organizationInfo);

	List<OrganizationInfoDTO> getPushObjectByRoleId(OrganizationInfoDTO organizationInfo);

	/**
	 * @return OrganizationInfoDTO
	 * 2020年5月28日
	 * @Description 根据条件查询机构信息
	 * <AUTHOR>
	 */
	OrganizationInfoDTO getOrganizationInfoByCode(String organizationCode);

	List<OrganizationInfoDTO> getChilrenOrg(OrganizationInfoDTO infoDTO);

	List<OrganizationInfoDTO> getChilrenOrgByRoleId(OrganizationInfoDTO org);

	Integer getChildrenOrganizationCount(@Param("parentId") Integer parentId);

	List<OrganizationInfo> getStatisticOrganizationList(@Param("organizationId") Integer organizationId);

	List<OrganizationInfoDTO> getAllDeptByOrgId(OrganizationInfoDTO org);

	List<OrganizationInfoDTO> getChildDeptByHigerDeptTransOrg(OrganizationInfoDTO dept);

	List<OrganizationInfoDTO> getAllWardByDeptIdTransOrg(Integer deptId);

	/**
	 * @description: 根据ClassCode获取其机构列表（目前绩效统计使用）
	 * @author: 严峡华
	 * @date: 2020/08/01
	 */
    List<OrganizationInfo> getOrganizationsByClassCode(@Param("classCode") String classCode);
    
    /**
     * @description:获取医疗机构信息（二级机构、app端登录选择机构、切换机构）
     * @author: 肖泽权
     * @date: 2020/11/26
     * @Param:
     * @return:
     */
	List<OrganizationInfoDTO> getOrgInfoForApp(OrganizationInfoDTO organizationInfo);

	/**
	 * @description: 根据条件获取机构列表，返回平级集合，非树形
	 * @author: 严峡华
	 * @date: 2021/3/31
	 */
    List<OrganizationInfoDTO> getOrgListByRoleId(OrganizationInfoDTO organizationInfoDTO);
}
