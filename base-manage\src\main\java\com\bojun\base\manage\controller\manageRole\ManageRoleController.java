/**
 * 
 */
package com.bojun.base.manage.controller.manageRole;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.IManageRoleService;
import com.bojun.base.manage.controller.manageRole.vo.EnableDisableRoleVO;
import com.bojun.base.manage.controller.manageRole.vo.GetManageRoleVO;
import com.bojun.base.manage.controller.manageRole.vo.GetRoleOneVO;
import com.bojun.base.manage.controller.manageRole.vo.ManageRoleVO;
import com.bojun.base.manage.controller.manageRole.vo.UpdateManageRoleVO;
import com.bojun.base.manage.controller.systemDict.vo.SystemDictInfoVO;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.ManageRoleDTO;
import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.entity.ManageRole;
import com.bojun.system.entity.ManageRoleDept;
import com.bojun.system.entity.ManageRoleOrganMenu;
import com.bojun.vo.Page;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;

/**
 * 
 * Model：角色管理
 * Description：角色管理
 * Author：lj
 * created： 2020年4月27日
 */
@SuppressWarnings("unchecked")
@RestController
@RequestMapping("manageRole")
@Api(tags = {"角色管理模块接口"})
@ApiSort(value = 4)
public class ManageRoleController extends BaseController {
	
	private static Logger logger = LoggerFactory.getLogger(ManageRoleController.class);
	
	@Autowired
	private IManageRoleService manageRoleService;
	
	
	 
	/**
	 * @Description 新增角色信息
	 * <AUTHOR>
	 * @return
	 * Results<addManageRole>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "新增角色", notes = "新增角色信息（lj）")
	@ApiOperationSupport(order = 2)
	@RequestMapping(value="/addManageRole", method = RequestMethod.POST)
	@SystemLog(action = "addManageRole", description = "新增角色", operationType = Contants.ADD_REQUEST)
	@AuthAnnotation(action = "addManageRole")
	public Results addManageRole(HttpServletRequest request, @RequestBody @Valid UpdateManageRoleVO addManageRoleVO) {
		try {
			ManageRoleDTO	manageRole2=manageOrganDeptRole(addManageRoleVO);
			BeanUtils.copyProperties(addManageRoleVO, manageRole2);
			String result = manageRoleService.addManageRole(manageRole2);
			return returnResults(result);
		} catch (RuntimeException e) {
			logger.error("addManageRole:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("addManageRole:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 编辑角色信息
	 * <AUTHOR>
	 * @return
	 * Results<updateManageRole>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "编辑角色", notes = "编辑角色信息（lj）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value="/updateManageRole", method = RequestMethod.POST)
	@SystemLog(action = "updateManageRole", description = "编辑角色", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "updateManageRole")
	public Results updateManageRole(HttpServletRequest request, @RequestBody @Valid UpdateManageRoleVO updateManageRoleVO) {
		try {
			ManageRoleDTO	manageRole2=manageOrganDeptRole(updateManageRoleVO);
			BeanUtils.copyProperties(updateManageRoleVO, manageRole2);
			String result = manageRoleService.updateManageRole(manageRole2);
			return returnResults(result);
		} catch (RuntimeException e) {
			logger.error("updateManageRole:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateManageRole:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	/**
	 * @Description 启用禁用角色状态
	 * <AUTHOR>
	 * @return
	 * Results<updateManageRole>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "启用禁用角色状态", notes = "启用禁用角色状态（lj）")
	@ApiOperationSupport(order = 3)
	@RequestMapping(value="/enableDisableRole", method = RequestMethod.POST)
	@SystemLog(action = "enableDisableRole", description = "启用禁用角色状态", operationType = Contants.UPDATE_REQUEST)
	@AuthAnnotation(action = "enableDisableRole")
	public Results enableDisableRole(HttpServletRequest request, @RequestBody @Valid EnableDisableRoleVO enableDisableRoleVO) {
		try {
			ManageRole manageRole = new ManageRole();
			BeanUtils.copyProperties(enableDisableRoleVO, manageRole);
			String result = manageRoleService.enableDisableManageRole(manageRole);
			return returnResults(result);
		} catch (RuntimeException e) {
			logger.error("updateManageRole:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("updateManageRole:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	
	
	/**
	 * @Description 查询角色列表数据
	 * <AUTHOR>
	 * @return
	 * Results<ManageRoleLoginVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "查询角色列表", notes = "查询角色列表数据（lj）")
	@ApiOperationSupport(order = 5)
	@RequestMapping(value="/getManageRoleList", method = RequestMethod.POST)
	@AuthAnnotation(action = "getManageRoleList")
	public Results<Page<List<ManageRoleVO>>> getManageRoleList(HttpServletRequest request, @RequestBody @Valid GetManageRoleVO getManageRoleVO) {		
		try {
			ManageRoleDTO manageRoleDTO=new ManageRoleDTO();
			BeanUtils.copyProperties(getManageRoleVO, manageRoleDTO);
			String result = manageRoleService.getManageRoleList(manageRoleDTO);
			return returnResultsPage(result, ManageRoleVO.class);
		} catch (RuntimeException e) {
			logger.error("getManageRoleList:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getManageRoleList:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
		
	}
	
	/**
	 * @Description 查询查询角色
	 * <AUTHOR>
	 * @return
	 * Results<SystemDictInfoVO>
	 * 2020年4月27日
	 */
	@ApiOperation(value = "查询单个角色", notes = "查询单个角色")
	@ApiOperationSupport(order = 4)
	@RequestMapping(value = "/getManageRoleOne", method = RequestMethod.POST)
	@AuthAnnotation(action = "getManageRoleOne")
	public Results<SystemDictInfoVO> getManageRoleOne(HttpServletRequest request, @RequestBody @Valid GetRoleOneVO getRoleOneVO) {		
		try {			
			ManageRoleDTO manageRole=new ManageRoleDTO();
			manageRole.setRoleId(getRoleOneVO.getRoleId());
			String result = manageRoleService.getManageRoleOne(manageRole);
			return returnResults(result, ManageRoleVO.class);
		} catch (RuntimeException e) {
			logger.error("getManageRoleOne:", e);
			return failResults(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode());
		} catch (Exception e) {
			logger.error("getManageRoleOne:", e);
			return failResults(ResponseCodeEnum.EXCEPTION_REQUEST.getCode());
		}
	}
	
	//封装公共的机构树方法
	public ManageRoleDTO manageOrganDeptRole(UpdateManageRoleVO addManageRoleVO) {
		//中间容器
		Map<Integer,ManageRoleOrganDTO> temp = new HashMap<>();
		//角色、机构、科室信息
		ManageRoleDTO manageRole = new ManageRoleDTO();
		List<ManageRoleOrganDTO> roleOrganList = new ArrayList<ManageRoleOrganDTO>();// 角色机构关联
		List<ManageRoleOrganMenu> roleOrganMenu = addManageRoleVO.getOrganMenu();
		if (null != roleOrganMenu && roleOrganMenu.size() > 0) {
			for (ManageRoleOrganMenu manageRoleOrganMenu : roleOrganMenu) {
				//是否存在部门信息
				boolean existDept = false;
				// 得到机构
				ManageRoleOrganDTO manageRoleOrganDTO = new ManageRoleOrganDTO();
				Integer organizationId = manageRoleOrganMenu.getOrganizationId();
				manageRoleOrganDTO.setOrganizationId(organizationId);
				// 得到部门
				ManageRoleDept manageRoleDept = new ManageRoleDept();
				if (null != manageRoleOrganMenu.getDeptId()) {
					manageRoleDept.setDeptId(manageRoleOrganMenu.getDeptId());
					manageRoleDept.setWardId(manageRoleOrganMenu.getWardId());
					existDept = true;
				}
				//机构去重，科室关联机构
				if(temp.containsKey(organizationId)){
					ManageRoleOrganDTO oldRoleOrg = temp.get(organizationId);
					List<ManageRoleDept> oldRoleDept = oldRoleOrg.getRoleDeptList();
					if(oldRoleDept == null){
						if (existDept) {
							List<ManageRoleDept> roleDeptList = new ArrayList<ManageRoleDept>();
							roleDeptList.add(manageRoleDept);
							oldRoleOrg.setRoleDeptList(roleDeptList);
						}
					}else{
						if (existDept) {
							oldRoleDept.add(manageRoleDept);
						}
					}
				}else{//新机构信息
					if (existDept) {
						List<ManageRoleDept> roleDeptList = new ArrayList<ManageRoleDept>();
						roleDeptList.add(manageRoleDept);
						manageRoleOrganDTO.setRoleDeptList(roleDeptList);
					}
					temp.put(organizationId, manageRoleOrganDTO);
				}
			}
			//转相应List
			Set<Entry<Integer, ManageRoleOrganDTO>> set = temp.entrySet();
			for (Entry<Integer, ManageRoleOrganDTO> entry : set) {
				roleOrganList.add(entry.getValue());
			}
			manageRole.setRoleOrganList(roleOrganList);
		}
		return manageRole;
	}
     
}
