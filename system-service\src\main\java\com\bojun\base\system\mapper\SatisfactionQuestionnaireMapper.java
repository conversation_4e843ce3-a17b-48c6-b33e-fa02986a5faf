
package com.bojun.base.system.mapper;

import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.github.pagehelper.Page;



/**
 * 
*Model：满意度问卷表
*Description：满意度问卷表
*Author:李欣颖
*created：2020年5月7日
 */
@Mapper
public interface SatisfactionQuestionnaireMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	int querySatisfactionQuestionnaireCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询满意度问卷表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<SatisfactionQuestionnaireDTO>
     * created：2020年5月7日
	 */
	Page<SatisfactionQuestionnaireDTO> getSatisfactionQuestionnaire(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO);

	/**
	 * 
	 * @Description 新增满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer addSatisfactionQuestionnaire(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO);

	/**
	 * 
	 * @Description 删除满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer deleteSatisfactionQuestionnaire(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer updateSatisfactionQuestionnaire(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO);

	/**
	 * 
	 * @Description 查询单个满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return SatisfactionQuestionnaireDTO
	 * created：2020年5月7日
	 */
	SatisfactionQuestionnaireDTO getSatisfactionQuestionnaireById(Integer questionnaireId);


	
}
