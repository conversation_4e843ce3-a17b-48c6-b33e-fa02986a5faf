<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserTestPlanRecordMapper">

    <resultMap type="com.bojun.sphygmometer.dto.UserTestPlanRecordDTO" id="UserTestPlanRecordDTOResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userTestPlanId" column="user_test_plan_id"/>
        <result property="userTestPlanTimeId" column="user_test_plan_time_id"/>
        <result property="sphygmometerRecordId" column="sphygmometer_record_id"/>
        <result property="testDate" column="test_date"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="pressureResult" column="pressure_result"/>
        <result property="heartbeatResult" column="heartbeat_result"/>
        <result property="systolicPressure" column="systolic_pressure"/>
        <result property="sendMsg" column="send_msg"/>
        <result property="diastolicPressure" column="diastolic_pressure"/>
        <result property="heartbeat" column="heartbeat"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectUserTestPlanRecord">
        select id,
               user_id,
               user_test_plan_id,
               user_test_plan_time_id,
               sphygmometer_record_id,
               test_date,
               start_time,
               end_time,
               pressure_result,
               heartbeat_result,
               send_msg,
               systolic_pressure,
               diastolic_pressure,
               heartbeat,
               create_time,
               update_time
        from t_user_test_plan_record
    </sql>

    <select id="selectUserTestPlanRecordById" parameterType="int" resultMap="UserTestPlanRecordDTOResult">
        <include refid="selectUserTestPlanRecord"/>
        where
        id = #{id}
    </select>

    <select id="selectUserTestPlanRecordList" parameterType="com.bojun.sphygmometer.dto.UserTestPlanRecordDTO"
            resultType="com.bojun.sphygmometer.dto.SphygmometerRecordSimpleDTO">
        <include refid="selectUserTestPlanRecord"/>
        <where>
            <if test="id != null ">and id = #{id}</if>
            <if test="userId != null ">and user_id = #{userId}</if>
            <if test="userTestPlanId != null ">and user_test_plan_id = #{userTestPlanId}</if>
            <if test="userTestPlanTimeId != null ">and user_test_plan_time_id = #{userTestPlanTimeId}</if>
            <if test="sphygmometerRecordId != null ">and sphygmometer_record_id = #{sphygmometerRecordId}</if>
            <if test="testDate != null  and testDate != ''">and test_date = #{testDate}</if>
            <if test="startTime != null  and endTime != null">and start_time between #{startTime} and #{endTime}</if>
            <if test="pressureResult != null ">and pressure_result = #{pressureResult}</if>
            <if test="heartbeatResult != null ">and heartbeat_result = #{heartbeatResult}</if>
            <if test="createTime != null ">and create_time = #{createTime}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
        </where>
    </select>

</mapper>