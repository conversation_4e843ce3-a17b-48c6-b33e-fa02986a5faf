package com.bojun.contants;

/**
 * 
 * Model：文件上传子路径常量类 Description：文件上传子路径常量类 Author:段德鹏 created：2020年6月2日
 *
 */
public class FilePathConstants {
	/**
	 * 封面文件上传中间路径
	 */
	public final static String BANNER_FILE_PATH = "banner/";
	/**
	 * 手册文件上传中间路径
	 */
	public final static String MANUAL_FILE_PATH = "manual/";

	/**
	 * 医生头像文件上传中间路径
	 */
	public final static String DOCTOR_AVATAR_IMG_PATH = "doctorAppUser/avatar_img/";
	/**
	 * 线上问诊图片
	 */
	public final static String INTERROGATION_IMG = "patient/symptom/";

	/**
	 * 医生动态圈文件上传中间路径
	 */
	public final static String DYNAMIC_PATH = "doctorAppUser/dynamic/";

	/**
	 * 患者头像文件上传中间路径
	 */
	public final static String PATIENT_AVATAR_IMG_PATH = "patientAppUser/avatar_img/";
	
	/**
	 * 资讯文件上传中间路径
	 */
	public final static String NEW_FILE_PATH = "news/";
	/**
	 * 健康测评上传中间路径
	 */
	public final static String HEALTH_TEST_PATH = "healthTest/";
	/**
	 * 用户体检上传中间路径
	 */
	public final static String EXAM_REPORTS_PATH = "examReports/";

	/**
	 * 满意度上传中间路径
	 */
	public final static String STATISFACTION_PATH = "satisfaction/";

	/**
	 * 随访图片上传中间路径
	 */
	public final static String DOCTOR_WARD_ROUND_PATH = "doctorWardRound/";

	/**
	 * 楼层导航图
	 */
	public final static String NAV_FLOOR_IMG_PATH = "navFloorImage/";
	
	/**
	 * 常见问题附件
	 */
	public final static String COMMON_PROBLEM = "commonProblem/";

	/**
	 * 机构图
	 */
	public final static String ORGANIZATION_IMG_PATH = "organization/org/";

	/**
	 * 部门科室图
	 */
	public final static String DEPTMENT_IMG_PATH = "organization/dept/";

	/**
	 * 病区平面图文件上传中间路径
	 */
	public final static String WARD_IMG_PATH = "organization/wardImage/";

	/**
	 * 投票文件上传中间路径
	 */
	public final static String VOTING_IMG_PATH = "voting/";

	/**
	 * 安卓，IOS上传路径
	 */
	public final static String APP_VERSION_PATH = "appversion/";

	/**
	 * 随访文件路径
	 */
	public final static String FOLLOW_UP_IMG = "followUpImg/";

	/* ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~公卫~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ */

	/**
	 * 公卫随访肺结核诊疗卡附件上传
	 */
	public final static String PHS_TUBERCULOSIS_CARD_PATH = "phs/tuberculosis/card/";

	/**
	 * 公卫健康教育管理存档材料附件
	 */
	public final static String PHS_PUBLICHEALTH_EDUCATION_PATH = "phs/publichealth/education/";

	/********************************** 药房处方管理系统 *************************************/
	// 药品图片地址
	public final static String DRUG_IMG = "drug/";
	
	/**
	 * 医生资质认证图片上传
	 */
	public final static String DOCTOR_AUTH_IMG_PATH = "realNameAuthentication/";
	/**
	 * 医生ca认证图片上传
	 */
	public final static String DOCTOR_CA_IMG_PATH = "ca/";
	/**
	 * 检查检验取药条形码
	 */
	public final static String JCJY_QYM_IMG_PATH = "qym/";
}
