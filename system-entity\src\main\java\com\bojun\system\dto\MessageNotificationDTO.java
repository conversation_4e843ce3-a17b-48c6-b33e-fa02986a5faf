package com.bojun.system.dto;


import com.bojun.system.entity.MessageNotification;

import java.util.List;
/**
 * 
*Model：消息通知（站内）
*Description：消息通知（站内）DTO
*Author:刘修纬
*created：2020年1月7日
 */
public class MessageNotificationDTO extends MessageNotification {
	private static final long serialVersionUID = -5150845530222400858L;
	private String deleteTimeStr; // 删除时间
	private String publishTimeStr; // 发布时间
	private String createUserName; // 创建人用户名称
	private String receiveUserName; // 创建人用户名称
	private String noticeTypeName;//类型名称
	//	private Integer receiveUserId;
	private String createTimeStr; // 创建时间
	private Integer count; // 阅读数量
	private String deptCode; // 科室CODE
	private String deptName; // 科室名称
	private List<MessageNotificationDTO> DeptMessageNotificationDTO; // 科室LIST
	private List<MessageNotificationDTO> UserMessageNotificationDTO;  //用户LIST
	private String timingTimeStr; // 定时发布时间
	private int pageNum;
	private int everyPage;
	private long totalCount;
	private String startTime;
	private String endTime;
	private List<String> noticeIds;
	private Integer deptId;
	private Integer wardId;
	private String wardName;
	private List<MessageNotificationObjectDTO> objectVO;
	private Integer organizationId;
	private String publishObject;
	private Integer number;//序列号
	
	private Integer userType;//用户类型 1患者app  2医生app
	
	private String appUserId;//app用户id(推送id "H + appUserId")
	
	private String contend;//通知内容
	
	public String getAppUserId() {
		return appUserId;
	}

	public void setAppUserId(String appUserId) {
		this.appUserId = appUserId;
	}

	public String getContend() {
		return contend;
	}

	public void setContend(String contend) {
		this.contend = contend;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public Integer getNumber() {
		return number;
	}

	public void setNumber(Integer number) {
		this.number = number;
	}

	public String getPublishObject() {
		return publishObject;
	}

	public void setPublishObject(String publishObject) {
		this.publishObject = publishObject;
	}

	@Override
	public Integer getOrganizationId() {
		return organizationId;
	}

	@Override
	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public List<MessageNotificationObjectDTO> getObjectVO() {
		return objectVO;
	}

	public void setObjectVO(List<MessageNotificationObjectDTO> objectVO) {
		this.objectVO = objectVO;
	}

	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public Integer getWardId() {
		return wardId;
	}

	public void setWardId(Integer wardId) {
		this.wardId = wardId;
	}

	public String getWardName() {
		return wardName;
	}

	public void setWardName(String wardName) {
		this.wardName = wardName;
	}

	public List<String> getNoticeIds() {
		return noticeIds;
	}

	public void setNoticeIds(List<String> noticeIds) {
		this.noticeIds = noticeIds;
	}

	public long getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(long totalCount) {
		this.totalCount = totalCount;
	}

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(int everyPage) {
		this.everyPage = everyPage;
	}

	public String getNoticeTypeName() {
		return noticeTypeName;
	}

	public void setNoticeTypeName(String noticeTypeName) {
		this.noticeTypeName = noticeTypeName;
	}

	public String getTimingTimeStr() {
		return timingTimeStr;
	}

	public void setTimingTimeStr(String timingTimeStr) {
		this.timingTimeStr = timingTimeStr;
	}

	public List<MessageNotificationDTO> getDeptMessageNotificationDTO() {
		return DeptMessageNotificationDTO;
	}

	public void setDeptMessageNotificationDTO(List<MessageNotificationDTO> deptMessageNotificationDTO) {
		DeptMessageNotificationDTO = deptMessageNotificationDTO;
	}

	public List<MessageNotificationDTO> getUserMessageNotificationDTO() {
		return UserMessageNotificationDTO;
	}

	public void setUserMessageNotificationDTO(List<MessageNotificationDTO> userMessageNotificationDTO) {
		UserMessageNotificationDTO = userMessageNotificationDTO;
	}

	public String getDeptCode() {
		return deptCode;
	}

	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getReceiveUserName() {
		return receiveUserName;
	}

	public void setReceiveUserName(String receiveUserName) {
		this.receiveUserName = receiveUserName;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public String getDeleteTimeStr() {
		return deleteTimeStr;
	}

	public void setDeleteTimeStr(String deleteTimeStr) {
		this.deleteTimeStr = deleteTimeStr;
	}

	public String getPublishTimeStr() {
		return publishTimeStr;
	}

	public void setPublishTimeStr(String publishTimeStr) {
		this.publishTimeStr = publishTimeStr;
	}

	public String getCreateUserName() {
		return createUserName;
	}

	public void setCreateUserName(String createUserName) {
		this.createUserName = createUserName;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

}
