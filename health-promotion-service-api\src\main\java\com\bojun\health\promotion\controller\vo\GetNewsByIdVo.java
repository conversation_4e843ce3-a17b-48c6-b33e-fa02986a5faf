package com.bojun.health.promotion.controller.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/7 14:42
 */
@ApiModel(value = "单查资讯", description = "单查资讯")
public class GetNewsByIdVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "Id")
    private Integer newsId;
    @ApiModelProperty(value = "科室名称")
    private String deptName;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "话题")
    private String topicName;
    @ApiModelProperty(value = "封面")
    private String coverImage;
    @ApiModelProperty(value = "文件")
    private String fileName;
    @ApiModelProperty(value = "分页排序")
    private Integer showIndex;
    @ApiModelProperty(value = "是否在首页")
    private Integer isHome;
    @ApiModelProperty(value = "首页排序")
    private Integer homeIndex;
    @ApiModelProperty(value = "文章")
    private String content;
    @ApiModelProperty(value = "发布方式  1:审核通过后立即发布   2:手动发布")
    private Integer publishType;
    @ApiModelProperty(value = "资讯类型Id")
    private String topicId;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    private String url;
    @ApiModelProperty(value = "文章来源")
    private String newsSource;
    @ApiModelProperty(value = "状态  1:已发布  2:待发布  3：已下架")
    private Integer status;
    @ApiModelProperty(value = "1健康资讯2医学资讯")
    private Integer sort;
    @ApiModelProperty(value = "1文章2视频")
    private Integer newsType;
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getTopicId() {
        return topicId;
    }

    public void setTopicId(String topicId) {
        this.topicId = topicId;
    }

    public Integer getNewsId() {
        return newsId;
    }

    public void setNewsId(Integer newsId) {
        this.newsId = newsId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Integer getShowIndex() {
        return showIndex;
    }

    public void setShowIndex(Integer showIndex) {
        this.showIndex = showIndex;
    }

    public Integer getIsHome() {
        return isHome;
    }

    public void setIsHome(Integer isHome) {
        this.isHome = isHome;
    }

    public Integer getHomeIndex() {
        return homeIndex;
    }

    public void setHomeIndex(Integer homeIndex) {
        this.homeIndex = homeIndex;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getPublishType() {
        return publishType;
    }

    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }

	public String getNewsSource() {
		return newsSource;
	}

	public void setNewsSource(String newsSource) {
		this.newsSource = newsSource;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getNewsType() {
		return newsType;
	}

	public void setNewsType(Integer newsType) {
		this.newsType = newsType;
	}
    
	
}
