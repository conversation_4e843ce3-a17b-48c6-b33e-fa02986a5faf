package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.ManageUserV2DTO;
import com.bojun.system.entity.ManageUserLogin;
import com.bojun.system.entity.ManageUserV2;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ManageUserV2Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-05 16:05:04
 */
@Mapper
public interface ManageUserV2Mapper extends BaseMapper<ManageUserV2>
{

    /**
     * 查询系统管理员用户
     *
     * @param userId 系统管理员用户ID
     * @return 系统管理员用户
     */
    public ManageUserV2DTO selectManageUserById(Integer userId);

    /**
     * 查询系统管理员用户列表
     * 
     * @param manageUserDTO 系统管理员用户
     * @return 系统管理员用户集合
     */
    public List<ManageUserV2DTO> selectManageUserList(ManageUserV2DTO manageUserDTO);

    public ManageUserLogin selectLastUserLoginById(@Param("userId") Integer userId,@Param("systemId")  String systemId);
}
