package com.bojun.organization.entity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：机构管理
 * Description：机构实体
 * Author：赖水秀
 * created： 2020年4月29日
 */
public class OrganizationInfo implements Serializable {
		
	/**
	 * 
	 */
	private static final long serialVersionUID = -3045601904373554436L;

	@ApiModelProperty(value = "机构id")
	private Integer organizationId; // 机构id

	@ApiModelProperty(value = "机构名称")
	private String organizationName; // 机构名称

	private String socialCreditCode; // 社会信用代码证号

	private Integer organizationProperty; // 单位性质 1:企业 2：事业单位（国家行政机关） 3：政府

	private String organizationScale; // 机构规模

	private String organizationClassCode; // 机构分类code

	private String organizationTypeCode; // 机构类型code

	private String organizationDirector; // 单位负责人

	private String telephoneNumber; // 联系电话

	private String provinceCode; // 省份code

	private String cityCode; // 城市code

	private String countyCode; // 城市code

	private String organizationAddress; // 单位地址

	private String longitude; // 经度

	private String latitude; // 纬度

	private String organizationIntroduction; // 机构介绍

	private String organizationHistory; // 机构历史

	private String organizationHonor; // 机构荣誉

	private String frontBusinessLicense; // 营业执照正面

	private String backBusinessLicense; // 营业执照背面

	private Integer createUserId; // 创建人用户id

	private Date createTime; // 创建时间
	
	private Integer parentId; // 上级机构id
	
	private String organizationCode;	//机构代码
	 
	private Integer isEnabled;	//是否启用  0：否  1:是
	private Integer weight;//机构权重值,机构级别越高,权重值越小，顶级机构权重是1
	
	
	//原人事扩展字段
	private Integer id; //
	private String hospitalName; // 医院名称
	private String hospitalProperty; // 单位性质
	private String hospitalDirector; // 单位负责人
	private String contactNumber; // 联系电话
	private String hospitalAddress; // 单位地址
	private Integer decideAuthorize; // 核定编制人数
	private Integer actualAuthorize; // 实际编制人数
	private Integer emptyAuthorize; // 空编制人数
	private Integer decideDirector; // 核定院长人数
	private Integer actualDirector; // 实际院长人数
	private Integer decideViceDirector; // 核定副院长人数
	private Integer actualViceDirector; // 实际副院长人数
	private String logoImg;
	private String logoImgUrl;
	private String townCode; // 乡镇代码
	private String villageCode; // 村居委会名称
	private String townName; // 乡镇名称
	private String villageName; // 村居委会名称
	
	
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getHospitalName() {
		return hospitalName;
	}

	public void setHospitalName(String hospitalName) {
		this.hospitalName = hospitalName;
	}

	public String getHospitalProperty() {
		return hospitalProperty;
	}

	public void setHospitalProperty(String hospitalProperty) {
		this.hospitalProperty = hospitalProperty;
	}

	public String getHospitalDirector() {
		return hospitalDirector;
	}

	public void setHospitalDirector(String hospitalDirector) {
		this.hospitalDirector = hospitalDirector;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getHospitalAddress() {
		return hospitalAddress;
	}

	public void setHospitalAddress(String hospitalAddress) {
		this.hospitalAddress = hospitalAddress;
	}

	public Integer getDecideAuthorize() {
		return decideAuthorize;
	}

	public void setDecideAuthorize(Integer decideAuthorize) {
		this.decideAuthorize = decideAuthorize;
	}

	public Integer getActualAuthorize() {
		return actualAuthorize;
	}

	public void setActualAuthorize(Integer actualAuthorize) {
		this.actualAuthorize = actualAuthorize;
	}

	public Integer getEmptyAuthorize() {
		return emptyAuthorize;
	}

	public void setEmptyAuthorize(Integer emptyAuthorize) {
		this.emptyAuthorize = emptyAuthorize;
	}

	public Integer getDecideDirector() {
		return decideDirector;
	}

	public void setDecideDirector(Integer decideDirector) {
		this.decideDirector = decideDirector;
	}

	public Integer getActualDirector() {
		return actualDirector;
	}

	public void setActualDirector(Integer actualDirector) {
		this.actualDirector = actualDirector;
	}

	public Integer getDecideViceDirector() {
		return decideViceDirector;
	}

	public void setDecideViceDirector(Integer decideViceDirector) {
		this.decideViceDirector = decideViceDirector;
	}

	public Integer getActualViceDirector() {
		return actualViceDirector;
	}

	public void setActualViceDirector(Integer actualViceDirector) {
		this.actualViceDirector = actualViceDirector;
	}

	public String getLogoImg() {
		return logoImg;
	}

	public void setLogoImg(String logoImg) {
		this.logoImg = logoImg;
	}

	public String getLogoImgUrl() {
		return logoImgUrl;
	}

	public void setLogoImgUrl(String logoImgUrl) {
		this.logoImgUrl = logoImgUrl;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public String getSocialCreditCode() {
		return socialCreditCode;
	}

	public void setSocialCreditCode(String socialCreditCode) {
		this.socialCreditCode = socialCreditCode;
	}

	public Integer getOrganizationProperty() {
		return organizationProperty;
	}

	public void setOrganizationProperty(Integer organizationProperty) {
		this.organizationProperty = organizationProperty;
	}

	public String getOrganizationScale() {
		return organizationScale;
	}

	public void setOrganizationScale(String organizationScale) {
		this.organizationScale = organizationScale;
	}

	public String getOrganizationClassCode() {
		return organizationClassCode;
	}

	public void setOrganizationClassCode(String organizationClassCode) {
		this.organizationClassCode = organizationClassCode;
	}

	public String getOrganizationTypeCode() {
		return organizationTypeCode;
	}

	public void setOrganizationTypeCode(String organizationTypeCode) {
		this.organizationTypeCode = organizationTypeCode;
	}

	public String getOrganizationDirector() {
		return organizationDirector;
	}

	public void setOrganizationDirector(String organizationDirector) {
		this.organizationDirector = organizationDirector;
	}

	public String getTelephoneNumber() {
		return telephoneNumber;
	}

	public void setTelephoneNumber(String telephoneNumber) {
		this.telephoneNumber = telephoneNumber;
	}

	public String getProvinceCode() {
		return provinceCode;
	}

	public void setProvinceCode(String provinceCode) {
		this.provinceCode = provinceCode;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}

	public String getCountyCode() {
		return countyCode;
	}

	public void setCountyCode(String countyCode) {
		this.countyCode = countyCode;
	}

	public String getOrganizationAddress() {
		return organizationAddress;
	}

	public void setOrganizationAddress(String organizationAddress) {
		this.organizationAddress = organizationAddress;
	}

	public String getLongitude() {
		return longitude;
	}

	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}

	public String getLatitude() {
		return latitude;
	}

	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getOrganizationIntroduction() {
		return organizationIntroduction;
	}

	public void setOrganizationIntroduction(String organizationIntroduction) {
		this.organizationIntroduction = organizationIntroduction;
	}

	public String getOrganizationHistory() {
		return organizationHistory;
	}

	public void setOrganizationHistory(String organizationHistory) {
		this.organizationHistory = organizationHistory;
	}

	public String getOrganizationHonor() {
		return organizationHonor;
	}

	public void setOrganizationHonor(String organizationHonor) {
		this.organizationHonor = organizationHonor;
	}

	public String getFrontBusinessLicense() {
		return frontBusinessLicense;
	}

	public void setFrontBusinessLicense(String frontBusinessLicense) {
		this.frontBusinessLicense = frontBusinessLicense;
	}

	public String getBackBusinessLicense() {
		return backBusinessLicense;
	}

	public void setBackBusinessLicense(String backBusinessLicense) {
		this.backBusinessLicense = backBusinessLicense;
	}

	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}

	public String getOrganizationCode() {
		return organizationCode;
	}

	public void setOrganizationCode(String organizationCode) {
		this.organizationCode = organizationCode;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getTownName() {
		return townName;
	}

	public void setTownName(String townName) {
		this.townName = townName;
	}

	public String getVillageName() {
		return villageName;
	}

	public void setVillageName(String villageName) {
		this.villageName = villageName;
	}

	public String getTownCode() {
		return townCode;
	}

	public void setTownCode(String townCode) {
		this.townCode = townCode;
	}

	public String getVillageCode() {
		return villageCode;
	}

	public void setVillageCode(String villageCode) {
		this.villageCode = villageCode;
	}

	public Integer getWeight() {
		return weight;
	}

	public void setWeight(Integer weight) {
		this.weight = weight;
	}
	
}
