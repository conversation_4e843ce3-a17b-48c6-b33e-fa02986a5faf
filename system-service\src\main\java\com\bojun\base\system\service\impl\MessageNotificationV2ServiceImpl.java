package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.MessageNotificationMapper;
import com.bojun.base.system.mapper.MessageNotificationV2Mapper;
import com.bojun.base.system.service.MessageNotificationV2Service;
import com.bojun.system.dto.MessageNotificationV2DTO;
import com.bojun.system.entity.MessageNotificationV2;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * MessageNotificationService业务层处理
 * 
 * <AUTHOR>
 * @date 2021-05-14 19:19:04
 */
@Service
public class MessageNotificationV2ServiceImpl extends ServiceImpl<MessageNotificationV2Mapper, MessageNotificationV2> implements MessageNotificationV2Service
{

    /**
     * 查询消息通知（站内）
     * 
     * @param noticeId 消息通知（站内）ID
     * @return 消息通知（站内）
     */
    @Override
    public MessageNotificationV2DTO selectMessageNotificationById(String noticeId)
    {
        return this.baseMapper.selectMessageNotificationById(noticeId);
    }

    /**
     * 查询消息通知（站内）列表
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 消息通知（站内）集合
     */
    @Override
    public List<MessageNotificationV2DTO> selectMessageNotificationList(MessageNotificationV2DTO messageNotificationDTO)
    {
        return this.baseMapper.selectMessageNotificationList(messageNotificationDTO);
    }

    /**
     * 新增消息通知（站内）
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 结果
     */
    @Override
    public int insertMessageNotification(MessageNotificationV2DTO messageNotificationDTO)
    {
        messageNotificationDTO.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(messageNotificationDTO);
    }

    /**
     * 修改消息通知（站内）
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 结果
     */
    @Override
    public int updateMessageNotification(MessageNotificationV2DTO messageNotificationDTO)
    {
        return this.baseMapper.updateById(messageNotificationDTO);
    }
    
    /**
     * 新增消息通知（站内）
     * 
     * @param messageNotification 消息通知（站内）
     * @return 结果
     */
    @Override
    public int insertMessageNotification(MessageNotificationV2 messageNotification)
    {
        messageNotification.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(messageNotification);
    }

    /**
     * 修改消息通知（站内）
     * 
     * @param messageNotification 消息通知（站内）
     * @return 结果
     */
    @Override
    public int updateMessageNotification(MessageNotificationV2 messageNotification)
    {
        return this.baseMapper.updateById(messageNotification);
    }

    /**
     * 删除消息通知（站内）对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除消息通知（站内）信息
     * 
     * @param noticeId 消息通知（站内）ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationById(String noticeId)
    {
        return this.removeById(noticeId) ? 1 : 0;
    }
}
