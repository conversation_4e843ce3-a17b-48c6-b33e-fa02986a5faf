package com.bojun.health.promotion.common;

import com.bojun.page.PageData;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @ClassName BaseFeign
 * <AUTHOR>
 * @Date 2021/3/19 09:21
 * @Description BaseFeign
 * @Version 1.0
 */
public class BaseFeignController {

    /**
     * 设置请求分页数据
     */
    protected void startPage(Integer pageNum, Integer everyPage) {
        pageNum = (pageNum == null ? 1 : pageNum);
        int pageSize = (everyPage == null ? 10 : everyPage);
        PageHelper.startPage(pageNum, pageSize);
    }

    /**
     * 响应请求分页数据
     *
     * @param <T>
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    protected <T> PageData<T> getPageData(List<T> list) {
        PageData pageData = new PageData();
        pageData.setDataList(list);
        pageData.setTotalCount(new PageInfo(list).getTotal());
        return pageData;
    }

}
