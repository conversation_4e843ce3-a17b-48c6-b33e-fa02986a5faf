package com.bojun.system.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Model：产品管理
 * Description：系统(产品)实体
 * Author：赖水秀
 * created： 2020年4月27日
 */
@Data
public class SystemDict implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7192147948131559959L;
	
	private String systemId; 		//系统id
	
	private String systemName;		//系统名称
	
	private Integer systemTypeId;	//系统类型id
	
	private Integer isMobile;	//是否移动端
	
	private String homeUrl;		//主页URL
	
	private Integer isEnabled;	//启用标记   0：否   1：是
	
	private Integer showIndex;	//排序下标
	
	private String iconCass;	//图标样式
	
	private String remark;	//备注

	@ApiModelProperty(value="内网URL前缀")
	private String innerUrlPrefix;

	@ApiModelProperty(value="外网URL前缀")
	private String outerUrlPrefix;
}
