package com.bojun.base.manage.api.system.hystrix;

import com.bojun.base.manage.api.system.RongNoticeService;
import com.bojun.response.HytrixResults;
import com.bojun.system.dto.RongNoticeDTO;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知熔断器
 * @Description: 基础控台融云消息通知熔断器
 * @since 2020-12-04
 */
@Component
public class RongNoticeServiceHystrix implements RongNoticeService {

    @Override
    public String saveRongNotice(RongNoticeDTO rongnoticeDTO) {
        return HytrixResults.failBack("saveRongNotice");
    }

    @Override
    public String deleteRongNotice(List<String> noticeId) {
        return HytrixResults.failBack("deleteRongNotice");
    }

    @Override
    public String getRongNotice(RongNoticeDTO rongnoticeDTO) {
        return HytrixResults.failBack("getRongNotice");
    }

    @Override
    public String getRongNoticeById(RongNoticeDTO rongnoticeDTO) {
        return HytrixResults.failBack("getRongNoticeById");
    }

    @Override
    public String updateRongNoticeById(RongNoticeDTO rongnoticeDTO) {
        return HytrixResults.failBack("updateRongNoticeById");
    }
}
