package com.bojun.system.dto;

import com.bojun.system.entity.MessageNotificationObject;

/**
 * 
*Model：消息通知（对象）
*Description：消息通知（对象）
*Author:赖允翔
 *created：2020年4月30日
 */
public class MessageNotificationObjectDTO extends MessageNotificationObject {

    private static final long serialVersionUID = -1344026328907379635L;

    private String deptName;
    private String wardName;
    private String organizationName;
    private Integer lastId;


    public Integer getLastId() {
        return lastId;
    }

    public void setLastId(Integer lastId) {
        this.lastId = lastId;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getWardName() {
        return wardName;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }
}
