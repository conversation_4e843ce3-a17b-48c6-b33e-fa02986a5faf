package com.bojun.organization.api.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.organization.dto.GetAuthOrganizationV2DTO;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;
import com.bojun.organization.service.OrganizationInfoV2Service;
import com.bojun.organization.service.api.OrganizationInfoV2FeignClient;
import com.bojun.page.Results;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机构信息表FeignController
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@RestController
public class OrganizationInfoV2FeignController extends BaseFeignController implements OrganizationInfoV2FeignClient
{
    @Autowired
    private OrganizationInfoV2Service organizationInfoService;

//	/**
//     * 查询机构信息表分页列表
//     */
//    @PostMapping(PREFIX + "/page")
//    public Results<PageData<OrganizationInfoV2DTO>> page(@RequestBody OrganizationInfoV2DTO organizationInfoDTO){
//        startPage(organizationInfoDTO.getPageNum(), organizationInfoDTO.getEveryPage());
//        List<OrganizationInfoV2DTO> list = organizationInfoService.selectOrganizationInfoList(organizationInfoDTO);
//        return Results.list(getPageData(list));
//    }

    /**
     * 查询机构信息表列表
     */
    @PostMapping(PREFIX + "/list")
    public Results<List<OrganizationInfoV2DTO>> list(@RequestBody OrganizationInfoV2DTO organizationInfoDTO){
        List<OrganizationInfoV2DTO> list = organizationInfoService.selectOrganizationInfoList(organizationInfoDTO);
        return Results.list(list);
    }

    /*
     * 获取机构信息表详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<OrganizationInfoV2DTO> getInfo(@RequestParam("organizationId") Integer organizationId){
        OrganizationInfoV2DTO organizationInfoDTO = organizationInfoService.selectOrganizationInfoById(organizationId);
        return Results.data(organizationInfoDTO);
    }

    /**
     * 根据机构名字获取机构信息
     * <AUTHOR>
     * @date 2021/7/15
     * @param organizationName
     * @return Results<OrganizationInfoV2>
     */
    @Override
    public Results<OrganizationInfoV2> getInfoByName(String organizationName) {
        OrganizationInfoV2 organizationInfo = organizationInfoService.getOne(Wrappers.<OrganizationInfoV2>query().lambda()
                .eq(OrganizationInfoV2::getOrganizationName, organizationName).last("LIMIT 1"));
        return Results.data(organizationInfo);
    }

//    /**
//     * 新增机构信息表DTO
//     */
//    @PostMapping(PREFIX + "/addDTO")
//    public Results addDTO(@RequestBody OrganizationInfoV2DTO organizationInfoDTO){
//    	Integer num = organizationInfoService.insertOrganizationInfo(organizationInfoDTO);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 修改机构信息表DTO
//     */
//    @PostMapping(PREFIX + "/editDTO")
//    public Results editDTO(@RequestBody OrganizationInfoV2DTO organizationInfoDTO){
//        Integer num = organizationInfoService.updateOrganizationInfo(organizationInfoDTO);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 新增机构信息表
//     */
//    @PostMapping(PREFIX + "/add")
//    public Results add(@RequestBody OrganizationInfoV2 organizationInfo){
//        Integer num = organizationInfoService.insertOrganizationInfo(organizationInfo);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 修改机构信息表
//     */
//    @PostMapping(PREFIX + "/edit")
//    public Results edit(@RequestBody OrganizationInfoV2 organizationInfo){
//        Integer num = organizationInfoService.updateOrganizationInfo(organizationInfo);
//        return Results.opResult(num);
//    }
//
//    /**
//     * 删除机构信息表，多个以逗号分隔
//     */
//    @GetMapping(PREFIX + "/removeByIds")
//    public Results removeByIds(@RequestParam("ids") String ids) {
//        Integer num = organizationInfoService.deleteOrganizationInfoByIds(ids);
//        return Results.opResult(num);
//    }

    /**
     * 获取有权限(不含半选)的机构列表, 非树形, 主要供后端使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    @PostMapping(PREFIX + "/getFullAuthOrgList")
    public Results<List<OrganizationInfoV2DTO>> getFullAuthOrgList(@RequestBody GetAuthOrganizationV2DTO getAuthOrganizationV2DTO) {
        List<OrganizationInfoV2DTO> list = this.organizationInfoService.getFullAuthOrgList(getAuthOrganizationV2DTO);
        return Results.list(list);
    }

    /**
     * 获取有权限(含半选)的机构树形列表, 供前端树形列表使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    @PostMapping(PREFIX + "/getAuthOrgTreeList")
    public Results<List<OrganizationInfoV2DTO>> getAuthOrgTreeList(@RequestBody GetAuthOrganizationV2DTO getAuthOrganizationV2DTO) {
        List<OrganizationInfoV2DTO> list = this.organizationInfoService.getAuthOrgTreeList(getAuthOrganizationV2DTO);
        return Results.list(list);
    }
}
