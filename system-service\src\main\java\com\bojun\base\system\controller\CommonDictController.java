/**
 * 
 */
package com.bojun.base.system.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.bojun.system.dto.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.ICommonDictService;
import com.bojun.enums.ResponseCodeEnum;

/** 
 * Model： 系统管理模块
 * Description： 系统管理
 * Author：赖水秀
 * created： 2020年4月27日
 */
@RestController
public class CommonDictController extends BoJunBaseController {
	
	
	private static Log log = LogFactory.getLog(CommonDictController.class);
	
	@Autowired
	private ICommonDictService commonDictService;
	
	
	
	/**
	 * @Description 省份列表
	 * <AUTHOR>
	 * void
	 * 2020年5月7日
	 */
	@RequestMapping(value = "/getProvinceList", method = RequestMethod.POST)
	public void getProvinceList() {
		try {
			List<ProvinceDictDTO> list = commonDictService.getProvinceList();
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getProvinceList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 市区列表
	 * <AUTHOR>
	 * void
	 * 2020年5月7日
	 */
	@RequestMapping(value = "/getCityList", method = RequestMethod.POST)
	public void getCityList(@Param(value="provinceCode") String provinceCode) {
		try {
			List<CityDictDTO> list = commonDictService.getCityList(provinceCode);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getCityList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 县区列表
	 * <AUTHOR>
	 * void
	 * 2020年5月7日
	 */
	@RequestMapping(value = "/getCountyList", method = RequestMethod.POST)
	public void getCountyList(@Param(value="cityCode") String cityCode) {
		try {
			List<CountyDictDTO> list = commonDictService.getCountyList(cityCode);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getCountyList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 乡镇列表
	 * <AUTHOR>
	 * void
	 * 2020年5月7日
	 */
	@RequestMapping(value = "/getTownList", method = RequestMethod.POST)
	public void getTownList(@Param(value="countyCode") String countyCode) {
		try {
			List<TownDictDTO> list = commonDictService.getTownList(countyCode);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getTownList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * @Description 村、社区列表
	 * <AUTHOR>
	 * void
	 * 2020年5月7日
	 */
	@RequestMapping(value = "/getVillageList", method = RequestMethod.POST)
	public void getVillageList(@Param(value="townCode") String townCode) {
		try {
			List<VillageDictDTO> list = commonDictService.getVillageList(townCode);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getVillageList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	
	/**
	 * 
	 * @Description 查询民族列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<Map<String,Object>>
	 * created：2020年7月25日
	 */
	@RequestMapping(value = "/getNationList", method = RequestMethod.POST)
	public void getNationList(@RequestBody Map<String, Object> paramsMap) {
		try {
			List<NationDTO> list = commonDictService.getNationList(paramsMap);
			if (list == null || list.isEmpty()) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(list));
		} catch (Exception e) {
			log.error("getNationList:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * @Description 获取所有民族（FeignClient使用）
	 * <AUTHOR>
	 * @return List<NationDTO>
	 * created：2020年7月27日
	 */
	@RequestMapping(value = "/getAllNation", method = RequestMethod.GET)
	public List<NationDTO> getAllNation() {
		List<NationDTO> list = commonDictService.getNationList(new HashMap<>());
		return list;
	}
	
	
}
