package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 消息通知关联产品表对象 t_message_notification_system_v2
 * 
 * <AUTHOR>
 * @date 2021-07-22 16:11:35
 */
@ApiModel(value = "MessageNotificationSystemV2对象" , description = "消息通知关联产品表")
@Data
@TableName("t_message_notification_system_v2")
public class MessageNotificationSystemV2 implements Serializable {
    private static final long serialVersionUID = 1L;


    /** id */
    @ApiModelProperty(value = "主键ID", example = "")
	@TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 消息通知id */
    @ApiModelProperty(value = "消息通知id", example = "")
	@TableField("notice_id")
    private String noticeId;

    /** 系统id */
    @ApiModelProperty(value = "系统id", example = "")
	@TableField("system_id")
    private String systemId;
}
