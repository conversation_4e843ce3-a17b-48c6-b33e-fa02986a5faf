package com.bojun.system.entity;

import java.io.Serializable;

/**
 * 
*Model：角色系统关联
*Description：角色系统关联实体类
*Author:刘俊
*created：2020年4月30日
 */
public class ManageRoleSystem implements Serializable {

   private static final long serialVersionUID = -1344026328907379635L;

      private Integer rsId; // 角色系统关联id
      
      private String roleId; // 角色ID（uuid）
    
      private String systemId; // 系统id
      
      


	public Integer getRsId() {
		return rsId;
	}

	public void setRsId(Integer rsId) {
		this.rsId = rsId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	


     
      
    
      
      

}
