package com.bojun.base.system.service.impl;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.bojun.base.system.mapper.FormQuestionItemMapper;
import com.bojun.base.system.mapper.FormQuestionMapper;
import com.bojun.base.system.mapper.FormQuestionOptionMapper;
import com.bojun.base.system.mapper.SatisfactionQuestionnaireMapper;
import com.bojun.base.system.service.ISatisfactionQuestionnaireService;
import com.bojun.system.dto.FormQuestionDTO;
import com.bojun.system.dto.FormQuestionItemDTO;
import com.bojun.system.dto.FormQuestionOptionDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.github.pagehelper.Page;

/**
 * 
*Model：满意度问卷表
*Description：满意度问卷表service
*Author:李欣颖
*created：2020年5月7日
 */
@Service
public class SatisfactionQuestionnaireServiceImpl implements ISatisfactionQuestionnaireService{

	@Autowired
	SatisfactionQuestionnaireMapper satisfactionQuestionnaireMapper;
	@Autowired
	FormQuestionMapper formQuestionMapper;
	@Autowired
	FormQuestionOptionMapper formQuestionOptionMapper;
	@Autowired
	FormQuestionItemMapper  formQuestionItemMapper;
	
	/**
	 * 
	 * @Description 查询满意度问卷表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<SatisfactionQuestionnaireDTO>
	 * created：2020年5月7日
	 */
	public Page<SatisfactionQuestionnaireDTO>  getSatisfactionQuestionnaire(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
		return satisfactionQuestionnaireMapper.getSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
		
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer querySatisfactionQuestionnaireCount( Map<String, Object> paramsMap) {
		return (Integer) satisfactionQuestionnaireMapper.querySatisfactionQuestionnaireCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	@Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 36000, rollbackFor = Exception.class)
	public Integer addSatisfactionQuestionnaire(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
				 satisfactionQuestionnaireMapper.addSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);

				// 保存选择题目
				List<FormQuestionDTO> formQuestionDTOList = satisfactionQuestionnaireDTO.getFormQuestionDTOList();
				if (null != formQuestionDTOList && formQuestionDTOList.size() > 0) {
					// 保存选择题目选项和分数
					for (FormQuestionDTO formQuestionDTO : formQuestionDTOList) {
						formQuestionDTO.setQuestionnaireId(satisfactionQuestionnaireDTO.getQuestionnaireId());
						formQuestionMapper.addFormQuestion(formQuestionDTO);
						List<FormQuestionOptionDTO> formQuestionOptionList = formQuestionDTO
								.getFormQuestionOptionDTOList();
						if (null != formQuestionOptionList && formQuestionOptionList.size() > 0) {
							for (int showIndex = 0; showIndex < formQuestionOptionList.size(); showIndex++) {
								FormQuestionOptionDTO formQuestionOptionDTO = formQuestionOptionList.get(showIndex);
								formQuestionOptionDTO.setQuestionId(formQuestionDTO.getQuestionId());
//								formQuestionOptionDTO.setOptionId(UuidGenerator.getUuidGenerator());
//								formQuestionOptionDTO.setShowIndex(showIndex);
								if (null == formQuestionOptionDTO.getScore()) {
									formQuestionOptionDTO.setScore(0);
								}
								formQuestionOptionMapper.addFormQuestionOption(formQuestionOptionDTO);
							}
						}
						//保存矩阵项目
						List<FormQuestionItemDTO> formQuestionItemDTOList = formQuestionDTO.getFormQuestionItemDTOList();
						if (null != formQuestionItemDTOList && formQuestionItemDTOList.size() > 0) {
							for (FormQuestionItemDTO formQuestionItemDTO : formQuestionItemDTOList) {
								formQuestionItemDTO.setQuestionId(formQuestionDTO.getQuestionId());
								formQuestionItemMapper.addFormQuestionItem(formQuestionItemDTO);
							}
							
						}
					}
				}
	                                     return 1;
	}
	/**
	 * 
	 * @Description 删除满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer deleteSatisfactionQuestionnaire(Map<String, Object> paramsMap) {

		return satisfactionQuestionnaireMapper.deleteSatisfactionQuestionnaire(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	@Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 36000, rollbackFor = Exception.class)
	public Integer updateSatisfactionQuestionnaire(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
		 satisfactionQuestionnaireMapper.updateSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
           //删除题目
		 Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("questionnaireId", satisfactionQuestionnaireDTO.getQuestionnaireId());
		 //获取题目
		 List<FormQuestionDTO> formQuestioList = formQuestionMapper.getFormQuestion(paramsMap);
		 //删除题目
		 for (FormQuestionDTO formQuestionDTO : formQuestioList) {
			 paramsMap.put("questionId", formQuestionDTO.getQuestionId());
			formQuestionOptionMapper.deleteFormQuestionOption(paramsMap);
			formQuestionItemMapper.deleteFormQuestionItem(paramsMap);
			 formQuestionMapper.deleteFormQuestion(paramsMap);
		}
		 
		
		 
			// 保存选择题目
			List<FormQuestionDTO> formQuestionDTOList = satisfactionQuestionnaireDTO.getFormQuestionDTOList();
			if (null != formQuestionDTOList && formQuestionDTOList.size() > 0) {
				// 保存选择题目选项和分数
				for (FormQuestionDTO formQuestionDTO : formQuestionDTOList) {
					formQuestionDTO.setQuestionnaireId(satisfactionQuestionnaireDTO.getQuestionnaireId());
					formQuestionMapper.addFormQuestion(formQuestionDTO);
					List<FormQuestionOptionDTO> formQuestionOptionList = formQuestionDTO
							.getFormQuestionOptionDTOList();
					if (null != formQuestionOptionList && formQuestionOptionList.size() > 0) {
						for (int showIndex = 0; showIndex < formQuestionOptionList.size(); showIndex++) {
							FormQuestionOptionDTO formQuestionOptionDTO = formQuestionOptionList.get(showIndex);
							formQuestionOptionDTO.setQuestionId(formQuestionDTO.getQuestionId());
							if (null == formQuestionOptionDTO.getScore()) {
								formQuestionOptionDTO.setScore(0);
							}
							formQuestionOptionMapper.addFormQuestionOption(formQuestionOptionDTO);
						}
					}
					//保存矩阵项目
					List<FormQuestionItemDTO> formQuestionItemDTOList = formQuestionDTO.getFormQuestionItemDTOList();
					if (null != formQuestionItemDTOList && formQuestionItemDTOList.size() > 0) {
						for (FormQuestionItemDTO formQuestionItemDTO : formQuestionItemDTOList) {
							formQuestionItemDTO.setQuestionId(formQuestionDTO.getQuestionId());
							formQuestionItemMapper.addFormQuestionItem(formQuestionItemDTO);
						}
						
					}
				}
			}		 
		 return 1;
	}
	/**
	 * 
	 * @Description 查询单个满意度问卷表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return SatisfactionQuestionnaireDTO
	 * created：2020年5月7日
	 */
	public SatisfactionQuestionnaireDTO getSatisfactionQuestionnaireById(Integer questionnaireId) {

		return satisfactionQuestionnaireMapper.getSatisfactionQuestionnaireById(questionnaireId);
	}

	/**
	 * 
	 * @Description 修改满意度问卷表状态
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer updateSatisfactionQuestionnaireStatus(SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
		
		return satisfactionQuestionnaireMapper.updateSatisfactionQuestionnaire(satisfactionQuestionnaireDTO);
	}
	

}