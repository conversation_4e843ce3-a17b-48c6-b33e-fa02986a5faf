package com.bojun.base.system.service.impl;

import com.bojun.base.system.mapper.NoticeMapper;
import com.bojun.base.system.service.INoticeService;
import com.bojun.healthcare.dto.UserNoticeDTO;
import com.bojun.system.dto.MessageNotificationDTO;
import com.bojun.system.dto.MessageNotificationObjectDTO;
import com.bojun.system.entity.MessageNotificaionType;
import com.bojun.system.entity.MessageNotificationObject;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 8:57
 */
@Service
public class NoticeServiceImpl implements INoticeService {
    @Autowired
    NoticeMapper noticeMapper;
    /**
     * @description: 通知公告查询（全查）
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @Override
    public Page<MessageNotificationDTO> getNotices(MessageNotificationDTO messageNotificationDTO) {
        Page<MessageNotificationDTO> m = noticeMapper.getNotices(messageNotificationDTO);
        return m;
    }

    /**
     * @description: 通知公告查询（单查）
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @Override
    public MessageNotificationDTO getNoticeById(@RequestParam("noticeId") String noticeId) {
        return noticeMapper.getNoticeById(noticeId);
    }
    /**
     * @description: 添加通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @Override
    public int addNotice(MessageNotificationDTO messageNotificationDTO) {
        return noticeMapper.addNotice(messageNotificationDTO);
    }
    
    /**
     * @description: 新app融云通知
     * @author: 肖泽权
     * @date: 2021/3/2
     * @Param:
     * @return:
     */
    public Integer addRongNotice(UserNoticeDTO notice){
    	return noticeMapper.addRongNotice(notice);
    }
    /**
     * @description: 删除通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @Override
    public int updateNotice(MessageNotificationDTO messageNotificationDTO) {
        return noticeMapper.updateNotice(messageNotificationDTO);
    }
    /**
     * @param
     * @description: 获取通知类型
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    @Override
    public List<MessageNotificaionType> getNoticeType() {
        return noticeMapper.getNoticeType();
    }


    @Override
    public int addMessageNotificationObject(MessageNotificationObject object) {
        return noticeMapper.addMessageNotificationObject(object);
    }

    @Override
    public int addMessageNotificationSystem(String noticeId, List<String> systemIds) {
        return noticeMapper.addMessageNotificationSystem(noticeId, systemIds);
    }

    @Override
    public int deleteMessageNotificationObject(MessageNotificationDTO messageNotificationDTO) {
        return noticeMapper.deleteMessageNotificationObject(messageNotificationDTO);
    }

    @Override
    public int deleteMessageNotificationSystem(MessageNotificationDTO messageNotificationDTO) {
        return noticeMapper.deleteMessageNotificationSystem(messageNotificationDTO);
    }
    
    public List<String> getAllAppUser(MessageNotificationDTO messageNotificationDTO){
    	return noticeMapper.getAllAppUser(messageNotificationDTO);
    }
    
    public Integer addAppNotice(MessageNotificationDTO messageNotificationDTO){
    	return noticeMapper.addAppNotice(messageNotificationDTO);
    }

    @Override
    public List<MessageNotificationObjectDTO> getObjectOrg(MessageNotificationDTO notificationDTO) {
        return noticeMapper.getObjectOrg(notificationDTO);
    }

    @Override
    public List<MessageNotificationObjectDTO> getObjectDept(MessageNotificationObjectDTO notificationObjectDTO) {
        return noticeMapper.getObjectDept(notificationObjectDTO);
    }

    @Override
    public List<MessageNotificationObjectDTO> getObjectWard(MessageNotificationDTO messageNotificationDTO) {
        return noticeMapper.getObjectWard(messageNotificationDTO);
    }

    @Override
    public Page<MessageNotificationDTO> getMessageNotification(MessageNotificationDTO messageNotification) {
        return noticeMapper.getMessageNotification(messageNotification);
    }

    @Override
    public MessageNotificationDTO getMessageNotificationById(String noticeId) {
        return noticeMapper.getMessageNotificationById(noticeId);
    }
}
