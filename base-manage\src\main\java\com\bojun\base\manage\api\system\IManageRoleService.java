/**
 * 
 */
package com.bojun.base.manage.api.system;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.bojun.base.manage.api.system.hystrix.ManageRoleServiceHystrix;
import com.bojun.system.dto.ManageRoleDTO;
import com.bojun.system.entity.ManageRole;

/**
*Model： 角色信息接口
*Description：角色信息接口
*Author: lj
*created：2020年4月30日
*/
@FeignClient(name="system-service", fallback = ManageRoleServiceHystrix.class)
public interface IManageRoleService {
	
	
	
	/**
	 * @Description 新增角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * String
	 * created：2020年4月30日
	 */
	@RequestMapping(value = "/addManageRole", method = RequestMethod.POST)
	String addManageRole(@RequestBody ManageRoleDTO manageRole);
	/**
	 * @Description 修改角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * String
	 * created：2020年4月30日
	 */
	@RequestMapping(value = "/updateManageRole", method = RequestMethod.POST)
	String updateManageRole(@RequestBody ManageRoleDTO manageRole);
	
	/**
	 * @Description 启用禁用角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * String
	 * created：2020年4月30日
	 */
	@RequestMapping(value = "/enableDisableManageRole", method = RequestMethod.POST)
	String enableDisableManageRole(@RequestBody ManageRole manageRole);
	
	/**
	 * @Description 查询所有角色
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * String
	 * created：2020年4月30日
	 */
	@RequestMapping(value = "/getManageRoleList", method = RequestMethod.POST)
	String getManageRoleList(@RequestBody ManageRoleDTO manageRole);
	/**
	 * @Description 查询角色
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * String
	 * created：2020年4月30日
	 */
	@RequestMapping(value = "/getManageRoleOne", method = RequestMethod.POST)
	String getManageRoleOne(@RequestBody ManageRoleDTO manageRole);

}
