package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：呼叫记录
 * Description：呼叫记录实体类
 * Author:刘修纬
 * created：2020年5月13日
 */
public class FormQuestionOption implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;


    private Integer optionId; // 选项id
    private Integer questionId; // 问题id
    private Integer score; // 分值
    private String optionValue; // 选项值
    private Integer showIndex; // 排序下标
    private Integer star; // '星级：1：一星  （依次类推）',
    private String remark; // 备注

    private Integer  isNoPoint;
    
    
    
    public Integer getIsNoPoint() {
		return isNoPoint;
	}

	public void setIsNoPoint(Integer isNoPoint) {
		this.isNoPoint = isNoPoint;
	}

	public Integer getStar() {
		return star;
	}

	public void setStar(Integer star) {
		this.star = star;
	}

	public Integer getOptionId() {
        return optionId;
    }

    public void setOptionId(Integer optionId) {
        this.optionId = optionId;
    }


    public Integer getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Integer questionId) {
        this.questionId = questionId;
    }


    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }


    public String getOptionValue() {
        return optionValue;
    }

    public void setOptionValue(String optionValue) {
        this.optionValue = optionValue;
    }


    public Integer getShowIndex() {
        return showIndex;
    }

    public void setShowIndex(Integer showIndex) {
        this.showIndex = showIndex;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


}
