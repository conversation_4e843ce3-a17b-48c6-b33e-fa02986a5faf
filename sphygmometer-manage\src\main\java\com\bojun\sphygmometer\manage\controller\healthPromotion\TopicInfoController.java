package com.bojun.sphygmometer.manage.controller.healthPromotion;

import com.bojun.author.AuthAnnotation;
import com.bojun.common.controller.BaseController;
import com.bojun.common.util.LoginUserThreadLocal;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.service.api.TopicInfoFeignClient;
import com.bojun.page.Results;
import com.bojun.sphygmometer.manage.controller.healthPromotion.vo.TopicInfoParamVo;
import com.bojun.sphygmometer.manage.controller.healthPromotion.vo.TopicInfoVo;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.enums.SystemDictEnums;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import org.apache.catalina.servlet4preview.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 10:09
 */
@Api(tags = {"健康栏目"})
@RestController
@ApiSort(value = 0)
public class TopicInfoController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(TopicInfoController.class);

    public static final String BASE_URL = "/healthPromotion";

    @Autowired
    private TopicInfoFeignClient topicInfoService;


    @ApiOperation("查询栏目列表")
    @PostMapping("/getNewsTopicList")
    @AuthAnnotation(action = TopicInfoController.BASE_URL + "/getNewsTopicList")
    @ApiOperationSupport(order = 1)
    public Results<List<TopicInfoVo>> getNewsTopicList(@RequestBody TopicInfoParamVo tTopicInfoVo) {
        try {
            TopicInfoDTO topicInfoDTO = new TopicInfoDTO();
            BeanUtils.copyProperties(tTopicInfoVo, topicInfoDTO);
            topicInfoDTO.setSystemId(SystemDictEnums.SPHYGMOMETER_MANAGE.getSystemId());
            Results<List<TopicInfoDTO>> newsTopicList = topicInfoService.getNewsTopicList(topicInfoDTO);
            return Results.list(getListVo(newsTopicList.getData(), TopicInfoVo.class));
        } catch (BeansException e) {
            logger.error("/addNewsTopic:", e);
            return Results.fail("查询栏目列表失败!");
        }
    }


    @ApiOperation("添加子栏目")
    @PostMapping("/addNewsTopic")
    @AuthAnnotation(action = TopicInfoController.BASE_URL + "/addNewsTopic")
    @ApiOperationSupport(order = 2)
    public Results addNewsTopic(HttpServletRequest request, @RequestBody TopicInfoParamVo tTopicInfoVo) {
        try {
            if(tTopicInfoVo.getParentTopicId() == null){
                return Results.fail("栏目父id为空!");
            }
            TopicInfoDTO topicInfoDTO = new TopicInfoDTO();
            BeanUtils.copyProperties(tTopicInfoVo, topicInfoDTO);
            buildTopicInfo(topicInfoDTO);
            Integer result = topicInfoService.addNewsTopic(topicInfoDTO);
            return Results.data(result);
        } catch (Exception e) {
            logger.error("/addNewsTopic:", e);
            return Results.fail("添加子栏目失败!");
        }
    }


    @ApiOperation("更新栏目")
    @PostMapping("/updateNewsTopic")
    @AuthAnnotation(action = TopicInfoController.BASE_URL + "/updateNewsTopic")
    @ApiOperationSupport(order = 3)
    public Results updateNewsTopic(HttpServletRequest request, @RequestBody TopicInfoParamVo tTopicInfoVo) {
        try {
            TopicInfoDTO topicInfoDTO = new TopicInfoDTO();
            BeanUtils.copyProperties(tTopicInfoVo, topicInfoDTO);
            buildTopicInfo(topicInfoDTO);
            Integer result = topicInfoService.updateNewsTopic(topicInfoDTO);
            return Results.data(result);
        } catch (Exception e) {
            logger.error("/updateNewsTopic:", e);
            return Results.fail("更新栏目失败!");
        }
    }

    @ApiOperation("删除栏目")
    @PostMapping("/deleteNewsTopic")
    @AuthAnnotation(action = TopicInfoController.BASE_URL + "/deleteNewsTopic")
    @ApiOperationSupport(order = 4)
    public Results deleteNewsTopic(@RequestBody TopicInfoParamVo tTopicInfoVo) {
        try {
            TopicInfoDTO topicInfoDTO = new TopicInfoDTO();
            BeanUtils.copyProperties(tTopicInfoVo, topicInfoDTO);
            Integer result = topicInfoService.deleteNewsTopic(topicInfoDTO);
            return Results.data(result);
        } catch (Exception e) {
            logger.error("/updateNewsTopic:", e);
            return Results.fail("删除栏目失败!");
        }
    }

    /**
     * @param topicInfoDTO
     */
    private void buildTopicInfo(TopicInfoDTO topicInfoDTO) {
        ManageUserDTO userInfo = LoginUserThreadLocal.getUserInfo();
        topicInfoDTO.setCreateUserId(userInfo.getUserId());
        topicInfoDTO.setUpdateTime(new Date());
        topicInfoDTO.setCreateTime(new Date());
        if (null == topicInfoDTO.getIsDelete()) {
            topicInfoDTO.setIsDelete(0);
        }
    }
}
