package com.bojun.base.system.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.IManageRoleService;
import com.bojun.base.system.service.ISystemDictService;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.ManageRoleDTO;
import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.dto.ManageRoleSystemDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.entity.ManageRole;
import com.bojun.system.entity.ManageRoleDept;
import com.bojun.system.entity.ManageRoleMenu;
import com.bojun.utils.UuidGenerator;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Model：角色管理 Description：角色管理 Author:刘俊 created：2020年5月3日
 */
@RestController
public class ManageRoleController extends BoJunBaseController {

	private static Log log = LogFactory.getLog(ManageRoleController.class);

	@Autowired
	private IManageRoleService manageRoleService;
	@Autowired
	private ISystemDictService systemDictService;
	@Autowired
	private RedisUtil redisUtil;

	/**
	 * @Description 新增角色
	 * <AUTHOR>
	 * @param systemDictDto void 2020年4月27日
	 */
	@RequestMapping(value = "/addManageRole", method = RequestMethod.POST)
	@Transactional(rollbackFor = Exception.class)
	public void addManageRole(@RequestBody ManageRoleDTO manageRoleDTO) {
		try {
			int dataPermissions = manageRoleDTO.getDataPermissions().intValue();
			// 走角色权限，必须选择机构
			if (1 == dataPermissions && null == manageRoleDTO.getRoleOrganList()) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "请选择机构"));
				return;
			}
			// 走帐号权限，机构设置为空
			if (2 == dataPermissions) {
				manageRoleDTO.setRoleOrganList(null);
			}
			String UUid = UuidGenerator.getUuidGenerator();
			manageRoleDTO.setRoleId(UUid);
			int addNumber = manageRoleService.addManageRole(manageRoleDTO);

			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}

			List<ManageRoleSystemDTO> roleSystemList = manageRoleDTO.getRoleSystemList();
			if (null != roleSystemList && roleSystemList.size() > 0) {
				for (ManageRoleSystemDTO manageRoleSystemDTO : roleSystemList) {
					manageRoleSystemDTO.setRoleId(UUid);
					// 绑定产品权限
					manageRoleService.addManageRoleSystem(manageRoleSystemDTO);
					Integer rsId = manageRoleSystemDTO.getRsId();
					List<ManageRoleMenu> roleMenuList = manageRoleSystemDTO.getRoleMenuList();
					if (null != roleMenuList && roleMenuList.size() > 0) {
						for (ManageRoleMenu manageRoleMenu : roleMenuList) {
							manageRoleMenu.setRsId(rsId);
							// 绑定产品下的菜单权限
							manageRoleService.addManageRoleMenu(manageRoleMenu);
						}
					}
				}
			}
			// 特殊处理机构树
			List<ManageRoleOrganDTO> roleOrganList = new ArrayList<ManageRoleOrganDTO>();
			List<ManageRoleDept> roleDeptList = new ArrayList<ManageRoleDept>();
			roleOrganList = manageRoleDTO.getRoleOrganList();
			Integer roId = 0;
			if (null != roleOrganList && roleOrganList.size() > 0) {
				for (ManageRoleOrganDTO manageRoleOrganDTO : roleOrganList) {
					manageRoleOrganDTO.setRoleId(UUid);
					Map<String, Object> map = new HashMap<String, Object>();
					map.put("role_id", UUid);
					map.put("organizationId", manageRoleOrganDTO.getOrganizationId());
					List<ManageRoleOrganDTO> OrganList = manageRoleService.getRoleOrganByOrganId(map);
					if (null == OrganList || OrganList.size() == 0) {
						manageRoleService.addManageRoleOrgan(manageRoleOrganDTO);
						roId = manageRoleOrganDTO.getRoId();
					} else {
						roId = OrganList.get(0).getRoId();
					}
					roleDeptList = manageRoleOrganDTO.getRoleDeptList();
					ManageRoleDept newManageRoleDept = new ManageRoleDept();
					newManageRoleDept.setRoId(roId);
					if (null != roleDeptList && roleDeptList.size() > 0) {
						for (ManageRoleDept newRoleDept : roleDeptList) {
							newManageRoleDept.setDeptId(newRoleDept.getDeptId());
							newManageRoleDept.setWardId(newRoleDept.getWardId());
							List<ManageRoleDept> oldRoleDeptList = manageRoleService.getRoleDeptByDeptId(newManageRoleDept);
							if (null == oldRoleDeptList || oldRoleDeptList.size() <= 0) {
								newRoleDept.setRoId(roId);
								// 绑定机构下的部门权限
								manageRoleService.addManageRoleDept(newRoleDept);
							}

						}
					}
				}
			}

			outJson(successInfo());
		} catch (Exception e) {
			log.error("addManageRole:", e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			outJson(info(500, e));

		}
	}

	/**
	 * @Description 修改角色
	 * <AUTHOR>
	 * @param ManageRole void 2020年4月27日
	 */
	@RequestMapping(value = "/updateManageRole", method = RequestMethod.POST)
	@Transactional(rollbackFor = Exception.class)
	public void updateManageRole(@RequestBody ManageRoleDTO manageRoleDTO) {
		try {
			// 走角色权限，必须选择机构  DataPermissions为1是角色权限 2是账号权限
			if (1 == manageRoleDTO.getDataPermissions().intValue() && null == manageRoleDTO.getRoleOrganList()) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "请选择机构"));
				return;
			}
			// 走帐号权限，机构设置为空
			if (2 == manageRoleDTO.getDataPermissions().intValue()) {
				manageRoleDTO.setRoleOrganList(null);
			}
			// 修改角色信息
			ManageRole manageRole = new ManageRole();
			manageRole.setRoleId(manageRoleDTO.getRoleId());
			manageRole.setRoleDescribe(manageRoleDTO.getRoleDescribe());
			manageRole.setDataPermissions(manageRoleDTO.getDataPermissions());
			manageRole.setRoleName(manageRoleDTO.getRoleName());
			int addNumber = manageRoleService.updateManageRole(manageRole);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			// 解除角色关联的表
			manageRoleService.deleteManageRoleSystem(manageRole);
			manageRoleService.deleteManageRoleMenu(manageRole);
			manageRoleService.deleteManageRoleOrgan(manageRole);
			manageRoleService.deleteManageRoleDept(manageRole);
			// 重新绑定关联关系
			List<ManageRoleSystemDTO> roleSystemList = manageRoleDTO.getRoleSystemList();
			if (null != roleSystemList && roleSystemList.size() > 0) {
				for (ManageRoleSystemDTO manageRoleSystemDTO : roleSystemList) {
					manageRoleSystemDTO.setRoleId(manageRoleDTO.getRoleId());
					// 绑定产品权限
					manageRoleService.addManageRoleSystem(manageRoleSystemDTO);
					Integer rsId = manageRoleSystemDTO.getRsId();
					List<ManageRoleMenu> roleMenuList = manageRoleSystemDTO.getRoleMenuList();
					if (null != roleMenuList && roleMenuList.size() > 0) {
						for (ManageRoleMenu manageRoleMenu : roleMenuList) {
							manageRoleMenu.setRsId(rsId);
							// 绑定产品下的菜单权限
							manageRoleService.addManageRoleMenu(manageRoleMenu);
						}
					}
				}
			}
			List<ManageRoleOrganDTO> roleOrganList = manageRoleDTO.getRoleOrganList();
			Integer roId = 0;
			if (null != roleOrganList && roleOrganList.size() > 0) {
				for (ManageRoleOrganDTO manageRoleOrganDTO : roleOrganList) {
					manageRoleOrganDTO.setRoleId(manageRoleDTO.getRoleId());
					// 绑定机构权限
//					Map<String, Object> map = new HashMap<String, Object>();
//					map.put("role_id", manageRoleDTO.getRoleId());
//					map.put("organizationId", manageRoleOrganDTO.getOrganizationId());
//					List<ManageRoleOrganDTO> OrganList = manageRoleService.getRoleOrganByOrganId(map);
//					if (null == OrganList || OrganList.size() == 0) {
					manageRoleService.addManageRoleOrgan(manageRoleOrganDTO);
					roId = manageRoleOrganDTO.getRoId();
//					} else {
//						roId = OrganList.get(0).getRoId();
//					}
					List<ManageRoleDept> roleDeptList = manageRoleOrganDTO.getRoleDeptList();
//					ManageRoleDept newManageRoleDept = new ManageRoleDept();
//					newManageRoleDept.setRoId(roId);
					if (null != roleDeptList && roleDeptList.size() > 0) {
						for (ManageRoleDept newRoleDept : roleDeptList) {
//							newManageRoleDept.setDeptId(newRoleDept.getDeptId());
//							newManageRoleDept.setWardId(newRoleDept.getWardId());
//							List<ManageRoleDept> oldRoleDeptList = manageRoleService.getRoleDeptByDeptId(newManageRoleDept);
//							if (null == oldRoleDeptList || oldRoleDeptList.size() <= 0) {
							newRoleDept.setRoId(roId);
							// 绑定机构下的部门权限
							manageRoleService.addManageRoleDept(newRoleDept);
//							}
     						}
					}
				}
			}
			//删除缓存中的超管、角色权限机构缓存
			this.redisUtil.deleteByPrefix(Contants.RK_SUPERADMIN_AUTH_PREFIX);
			this.redisUtil.deleteByPrefix(Contants.RK_ROLE_AUTH_PREFIX);
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateManageRole:", e);
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			outJson(info(500, e));
		}
	}

	/**
	 * @Description 启用禁用角色
	 * <AUTHOR>
	 * @param ManageRole void 2020年4月27日
	 */
	@RequestMapping(value = "/enableDisableManageRole", method = RequestMethod.POST)
	public void enableDisableManageRole(@RequestBody ManageRole manageRole) {
		try {

			int addNumber = manageRoleService.updateManageRole(manageRole);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("enableDisableManageRole:", e);
			outJson(info(500, e));
		}
	}

	/**
	 * @Description 分页查询角色列表
	 * <AUTHOR> void 2020年4月27日
	 */
	@RequestMapping(value = "/getManageRoleList", method = RequestMethod.POST)
	public void getManageRoleList(@RequestBody ManageRoleDTO manageRoleDTO) {
		try {
			int pageNum = (null == manageRoleDTO.getPageNum() ? 1 : manageRoleDTO.getPageNum());
			int everyPage = (null == manageRoleDTO.getEveryPage() ? 10 : manageRoleDTO.getEveryPage());
			PageHelper.startPage(pageNum, everyPage);
			Page<ManageRoleDTO> page = manageRoleService.getManageRoleList(manageRoleDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {
			log.error("getManageRoleList:", e);
			outJson(info(500, e));
		}
	}

	/**
	 * @Description 查询角色
	 * <AUTHOR> void 2020年4月27日
	 */
	@RequestMapping(value = "/getManageRoleOne", method = RequestMethod.POST)
	public void getManageRoleOne(@RequestBody ManageRoleDTO manageRoleDTO) {
		try {

			ManageRoleDTO dto = manageRoleService.getManageRoleOne(manageRoleDTO);
			if (dto == null) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			// 查询绑定的菜单
			SystemDictDTO systemDictDto = new SystemDictDTO();
			systemDictDto.setRoleId(manageRoleDTO.getRoleId());
			dto.setSystemList(systemDictService.getSystemDictByRoleId(systemDictDto));
			// 查询绑定的机构
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("roleId", manageRoleDTO.getRoleId());
			dto.setRoleOrganTreeList(manageRoleService.roleOrganTreeList(map));
			outJson(successInfo(dto));
		} catch (Exception e) {
			log.error("getManageRoleOne:", e);
			outJson(info(500, e));
		}
	}
}
