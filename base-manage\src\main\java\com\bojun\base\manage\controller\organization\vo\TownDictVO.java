/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model： 乡镇管理
 * Description：乡镇管理
 * Author：赖水秀
 * created： 2020年7月2日
 */
@ApiModel(value = "乡镇信息", description = "查询乡镇返回信息")
public class TownDictVO implements Serializable {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = -2392360913295507404L;

	@ApiModelProperty(value="乡镇code")
	private String townCode;

	@ApiModelProperty(value="乡镇名称")
	private String townName;

	@ApiModelProperty(value="县code")
	private String countyCode;

	public String getTownCode() {
		return townCode;
	}

	public void setTownCode(String townCode) {
		this.townCode = townCode;
	}

	public String getTownName() {
		return townName;
	}

	public void setTownName(String townName) {
		this.townName = townName;
	}

	public String getCountyCode() {
		return countyCode;
	}

	public void setCountyCode(String countyCode) {
		this.countyCode = countyCode;
	}
	
}
