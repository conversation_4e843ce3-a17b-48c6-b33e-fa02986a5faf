package com.bojun.organization.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName GetAuthOrganizationV2DTO
 * <AUTHOR>
 * @Date 2021/5/7 18:02
 * @Description GetAuthOrganizationV2DTO
 * @Version 1.0
 */
@ApiModel(value = "GetAuthOrganizationV2DTO对象")
@Data
public class GetAuthOrganizationV2DTO implements Serializable {
    private static final long serialVersionUID = 4080716901998412843L;

    @ApiModelProperty(value = "角色ID", example = "")
    private String roleId;

    @ApiModelProperty(value = "权限类型（0：超级管理员，1：普通管理员）", example = "")
    private Integer authType;

    @ApiModelProperty(value = "父级ID, 传此ID返回的是包含此机构在内的及其子机构, 不传则查全部", example = "")
    private Integer parentId;

    @ApiModelProperty(value = "机构分类code(1医疗，2养老，3监管，4运维，5疾控)", example = "")
    private String organizationClassCode;

    @ApiModelProperty(value = "用户所属机构ID", example = "")
    private Integer organizationId;

    @ApiModelProperty(value = "数据权限1角色机构2账号机构", example = "")
    private Integer dataPermissions;

}
