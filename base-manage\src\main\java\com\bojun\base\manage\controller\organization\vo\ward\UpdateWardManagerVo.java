package com.bojun.base.manage.controller.organization.vo.ward;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 11:25
 */
@ApiModel(value = "添加病区-1", description = "添加病区")
public class UpdateWardManagerVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "病区id")
    private Integer wardId;
    @ApiModelProperty(value = "病区编码")
    private String wardNumber;
    @ApiModelProperty(value = "病区名称")
    private String wardName;
    @ApiModelProperty(value = "护士长ID", example = "1,2,3,4")
    private String matronId;
    @ApiModelProperty(value = "病区电话")
    private String telephoneNumber;
    @ApiModelProperty(value = "病区平面图")
    private String planeImage;
    @ApiModelProperty(value = "是否启用 0：否 1：是")
    private Integer isEnabled;
    @ApiModelProperty(value = "病区简介")
    private String content;
    @ApiModelProperty(value = "科室Id")
    private Integer deptId;

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }

    public String getWardNumber() {
        return wardNumber;
    }

    public void setWardNumber(String wardNumber) {
        this.wardNumber = wardNumber;
    }

    public String getWardName() {
        return wardName;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }

    public String getMatronId() {
        return matronId;
    }

    public void setMatronId(String matronId) {
        this.matronId = matronId;
    }

    public String getTelephoneNumber() {
        return telephoneNumber;
    }

    public void setTelephoneNumber(String telephoneNumber) {
        this.telephoneNumber = telephoneNumber;
    }

    public String getPlaneImage() {
        return planeImage;
    }

    public void setPlaneImage(String planeImage) {
        this.planeImage = planeImage;
    }

    public Integer getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}