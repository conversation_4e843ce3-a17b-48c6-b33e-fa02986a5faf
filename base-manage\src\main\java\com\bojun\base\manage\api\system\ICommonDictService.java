/**
 * 
 */
package com.bojun.base.manage.api.system;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.bojun.base.manage.api.system.hystrix.CommonDictServiceHystrix;

/**
 * Model： 公共字典表
 * Description： 公共字典表
 * Author：赖水秀
 * created： 2020年5月7日
 */
@FeignClient(name="system-service", fallback = CommonDictServiceHystrix.class)
public interface ICommonDictService {
	
	/**
	 * @Description 省份列表
	 * <AUTHOR>	
	 * 2020年5月7日
	 */
	@RequestMapping(value = "/getProvinceList", method = RequestMethod.POST)
	String getProvinceList();
	
	
	/**
	 * @Description 市区列表
	 * <AUTHOR>
	 * @param provinceCode	
	 * 2020年5月7日
	 */	
	@RequestMapping(value = "/getCityList", method = RequestMethod.POST)
	String getCityList(@RequestParam(value="provinceCode") String provinceCode);
	
	
	
	/**
	 * @Description 县区列表
	 * <AUTHOR>
	 * @param cityCode
	 * 2020年4月27日
	 */	
	@RequestMapping(value = "/getCountyList", method = RequestMethod.POST)
	String getCountyList(@RequestParam(value="cityCode") String cityCode);

	
	/**
	 * @Description 乡镇列表
	 * <AUTHOR>
	 * @param countyCode
	 * @return
	 * String
	 * 2020年7月2日
	 */
	@RequestMapping(value = "/getTownList", method = RequestMethod.POST)
	public String getTownList(@RequestParam(value="countyCode") String countyCode);
	
	
	/**
	 * @Description 村居委会列表
	 * <AUTHOR>
	 * @param townCode
	 * @return
	 * String
	 * 2020年7月2日
	 */
	@RequestMapping(value = "/getVillageList", method = RequestMethod.POST)
	public String getVillageList(@RequestParam(value="townCode") String townCode);

}
