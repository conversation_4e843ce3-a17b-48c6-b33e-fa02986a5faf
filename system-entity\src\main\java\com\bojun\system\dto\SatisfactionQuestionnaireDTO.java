package com.bojun.system.dto;

import com.bojun.system.entity.SatisfactionQuestionnaire;

import java.util.List;
import java.util.Map;


/**
 *
 * Model：满意度问卷表 Description：满意度问卷表DTO Author:李欣颖 created：2020年5月7日
 */
public class SatisfactionQuestionnaireDTO extends SatisfactionQuestionnaire {

	private static final long serialVersionUID = -1344026328907379635L;
	private String validBeginDateStr; // 有效开始日期
	private String validEndDateStr; // 有效结束日期
	private String deleteTimeStr; // 删除时间
	private String createTimeStr; // 创建时间
	private String updateTimeStr;//// 更新时间
	private String deptNames;//科室字符串
	private Integer everyPage;    //每页显示数

	private Integer pageNum;   //页面数

	private Integer totalCount;  //总记录数

	private String numberScore;//人数/平均分分数
	private String keyWords;//关键字查询
	private List<FormQuestionDTO> formQuestionDTOList;//满意度问卷题目
	private List<Map<String, Object>> numberMapList;//饼图比例统计
	private Integer resultCount;//总人数
	private Integer totalScores; //全院总得分


	private int totalPerson;
	private List<FormQuestionDTO> formQuestionList;
	private String userId;
	 private String coverImgStr; // 封面图片
	 
	public String getCoverImgStr() {
		return coverImgStr;
	}

	public void setCoverImgStr(String coverImgStr) {
		this.coverImgStr = coverImgStr;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public List<FormQuestionDTO> getFormQuestionList() {
		return formQuestionList;
	}

	public void setFormQuestionList(List<FormQuestionDTO> formQuestionList) {
		this.formQuestionList = formQuestionList;
	}


	public int getTotalPerson() {
		return totalPerson;
	}

	public void setTotalPerson(int totalPerson) {
		this.totalPerson = totalPerson;
	}

	public String getDeptNames() {
		return deptNames;
	}

	public void setDeptNames(String deptNames) {
		this.deptNames = deptNames;
	}

	public List<Map<String, Object>> getNumberMapList() {
		return numberMapList;
	}

	public void setNumberMapList(List<Map<String, Object>> numberMapList) {
		this.numberMapList = numberMapList;
	}

	public Integer getResultCount() {
		return resultCount;
	}

	public void setResultCount(Integer resultCount) {
		this.resultCount = resultCount;
	}

	public Integer getTotalScores() {
		return totalScores;
	}

	public void setTotalScores(Integer totalScores) {
		this.totalScores = totalScores;
	}

	public List<FormQuestionDTO> getFormQuestionDTOList() {
		return formQuestionDTOList;
	}

	public void setFormQuestionDTOList(List<FormQuestionDTO> formQuestionDTOList) {
		this.formQuestionDTOList = formQuestionDTOList;
	}


	public String getNumberScore() {
		return numberScore;
	}

	public void setNumberScore(String numberScore) {
		this.numberScore = numberScore;
	}

	public String getKeyWords() {
		return keyWords;
	}

	public void setKeyWords(String keyWords) {
		this.keyWords = keyWords;
	}

	public Integer getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(Integer everyPage) {
		this.everyPage = everyPage;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public String getValidBeginDateStr() {
		return validBeginDateStr;
	}

	public void setValidBeginDateStr(String validBeginDateStr) {
		this.validBeginDateStr = validBeginDateStr;
	}

	public String getValidEndDateStr() {
		return validEndDateStr;
	}

	public void setValidEndDateStr(String validEndDateStr) {
		this.validEndDateStr = validEndDateStr;
	}

	public String getDeleteTimeStr() {
		return deleteTimeStr;
	}

	public void setDeleteTimeStr(String deleteTimeStr) {
		this.deleteTimeStr = deleteTimeStr;
	}

	public String getCreateTimeStr() {
		return createTimeStr;
	}

	public void setCreateTimeStr(String createTimeStr) {
		this.createTimeStr = createTimeStr;
	}

	public String getUpdateTimeStr() {
		return updateTimeStr;
	}

	public void setUpdateTimeStr(String updateTimeStr) {
		this.updateTimeStr = updateTimeStr;
	}

}
