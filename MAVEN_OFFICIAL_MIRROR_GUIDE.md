# Maven官方镜像配置指南

## 当前已实施的配置

### ✅ 项目级别配置
已在 `commons_utils/pom.xml` 中配置了官方仓库：

```xml
<repositories>
    <!-- Maven中央仓库 -->
    <repository>
        <id>central</id>
        <name>Maven Central Repository</name>
        <url>https://repo1.maven.org/maven2/</url>
    </repository>
    
    <!-- 个推官方仓库 -->
    <repository>
        <id>getui-nexus</id>
        <name>Getui Maven Repository</name>
        <url>http://mvn.gt.getui.com/nexus/content/repositories/releases/</url>
    </repository>
    
    <!-- Spring官方仓库 -->
    <repository>
        <id>spring-releases</id>
        <name>Spring Releases</name>
        <url>https://repo.spring.io/libs-release</url>
    </repository>
</repositories>
```

## 全局配置方案

### 方案1：使用提供的模板文件

1. **复制模板文件**
```bash
# 复制maven-settings-template.xml到用户目录
cp maven-settings-template.xml %USERPROFILE%\.m2\settings.xml
```

2. **或者手动创建**
在 `%USERPROFILE%\.m2\settings.xml` 位置创建配置文件

### 方案2：修改Maven安装目录的settings.xml

找到Maven安装目录下的 `conf/settings.xml` 文件并添加镜像配置。

## 官方仓库地址

### 主要仓库
- **Maven中央仓库**: https://repo1.maven.org/maven2/
- **Spring官方仓库**: https://repo.spring.io/libs-release
- **Apache仓库**: https://repository.apache.org/content/repositories/releases/
- **Sonatype仓库**: https://oss.sonatype.org/content/repositories/releases/

### 特殊仓库
- **个推SDK**: http://mvn.gt.getui.com/nexus/content/repositories/releases/
- **阿里云镜像**: https://maven.aliyun.com/repository/central (可选，用于加速)

## 验证配置

### 1. 检查配置是否生效
```bash
mvn help:effective-settings
```

### 2. 测试依赖下载
```bash
# 清理本地缓存
mvn dependency:purge-local-repository

# 重新下载依赖
mvn clean compile -U
```

### 3. 查看依赖来源
```bash
mvn dependency:tree -Dverbose
```

## 性能对比

| 仓库类型 | 下载速度 | 稳定性 | 完整性 |
|---------|---------|--------|--------|
| Maven中央仓库 | 🌟🌟🌟 | 🌟🌟🌟🌟🌟 | 🌟🌟🌟🌟🌟 |
| 阿里云镜像 | 🌟🌟🌟🌟🌟 | 🌟🌟🌟🌟 | 🌟🌟🌟🌟 |
| 其他镜像 | 🌟🌟🌟 | 🌟🌟🌟 | 🌟🌟🌟 |

## 推荐配置

### 开发环境
```xml
<!-- 使用阿里云镜像加速 -->
<mirror>
    <id>aliyun-maven</id>
    <mirrorOf>central</mirrorOf>
    <name>Aliyun Maven Repository</name>
    <url>https://maven.aliyun.com/repository/central</url>
</mirror>
```

### 生产环境
```xml
<!-- 使用官方中央仓库确保稳定性 -->
<mirror>
    <id>central-mirror</id>
    <mirrorOf>central</mirrorOf>
    <name>Maven Central Repository</name>
    <url>https://repo1.maven.org/maven2/</url>
</mirror>
```

## 常见问题

### 1. 依赖下载慢
- 使用阿里云镜像加速
- 配置HTTP代理
- 使用企业内部仓库

### 2. 某些依赖找不到
- 添加特定的仓库配置
- 检查依赖坐标是否正确
- 使用 `-U` 强制更新

### 3. 网络连接问题
- 检查防火墙设置
- 配置代理服务器
- 使用HTTPS协议

## 下一步操作

1. **测试当前配置**
```bash
cd commons_utils
mvn clean compile
```

2. **如果需要全局配置**
```bash
# 复制模板到用户目录
copy maven-settings-template.xml %USERPROFILE%\.m2\settings.xml
```

3. **验证配置生效**
```bash
mvn help:effective-settings | findstr repository
```
