package com.bojun.zip;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class ZipUtil {
	private static final Log log = LogFactory.getLog(ZipUtil.class);
	
	public static final String DOT = ".";
	public static final String EXT = "zip";
	/**
	 * 压缩文件
	 * 
	 * @param srcfile
	 *            File[] 需要压缩的文件列表
	 * @param zipfile
	 *            File 压缩后的文件
	 */
	public static void zipFiles(List<File> srcfile, File zipfile) {
		byte[] buf = new byte[1024];
		try {
			// Create the ZIP file
			ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
			// Compress the files
			for (int i = 0; i < srcfile.size(); i++) {
				File file = srcfile.get(i);
				FileInputStream in = new FileInputStream(file);
				// Add ZIP entry to output stream.
				out.putNextEntry(new ZipEntry(file.getName()));
				// Transfer bytes from the file to the ZIP file
				int len;
				while ((len = in.read(buf)) > 0) {
					out.write(buf, 0, len);
				}
				// Complete the entry
				out.closeEntry();
				in.close();
			}
			// Complete the ZIP file
			out.close();
		} catch (IOException e) {
			log.error("ZipUtil zipFiles exception:" + e);
		}
	}
	public static void compress(String path, boolean delete) throws Exception {
		File file = new File(path);
		int dotIndex = path.indexOf(".");
		if(dotIndex != -1){
			path = path.substring(0, dotIndex);
		}
		File zipfile = new File(path + DOT + EXT);
		zipFiles(file, zipfile, delete);
	}
	/**
	 * 压缩文件
	 * 
	 * @param srcfile
	 *            File[] 需要压缩的文件列表
	 * @param zipfile
	 *            File 压缩后的文件
	 */
	public static void zipFiles(File srcfile, File zipfile, Boolean delete) {
		byte[] buf = new byte[1024];
		try {
			// Create the ZIP file
			ZipOutputStream out = new ZipOutputStream(new FileOutputStream(
					zipfile));
			// Compress the files			
			FileInputStream in = new FileInputStream(srcfile);
			// Add ZIP entry to output stream.
			out.putNextEntry(new ZipEntry(srcfile.getName()));			
			// Transfer bytes from the file to the ZIP file
			int len;
			while ((len = in.read(buf)) > 0) {
				out.write(buf, 0, len);
			}						
			// Complete the entry
			out.closeEntry();
			in.close();		
			// Complete the ZIP file
			out.close();
			//delete the file
			if (delete) {
				ZipUtil.deleteFile(srcfile);
			}
		} catch (IOException e) {
			log.error("ZipUtil zipFiles exception:" + e);
		}
	}
	
	/**
	 * 解压缩
	 * 
	 * @param zipfile
	 *            File 需要解压缩的文件
	 * @param descDir
	 *            String 解压后的目标目录
	 */
	public static List<String> unZipFiles(File zipfile, String descDir) {
		List<String> fileNameList = new ArrayList<String>();
		try {
			// Open the ZIP file
			ZipFile zf = new ZipFile(zipfile);
			for (Enumeration entries = zf.entries(); entries.hasMoreElements();) {
				// Get the entry name
				ZipEntry entry = ((ZipEntry) entries.nextElement());
				String zipEntryName = entry.getName();
				fileNameList.add(zipEntryName);
				InputStream in = zf.getInputStream(entry);
				// System.out.println(zipEntryName);
				OutputStream out = new FileOutputStream(descDir + zipEntryName);
				byte[] buf1 = new byte[1024];
				int len;
				while ((len = in.read(buf1)) > 0) {
					out.write(buf1, 0, len);
				}
				// Close the file and stream
				in.close();
				out.close();
			}
			zf.close();//关闭压缩文件，否则不能删除压缩包  
		} catch (IOException e) {
			log.error("ZipUtil unZipFiles exception:" + e);
		}
		return fileNameList;
	}



	/**
	 * 下载文件
	 * 
	 * @param response
	 * @param serverPath
	 * @param str
	 * @return
	 */
	public static boolean downloadFile(HttpServletResponse response,
			String serverPath, String str) {
		boolean flag = false;
		try {
			String path = serverPath + str;
			File file = new File(path);
			if (file.exists()) {
				InputStream ins = new FileInputStream(path);
				BufferedInputStream bins = new BufferedInputStream(ins);// 放到缓冲流里面
				OutputStream outs = response.getOutputStream();// 获取文件输出IO流
				BufferedOutputStream bouts = new BufferedOutputStream(outs);
				response.setContentType("application/x-download");// 设置response内容的类型
				response.setHeader("Content-disposition",
						"attachment;filename=" + URLEncoder.encode(str.replaceFirst("\\\\", "").replaceFirst("/", ""), "GBK"));// 设置头部信息
				int bytesRead = 0;
				byte[] buffer = new byte[8192];
				// 开始向网络传输文件流
				while ((bytesRead = bins.read(buffer, 0, 8192)) != -1) {
					bouts.write(buffer, 0, bytesRead);
				}
				bouts.flush();// 这里一定要调用flush()方法
				ins.close();
				bins.close();
				outs.close();
				bouts.close();
				flag = true;
			} else {
				// response.sendRedirect("../error.jsp");
				return false;
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return flag;
	}
	
	/**
	 * 下载ZIP文件
	 * 
	 * @param response
	 * @param path
	 * @param fileName
	 * @return
	 */
	public static boolean downloadZipFile(HttpServletResponse response,
			String path, String fileName) {
		boolean flag = false;
		try {			
			File file = new File(path);
			if (file.exists()) {
				InputStream ins = new FileInputStream(path);
				BufferedInputStream bins = new BufferedInputStream(ins);// 放到缓冲流里面
				OutputStream outs = response.getOutputStream();// 获取文件输出IO流
				BufferedOutputStream bouts = new BufferedOutputStream(outs);
				response.setContentType("application/x-download");// 设置response内容的类型
				response.setHeader("Content-disposition",
						"attachment;filename=" + URLEncoder.encode(fileName, "GBK"));// 设置头部信息
				int bytesRead = 0;
				byte[] buffer = new byte[8192];
				// 开始向网络传输文件流
				while ((bytesRead = bins.read(buffer, 0, 8192)) != -1) {
					bouts.write(buffer, 0, bytesRead);
				}
				bouts.flush();// 这里一定要调用flush()方法
				ins.close();
				bins.close();
				outs.close();
				bouts.close();
				flag = true;
			} else {
				// response.sendRedirect("../error.jsp");
				return false;
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return flag;
	}

	/**
	 * 下载文件
	 * 
	 * @param response
	 * @param serverPath
	 * @param str
	 * @return
	 */
	public static boolean deleteFile(File file) {
		if (file.exists()) {
			if (file.isFile()) {
				return file.delete();
			} else if (file.isDirectory()) {
				File files[] = file.listFiles();
				for (int i = 0; i < files.length; i++) {
					deleteFile(files[i]);
				}
			}
			return file.delete();
		} else {
			System.out.println("所删除的文件不存在！" + '\n');
			return false;
		}
	}
	
	
	/**
	 * Main
	 * 
	 * @param args
	 */
	public static void main(String[] args) {
		List<File> srcfile = new ArrayList<File>();
		srcfile.add(new File("e:\\1.xls"));
		srcfile.add(new File("e:\\2.xls"));
		srcfile.add(new File("e:\\3.xls"));
		srcfile.add(new File("e:\\4.xls"));
		srcfile.add(new File("e:\\5.xls"));
		/*File srcfile = new File("e:\\1.xls");*/
		File zipfile = new File("e:\\edm.zip");
		ZipUtil.zipFiles(srcfile, zipfile);
	}

}