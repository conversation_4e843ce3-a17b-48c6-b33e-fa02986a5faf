/**
 *
 */
package com.bojun.base.system.service.impl;

import com.bojun.base.system.mapper.ManageRoleMapper;
import com.bojun.base.system.mapper.ManageUserLoginMapper;
import com.bojun.base.system.mapper.ManageUserMapper;
import com.bojun.base.system.mapper.SystemDictMapper;
import com.bojun.base.system.service.IManageUserService;
import com.bojun.commons.rong.RongUtils;
import com.bojun.healthcare.dto.DoctorInfoDTO;
import com.bojun.healthcare.entity.dto.AppUserDTO;
import com.bojun.organization.dto.OrganizationInfoDTO;
import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.entity.ManageRoleDept;
import com.bojun.system.entity.ManageUser;
import com.bojun.system.entity.ManageUserLogin;
import com.bojun.system.enums.SystemTypeEnums;
import com.bojun.utils.PropertiesUtils;
import com.github.pagehelper.Page;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *Model：
 *Description：
 *Author:段德鹏
 *created：2020年4月24日
 **/
@Service
public class ManageUserServiceImpl implements IManageUserService {

    @Autowired
    private ManageRoleMapper manageRoleMapper;

    @Autowired
    private ManageUserMapper manageUserMapper;

    @Autowired
    private ManageUserLoginMapper manageUserLoginMapper;


    @Autowired
    private SystemDictMapper systemDictMapper;

    private String internetHospital = PropertiesUtils.getProperty("application.properties", "internetHospital");

    /**
     * @Description 根据系统管理员账号查询管理员信息
     * <AUTHOR>
     * created：2020年4月24日
     */
    @Override
    public ManageUserDTO getManageUserByParams(ManageUserDTO manageUserDTO) {
        ManageUserDTO manageUser = manageUserMapper.getManageUserByParams(manageUserDTO);
        //走角色权限
        if (null == manageUser) {
            return null;
        }
        if (null != manageUser.getDataPermissions() &&
                1 == manageUser.getDataPermissions().intValue() &&
                1 == manageUser.getAuthType().intValue()) {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("role_id", manageUser.getRoleId());
            //查询机构
            List<ManageRoleOrganDTO> manageRoleOrganList = manageRoleMapper.getRoleOrganDet(map);
            manageUser.setManageRoleOrganList(manageRoleOrganList);
        }
        return manageUser;
    }

    /**
     *
     * @Description 标记数据权限机构
     * <AUTHOR>
     * @param srcOrg
     * @param dataPermissionOrg
     * void
     * 2020年9月23日
     */
    private List<OrganizationInfoDTO> markOrg(List<OrganizationInfoDTO> srcOrg, List<ManageRoleOrganDTO> dataPermissionOrg) {
        for (OrganizationInfoDTO org : srcOrg) {
            for (ManageRoleOrganDTO dataOrg : dataPermissionOrg) {
                org.setDataPermissionNode(0);
                if (org.getOrganizationId().equals(dataOrg.getOrganizationId())) {
                    org.setDataPermissionNode(1);
                }
            }
            List<OrganizationInfoDTO> children = org.getChildren();
            if (children == null || !children.isEmpty()) {
                continue;
            }
            List<OrganizationInfoDTO> childrenOrg = markOrg(children, dataPermissionOrg);
            org.setChildren(childrenOrg);
        }
        return srcOrg;
    }

    /**
     * @param sourceList
     * @return List
     * 2020年5月4日
     * @Description 列表转换成树形机构
     * <AUTHOR>
     */
    private static List<OrganizationInfoDTO> toTree(List<OrganizationInfoDTO> sourceList) {
        List<OrganizationInfoDTO> rest = new ArrayList<OrganizationInfoDTO>();
        Map map = (Map) sourceList.stream().collect(Collectors.toMap(OrganizationInfoDTO::getOrganizationId, Function.identity()));

        sourceList.forEach(data -> {
            OrganizationInfoDTO item = (OrganizationInfoDTO) data;

            if (item.getParentId() == null || item.getParentId() <= 0) {
                item.setType("1");
                rest.add(item);

                item.setChildren(new ArrayList<>());
            }
        });
        sourceList.forEach(data -> {
            OrganizationInfoDTO item = (OrganizationInfoDTO) data;

            if (item.getParentId() != null && item.getParentId() > 0) {
                OrganizationInfoDTO parent = (OrganizationInfoDTO) map.get(item.getParentId());
                if (parent.getChildren() == null) {
                    parent.setChildren(new ArrayList<>());
                    parent.setType("1");
                } else {
                    parent.setType("2");
                }
                parent.getChildren().add(item);
            }
        });

        return rest;
    }

    /**
     * @Description 更新管理员用户信息
     * <AUTHOR>
     * created：2020年4月24日
     */
    @Override
    public int updateManageUserByUserId(ManageUser manageUser) {
        return manageUserMapper.updateManageUserByUserId(manageUser);
    }

    /**
     * @Description 新增管理员用户登陆信息
     * <AUTHOR>
     * created：2020年4月24日
     */
    @Override
    public void addManageUserLogin(ManageUserLogin manageUserLogin) {
        manageUserLoginMapper.addManageUserLogin(manageUserLogin);
    }

    /**
     * @Description 新增用户
     * <AUTHOR>
     * @return
     * int
     * 2020年4月27日
     */
    public int addManageUser(ManageUser manageUser) {
        manageUserMapper.addManageUser(manageUser);
        return 1;
    }

    /**
     * @Description 修改用户
     * <AUTHOR>
     * @return
     * int
     * 2020年4月27日
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateManageUser(ManageUser manageUser) {
        return manageUserMapper.updateManageUser(manageUser);
    }

    /**
     * @Description 重置用户密码
     * <AUTHOR>
     * @return
     * int
     * 2020年4月27日
     */
    public int resetManageUserPassword(ManageUser manageUser) {
        return manageUserMapper.resetManageUserPassword(manageUser);
    }

    /**
     * @Description 查询用户列表
     * <AUTHOR>
     * @return
     * List<ManageUserDTO>
     * 2020年4月27日
     */
    public Page<ManageUserDTO> getManageUserList(ManageUserDTO manageUserDTO) {

        Page<ManageUserDTO> userList = manageUserMapper.getManageUserList(manageUserDTO);

//				for (ManageUserDTO user : userList.getResult()) {
//					 //走角色权限
//					if(1==user.getDataPermissions().intValue()) {
//						Map<String,Object> map=new HashMap<String, Object>();
//						map.put("role_id", user.getRoleId());
//						List<ManageRoleOrganDTO>	manageRoleOrganList=manageRoleMapper.getRoleOrganDet(map);
//						user.setManageRoleOrganList(manageRoleOrganList);
//					}
//				}
        return userList;
    }

    /**
     * @Description 启用禁用用户状态
     * <AUTHOR>
     * @return
     * int
     * 2020年4月27日
     */
    public int enableDisableUser(ManageUser manageUser) {
        return manageUserMapper.enableDisableUser(manageUser);
    }

    @Override
    public List<ManageUserDTO> getManageUserListByOrgId(Integer orgId) {
        return manageUserMapper.getManageUserListByOrgId(orgId);
    }

    /**
     * @Description 修改用户密码
     * <AUTHOR>
     * @return
     * List<ManageUserDTO>
     * 2020年4月27日
     */
    //@Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT, timeout = 36000, rollbackFor = Exception.class)
    public int updateUserPasswords(ManageUser manageUser) {
        return manageUserMapper.updateUserPasswords(manageUser);
        //重新登录
        //manageUserLoginMapper.removeManageUserToken(manageUser.getUserId());
    }

    /**
     * @Description 通过身份证获取用户信息
     * <AUTHOR>
     * @return com.bojun.system.dto.ManageUserDTO
     * @date：2020-07-23日
     */
    @Override
    public ManageUserDTO loginByIdNo(ManageUserDTO manageUser) {
        return manageUserMapper.getManageUserByIdNo(manageUser);
    }


    @Override
    public ManageUserDTO getManageUserByMobile(String mobile) {
        return manageUserMapper.getManageUserByMobile(mobile);
    }

    /**
     *
     * @Description 确认用户是否存在于人事系统
     * <AUTHOR>
     * @param condition
     * @return
     * Integer
     * 2020年9月27日
     */
    public Integer checkUserExistEmployee(Map<String, Object> condition) {
        return manageUserMapper.checkUserExistEmployee(condition);
    }

    /**
     * 查询运维权限和角色权限
     * @return
     */
    @Override
    public Page<ManageUserDTO> getManageRoleUserList() {
        return manageUserMapper.getManageRoleUserList();
    }

    /**
     * 查询运维权限和角色权限
     * @return
     */
    @Override
    public Page<ManageUserDTO> getManageRoleUserListBySystemId(ManageUserDTO manageUser) {
        return manageUserMapper.getManageRoleUserListBySystemId(manageUser);
    }
}
