package com.bojun.base.system.controller;


import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.common.RongYunNoticeImpl;
import com.bojun.base.system.service.IRongNoticeService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.RongNoticeDTO;
import com.bojun.system.entity.RongNotice;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知前端控制器
 * @Description: 基础控台融云消息通知前端控制器
 * @since 2020-12-04
 */
@RestController
public class RongNoticeController extends BoJunBaseController {
    Log log = LogFactory.getLog(RongNoticeController.class);
    @Autowired
    private IRongNoticeService rongNoticeService;
    @Autowired
    private RongYunNoticeImpl rongYunNotice;

    /**
     * @return 2020-12-04
     * @Description 添加基础控台融云消息通知
     * <AUTHOR>
     */
    @RequestMapping(value = "/saveRongNotice", method = RequestMethod.POST)
    public void saveRongNotice(@RequestBody RongNoticeDTO rongnoticeDTO) {
        try {

            RongNotice rongnotice = new RongNotice();
            BeanUtils.copyProperties(rongnoticeDTO, rongnotice);
            rongnotice.setNoticeId(UUID.randomUUID().toString());
            if (rongnotice.getIsImmediately() == 1) {
                rongnotice.setPublishTime(new Date());
            }
            if (rongNoticeService.save(rongnotice)) {
                if (rongnoticeDTO.getIsImmediately() == 1) {
//                    RongYunDTO rongYunDTO = new RongYunDTO();
//                    rongYunDTO.setContent("《" + rongnoticeDTO.getTitle() + "》" + rongnoticeDTO.getNoticeContent());
//                    if (rongYunNotice.doctorPushNotice(rongYunDTO)) {
                    outJson(successInfo());
                    return;
//                    }
                }
                outJson(successInfo());
                return;
            }
            outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
        } catch (Exception e) {
            log.error("saveRongNotice:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @return 2020-12-04
     * @Description 删除基础控台融云消息通知
     * <AUTHOR>
     */
    @RequestMapping(value = "/deleteRongNotice", method = RequestMethod.POST)
    public void deleteRongNotice(@RequestBody List<String> noticeIds) {
        try {
            for (String noticeId : noticeIds) {
                RongNotice rongNotice = new RongNotice();
                rongNotice.setNoticeId(noticeId);
                rongNotice.setIsDelete(1);
                rongNoticeService.updateById(rongNotice);
            }
            outJson(successInfo());
        } catch (Exception e) {
            log.error("deleteRongNotice:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @return 2020-12-04
     * @Description 全查基础控台融云消息通知
     * <AUTHOR>
     */
    @RequestMapping(value = "/getRongNotice", method = RequestMethod.POST)
    public void getRongNotice(@RequestBody RongNoticeDTO rongnoticeDTO) {
        try {
            //分页默认处理
            int pageSize = (rongnoticeDTO.getEveryPage() == null) ? 10 : rongnoticeDTO.getEveryPage();
            int pageNum = (rongnoticeDTO.getPageNum() == null) ? 1 : rongnoticeDTO.getPageNum();
            PageHelper.startPage(pageNum, pageSize);
            Page<RongNoticeDTO> rongNoticeDTOS = rongNoticeService.getRongNotice(rongnoticeDTO);
            if (rongNoticeDTOS.getTotal() > 0) {
                outJson(successPageInfo(rongNoticeDTOS.getResult(), rongNoticeDTOS.getTotal()));
                return;
            }
            outJson(info(ResponseCodeEnum.NO_DATA.getCode()));
        } catch (Exception e) {
            log.error("getRongNotice:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @return 2020-12-04
     * @Description 单查基础控台融云消息通知
     * <AUTHOR>
     */
    @RequestMapping(value = "/getRongNoticeById", method = RequestMethod.POST)
    public void getRongNoticeById(@RequestBody RongNoticeDTO rongnoticeDTO) {
        try {
            RongNotice rongNotice = rongNoticeService.getById(rongnoticeDTO.getNoticeId());
            if (rongNotice != null) {
                outJson(successInfo(rongNotice));
                return;
            }
            outJson(info(ResponseCodeEnum.NO_DATA.getCode()));
        } catch (Exception e) {
            log.error("getRongNoticeById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }

    /**
     * @return 2020-12-04
     * @Description 编辑基础控台融云消息通知
     * <AUTHOR>
     */
    @RequestMapping(value = "/updateRongNoticeById", method = RequestMethod.POST)
    @Transactional(rollbackFor = Exception.class)
    public void updateRongNoticeById(@RequestBody RongNoticeDTO rongnoticeDTO) {
        try {
            if (rongnoticeDTO.getIsImmediately() == 1) {
                rongnoticeDTO.setPublishTime(new Date());
            }
            if (rongNoticeService.updateById(rongnoticeDTO)) {
                if (rongnoticeDTO.getIsImmediately() == 1) {
//                    RongYunDTO rongYunDTO = new RongYunDTO();
//                    rongYunDTO.setContent("《" + rongnoticeDTO.getTitle() + "》" + rongnoticeDTO.getNoticeContent());
//                    if (rongYunNotice.patientPushNotice(rongYunDTO)) {
                    outJson(successInfo());
                    return;
//                    }
                }
            }
            outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            log.error("updateRongNoticeById:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }
}

