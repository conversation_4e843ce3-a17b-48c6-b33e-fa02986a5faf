package com.bojun.base.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.SystemDictV2DTO;
import com.bojun.system.entity.SystemDictV2;

import java.util.List;

/**
 * SystemDictV2Service接口
 * 
 * <AUTHOR>
 * @date 2021-06-21 11:37:26
 */
public interface SystemDictV2Service extends IService<SystemDictV2>
{
    /**
     * 查询系统（项目）字典信息表
     * 
     * @param systemId 系统（项目）字典信息表ID
     * @return 系统（项目）字典信息表
     */
    public SystemDictV2DTO selectSystemDictV2ById(String systemId);

    /**
     * 查询系统（项目）字典信息表列表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 系统（项目）字典信息表集合
     */
    public List<SystemDictV2DTO> selectSystemDictV2List(SystemDictV2DTO systemDictV2DTO);

    /**
     * 新增系统（项目）字典信息表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 结果
     */
    public int insertSystemDictV2(SystemDictV2DTO systemDictV2DTO);

    /**
     * 修改系统（项目）字典信息表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 结果
     */
    public int updateSystemDictV2(SystemDictV2DTO systemDictV2DTO);
    
    /**
     * 新增系统（项目）字典信息表
     * 
     * @param systemDictV2 系统（项目）字典信息表
     * @return 结果
     */
    public int insertSystemDictV2(SystemDictV2 systemDictV2);

    /**
     * 修改系统（项目）字典信息表
     * 
     * @param systemDictV2 系统（项目）字典信息表
     * @return 结果
     */
    public int updateSystemDictV2(SystemDictV2 systemDictV2);

    /**
     * 批量删除系统（项目）字典信息表
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSystemDictV2ByIds(String ids);

    /**
     * 删除系统（项目）字典信息表信息
     * 
     * @param systemId 系统（项目）字典信息表ID
     * @return 结果
     */
    public int deleteSystemDictV2ById(String systemId);
}
