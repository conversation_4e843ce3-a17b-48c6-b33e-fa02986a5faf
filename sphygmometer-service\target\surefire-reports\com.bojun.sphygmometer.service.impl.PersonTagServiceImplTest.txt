-------------------------------------------------------------------------------
Test set: com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest
-------------------------------------------------------------------------------
Tests run: 2, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.155 s <<< FAILURE! - in com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest
testSelectListForTypeAndValue(com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest)  Time elapsed: 0.077 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\PersonTagMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select           id, code, name, table_name, column_name, value, tag_type         from t_person_tag     where tag_type = ?             and `value` = ?
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
	at com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest.testSelectListForTypeAndValue(PersonTagServiceImplTest.java:23)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
	at com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest.testSelectListForTypeAndValue(PersonTagServiceImplTest.java:23)

testGetPersonTagList(com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest)  Time elapsed: 0.064 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\PersonTagMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select            id, code, name, table_name, column_name, value, tag_type         from t_person_tag
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
	at com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest.testGetPersonTagList(PersonTagServiceImplTest.java:17)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_person_tag' doesn't exist
	at com.bojun.sphygmometer.service.impl.PersonTagServiceImplTest.testGetPersonTagList(PersonTagServiceImplTest.java:17)

