package com.bojun.health.promotion.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.common.entity.TopicInfo;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 11:24
 */
public interface TopicInfoService extends IService<TopicInfo> {
    int deleteByPrimaryKey(TopicInfoDTO record) throws Exception;

    int insert(TopicInfoDTO topicInfo);

    int insertSelective(TopicInfoDTO topicInfo);

    TopicInfoDTO selectByPrimaryKey(Integer topicId);

    List<TopicInfoDTO> getTopicInfo(TopicInfoDTO topicInfo);

    int updateByPrimaryKeySelective(TopicInfoDTO topicInfo);

    int updateByPrimaryKey(TopicInfoDTO topicInfo);
}
