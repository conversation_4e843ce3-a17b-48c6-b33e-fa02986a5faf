package com.bojun.health.promotion.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/7 14:42
 */
@ApiModel(value = "查询资讯参数", description = "查询资讯参数")
public class SelectNewsParamVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;

    @ApiModelProperty(value = "关键字")
    private String keyWord;
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    @ApiModelProperty(value = "行数  默认20")
    private Integer everyPage;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "结束时间")
    private String topicId;
    @ApiModelProperty(value = "状态  0:全部 1:已发布  2:待发布  3：已下架")
    private Integer status;
    @ApiModelProperty(value = "1健康资讯2医学资讯")
    private Integer sort;
    @ApiModelProperty(value = "1患者app2医生app3全部")
    private Integer showType;

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getEveryPage() {
        return everyPage;
    }

    public void setEveryPage(Integer everyPage) {
        this.everyPage = everyPage;
    }

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public String getTopicId() {
		return topicId;
	}

	public void setTopicId(String topicId) {
		this.topicId = topicId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}

	public Integer getShowType() {
		return showType;
	}

	public void setShowType(Integer showType) {
		this.showType = showType;
	}


}
