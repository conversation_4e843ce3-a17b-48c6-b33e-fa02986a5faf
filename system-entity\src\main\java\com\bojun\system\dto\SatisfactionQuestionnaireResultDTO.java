package com.bojun.system.dto;

import java.util.List;

import com.bojun.system.entity.SatisfactionQuestionnaireResult;
/**
 * 
*Model：满意度问卷答题结果信息表
*Description：满意度问卷答题结果信息表DTO
*Author:李欣颖
*created：2020年5月7日
 */
public class SatisfactionQuestionnaireResultDTO  extends SatisfactionQuestionnaireResult {

   private static final long serialVersionUID = -1344026328907379635L;
   private String startAnswerTimeStr; // 开始答题时间
   private String endAnswerTimeStr; // 结束答题时间
   private List<FormQuestionAnswerDTO> formQuestionAnswerDTOList;
   
   private List<FormQuestionAnswerDTO> formQuestionAnswerDTOS; //问题列表

   public List<FormQuestionAnswerDTO> getFormQuestionAnswerDTOS() {
       return formQuestionAnswerDTOS;
   }

   public void setFormQuestionAnswerDTOS(List<FormQuestionAnswerDTO> formQuestionAnswerDTOS) {
       this.formQuestionAnswerDTOS = formQuestionAnswerDTOS;
   }
public List<FormQuestionAnswerDTO> getFormQuestionAnswerDTOList() {
	return formQuestionAnswerDTOList;
}
public void setFormQuestionAnswerDTOList(List<FormQuestionAnswerDTO> formQuestionAnswerDTOList) {
	this.formQuestionAnswerDTOList = formQuestionAnswerDTOList;
}
public String getStartAnswerTimeStr() {
	return startAnswerTimeStr;
}
public void setStartAnswerTimeStr(String startAnswerTimeStr) {
	this.startAnswerTimeStr = startAnswerTimeStr;
}
public String getEndAnswerTimeStr() {
	return endAnswerTimeStr;
}
public void setEndAnswerTimeStr(String endAnswerTimeStr) {
	this.endAnswerTimeStr = endAnswerTimeStr;
}
    

  

}
