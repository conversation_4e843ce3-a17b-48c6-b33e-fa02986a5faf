package com.bojun.health.promotion.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.health.promotion.common.dto.NewsInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoParamDTO;
import com.bojun.health.promotion.common.dto.UpdateNewsDTO;
import com.bojun.health.promotion.common.entity.NewsInfo;
import com.bojun.health.promotion.common.entity.NewsTopic;
import com.bojun.health.promotion.common.entity.TopicInfo;
import com.bojun.health.promotion.mapper.NewsInfoMapper;
import com.bojun.health.promotion.mapper.NewsTopicMapper;
import com.bojun.health.promotion.mapper.TopicInfoMapper;
import com.bojun.health.promotion.service.NewsInfoService;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 18:19
 */
@Service
public class NewsInfoServiceImpl extends ServiceImpl<NewsInfoMapper, NewsInfo> implements NewsInfoService {
    private static Logger logger = LoggerFactory.getLogger(NewsInfoServiceImpl.class);
    @Autowired
    private NewsInfoMapper newsInfoMapper;
    @Autowired
    private NewsTopicMapper newsTopicMapper;
    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;
    @Autowired
    private TopicInfoMapper topicInfoMapper;

    @Override
    public int addNewsInfo(NewsInfoDTO newsInfo) {
        return newsInfoMapper.insertSelective(newsInfo);
    }

    @Override
    public NewsInfoDTO selectNewsInfoByNewsId(Integer newsInfo,boolean isLookOver) {
        NewsInfoDTO newsInfoDTO = newsInfoMapper.selectByPrimaryKey(newsInfo);
        if(isLookOver){
            // 阅读量加一 更新阅读数量
            int i = newsInfoMapper.updateReadNumberByNewsId(newsInfoDTO);
        }
        return newsInfoDTO;
    }

    @Override
    public int updateByPrimaryKeySelective(NewsInfoDTO newsInfo) throws Exception {
        return newsInfoMapper.updateByPrimaryKeySelective(newsInfo);
    }

    /**
     * 文章上架 下架
     * @param newsInfo
     * @return
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int newsPutonOrOffShelf(NewsInfoDTO newsInfo) throws Exception {
        NewsInfoDTO newsInfoDTO = newsInfoMapper.selectByPrimaryKey(newsInfo.getNewsId());
        // 1.对文章上架
        if (newsInfo.getStatus().equals(1)) {
            if (newsInfoDTO.getStatus().equals(1)) {
                throw new Exception("文章已上架，请勿重复操作！");
            }
            newsInfo.setPushTime(new Date());
            int i = newsInfoMapper.updateByPrimaryKeySelective(newsInfo);
            if (i <= 0) {
                throw new Exception("上架失败！");
            }
            newsInfo.setAccuratePush(newsInfoDTO.getAccuratePush());
            // 组装文章和栏目关联关系
            List<NewsTopic> newsTopicList = getNewsTopics(newsInfo);
            return BatchInsert(newsTopicList);
        }
        // 2.对文章下架
        else if (newsInfo.getStatus().equals(3)) {
            if (newsInfoDTO.getStatus().equals(1)) {
                int i = newsInfoMapper.updateByPrimaryKey(newsInfo);
                if (i <= 0) {
                    throw new Exception("下架失败！");
                }
            } else {
                throw new Exception("文章未上架，请先上架在操作下架！");
            }
            newsInfo.setAccuratePush(newsInfoDTO.getAccuratePush());
            // 删除文章和栏目关联关系
            return newsTopicMapper.deleteByNewsId(newsInfo.getNewsId());
        }
        return 0;
    }

    /**
     * 组装文章和栏目关联关系
     *
     * @param newsInfo
     * @return
     */
    private List<NewsTopic> getNewsTopics(NewsInfoDTO newsInfo) {
        List<UpdateNewsDTO> topicIdList = newsInfo.getTopicIdList();
        List<NewsTopic> newsTopicList = new ArrayList<>();
        for (UpdateNewsDTO updateNewsDTO : topicIdList) {
            NewsTopic newsTopic = new NewsTopic();
            newsTopic.setNewsId(newsInfo.getNewsId());
            newsTopic.setTopicId(String.valueOf(updateNewsDTO.getTopicId()));
            newsTopic.setCreateTime(new Date());
            newsTopic.setCreateUserId(newsInfo.getCreateUserId());
            newsTopic.setCreateUserName(newsInfo.getCreateUserName());
            newsTopic.setNewsShowIndex(updateNewsDTO.getNewsShowIndex());
            newsTopic.setIsTop(updateNewsDTO.getIsTop());
            newsTopicList.add(newsTopic);
        }
        // todo 保存下标

        // todo 置顶
        return newsTopicList;
    }

    @Override
    public int deleteByPrimaryKey(NewsInfoDTO newsInfo) {
        return newsInfoMapper.deleteByPrimaryKey(newsInfo);
    }

    //传了topicId，查的是栏目下的文章
    //不传代表查所有
    @Override
    public List<NewsInfoDTO> getNewsInfo(TopicInfoParamDTO topicInfo) {
        List<Integer> topicIdList = new ArrayList<>();
        // 父栏目id不为空，需要查询该栏目下所有的文章
        if (null != topicInfo.getTopicId()) {
            LambdaQueryWrapper<TopicInfo> queryWrapper = Wrappers.<TopicInfo>lambdaQuery()
                    .eq(TopicInfo::getParentTopicId, topicInfo.getTopicId())
                    .eq(TopicInfo::getIsDelete, 0)
                    .eq(TopicInfo::getSystemId, topicInfo.getSystemId());
            //如果是app请求，就过滤调禁用的栏目
            if (topicInfo.getIsApp() != null && topicInfo.getIsApp() == 1) {
                queryWrapper.eq(TopicInfo::getIsEnabled, 1);
            }
            List<TopicInfo> topicInfos = topicInfoMapper.selectList(queryWrapper);
            topicIdList = topicInfos.stream().map(a -> a.getTopicId()).collect(Collectors.toList());
            topicIdList.add(topicInfo.getTopicId());
            topicInfo.setTopicIdList(topicIdList);
        }
        PageHelper.startPage(topicInfo.getPageNum(),  topicInfo.getEveryPage());
        Page<NewsInfoDTO> newsInfo = newsInfoMapper.getNewsInfo(topicInfo);
        return newsInfo.getResult();
    }


    /**
     * 批量插入
     *
     * @param newsTopicList
     * @throws Exception
     */
    public Integer BatchInsert(List<NewsTopic> newsTopicList) throws Exception {
        long start = System.currentTimeMillis();
        SqlSession sqlSession = sqlSessionTemplate.getSqlSessionFactory().openSession(ExecutorType.BATCH, false);
        for (NewsTopic newsTopic : newsTopicList) {
            newsTopicMapper.insert(newsTopic);
        }
        sqlSession.commit();
        long end = System.currentTimeMillis();
        logger.info("---------------" + (start - end) + "---------------");
        return 1;

    }


}
