/**
 * 
 */
package com.bojun.base.manage.api.system.hystrix;

import org.springframework.stereotype.Component;

import com.bojun.base.manage.api.system.IManageUserService;
import com.bojun.exception.BaseRuntimeException;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.entity.ManageUser;

/**
*Model：用户信息接口熔断器
*Description：用户信息接口熔断器
*Author: lj
*created：2020年4月23日
*/
@Component
public class ManageUserServiceHystrix implements IManageUserService {

	@Override
	public String userLoginByPwd(ManageUserDTO manageUserDTO) {
		throw new BaseRuntimeException("userLoginByPwd接口服务已断开");
	}

	@Override
	public String addManageUser(ManageUser manageUser) {
		throw new BaseRuntimeException("addManageUser接口服务已断开");
	}

	@Override
	public String updateManageUser(ManageUser manageUser) {
		throw new BaseRuntimeException("updateManageUser接口服务已断开");
	}

	@Override
	public String getManageUserList(ManageUserDTO manageUser) {
		throw new BaseRuntimeException("getManageUserList接口服务已断开");
	}

	@Override
	public String updateUserPasswords(ManageUserDTO manageUser) {
		throw new BaseRuntimeException("updateUserPasswords接口服务已断开");
	}

	@Override
	public String enableDisableUser(ManageUser manageUser) {
		throw new BaseRuntimeException("enableDisableUser接口服务已断开");
	}

}
