/**
 * 
 */
package com.bojun.memcached;

import java.io.Serializable;
import java.util.Date;

/**
*Model：缓存信息实体类
*Description：缓存信息实体类
*Author: 段德鹏
*created：2019年1月13日
*/
public class MemCached implements Serializable {

	private static final long serialVersionUID = 8368616240036607406L;
	
	//缓存键
	private String key;
	//缓存值
	private Object value;
	//过期时间
	private Date expiry;
	
	public MemCached() {
		super();
	}

	public MemCached(String key, Object value, Date expiry) {
		super();
		this.key = key;
		this.value = value;
		this.expiry = expiry;
	}
	
	public String getKey() {
		return key;
	}
	public void setKey(String key) {
		this.key = key;
	}
	public Object getValue() {
		return value;
	}
	public void setValue(Object value) {
		this.value = value;
	}
	public Date getExpiry() {
		return expiry;
	}
	public void setExpiry(Date expiry) {
		this.expiry = expiry;
	}
}
