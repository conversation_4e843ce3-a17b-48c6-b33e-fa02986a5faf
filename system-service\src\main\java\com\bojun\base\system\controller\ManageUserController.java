package com.bojun.base.system.controller;


import com.alibaba.fastjson.JSON;
import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.config.CommonConfig;
import com.bojun.base.system.service.IManageUserService;
import com.bojun.base.system.service.ISystemOperationLogService;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.commons.rong.RongUtils;
import com.bojun.commons.sms.AliyunSMSUtil;
import com.bojun.contants.Contants;
import com.bojun.contants.FilePathConstants;
import com.bojun.encrypt.MD5Util;
import com.bojun.encrypt.comms.StrEncrypt;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.page.Results;
import com.bojun.system.dto.CommonUserCacheBean;
import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.entity.ManageUser;
import com.bojun.system.entity.ManageUserLogin;
import com.bojun.system.entity.OperationLog;
import com.bojun.system.enums.SystemDictEnums;
import com.bojun.utils.PropertiesUtils;
import com.bojun.utils.RandomUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 *Model：系统管理员用户表
 *Description：系统管理员用户表
 *Author:李欣颖
 *created：2020年3月3日
*/
@RestController
public class ManageUserController extends  BoJunBaseController {

	private static Log log = LogFactory.getLog(ManageUserController.class);

	@Autowired
	private IManageUserService manageUserService;
	
	@Autowired
	private ISystemOperationLogService systemOperationLogService;
	
	@Autowired
	private RedisUtil redisUtil;
	
	@Autowired
	private CommonConfig commonConfig;
	
	/**
	 * @Description  账号密码登陆
	 * <AUTHOR>
	 * @param manageUserDTO
	 * @return void
	 * created：2020年4月27日
	 */
	@RequestMapping(value = "/userLoginByPwd", method = RequestMethod.POST)
	public void  userLoginByPwd(@RequestBody ManageUserDTO manageUserDTO) {
		try {
			//判断用户连续输入密码错误次数
			String pwdErrCacheKey = "com.bojun.system.controller.login.password.error.times." + manageUserDTO.getAccountNo();
			int pwdErrorTimes = 0;
			if (redisUtil.hasKey(pwdErrCacheKey)) {
				pwdErrorTimes =  (Integer) redisUtil.get(pwdErrCacheKey);
				if (pwdErrorTimes >= 6) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户名或密码错误6次，请在30分钟后再尝试登陆"));
					return;
				}
			}
			if(0==manageUserDTO.getLoginType().intValue()&&null==manageUserDTO.getOrganizationId()) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "请选择机构"));
				return;
			}
			//获取用户登录信息
			ManageUserDTO manageUser = manageUserService.getManageUserByParams(manageUserDTO);
			if (null == manageUser) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户信息不存在"));
				return;
			}
			if(null != manageUserDTO.getPasswords()) {
				//校验密码
				String password = StrEncrypt.sha1(manageUserDTO.getPasswords(), manageUser.getSalt());
				if (!password.equals(manageUser.getPasswords())) {
					pwdErrorTimes++;
					String promptInfo = "用户名或密码错误";
					if (pwdErrorTimes >= 3) {
						promptInfo = "用户名或密码错误, 您还有"+(6-pwdErrorTimes)+"次机会";
					}
					redisUtil.set(pwdErrCacheKey, pwdErrorTimes, 1800);
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), promptInfo));
					return;
				}
			}
			//判断用户状态
			if (0 == manageUser.getStatus().intValue()&&1==manageUser.getAuthType().intValue()) {
                outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户状态非正常，请联系管理员"));
                return;
            }
			//判断用户状态
			if (null == manageUser.getOrganizationId()||0==manageUser.getOrganizationId()) {
                outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户未选择机构，请联系管理员"));
                return;
            }
			//判断用户角色状态
			if (null!=manageUser.getIsEnabled()&&0 == manageUser.getIsEnabled().intValue()&&1==manageUser.getAuthType().intValue()) {
                outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户角色状态非正常，请联系管理员"));
                return;
            }
			//清空密码错误次数缓存
			if (null != redisUtil.get(pwdErrCacheKey)) {
				redisUtil.del(pwdErrCacheKey);
			}
			//获取旧token
			String oldToken = manageUser.getToken();
			//当前登录token
			String token = MD5Util.MD5Encrypt(manageUserDTO.getAccountNo()
					+ manageUserDTO.getIpAddress() + MD5Util.MD5Encrypt(manageUser.getSalt()));
			//旧token是否在线,如果旧token和新token不同在视为不同地方登录，提示用户是否强制旧token下线
			if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken) && !oldToken.equals(token)
					&& null != manageUserDTO.getIsForced() && manageUserDTO.getIsForced().intValue() <= 0) {
				outJson(errorInfo(ResponseCodeEnum.ALREADY_LOGIN_REQUEST.getCode()));
				return;
			}
			//强制登录，清除旧token缓存
			if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken)
					&& (null == manageUserDTO.getIsForced() || manageUserDTO.getIsForced().intValue() == 1)) {
//			if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken) 
//					) {//&& (null == manageUserDTO.getIsForced() || manageUserDTO.getIsForced().intValue() == 1)
				//清除旧token信息
				redisUtil.del(oldToken);
				redisUtil.del("personnel" + oldToken);
			}
			
			//生成会话ID，保存登录信息
			if (StringUtils.isBlank(manageUserDTO.getIpAddress())) {
				manageUserDTO.setIpAddress("127.0.0.1");
			}
			//更新用户token
			manageUser.setToken(token);
			manageUser.setLastestLoginTime(new Date());
			manageUserService.updateManageUserByUserId(manageUser);
			//插入登录信息
			ManageUserLogin manageUserLogin = new ManageUserLogin();
			manageUserLogin.setUserId(manageUser.getUserId());
			manageUserLogin.setIpAddress(manageUserDTO.getIpAddress());
			manageUserLogin.setSystemId(manageUserDTO.getSystemId());
			manageUserService.addManageUserLogin(manageUserLogin);
			//封装操作日志参数
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(manageUser.getUserId());
            operationLog.setSystemId(SystemDictEnums.BASE_CONFIG_MANAGE.getSystemId());
            operationLog.setOperationType(Contants.LOGIN_IN_REQUEST);
            operationLog.setRequestAction("userLoginByPwd");
            operationLog.setOperationContent("用户登陆");
            operationLog.setIsException(0);
            operationLog.setIpAddress(manageUserDTO.getIpAddress());
            systemOperationLogService.addSystemLog(operationLog);
            //机构封面图片处理
            if(StringUtils.isNotBlank(manageUser.getOrganizationImage())){
				//图片查看路径
	            String showFilePath = commonConfig.getBaseHttpUrl() + FilePathConstants.ORGANIZATION_IMG_PATH;
	            manageUser.setOrganizationImageUrl(showFilePath + manageUser.getOrganizationImage());
			}
            //公用用户缓存实体
            CommonUserCacheBean userCacheBean = new CommonUserCacheBean();
            BeanUtils.copyProperties(manageUser, userCacheBean);
			if(StringUtils.isNotEmpty(manageUserDTO.getMachineId())){
				userCacheBean.setMachineId(manageUserDTO.getMachineId());
			}
			//保存缓存信息
			redisUtil.set(token, userCacheBean, 3 * 60 * 60);
			redisUtil.set("personnel" + token, manageUser, 3 * 60 * 60);
			outJson(successInfo(manageUser));
		} catch (Exception e) {
			log.error("userLoginByPwd:", e);
			outJson(info(500,e));
		}
		
	}
	/**
	 * @Description 新增用户
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/addManageUser", method = RequestMethod.POST)
	public void addManageUser(@RequestBody ManageUser manageUser) {
		try {

			//登录账户不允许重复
			ManageUserDTO manageUserDTO=new ManageUserDTO();
			manageUserDTO.setAccountNo(manageUser.getAccountNo());
			
			Page<ManageUserDTO> page = manageUserService.getManageUserList(manageUserDTO);
			if (page.getResult() != null && page.getResult().size() >0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "登录账户已经存在，请重新输入！"));
				return;
			}
			if(StringUtils.isNotBlank(manageUser.getWorkNumber())){
				manageUserDTO.setAccountNo(null);
				manageUserDTO.setOrganizationId(manageUser.getOrganizationId());
				manageUserDTO.setWorkNumber(manageUser.getWorkNumber());
				Page<ManageUserDTO> page2 = manageUserService.getManageUserList(manageUserDTO);
				if (page2.getResult() != null && page2.getResult().size() >0) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "该机构下已存在相同工号，请重新输入！"));
					return;
				}
			}
			//医疗养老账户
//			if(1 == manageUser.getUserType() || 2 == manageUser.getUserType()){
//				//校验该人员基本信息是否在人事系统存在
//				Map<String,Object> condition = new HashMap<>();
//				condition.put("realName", manageUser.getRealName());
//				condition.put("organizationId", manageUser.getOrganizationId());
//				condition.put("idNo", manageUser.getIdNo());
//				condition.put("workNumber", manageUser.getWorkNumber());
//				Integer checkCount = manageUserService.checkUserExistEmployee(condition);
//				if(checkCount == null || checkCount == 0){
//					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "该账户人员信息与人事系统信息不一致，请重新确认！"));
//					return;
//				}
//			}
			//密码加密
			String salt=RandomUtil.getRandomString(20);
			manageUser.setSalt(salt);
			manageUser.setPasswords(StrEncrypt.sha1(manageUser.getPasswords(), salt));
			//只能新增普通管理员
			manageUser.setAuthType(1);
			int addNumber = manageUserService.addManageUser(manageUser);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("addManageUser:", e);
			outJson(info(500,e));
		}
	}
	/**
	 * @Description 修改用户
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/updateManageUser", method = RequestMethod.POST)
	public void updateManageUser(@RequestBody ManageUser manageUser) {
		try {	
			ManageUserDTO manageUserDTO=new ManageUserDTO();
			//修改了账户，重新验证登录账户不允许重复
			if(StringUtils.isNotBlank(manageUser.getAccountNo())) {
				manageUserDTO.setAccountNo(manageUser.getAccountNo());
				Page<ManageUserDTO> page = manageUserService.getManageUserList(manageUserDTO);
				if (page.getResult() != null && page.getResult().size() >0 && !manageUser.getUserId().equals(page.get(0).getUserId())) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "登录账户已经存在，请重新输入！"));
					return;
				}			
			}
			if(StringUtils.isNotBlank(manageUser.getWorkNumber())) {
				manageUserDTO.setAccountNo(null);
				manageUserDTO.setOrganizationId(manageUser.getOrganizationId());
				manageUserDTO.setWorkNumber(manageUser.getWorkNumber());
				Page<ManageUserDTO> page2 = manageUserService.getManageUserList(manageUserDTO);
				if (page2.getResult() != null && page2.getResult().size() >0 && !manageUser.getUserId().equals(page2.get(0).getUserId())) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "该机构下已存在相同工号，请重新输入！"));
					return;
				}
			}
			//医疗养老账户
//			if(1 == manageUser.getUserType() || 2 == manageUser.getUserType()){
//				//校验该人员基本信息是否在人事系统存在
//				Map<String,Object> condition = new HashMap<>();
//				condition.put("realName", manageUser.getRealName());
//				condition.put("organizationId", manageUser.getOrganizationId());
//				condition.put("idNo", manageUser.getIdNo());
//				condition.put("workNumber", manageUser.getWorkNumber());
//				Integer checkCount = manageUserService.checkUserExistEmployee(condition);
//				if(checkCount == null || checkCount == 0){
//					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "该账户人员信息与人事系统信息不一致，请重新确认！"));
//					return;
//				}
//			}
			//修改了密码
			if(StringUtils.isNotBlank(manageUser.getPasswords())) {
				String salt=RandomUtil.getRandomString(20);
				manageUser.setSalt(salt);
				manageUser.setPasswords(StrEncrypt.sha1(manageUser.getPasswords(), salt));
			}
			
			int addNumber = manageUserService.updateManageUser(manageUser);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateManageUser:", e);
			outJson(info(500,e));
		}
	}
	/**
	 * @Description 启用禁用用户状态
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/enableDisableUser", method = RequestMethod.POST)
	public void enableDisableUser(@RequestBody ManageUser manageUser) {
		try {
			
			// 禁用状态，要求用户立即不能使用
			if (0==manageUser.getStatus().intValue()) {
				// 传入token
				String token = manageUser.getToken();
				// 校验信息是否合法
				if (StringUtils.isNotBlank(token) && redisUtil.hasKey(token)) {
					//清除token信息,退出登录
					redisUtil.del(token);
					redisUtil.del("personnel"+token);
					manageUser.setToken(null);
				}
			}
			int addNumber = manageUserService.enableDisableUser(manageUser);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("enableDisableUser:", e);
			outJson(info(500,e));
		}
	}
	
	/**
	 * 
	 * @Description 修改管理员密码
	 * <AUTHOR>
	 * void
	 * 2020年6月2日
	 */
	@RequestMapping(value="/updateUserPasswords", method = RequestMethod.POST)
	public void updateUserPasswords(@RequestBody ManageUserDTO manageUserDTO) {
		try {
			// 参数非空校验
			if (StringUtils.isBlank(manageUserDTO.getOldPwd()) || StringUtils.isBlank(manageUserDTO.getNewPwd())) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			if (!StringUtils.isBlank(manageUserDTO.getAgainNewPwd())) {
				if (!manageUserDTO.getAgainNewPwd().equals(manageUserDTO.getNewPwd())) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "两次密码不一致"));
					return;
				}
			}
			// 传入token
			String token = manageUserDTO.getHanderToken();
			// 校验信息是否合法
			if (null == redisUtil.get(token)) {
				outJson(errorInfo(ResponseCodeEnum.UNLOGIN_REQUEST.getCode()));
				return;
			}
			String jsonString = JSON.toJSONString(redisUtil.get(token));
			CommonUserCacheBean userCacheBean = JSON.parseObject(jsonString, CommonUserCacheBean.class);
			ManageUserDTO manageUser = new ManageUserDTO();
			BeanUtils.copyProperties(userCacheBean, manageUser);
			//密码盐
			String salt = manageUser.getSalt();
			// 校验密码
			String password = StrEncrypt.sha1(manageUserDTO.getOldPwd(), salt);
			if (!password.equals(manageUser.getPasswords())) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "旧密码验证失败"));
				return;
			}
			
			// 设置新密码
			manageUser.setPasswords(StrEncrypt.sha1(manageUserDTO.getNewPwd(), salt));
			manageUser.setSalt(salt);
			int l = manageUserService.updateUserPasswords(manageUser);
			if (1 != l) {
				outJson(info(ResponseCodeEnum.EXCEPTION_REQUEST.getCode(), "修改管理员密码失败"));
				return;
			}
			//获取旧token
			String oldToken = manageUser.getToken();
			if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken)) {
				//清除旧token信息,退出登录
				redisUtil.del(oldToken);
				redisUtil.del("personnel"+token+oldToken);
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateUserPasswords:", e);
			outJson(info(500,e));
		}
	}
	
	
	/**
	 * @Description 分页查询用户列表
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getManageUserList", method = RequestMethod.POST)
	public void getManageUserList(@RequestBody ManageUserDTO manageUserDTO) {
		try {
			int pageNum = (null == manageUserDTO.getPageNum() ? 1 : manageUserDTO.getPageNum());
			int everyPage = (null == manageUserDTO.getEveryPage() ? 10 : manageUserDTO.getEveryPage());
			if (StringUtils.isBlank(manageUserDTO.getIsEnabledRole())) {
				String organizationIds = "";
//				String jsonString = JSON.toJSONString(redisUtil.get(manageUserDTO.getHanderToken()));
//				CommonUserCacheBean userCacheBean = JSON.parseObject(jsonString, CommonUserCacheBean.class);
//				ManageUserDTO manageUser = new ManageUserDTO();
//				BeanUtils.copyProperties(userCacheBean, manageUser);
				// 普通管理人员，如果走角色数据权限
				/*if (1 == manageUserDTO.getAuthType().intValue() && 1 == manageUserDTO.getDataPermissions().intValue()) {
					List<ManageRoleOrganDTO> manageRoleOrganList = manageUserDTO.getManageRoleOrganList();
					for (ManageRoleOrganDTO manageRoleOrganDTO : manageRoleOrganList) {
						organizationIds += manageRoleOrganDTO.getOrganizationId() + ",";
					}
					manageUserDTO.setOrganizationIds(organizationIds.substring(0, organizationIds.length() - 1));
					// 走帐号权限
				} else if (1 == manageUserDTO.getAuthType().intValue()
						&& 2 == manageUserDTO.getDataPermissions().intValue()) {
					manageUserDTO.setOrganizationId(manageUserDTO.getOrganizationId());
				}*/
			}
			PageHelper.startPage(pageNum, everyPage);
			Page<ManageUserDTO> page = manageUserService.getManageUserList(manageUserDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {
			log.error("getManageUserList:", e);
			outJson(info(500, e));
		}
	}

	/**
	 * @Description 查询机构下所有用户
	 * <AUTHOR>
	 * void
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getManageUserListByOrgId", method = RequestMethod.GET)
	public void getManageUserListByOrgId(@RequestParam(value = "orgId") Integer orgId) {
		try {
			List<ManageUserDTO> page = manageUserService.getManageUserListByOrgId(orgId);
			if (page == null || page.size() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			outJson(successInfo(page));
		} catch (Exception e) {
			log.error("getManageUserListByOrgId:", e);
			outJson(info(500, e));
		}
	}

	/**
	 * @return void
	 * @Description 身份证登陆
	 * <AUTHOR>
	 * @date：2020-07-24日
	 */
	@RequestMapping(value = "/loginByIdNo", method = RequestMethod.POST)
	public void loginByIdNo(@RequestBody ManageUserDTO manageUserDTO) {
		try {
			// 通过身份证查询信息
			ManageUserDTO mdto = manageUserService.loginByIdNo(manageUserDTO);
			if (mdto != null) {
				manageUserDTO.setAccountNo(mdto.getAccountNo());
				// 判断机构
				if (0 == manageUserDTO.getLoginType().intValue() && null == manageUserDTO.getOrganizationId()) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "请选择机构"));
					return;
				}
				//获取用户登录信息
				ManageUserDTO manageUser = manageUserService.getManageUserByParams(manageUserDTO);
				if (null == manageUser) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户信息不存在"));
					return;
				}
				//判断用户状态
				if (0 == manageUser.getStatus().intValue() && 1 == manageUser.getAuthType().intValue()) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户状态非正常，请联系管理员"));
					return;
				}
				//判断用户角色状态
				if (null != manageUser.getIsEnabled() && 0 == manageUser.getIsEnabled().intValue() && 1 == manageUser.getAuthType().intValue()) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户角色状态非正常，请联系管理员"));
					return;
				}
				//获取旧token
				String oldToken = manageUser.getToken();
				if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken)) {
					//清除旧token信息
					redisUtil.del(oldToken);
					redisUtil.del("personnel"+oldToken);
				}
				//生成会话ID，保存登录信息
				if (StringUtils.isBlank(manageUserDTO.getIpAddress())) {
					manageUserDTO.setIpAddress("127.0.0.1");
				}
				String token = MD5Util.MD5Encrypt(manageUserDTO.getAccountNo()
						+ manageUserDTO.getIpAddress() + MD5Util.MD5Encrypt(manageUser.getSalt()));
				//更新用户token
				manageUser.setToken(token);
				manageUser.setLastestLoginTime(new Date());
				manageUserService.updateManageUserByUserId(manageUser);
				//插入登录信息
				ManageUserLogin manageUserLogin = new ManageUserLogin();
				manageUserLogin.setUserId(manageUser.getUserId());
				manageUserLogin.setIpAddress(manageUserDTO.getIpAddress());
				manageUserLogin.setSystemId(manageUserDTO.getSystemId());
				manageUserService.addManageUserLogin(manageUserLogin);
				//封装操作日志参数
				OperationLog operationLog = new OperationLog();
				operationLog.setUserId(manageUser.getUserId());
				operationLog.setSystemId(SystemDictEnums.BASE_CONFIG_MANAGE.getSystemId());
				operationLog.setOperationType(Contants.LOGIN_IN_REQUEST);
				operationLog.setRequestAction("loginByIdNo");
				operationLog.setOperationContent("用户登陆");
				operationLog.setIsException(0);
				operationLog.setIpAddress(manageUserDTO.getIpAddress());
				systemOperationLogService.addSystemLog(operationLog);
				//保存缓存信息
				redisUtil.set(token, manageUser, 24 * 60 * 60);
				redisUtil.set("personnel"+token, manageUser, 24 * 60 * 60);
				outJson(successInfo(manageUser));
			}else{
				outJson(errorInfo(404));
			}
		} catch (Exception e) {
			log.error("loginByIdNo:", e);
			outJson(info(500,e));
		}
	}



	/**
	 *                   2020年4月27日
	 * @Description 重置用户密码
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/resetUserPassword", method = RequestMethod.POST)
	public void resetUserPassword(@RequestBody ManageUser manageUser) {
		try {
			//修改了密码
			if (StringUtils.isNotBlank(manageUser.getPasswords())) {
				String salt = RandomUtil.getRandomString(20);
				manageUser.setSalt(salt);
				manageUser.setPasswords(StrEncrypt.sha1(manageUser.getPasswords(), salt));
			}
			int addNumber = manageUserService.resetManageUserPassword(manageUser);
			if (addNumber <= 0) {
				outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));
				return;
			}
			//获取旧token
			String oldToken = manageUser.getToken();
			if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken)) {
				//清除旧token信息,退出登录
				redisUtil.del(oldToken);
				redisUtil.del("personnel" + oldToken);
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("resetUserPassword:", e);
			outJson(info(500, e));
		}
	}

	/**
	 *                   2020年4月27日
	 * @Description 修改手机号码
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/updateMobilePhone", method = RequestMethod.POST)
	public void updateMobilePhone(@RequestBody ManageUserDTO manageUserDTO) {
		try {
			if (redisUtil.hasKey("mobilePhone")) {
				if (manageUserDTO.getOldMobilePhone().equals(redisUtil.get("mobilePhone"))) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "验证码3次出错，请10分钟后再次修改"));
					return;
				}
			}
			if (StringUtils.isEmpty(manageUserDTO.getOldMobilePhone())
					|| StringUtils.isEmpty(manageUserDTO.getNewMobilePhone())) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			int number = 0;
			//判断验证码是否过期
			if (!redisUtil.hasKey("code")) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "验证码已过期"));
				return;
			}
			//判断验证码是否一致
			if (!manageUserDTO.getCode().equals(redisUtil.get("code"))) {
				number++;
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "验证码不一致"));
				return;
			}
			//验证码输入错误3次以上锁10分钟
			if (number >= 3) {
				redisUtil.set("mobilePhone", manageUserDTO.getOldMobilePhone(), 6000);
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "验证码3次出错，请10分钟后再次修改"));
				return;
			}
			manageUserDTO.setMobile(manageUserDTO.getNewMobilePhone());
			Integer count = manageUserService.updateManageUser(manageUserDTO);
			if (count > 0) {
				redisUtil.del("mobilePhone");
				outJson(successInfo());
				return;
			}
			outJson(errorInfo(ResponseCodeEnum.FAIL_REQUEST.getCode()));

		} catch (Exception e) {
			log.error("updateMobilePhone:", e);
			outJson(info(500, e));
		}
	}

	/**
	 *                   2020年4月27日
	 * @Description 获取验证码
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/getCode", method = RequestMethod.POST)
	public void getCode(@RequestParam("newMobilePhone") @Valid String newMobilePhone,
						@RequestParam("template") @Valid String template) {
		try {
			//防刷验证码
			if (redisUtil.hasKey("newMobilePhone")) {
				if (newMobilePhone.equals(redisUtil.get("newMobilePhone"))) {
					outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "请一分钟后再次请求"));
					return;
				}
			}
			String code = RandomUtil.getRandomStringByLength(4);
			redisUtil.set("newMobilePhone", newMobilePhone, 60);
			redisUtil.set("code", code, 300);
			AliyunSMSUtil.sendVerifyCodeMessage(newMobilePhone, code, template);
			outJson(successInfo());
		} catch (Exception e) {
			log.error("getCode:", e);
			outJson(info(500, e));
		}
	}
	/**
	 *                   2020年4月27日
	 * @Description 获取验证码
	 * <AUTHOR>
	 */
	@RequestMapping(value = "/getCodes", method = RequestMethod.POST)
	public void getCodes(@RequestParam("newMobilePhone") @Valid String newMobilePhone,
						@RequestParam("template") @Valid String template) {
		try {
			//防刷验证码
			if (redisUtil.hasKey("newMobilePhone")) {
				if (newMobilePhone.equals(redisUtil.get("newMobilePhone"))) {
					outJson(info(ResponseCodeEnum.FAIL_REQUEST.getCode(), "请一分钟后再次请求"));
					return;
				}
			}
//			String code = RandomUtil.getRandomStringByLength(4);
			redisUtil.set("newMobilePhone", newMobilePhone, 60);
			redisUtil.set("code", "719888", 300);
//			AliyunSMSUtil.sendVerifyCodeMessage(newMobilePhone, code, template);
			outJson(successInfo());
		} catch (Exception e) {
			log.error("getCode:", e);
			outJson(info(500, e));
		}
	}

	@RequestMapping(value="/getManageUserByMobile", method = RequestMethod.POST)
	public void getManageUserByMobile(@RequestParam String mobile){
		try {
			// 通过身份证查询信息
			ManageUserDTO manageUser = manageUserService.getManageUserByMobile(mobile);
			if (null == manageUser) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户信息不存在"));
				return;
			}
			outJson(successInfo(manageUser));
		} catch (Exception e) {
			log.error("loginByIdNo:", e);
			outJson(info(500,e));
		}
	}


	/**
	 * @Description 根据手机号获取用户
	 * @param
	 * @return void
	 * @date：2020-07-24日
	 */
	@RequestMapping(value="/loginUserByMobile", method = RequestMethod.POST)
	public void loginUserByMobile(@RequestBody ManageUserDTO manageUserDTO){
		try {
			// 通过身份证查询信息
			ManageUserDTO mdto = manageUserService.getManageUserByMobile(manageUserDTO.getMobile());
			if(mdto!=null) {
				manageUserDTO.setAccountNo(mdto.getAccountNo());
				// 判断机构
				if (0 == manageUserDTO.getLoginType().intValue() && null == manageUserDTO.getOrganizationId()) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "请选择机构"));
					return;
				}
				//获取用户登录信息
				ManageUserDTO manageUser = manageUserService.getManageUserByParams(manageUserDTO);
				if (null == manageUser) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户信息不存在"));
					return;
				}
				//判断用户状态
				if (0 == manageUser.getStatus().intValue() && 1 == manageUser.getAuthType().intValue()) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户状态非正常，请联系管理员"));
					return;
				}
				//判断用户角色状态
				if (null != manageUser.getIsEnabled() && 0 == manageUser.getIsEnabled().intValue() && 1 == manageUser.getAuthType().intValue()) {
					outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "用户角色状态非正常，请联系管理员"));
					return;
				}
				//获取旧token
				String oldToken = manageUser.getToken();
				if (StringUtils.isNotBlank(oldToken) && redisUtil.hasKey(oldToken)) {
					//清除旧token信息
					redisUtil.del(oldToken);
					redisUtil.del("personnel"+oldToken);
				}
				//生成会话ID，保存登录信息
				if (StringUtils.isBlank(manageUserDTO.getIpAddress())) {
					manageUserDTO.setIpAddress("127.0.0.1");
				}
				String token = MD5Util.MD5Encrypt(manageUserDTO.getAccountNo()
						+ manageUserDTO.getIpAddress() + MD5Util.MD5Encrypt(manageUser.getSalt()));
				//更新用户token
				manageUser.setToken(token);
				manageUser.setLastestLoginTime(new Date());
				manageUserService.updateManageUserByUserId(manageUser);
				//插入登录信息
				ManageUserLogin manageUserLogin = new ManageUserLogin();
				manageUserLogin.setUserId(manageUser.getUserId());
				manageUserLogin.setIpAddress(manageUserDTO.getIpAddress());
				manageUserLogin.setSystemId(manageUserDTO.getSystemId());
				manageUserService.addManageUserLogin(manageUserLogin);
				//封装操作日志参数
				OperationLog operationLog = new OperationLog();
				operationLog.setUserId(manageUser.getUserId());
				operationLog.setSystemId(SystemDictEnums.BASE_CONFIG_MANAGE.getSystemId());
				operationLog.setOperationType(Contants.LOGIN_IN_REQUEST);
				operationLog.setRequestAction("loginByIdNo");
				operationLog.setOperationContent("用户登陆");
				operationLog.setIsException(0);
				operationLog.setIpAddress(manageUserDTO.getIpAddress());
				systemOperationLogService.addSystemLog(operationLog);
				//保存缓存信息
				redisUtil.set(token, manageUser, 24 * 60 * 60);
				redisUtil.set("personnel"+token, manageUser, 24 * 60 * 60);
				outJson(successInfo(manageUser));
			}else{
				outJson(errorInfo(404));
			}
		} catch (Exception e) {
			log.error("getManageUserByMobile:", e);
			outJson(info(500,e));
		}
	}

	/**
	 * 查询运维权限和角色权限
	 */
	@RequestMapping(value = "/getManageRoleUser", method = RequestMethod.POST)
	public Results<List<ManageUserDTO>> getManageRoleUser() {
		try {
			Page<ManageUserDTO> manageRoleUserList = manageUserService.getManageRoleUserList();
			if (manageRoleUserList == null || manageRoleUserList.size() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return Results.fail("查询用户信息失败！");
			}
			return Results.list(manageRoleUserList.getResult());
		} catch (Exception e) {
			log.error("wxUserLogin:", e);
			return Results.fail("查询用户信息失败！");
		}
	}


	/**
	 * 查询运维权限和角色权限通过systemId
	 */
	@RequestMapping(value = "/getManageRoleUserListBySystemId", method = RequestMethod.POST)
	public Results<List<ManageUserDTO>> getManageRoleUserListBySystemId(@RequestBody ManageUserDTO manageUser) {
		try {
			Page<ManageUserDTO> manageRoleUserList = manageUserService.getManageRoleUserListBySystemId( manageUser);
			if (manageRoleUserList == null || manageRoleUserList.size() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return Results.fail("查询用户信息失败！");
			}
			return Results.list(manageRoleUserList.getResult());
		} catch (Exception e) {
			log.error("wxUserLogin:", e);
			return Results.fail("查询用户信息失败！");
		}
	}
}
