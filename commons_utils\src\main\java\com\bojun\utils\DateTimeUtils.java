package com.bojun.utils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.bojun.http.HttpRequest;

public class DateTimeUtils extends DateUtils {

    public static final String UTC_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    public static String formatUTC(Date date) {
        if (date == null) {
            return null;
        }
        return DateFormatUtils.format(date, UTC_FORMAT);
    }

    /**
     * 获取开始时间
     *
     * @param startDate
     * @return
     */
    public static Date getStartDate(String startDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(transferStrToDates(startDate));
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取结束时间
     *
     * @param endDate
     * @return
     */
    public static Date getEndDate(String endDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(transferStrToDates(endDate));
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();

    }

    /**
     * 获取指定年份和月份对应的天数
     *
     * @param year  指定的年份
     * @param month 指定的月份
     * @return int 返回天数
     */
    public static int getDaysInMonth(int year, int month) {
        if ((month == 1) || (month == 3) || (month == 5) || (month == 7) || (month == 8) || (month == 10)
                || (month == 12)) {
            return 31;
        } else if ((month == 4) || (month == 6) || (month == 9) || (month == 11)) {
            return 30;
        } else {
            if (((year % 4) == 0) && ((year % 100) != 0) || ((year % 400) == 0)) {
                return 29;
            } else {
                return 28;
            }
        }
    }

    /**
     * 根据所给的起止时间来计算间隔的天数
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return int 返回间隔天数
     */
    public static int getIntervalDays(java.sql.Date startDate, java.sql.Date endDate) {
        long startdate = startDate.getTime();
        long enddate = endDate.getTime();
        long interval = enddate - startdate;
        int intervalday = (int) (interval / (1000 * 60 * 60 * 24));
        return intervalday;
    }

    /**
     * 根据所给的起止时间来计算间隔的月数
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return int 返回间隔月数
     */
    public static int getIntervalMonths(java.sql.Date startDate, java.sql.Date endDate) {
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(startDate);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(endDate);
        int startDateM = startCal.MONTH;
        int startDateY = startCal.YEAR;
        int enddatem = endCal.MONTH;
        int enddatey = endCal.YEAR;
        int interval = (enddatey * 12 + enddatem) - (startDateY * 12 + startDateM);
        return interval;
    }

    /**
     * 返回当前日期时间字符串<br>
     * 默认格式:yyyy-mm-dd hh:mm:ss
     *
     * @return String 返回当前字符串型日期时间
     */
    public static String getCurrentTime() {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        returnStr = f.format(date);
        return returnStr;
    }

    /**
     * 返回当前日期时间字符串<br>
     * 默认格式:yyyymmddhhmmss
     *
     * @return String 返回当前字符串型日期时间
     */
    public static BigDecimal getCurrentTimeAsNumber() {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date();
        returnStr = f.format(date);
        return new BigDecimal(returnStr);
    }

    /**
     * 返回自定义格式的当前日期时间字符串
     *
     * @param format 格式规则
     * @return String 返回当前字符串型日期时间
     */
    public static String getCurrentTime(String format) {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat(format);
        Date date = new Date();
        returnStr = f.format(date);
        return returnStr;
    }

    /**
     * 返回时间格式
     *
     * @param format
     * @param date
     * @return
     */
    public static String getTimeFormat(String format, Date date) {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat(format);
        returnStr = f.format(date);
        return returnStr;
    }

    public static String getTimeFormat(Date date) {
        String returnStr = null;
        SimpleDateFormat f = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        returnStr = f.format(date);
        return returnStr;
    }

    public static String getDayCustomer(int calendar, int step, String format) {
        Calendar cal = Calendar.getInstance();
        cal.add(calendar, step);
        return DateFormatUtils.format(cal.getTime(), format);
    }

    /**
     * 返回当前字符串型日期
     *
     * @return String 返回的字符串型日期
     */
    public static String getCurDate() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpledateformat = new SimpleDateFormat("yyyy-MM-dd");
        String strDate = simpledateformat.format(calendar.getTime());
        return strDate;
    }

    /**
     * 根据日期获取星期
     *
     * @param strdate
     * @return
     */
    public static String getWeekDayByDate(String strdate) {
        final String dayNames[] = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        SimpleDateFormat sdfInput = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        Date date = new Date();
        try {
            date = sdfInput.parse(strdate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        calendar.setTime(date);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        if (dayOfWeek < 0)
            dayOfWeek = 0;
        return dayNames[dayOfWeek];
    }

    /**
     * 返回指定格式的字符型日期
     *
     * @param date
     * @param formatString
     * @return
     */
    public static String Date2String(Date date, String formatString) {
        if (null == date) {
            return null;
        }
        SimpleDateFormat simpledateformat = new SimpleDateFormat(formatString);
        String strDate = simpledateformat.format(date);
        return strDate;
    }

    /**
     * 返回当前字符串型日期
     *
     * @param format 格式规则
     * @return String 返回的字符串型日期
     */
    public static String getCurDate(String format) {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat simpledateformat = new SimpleDateFormat(format);
        String strDate = simpledateformat.format(calendar.getTime());
        return strDate;
    }

    /**
     * 格式时间
     *
     * @param source
     * @param format
     * @return
     * @throws ParseException
     */
    public static Date getDate4String(String source, String... format) throws ParseException {
        return parseDate(source, format);
    }

    /**
     * 将字符串型日期转换为日期型
     *
     * @param strDate       字符串型日期
     * @param srcDateFormat 源日期格式
     * @param dstDateFormat 目标日期格式
     * @return Date 返回的util.Date型日期
     */
    public static Date stringToDate(String strDate, String srcDateFormat, String dstDateFormat) {
        Date rtDate = null;
        Date tmpDate = (new SimpleDateFormat(srcDateFormat)).parse(strDate, new ParsePosition(0));
        String tmpString = null;
        if (tmpDate != null)
            tmpString = (new SimpleDateFormat(dstDateFormat)).format(tmpDate);
        if (tmpString != null)
            rtDate = (new SimpleDateFormat(dstDateFormat)).parse(tmpString, new ParsePosition(0));
        return rtDate;
    }

    /**
     * 获取当前时间前几个月的时间
     *
     * @param n 前几个月
     * @return
     */
    public static Date getPreMonth(int n) {
        n = -n;
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, n);
        return c.getTime();

    }

    /**
     * 获取前n天时间
     *
     * @param n
     * @return
     */
    public static Date getPreDay(int n) {
        n = -n;
        Calendar c = Calendar.getInstance();
        c.add(Calendar.DATE, n);
        return c.getTime();
    }


    /**
     * 获取前n天开始时间
     *
     * @param n
     * @return
     */
    public static Date getStartPreDay(int n) {
        n = -n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取去年前n天开始时间
     *
     * @param n
     * @return
     */
    public static Date getLastYearStartPreDay(int n) {
        n = -n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.YEAR, -1);
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取前n天开始时间
     *
     * @param n
     * @return
     */
    public static Date getEndPreDay(int n) {
        n = -n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    public static Date getLastYearEndPreDay(int n) {
        n = -n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.YEAR, -1);
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    /**
     * 获取指定日期后n天开始时间
     *
     * @param n
     * @return
     */
    public static Date getDateNextDay(String date, int n) {
        n = +n;
        Calendar cal = Calendar.getInstance();
        cal.setTime(transferStrToDates(date));
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取后n天开始时间
     *
     * @param n
     * @return
     */
    public static Date getStartNextDay(int n) {
        n = +n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }
    /**
     * 获取后n天开始时间sqlDate
     *
     * @param n
     * @return
     */
    public static java.sql.Date getDataNextDay(int n) {
        n = +n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        java.sql.Date date=new java.sql.Date(cal.getTimeInMillis());
        return date;
    }

    /**
     * 获取后n天结束时间
     *
     * @param n
     * @return
     */
    public static Date getEndNextDay(int n) {
        n = +n;
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, n);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    /**
     * 相对时间格式化
     *
     * @param strDate
     * @return
     */
    public static String getRelativeTimeStr(String strDate) {
        StringBuffer sbf = new StringBuffer();
        String dayStr = strDate.substring(0, 10);
        String today = Date2String(getStartNextDay(0), "yyyy-MM-dd");
        String tomorrow = Date2String(getStartNextDay(1), "yyyy-MM-dd");
        String afterTomorrow = Date2String(getStartNextDay(2), "yyyy-MM-dd");
        if (today.equals(dayStr)) {
            sbf.append("今天 ").append(strDate.substring(10, strDate.length()));
        } else if (tomorrow.equals(dayStr)) {
            sbf.append("明天 ").append(strDate.substring(10, strDate.length()));
        } else if (afterTomorrow.equals(dayStr)) {
            sbf.append("后天 ").append(strDate.substring(10, strDate.length()));
        } else {
            sbf.append(strDate);
        }
        return sbf.toString();
    }

    /**
     * 获取某年某月的第一天
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getFisrtDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        // 设置年份
        cal.set(Calendar.YEAR, year);
        // 设置月份
        cal.set(Calendar.MONTH, month - 1);
        // 获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取某年某月的第一天
     *
     * @param date
     * @return
     */
    public static Date getFisrtDayByDate(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取某年某月的第一天
     *
     * @param year
     * @param month
     * @return
     */
    public static Date getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        // 设置年份
        cal.set(Calendar.YEAR, year);
        // 设置月份
        cal.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        return cal.getTime();
    }

    /**
     * 获取某年第一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getCurrYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.getTime();
        return calendar.getTime();
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return Date
     */
    public static Date getCurrYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        return calendar.getTime();
    }

    /**
     * 查询时间段的所有日期
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<String> findDates(Date dBegin, Date dEnd) {
        List<String> lDate = new ArrayList<String>();
        lDate.add(getTimeFormat("yyyy-MM-dd", dBegin));
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (dEnd.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(getTimeFormat("yyyy-MM-dd", calBegin.getTime()));
        }
        return lDate;
    }

    /**
     * 查询时间段的所有月份
     *
     * @param dBegin
     * @param dEnd
     * @return
     */
    public static List<String> findMonths(Date dBegin, Date dEnd) {
        List<String> lDate = new ArrayList<String>();
        lDate.add(getTimeFormat("yyyy-MM", dBegin));
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(dBegin);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(dEnd);
        // 测试此日期是否在指定日期之后
        while (calBegin.before(calEnd)) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.MONTH, 1);
            lDate.add(getTimeFormat("yyyy-MM", calBegin.getTime()));
        }
        return lDate;
    }

    /**
     * String 转 Date
     *
     * @param strDate
     * @return
     */
    public static Date transferStrToDate(String strDate) {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            return sim.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Date transferStrToDate(String strDate, String formatStr) {
        SimpleDateFormat sim = new SimpleDateFormat(formatStr);
        try {
            return sim.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * String 转 Date
     *
     * @param strDate
     * @return
     */
    public static Date transferStrToDates(String strDate) {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sim.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据所给的起止时间来计算间隔的天数
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return int 返回间隔天数
     */
    public static int getIntervalDays(Date startDate, Date endDate) {
        long startdate = startDate.getTime();
        long enddate = endDate.getTime();
        long interval = enddate - startdate;
        int intervalday = (int) (interval / (1000 * 60 * 60 * 24));
        return intervalday;
    }

    /**
     * 获取当前时间年份
     *
     * @return
     */
    public static int getCurrentYear() {
        Calendar date = Calendar.getInstance();
        return date.get(Calendar.YEAR);
    }

    /**
     * 计算某个日期的 加减天数后的日期
     *
     * @param current 需要计算的日期
     * @param days    加减天数
     * @return Date
     * @Description 计算某个日期的 加减天数后的日期
     * <AUTHOR>
     */
    public static Date getDatePlusDays(Date current, int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(current);
        calendar.get(Calendar.DAY_OF_MONTH);// 今天的日期
        calendar.set(Calendar.DAY_OF_MONTH, calendar.get(Calendar.DAY_OF_MONTH) + days);
        return calendar.getTime();
    }


    /**
     * 计算某个日期的 加减毫秒数后的日期
     *
     * @param current 需要计算的日期
     * @param minutes 加减毫秒数
     * @return Date
     * @Description 计算某个日期的 加减毫秒数后的日期
     * <AUTHOR>
     */
    public static Date getDatePlusMinutes(Date current, int minutes) {
        Calendar c = Calendar.getInstance();
        c.setTime(current);
        c.add(Calendar.MINUTE, minutes);
        return c.getTime();

    }

    /**
     * @param sourceDate
     * @param month
     * @return Date
     * @Description 计算某个日期的 加多少后的日期
     * <AUTHOR>
     */
    public static Date getDatePlusMonth(Date sourceDate, int month) {
        Calendar c = Calendar.getInstance();
        c.setTime(sourceDate);
        c.add(Calendar.MONTH, month);
        return c.getTime();

    }

    /**
     * @param dateStr
     * @return List<String>
     * @Description 输入一个日期，获取在该日期所在的一周的日期
     * <AUTHOR>
     */
    public static List<String> getDateToWeek(String dateStr) {
        //String 转date
        Date date = transferStrToDates(dateStr);

        List<String> dateWeekList = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String time = "";
        //flag用来存取与当天日期的相差数
        int flag = 0;
        for (int i = 1; i < 8; i++) {
            //新建日历
            Calendar cal = Calendar.getInstance();
            //在日历中找到当前日期
            cal.setTime(date);
            //当前日期时本周第几天(默认按照西方惯例上周星期天为第一天 --把1去掉）
            flag = 1 - cal.get(Calendar.DAY_OF_WEEK);
            //根据循环。当天与上周星期天和本周一到周五相差的天数
            cal.add(Calendar.DATE, flag + i);
            //转化格式
            time = sdf.format(cal.getTime());
            //存入list
            dateWeekList.add(time);
        }
        return dateWeekList;
    }

    /**
     * @param source
     * @param formart
     * @return Date
     * @Description 把日期格式的字符串转换成一个Date类型
     * <AUTHOR>
     */
    public static Date getDateFormat(String source, String formart) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(source);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }


    /**
     * @param date
     * @return 返回结果：检查具体日期是否为节假日，工作日对应结果为 0, 休息日对应结果为 1, 节假日对应的结果为 2
     * String
     * @Description 根据返回值判断当前日期是否为工作日
     * <AUTHOR>
     */
    public static String isWorkDay(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String url = "http://tool.bitefu.net/jiari";
        return HttpRequest.sendGet(url, "d=" + sdf.format(date));
    }

    /**
     * @param date 查询日期
     * @return 返回结果：上一个工作日
     * String
     * @Description 根据返日期返回上一个工作日
     * <AUTHOR>
     */
    public static String getPreWorkDay(Date date) {
        //获取前一天日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, -1);
        String flag = isWorkDay(calendar.getTime());
        //工作日为0
        if ("0".equals(flag)) {
            return DateUtil.getAppointDate(calendar.getTime());
        } else {
            return getPreWorkDay(calendar.getTime());
        }
    }

    /**
     * @param date 查询日期
     * @return 返回结果：下一个工作日
     * String
     * @Description 根据返日期返回下一个工作日
     * <AUTHOR>
     */
    public static String getNextWorkDay(Date date) {
        //获取后一天日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 1);
        String flag = isWorkDay(calendar.getTime());
        //工作日为0
        if ("0".equals(flag)) {
            return DateUtil.getAppointDate(calendar.getTime());
        } else {
            return getNextWorkDay(calendar.getTime());
        }
    }

    /**
     * 主函数测试
     *
     * @throws ParseException
     */
    public static void main(String[] args) throws ParseException {
        System.out.println(getNextWorkDay(transferStrToDate("2020-09-19 00:00:00")));
    }

    /**
     * 根据所给的起止时间来计算间隔的分钟数
     *
     * @param startDate 起始时间
     * @param endDate   结束时间
     * @return int 返回间隔分钟
     */
    public static int getIntervalMinute(java.util.Date startDate, java.util.Date endDate) {
        long startdate = startDate.getTime();
        long enddate = endDate.getTime();
        long interval = enddate - startdate;
        int intervalMinute = (int) (interval / (1000 * 60));
        return intervalMinute;
    }

    /**
     * 处理时间
     *
     * @param oldTime 原时间
     * @param add     增加时间
     * @return
     * @throws ParseException
     */
    public static String TimeAdd(String oldTime, String add) throws ParseException {
        int addMit = Integer.valueOf(add);
        DateFormat df = new SimpleDateFormat("hh:mm");
        Date date = df.parse(oldTime);
        Date expireTime = new Date(date.getTime() + addMit * 60 * 1000);
        String newTime = df.format(date);
        return newTime;
    }


    /**
     * 获取指定日期所在月份开始的时间
     * @param date 指定日期
     * @return Date
     */
    public static Date getMonthBegin(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为1号,当前日期既为本月第一天
        c.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        c.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        c.set(Calendar.MINUTE, 0);
        //将秒至0
        c.set(Calendar.SECOND,0);
        //将毫秒至0
        c.set(Calendar.MILLISECOND, 0);
        // 获取本月第一天的时间
        return c.getTime();
    }

    /**
     * 获取指定日期所在月份结束的时间
     * @param date 指定日期
     * @return Date
     */
    public static Date getMonthEnd(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);

        //设置为当月最后一天
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        //将小时至23
        c.set(Calendar.HOUR_OF_DAY, 23);
        //将分钟至59
        c.set(Calendar.MINUTE, 59);
        //将秒至59
        c.set(Calendar.SECOND,59);
        //将毫秒至999
        c.set(Calendar.MILLISECOND, 999);
        // 获取本月最后一天的时间
        return c.getTime();
    }

}
