package com.bojun.base.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.MessageNotificationObjectDTO;
import com.bojun.system.entity.MessageNotificationObject;

import java.util.List;

/**
 * MessageNotificationObjectService接口
 * 
 * <AUTHOR>
 * @date 2021-09-01 09:41:11
 */
public interface MessageNotificationObjectService extends IService<MessageNotificationObject> {
    /**
     * 查询消息通知关联推送对象表
     * 
     * @param id 消息通知关联推送对象表ID
     * @return 消息通知关联推送对象表
     */
    public MessageNotificationObjectDTO selectMessageNotificationObjectById(Integer id);

    /**
     * 查询消息通知关联推送对象表列表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 消息通知关联推送对象表集合
     */
    public List<MessageNotificationObjectDTO> selectMessageNotificationObjectList(MessageNotificationObjectDTO messageNotificationObjectDTO);

    /**
     * 新增消息通知关联推送对象表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 结果
     */
    public int insertMessageNotificationObject(MessageNotificationObjectDTO messageNotificationObjectDTO);

    /**
     * 修改消息通知关联推送对象表
     * 
     * @param messageNotificationObjectDTO 消息通知关联推送对象表
     * @return 结果
     */
    public int updateMessageNotificationObject(MessageNotificationObjectDTO messageNotificationObjectDTO);
    
    /**
     * 新增消息通知关联推送对象表
     * 
     * @param messageNotificationObject 消息通知关联推送对象表
     * @return 结果
     */
    public int insertMessageNotificationObject(MessageNotificationObject messageNotificationObject);

    /**
     * 修改消息通知关联推送对象表
     * 
     * @param messageNotificationObject 消息通知关联推送对象表
     * @return 结果
     */
    public int updateMessageNotificationObject(MessageNotificationObject messageNotificationObject);

    /**
     * 批量删除消息通知关联推送对象表
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteMessageNotificationObjectByIds(String ids);

    /**
     * 删除消息通知关联推送对象表信息
     * 
     * @param id 消息通知关联推送对象表ID
     * @return 结果
     */
    public int deleteMessageNotificationObjectById(Integer id);
}
