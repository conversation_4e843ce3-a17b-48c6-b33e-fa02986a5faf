package com.bojun.system.dto;

import com.bojun.system.entity.ManageUserV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 系统管理员用户对象 t_manage_user
 * 
 * <AUTHOR>
 * @date 2021-06-05 16:05:04
 */
@ApiModel(value = "ManageUserV2DTO对象")
@Data
public class ManageUserV2DTO extends ManageUserV2
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
}
