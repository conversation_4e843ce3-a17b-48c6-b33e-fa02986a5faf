<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.SphygmometerDeviceTransferMapper">

    <resultMap type="com.bojun.sphygmometer.dto.SphygmometerDeviceTransferDTO" id="SphygmometerDeviceTransferDTOResult">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceNo" column="device_no"/>
        <result property="fromRelationOrgId" column="from_relation_org_id"/>
        <result property="fromOrganizationId" column="from_organization_id"/>
        <result property="fromPlace" column="from_place"/>
        <result property="toRelationOrgId" column="to_relation_org_id"/>
        <result property="toOrganizationId" column="to_organization_id"/>
        <result property="toPlace" column="to_place"/>
        <result property="transferUserId" column="transfer_user_id"/>
        <result property="transferUserName" column="transfer_user_name"/>
        <result property="transferTime" column="transfer_time"/>
    </resultMap>

    <sql id="selectSphygmometerDeviceTransfer">
    	select
	        tsdr.id,
	        tsdr.device_id,
	        from_relation_org_id,
	        (select organization_name  from organization.t_organization_info where tsdr.from_organization_id = from_relation_org_id) from_relation_org_name,
	        from_organization_id,
            (select organization_name  from organization.t_organization_info where tsdr.from_organization_id = organization_id) from_organization_name,
	        from_place,
	        to_relation_org_id,
            (select organization_name  from organization.t_organization_info where tsdr.to_organization_id = to_relation_org_id) to_relation_org_name,
	        to_organization_id,
            (select organization_name  from organization.t_organization_info where tsdr.to_organization_id = organization_id) to_organization_name,
	        to_place,
	        transfer_user_id,
	        transfer_user_name,
	        transfer_time,
			tsd.device_type,
			tsd.device_no,
			tsd.device_model,
			tsd.device_imei
		from 
        	t_sphygmometer_device_transfer tsdr
        	left join t_sphygmometer_device tsd on tsd.device_id = tsdr.device_id
    </sql>

    <select id="selectSphygmometerDeviceTransferById" parameterType="int"
            resultMap="SphygmometerDeviceTransferDTOResult">
        <include refid="selectSphygmometerDeviceTransfer"/>
        where
        id = #{id}
    </select>

    <select id="selectSphygmometerDeviceTransferList"
            parameterType="com.bojun.sphygmometer.dto.SphygmometerDeviceTransferDTO"
            resultMap="SphygmometerDeviceTransferDTOResult">
        <include refid="selectSphygmometerDeviceTransfer"/>
        <where>
            <if test="id != null ">and tsdr.id = #{id}</if>
            <if test="deviceId != null ">and tsdr.device_id = #{deviceId}</if>
            <if test="fromPlace != null  and fromPlace != ''">and from_place = #{fromPlace}</if>
            <if test="toPlace != null  and toPlace != ''">and to_place = #{toPlace}</if>
            <if test="transferUserId != null ">and transfer_user_id = #{transferUserId}</if>
            <if test="transferUserName != null  and transferUserName != ''">and transfer_user_name =
                #{transferUserName}
            </if>
            <if test="transferTime != null ">and transfer_time = #{transferTime}</if>
            <if test="transferStartTime != null and transferEndTime != null ">
                AND transfer_time > #{transferStartTime}
                AND transfer_time &lt; #{transferEndTime}
            </if>
            <if test="fromOrgIdList != null and fromOrgIdList.size() > 0">
            	and from_organization_id in
				<foreach item="item" index="index"
						 collection="fromOrgIdList" open="(" separator=","
						 close=")">
					#{item}
				</foreach>
            </if>
            <if test="toOrgIdList != null and toOrgIdList.size() > 0">
            	and to_organization_id in
				<foreach item="item" index="index"
						 collection="toOrgIdList" open="(" separator=","
						 close=")">
					#{item}
				</foreach>
            </if>
            <if test="searchContent != null  and searchContent != ''">
                AND (
                    tsd.device_no LIKE concat( '%', '${searchContent}', '%' ) OR
                    tsd.device_model LIKE concat( '%', '${searchContent}', '%' ) OR
                    tsdr.from_place LIKE concat( '%', '${searchContent}', '%' ) OR
                    tsdr.to_place LIKE concat( '%', '${searchContent}', '%' )
                )
            </if>
        </where>
        order by transfer_time desc
    </select>

</mapper>