package com.bojun.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Model： ID生成器
 * Description：ID生成器
 * Author：赖水秀
 * created： 2019年11月2日
 */
public class IdWorker {

    protected static final Logger LOG = LoggerFactory.getLogger(IdWorker.class);

    private final long workerId;
    // 当前时间
    private final static long twepoch = 1288834974657L;
    private long sequence = 0L;
    // 机器标识位数
    private final static long workerIdBits = 4L;
    // 机器id最大值
    public final static long maxWorkerId = -1L ^ -1L << workerIdBits;
    // 序列号位数
    private final static long sequenceBits = 10L;
    // 机器id左移10位
    private final static long workerIdShift = sequenceBits;
    // 时间毫秒左移14位
    private final static long timestampLeftShift = sequenceBits + workerIdBits;
    public final static long sequenceMask = -1L ^ -1L << sequenceBits;
    private long lastTimestamp = -1L;

    public IdWorker(final long workerId) {
        super();
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format(
                    "worker Id can't be greater than %d or less than 0",
                    maxWorkerId));
        }
        this.workerId = workerId;
    }

    public IdWorker() {
        this.workerId = 1;
    }

    public synchronized long nextId() {
        long timestamp = this.timeGen();
        if (this.lastTimestamp == timestamp) {
            this.sequence = (this.sequence + 1) & sequenceMask;
            if (this.sequence == 0) {
                timestamp = this.tilNextMillis(this.lastTimestamp);
            }
        } else {
            this.sequence = 0;
        }
        // 当前时间小于上次时间（异常）
        if (timestamp < this.lastTimestamp) {
            try {
                throw new Exception(
                        String.format(
                                "Clock moved backwards. Refusing to generate id for %d milliseconds",
                                this.lastTimestamp - timestamp));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        this.lastTimestamp = timestamp;
        // 或运算
        long nextId = ((timestamp - twepoch << timestampLeftShift))
                | (this.workerId << workerIdShift) | (this.sequence);
        return nextId;
    }

    private long tilNextMillis(final long lastTimestamp) {
        long timestamp = this.timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = this.timeGen();
        }
        return timestamp;
    }

    private long timeGen() {
        return System.currentTimeMillis();
    }
    
    public static void main(String[] args) {
		System.out.println(new IdWorker().nextId());
	}
}

