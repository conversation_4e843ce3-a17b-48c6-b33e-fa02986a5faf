/**
 * 
 */
package com.bojun.contants;

/**
 * <AUTHOR> 常量类
 */
public class Contants {

	// 默认分页页码
	public static final int DEFAULT_PAGE_NUM = 1;
	// 默认分页页容量
	public static final int DEFAULT_EVERY_PAGE = 999999;
	//
	public static final String INITIAL_PASSWORD = "888888";

	// 二维码宽
	public static final int QRCORD_WIDTH = 200;
	
	// 二维码高
	public static final int QRCORD_HEIGHT = 200;

	// 新增请求
	public static final int ADD_REQUEST = 1;
	// 修改请求
	public static final int UPDATE_REQUEST = 2;
	// 删除请求
	public static final int DELETE_REQUEST = 3;
	// 登陆请求
	public static final int LOGIN_IN_REQUEST = 4;
	// 登出请求
	public static final int LOGIN_OUT_REQUEST = 5;
	// 随访状态  1：已完成
	public static final int FOLLOW_FINISH_STATUS = 1;
	// 随访状态  2：待随访
	public static final int FOLLOW_WAIT_STATUS = 2;
	// 随访状态  3:待完善
	public static final int FOLLOW_UNFINISH_STATUS = 3;
	// 随访状态  3:待随访
	public static final int TO_BE_FOLLOWED_UP = 2;

	/**
	 * 超管、角色权限相关Key的前缀通配符
	 * 主做删除用
	 */
	public static final String RK_SUPERADMIN_AUTH_PREFIX = "SuperAdminAuth:*";

	public static final String RK_ROLE_AUTH_PREFIX = "RoleAuth:*";

	/*
	机构权限相关RedisKey
	 */
	//Redis Key: 保存的是超管的机构集合, 非树形(对象类型)
	public static final String RK_SUPERADMIN_ORG_LIST = "SuperAdminAuth:OrgList";
	//Redis Key: 保存的是超管的机构集合, 树形(对象类型)
	public static final String RK_SUPERADMIN_ORG_TREE_LIST = "SuperAdminAuth:OrgTreeList";
	//Redis Key: 保存的是超管分机构类别的机构集合, 树形(对象类型)
	public static final String RK_SUPERADMIN_CLASSCODE_ORG_TREE_LIST = "SuperAdminAuth:ClassCode:{0}:OrgTreeList";

	//Redis Key: 保存的是角色的机构集合, 非树形(对象类型)
	public static final String RK_ROLE_ORG_LIST = "RoleAuth:RoleId:{0}:OrgList";
	//Redis Key: 保存的是角色的机构集合, 树形(对象类型)
	public static final String RK_ROLE_ORG_TREE_LIST = "RoleAuth:RoleId:{0}:OrgTreeList";
	//Redis Key: 保存的是角色分机构类别的机构集合, 树形(对象类型)
	public static final String RK_ROLE_CLASSCODE_ORG_TREE_LIST = "RoleAuth:RoleId:{0}:ClassCode:{1}:OrgTreeList";


	/*
	科室权限相关RedisKey
	 */
	//Redis Key: 保存的是超管的机构下的科室集合, 非树形(对象类型)
	public static final String RK_SUPERADMIN_ORG_DEPT_LIST = "SuperAdminAuth:OrgId:{0}:DeptList";
	//Redis Key: 保存的是超管的机构下的科室集合, 树形(对象类型)
	public static final String RK_SUPERADMIN_ORG_DEPT_TREE_LIST = "SuperAdminAuth:OrgId:{0}:DeptTreeList";

	//Redis Key: 保存的是角色的机构下的科室集合, 非树形(对象类型)
	public static final String RK_ROLE_ORG_DEPT_LIST = "RoleAuth:RoleId:{0}:OrgId:{1}:DeptList";
	//Redis Key: 保存的是角色的机构下的科室集合, 树形(对象类型)
	public static final String RK_ROLE_ORG_DEPT_TREE_LIST = "RoleAuth:RoleId:{0}:OrgId:{1}:DeptTreeList";

	/**
	 * 单点登录相关RedisKey
	 */
	//【控台】单点登录缓存Key，存的是token
	public static final String RK_CONSOLELOGINSSO = "ConsoleLoginSSO:UserId:{0}";
	//【控台】上一个登录的缓存信息Key，存的是用户信息
	public static final String RK_CONSOLELOGINSSO_INVALID = "ConsoleLoginSSO:Invalid:Token:{0}";

	//【APP】单点登录缓存Key，存的是token
	public static final String RK_APPLOGINSSO = "AppLoginSSO:UserId:{0}";
	//【APP】上一个登录的缓存信息Key，存的是用户信息
	public static final String RK_APPLOGINSSO_INVALID = "AppLoginSSO:Invalid:Token:{0}";
}
