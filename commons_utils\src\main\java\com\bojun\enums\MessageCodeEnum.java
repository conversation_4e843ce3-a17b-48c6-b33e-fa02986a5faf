package com.bojun.enums;

/**
 * Model： 消息推送类型
 * Description：消息推送类型
 * Author:廖德财 
 * created：2019年3月4日
 */
public enum MessageCodeEnum {
	
	OPERATION("OMSG001", "手术提醒"),
	ORDER("OMSG002", "护理提醒"),
	EXAM("EMSG001", "检查安排提醒"),
	MEMORANDUM("MMSG001", "备忘录提醒"),
	VOICE("VMSG001", "语音呼叫"),
	REFOREVOICE("RMSG001", "增援呼叫"),
	CRITICALVALUE("CMSG001", "危急值提醒"),
	CARENEEDVALUE("CMSG002", "照护需求提醒"),
	INFUSIONVALUE("IMSG001", "输液提醒"),
	DISCHARGE("DMSG001", "出院提醒"),
	PROPAGATION("XMSG001", "宣教推送")
	;

	private String code;
	private String 	desc;
	
	private MessageCodeEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}

	public static String getCodeDesc(String code) {
		String desc = "";
		for (MessageCodeEnum messageCodeEnum : MessageCodeEnum.values()) {
			if (code.equals(messageCodeEnum.getCode())) {
				desc = messageCodeEnum.getDesc();
				break;
			}
		}
		return desc;
	}

	public String getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
}
