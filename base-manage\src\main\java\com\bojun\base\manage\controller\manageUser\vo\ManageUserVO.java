/**
 * 
 */
package com.bojun.base.manage.controller.manageUser.vo;

import java.io.Serializable;
import java.util.Date;

import javax.validation.constraints.NotNull;

import com.bojun.utils.DateUtil;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：用户列表信息返参
 * Description：用户列表信息返参
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "用户列表信息返参", description = "用户列表信息返参")
public class ManageUserVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;

	
	@ApiModelProperty(value = "系统id", required = true, example = "S001")
	private String systemId;

	
	@ApiModelProperty(value = "系统类型id", required = true, example = "1")
	private Integer systemTypeId;

	@ApiModelProperty(value = "用户Id", required = true, example = "1")
	private Integer userId;

	@ApiModelProperty(value = "登录账号", required = true, example = "1")
	private String accountNo;

	
	@ApiModelProperty(value = "机构id", required = true, example = "1")
	private Integer organizationId;

	
	@ApiModelProperty(value = "系统名称", required = true, example = "测试")
	private String organizationName;

	
	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;
	
	@ApiModelProperty(value = "角色名称", required = true, example = "1")
	private String roleName;

	
	@ApiModelProperty(value = "工号", required = true, example = "1")
	private String workNumber;

	@NotNull(message = "部门id不能为空")
	@ApiModelProperty(value = "部门id", required = true, example = "1")
	private Integer deptId;

	
	@ApiModelProperty(value = "姓名", required = true, example = "1")
	private String realName; 
		
	@ApiModelProperty(value = "身份证号码", example = "360721111111111111")
	private String idNo;

	@ApiModelProperty(value = "权限类型（0：超级管理员，1：普通管理员）", required = true, example = "1")
	private Integer authType; 
	
	@ApiModelProperty(value = "用户类型 1：医疗机构人员 2：养老机构人员 3：监管人员 4：其他", required = true, example = "1")
	private Integer userType; 

	@ApiModelProperty(value = "状态0:停用 1:启用", required = true, example = "1")
	private Integer status;

	
	@ApiModelProperty(value = "创建来源（初始创建的系统code）", required = true, example = "1")
	private String sourceSystemId; 

	
	@ApiModelProperty(value = "备注", required = true, example = "1")
	private String remark; //

	
	@ApiModelProperty(value = "区域（病区）id", required = true, example = "1")
	private Integer wardId;

	
	@ApiModelProperty(value = "手机号", required = true, example = "1")
	private String mobile;
	
	
	@ApiModelProperty(value = "职称", required = true, example = "1")
	private String jobTitleName;
	
	
	
	@ApiModelProperty(value = "部门名称", required = true, example = "1")
	private String deptName;
	
	
	@ApiModelProperty(value = "病区名称", required = true, example = "1")
	private String wardName;
	
	@ApiModelProperty(value = "最近登陆时间", required = true, example = "1")
    private Date lastestLoginTime;
	
	@ApiModelProperty(value = "登录身份令牌（token）", required = true, example = "1")
	private String token; 
	
	@ApiModelProperty(value = "数据权限  1: 角色机构  2：账号机构", required = true, example = "1")
	private Integer dataPermissions;
	

	
	
	public Integer getDataPermissions() {
		return dataPermissions;
	}


	public void setDataPermissions(Integer dataPermissions) {
		this.dataPermissions = dataPermissions;
	}


	public String getToken() {
		return token;
	}


	public void setToken(String token) {
		this.token = token;
	}


	public String getRoleName() {
		return roleName;
	}


	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}


	public Date getLastestLoginTime() {
		return lastestLoginTime;
	}


	public String getLastestLoginTimeStr() {
		 if (null == this.lastestLoginTime) {
	            return null;
	        }
	       return DateUtil.getFormatDate(this.lastestLoginTime, "yyyy-MM-dd HH:mm:ss");
	}


	public void setLastestLoginTime(Date lastestLoginTime) {
		this.lastestLoginTime = lastestLoginTime;
	}


	public String getJobTitleName() {
		return jobTitleName;
	}


	public void setJobTitleName(String jobTitleName) {
		this.jobTitleName = jobTitleName;
	}


	public String getDeptName() {
		return deptName;
	}


	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}


	public String getWardName() {
		return wardName;
	}


	public void setWardName(String wardName) {
		this.wardName = wardName;
	}


	public String getSystemId() {
		return systemId;
	}


	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}


	public Integer getSystemTypeId() {
		return systemTypeId;
	}


	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}


	public Integer getUserId() {
		return userId;
	}


	public void setUserId(Integer userId) {
		this.userId = userId;
	}


	public String getAccountNo() {
		return accountNo;
	}


	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}


	public Integer getOrganizationId() {
		return organizationId;
	}


	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}


	public String getOrganizationName() {
		return organizationName;
	}


	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}


	public String getRoleId() {
		return roleId;
	}


	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}


	public String getWorkNumber() {
		return workNumber;
	}


	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}


	public Integer getDeptId() {
		return deptId;
	}


	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}


	public String getRealName() {
		return realName;
	}


	public void setRealName(String realName) {
		this.realName = realName;
	}


	public Integer getAuthType() {
		return authType;
	}


	public void setAuthType(Integer authType) {
		this.authType = authType;
	}


	public Integer getUserType() {
		return userType;
	}


	public void setUserType(Integer userType) {
		this.userType = userType;
	}
	
	public Integer getStatus() {
		return status;
	}
	
	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getSourceSystemId() {
		return sourceSystemId;
	}

	public void setSourceSystemId(String sourceSystemId) {
		this.sourceSystemId = sourceSystemId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Integer getWardId() {
		return wardId;
	}

	public void setWardId(Integer wardId) {
		this.wardId = wardId;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getIdNo() {
		return idNo;
	}

	public void setIdNo(String idNo) {
		this.idNo = idNo;
	}


}
