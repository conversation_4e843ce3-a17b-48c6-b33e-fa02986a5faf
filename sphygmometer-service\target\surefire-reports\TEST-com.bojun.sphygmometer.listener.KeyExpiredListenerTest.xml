<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.bojun.sphygmometer.listener.KeyExpiredListenerTest" time="48.265" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="pentium_pro+mmx pentium_pro pentium+mmx pentium i486 i386 i86"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\test-classes;C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes;C:\Leon\repository\org\springframework\boot\spring-boot-starter-web\2.0.3.RELEASE\spring-boot-starter-web-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter\2.0.3.RELEASE\spring-boot-starter-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot\2.0.3.RELEASE\spring-boot-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-logging\2.0.3.RELEASE\spring-boot-starter-logging-2.0.3.RELEASE.jar;C:\Leon\repository\org\apache\logging\log4j\log4j-to-slf4j\2.10.0\log4j-to-slf4j-2.10.0.jar;C:\Leon\repository\org\apache\logging\log4j\log4j-api\2.10.0\log4j-api-2.10.0.jar;C:\Leon\repository\org\slf4j\jul-to-slf4j\1.7.25\jul-to-slf4j-1.7.25.jar;C:\Leon\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Leon\repository\org\yaml\snakeyaml\1.19\snakeyaml-1.19.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-json\2.0.3.RELEASE\spring-boot-starter-json-2.0.3.RELEASE.jar;C:\Leon\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.9.6\jackson-datatype-jdk8-2.9.6.jar;C:\Leon\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.9.6\jackson-datatype-jsr310-2.9.6.jar;C:\Leon\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.9.6\jackson-module-parameter-names-2.9.6.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-tomcat\2.0.3.RELEASE\spring-boot-starter-tomcat-2.0.3.RELEASE.jar;C:\Leon\repository\org\apache\tomcat\embed\tomcat-embed-core\8.5.31\tomcat-embed-core-8.5.31.jar;C:\Leon\repository\org\apache\tomcat\embed\tomcat-embed-el\8.5.31\tomcat-embed-el-8.5.31.jar;C:\Leon\repository\org\apache\tomcat\embed\tomcat-embed-websocket\8.5.31\tomcat-embed-websocket-8.5.31.jar;C:\Leon\repository\org\hibernate\validator\hibernate-validator\6.0.10.Final\hibernate-validator-6.0.10.Final.jar;C:\Leon\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Leon\repository\org\jboss\logging\jboss-logging\3.3.2.Final\jboss-logging-3.3.2.Final.jar;C:\Leon\repository\com\fasterxml\classmate\1.3.4\classmate-1.3.4.jar;C:\Leon\repository\org\springframework\spring-web\5.0.7.RELEASE\spring-web-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-webmvc\5.0.7.RELEASE\spring-webmvc-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-aop\5.0.7.RELEASE\spring-aop-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-expression\5.0.7.RELEASE\spring-expression-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\2.0.0.RELEASE\spring-cloud-starter-netflix-eureka-client-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter\2.0.0.RELEASE\spring-cloud-starter-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-context\2.0.0.RELEASE\spring-cloud-context-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\security\spring-security-rsa\1.0.5.RELEASE\spring-security-rsa-1.0.5.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-core\2.0.0.RELEASE\spring-cloud-netflix-core-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-aop\2.0.3.RELEASE\spring-boot-starter-aop-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-eureka-client\2.0.0.RELEASE\spring-cloud-netflix-eureka-client-2.0.0.RELEASE.jar;C:\Leon\repository\com\netflix\eureka\eureka-client\1.9.2\eureka-client-1.9.2.jar;C:\Leon\repository\org\codehaus\jettison\jettison\1.3.7\jettison-1.3.7.jar;C:\Leon\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;C:\Leon\repository\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;C:\Leon\repository\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;C:\Leon\repository\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;C:\Leon\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Leon\repository\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;C:\Leon\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Leon\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Leon\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Leon\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Leon\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Leon\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Leon\repository\com\google\inject\guice\4.1.0\guice-4.1.0.jar;C:\Leon\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Leon\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Leon\repository\com\github\vlsi\compactmap\compactmap\1.2.1\compactmap-1.2.1.jar;C:\Leon\repository\com\github\andrewoma\dexx\dexx-collections\0.2\dexx-collections-0.2.jar;C:\Leon\repository\com\fasterxml\jackson\core\jackson-core\2.9.6\jackson-core-2.9.6.jar;C:\Leon\repository\com\netflix\eureka\eureka-core\1.9.2\eureka-core-1.9.2.jar;C:\Leon\repository\org\codehaus\woodstox\woodstox-core-asl\4.4.1\woodstox-core-asl-4.4.1.jar;C:\Leon\repository\javax\xml\stream\stax-api\1.0-2\stax-api-1.0-2.jar;C:\Leon\repository\org\codehaus\woodstox\stax2-api\3.1.4\stax2-api-3.1.4.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.0.0.RELEASE\spring-cloud-starter-netflix-archaius-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.0.0.RELEASE\spring-cloud-netflix-archaius-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.0.0.RELEASE\spring-cloud-starter-netflix-ribbon-2.0.0.RELEASE.jar;C:\Leon\repository\com\netflix\ribbon\ribbon\2.2.5\ribbon-2.2.5.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-transport\2.2.5\ribbon-transport-2.2.5.jar;C:\Leon\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Leon\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Leon\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Leon\repository\io\netty\netty-codec-http\4.1.25.Final\netty-codec-http-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-transport-native-epoll\4.1.25.Final\netty-transport-native-epoll-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-transport-native-unix-common\4.1.25.Final\netty-transport-native-unix-common-4.1.25.Final.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-core\2.2.5\ribbon-core-2.2.5.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-httpclient\2.2.5\ribbon-httpclient-2.2.5.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-loadbalancer\2.2.5\ribbon-loadbalancer-2.2.5.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Leon\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-eureka\2.2.5\ribbon-eureka-2.2.5.jar;C:\Leon\repository\com\thoughtworks\xstream\xstream\1.4.10\xstream-1.4.10.jar;C:\Leon\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Leon\repository\xpp3\xpp3_min\1.1.4c\xpp3_min-1.1.4c.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-test\2.0.3.RELEASE\spring-boot-starter-test-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-test\2.0.3.RELEASE\spring-boot-test-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.0.3.RELEASE\spring-boot-test-autoconfigure-2.0.3.RELEASE.jar;C:\Leon\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Leon\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Leon\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Leon\repository\junit\junit\4.12\junit-4.12.jar;C:\Leon\repository\org\assertj\assertj-core\3.9.1\assertj-core-3.9.1.jar;C:\Leon\repository\org\mockito\mockito-core\2.15.0\mockito-core-2.15.0.jar;C:\Leon\repository\net\bytebuddy\byte-buddy\1.7.11\byte-buddy-1.7.11.jar;C:\Leon\repository\net\bytebuddy\byte-buddy-agent\1.7.11\byte-buddy-agent-1.7.11.jar;C:\Leon\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Leon\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Leon\repository\org\hamcrest\hamcrest-library\1.3\hamcrest-library-1.3.jar;C:\Leon\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Leon\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Leon\repository\org\springframework\spring-core\5.0.7.RELEASE\spring-core-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-jcl\5.0.7.RELEASE\spring-jcl-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-test\5.0.7.RELEASE\spring-test-5.0.7.RELEASE.jar;C:\Leon\repository\org\xmlunit\xmlunit-core\2.5.1\xmlunit-core-2.5.1.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.0.0.RELEASE\spring-cloud-starter-openfeign-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.0.0.RELEASE\spring-cloud-openfeign-core-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-commons\2.0.0.RELEASE\spring-cloud-commons-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\security\spring-security-crypto\5.0.6.RELEASE\spring-security-crypto-5.0.6.RELEASE.jar;C:\Leon\repository\io\github\openfeign\feign-core\9.5.1\feign-core-9.5.1.jar;C:\Leon\repository\io\github\openfeign\feign-slf4j\9.5.1\feign-slf4j-9.5.1.jar;C:\Leon\repository\io\github\openfeign\feign-hystrix\9.5.1\feign-hystrix-9.5.1.jar;C:\Leon\repository\io\github\openfeign\feign-java8\9.5.1\feign-java8-9.5.1.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-hystrix\2.0.0.RELEASE\spring-cloud-starter-netflix-hystrix-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.0.0.RELEASE\spring-cloud-netflix-ribbon-2.0.0.RELEASE.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-core\1.5.12\hystrix-core-1.5.12.jar;C:\Leon\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-serialization\1.5.12\hystrix-serialization-1.5.12.jar;C:\Leon\repository\com\fasterxml\jackson\module\jackson-module-afterburner\2.9.6\jackson-module-afterburner-2.9.6.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-metrics-event-stream\1.5.12\hystrix-metrics-event-stream-1.5.12.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-javanica\1.5.12\hystrix-javanica-1.5.12.jar;C:\Leon\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Leon\repository\org\aspectj\aspectjweaver\1.8.13\aspectjweaver-1.8.13.jar;C:\Leon\repository\com\google\guava\guava\15.0\guava-15.0.jar;C:\Leon\repository\io\reactivex\rxjava-reactive-streams\1.2.1\rxjava-reactive-streams-1.2.1.jar;C:\Leon\repository\org\reactivestreams\reactive-streams\1.0.2\reactive-streams-1.0.2.jar;C:\Leon\repository\com\zaxxer\HikariCP\2.7.9\HikariCP-2.7.9.jar;C:\Leon\repository\org\slf4j\slf4j-api\1.7.25\slf4j-api-1.7.25.jar;C:\Leon\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.3\mybatis-spring-boot-starter-2.1.3.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-jdbc\2.0.3.RELEASE\spring-boot-starter-jdbc-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\spring-jdbc\5.0.7.RELEASE\spring-jdbc-5.0.7.RELEASE.jar;C:\Leon\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.3\mybatis-spring-boot-autoconfigure-2.1.3.jar;C:\Leon\repository\org\mybatis\mybatis\3.5.5\mybatis-3.5.5.jar;C:\Leon\repository\org\mybatis\mybatis-spring\2.0.5\mybatis-spring-2.0.5.jar;C:\Leon\repository\com\baomidou\mybatis-plus-boot-starter\3.3.2\mybatis-plus-boot-starter-3.3.2.jar;C:\Leon\repository\com\baomidou\mybatis-plus\3.3.2\mybatis-plus-3.3.2.jar;C:\Leon\repository\com\baomidou\mybatis-plus-extension\3.3.2\mybatis-plus-extension-3.3.2.jar;C:\Leon\repository\com\baomidou\mybatis-plus-core\3.3.2\mybatis-plus-core-3.3.2.jar;C:\Leon\repository\org\springframework\boot\spring-boot-autoconfigure\2.0.3.RELEASE\spring-boot-autoconfigure-2.0.3.RELEASE.jar;C:\Leon\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.2.3\pagehelper-spring-boot-starter-1.2.3.jar;C:\Leon\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.2.3\pagehelper-spring-boot-autoconfigure-1.2.3.jar;C:\Leon\repository\com\github\pagehelper\pagehelper\5.1.2\pagehelper-5.1.2.jar;C:\Leon\repository\com\github\jsqlparser\jsqlparser\1.0\jsqlparser-1.0.jar;C:\Leon\repository\mysql\mysql-connector-java\5.1.46\mysql-connector-java-5.1.46.jar;C:\Leon\repository\joda-time\joda-time\2.9.9\joda-time-2.9.9.jar;C:\Leon\repository\com\bojun\commons\commons_utils\0.0.1-SNAPSHOT\commons_utils-0.0.1-SNAPSHOT.jar;C:\Leon\repository\org\apache\commons\commons-lang3\3.7\commons-lang3-3.7.jar;C:\Leon\repository\commons-lang\commons-lang\2.4\commons-lang-2.4.jar;C:\Leon\repository\commons-configuration\commons-configuration\1.7\commons-configuration-1.7.jar;C:\Leon\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Leon\repository\commons-digester\commons-digester\1.8.1\commons-digester-1.8.1.jar;C:\Leon\repository\commons-beanutils\commons-beanutils\1.8.3\commons-beanutils-1.8.3.jar;C:\Leon\repository\com\google\zxing\core\3.3.2\core-3.3.2.jar;C:\Leon\repository\org\apache\poi\poi\3.9\poi-3.9.jar;C:\Leon\repository\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Leon\repository\org\apache\poi\poi-ooxml\3.9\poi-ooxml-3.9.jar;C:\Leon\repository\org\apache\poi\poi-ooxml-schemas\3.9\poi-ooxml-schemas-3.9.jar;C:\Leon\repository\org\apache\xmlbeans\xmlbeans\2.3.0\xmlbeans-2.3.0.jar;C:\Leon\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Leon\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\Leon\repository\commons-io\commons-io\2.1\commons-io-2.1.jar;C:\Leon\repository\com\alibaba\fastjson\1.2.47\fastjson-1.2.47.jar;C:\Leon\repository\org\springframework\spring-beans\5.0.7.RELEASE\spring-beans-5.0.7.RELEASE.jar;C:\Leon\repository\com\belerweb\pinyin4j\2.5.0\pinyin4j-2.5.0.jar;C:\Leon\repository\com\artofsolving\jodconverter\2.2.1\jodconverter-2.2.1.jar;C:\Leon\repository\org\openoffice\ridl\4.1.2\ridl-4.1.2.jar;C:\Leon\repository\org\openoffice\jurt\3.2.1\jurt-3.2.1.jar;C:\Leon\repository\com\hynnet\jacob\1.18\jacob-1.18.jar;C:\Leon\repository\org\openoffice\juh\3.1.0\juh-3.1.0.jar;C:\Leon\repository\org\openoffice\unoil\3.0.0\unoil-3.0.0.jar;C:\Leon\repository\org\apache\httpcomponents\httpclient\4.5.5\httpclient-4.5.5.jar;C:\Leon\repository\org\apache\httpcomponents\httpcore\4.4.9\httpcore-4.4.9.jar;C:\Leon\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Leon\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Leon\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Leon\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Leon\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Leon\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Leon\repository\org\jdom\jdom\2.0.2\jdom-2.0.2.jar;C:\Leon\repository\com\google\code\gson\gson\2.8.5\gson-2.8.5.jar;C:\Leon\repository\cn\jpush\api\jpush-client\3.4.7\jpush-client-3.4.7.jar;C:\Leon\repository\cn\jpush\api\jiguang-common\1.1.9\jiguang-common-1.1.9.jar;C:\Leon\repository\io\netty\netty-all\4.1.25.Final\netty-all-4.1.25.Final.jar;C:\Leon\repository\org\bouncycastle\bcprov-jdk15on\1.60\bcprov-jdk15on-1.60.jar;C:\Leon\repository\com\aliyun\oss\aliyun-sdk-oss\3.9.1\aliyun-sdk-oss-3.9.1.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-ram\3.0.0\aliyun-java-sdk-ram-3.0.0.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-sts\3.0.0\aliyun-java-sdk-sts-3.0.0.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-ecs\4.2.0\aliyun-java-sdk-ecs-4.2.0.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-kms\2.7.0\aliyun-java-sdk-kms-2.7.0.jar;C:\Leon\repository\commons-net\commons-net\3.3\commons-net-3.3.jar;C:\Leon\repository\com\google\zxing\javase\3.3.3\javase-3.3.3.jar;C:\Leon\repository\com\beust\jcommander\1.72\jcommander-1.72.jar;C:\Leon\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Leon\repository\com\getui\push\restful-sdk\1.0.0.3\restful-sdk-1.0.0.3.jar;C:\Leon\repository\org\apache\pdfbox\pdfbox\2.0.6\pdfbox-2.0.6.jar;C:\Leon\repository\org\apache\pdfbox\fontbox\2.0.6\fontbox-2.0.6.jar;C:\Leon\repository\org\apache\pdfbox\pdfbox-tools\2.0.6\pdfbox-tools-2.0.6.jar;C:\Leon\repository\org\apache\pdfbox\pdfbox-debugger\2.0.6\pdfbox-debugger-2.0.6.jar;C:\Leon\repository\com\aspose\aspose-words\14.9.0\aspose-words-14.9.0.jar;C:\Leon\repository\com\aspose\aspose-cells\8.5.2\aspose-cells-8.5.2.jar;C:\Leon\repository\com\aspose\aspose-slides\15.9.0\aspose-slides-15.9.0.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-data-redis\2.0.3.RELEASE\spring-boot-starter-data-redis-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\data\spring-data-redis\2.0.8.RELEASE\spring-data-redis-2.0.8.RELEASE.jar;C:\Leon\repository\org\springframework\data\spring-data-keyvalue\2.0.8.RELEASE\spring-data-keyvalue-2.0.8.RELEASE.jar;C:\Leon\repository\org\springframework\data\spring-data-commons\2.0.8.RELEASE\spring-data-commons-2.0.8.RELEASE.jar;C:\Leon\repository\org\springframework\spring-tx\5.0.7.RELEASE\spring-tx-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-oxm\5.0.7.RELEASE\spring-oxm-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-context-support\5.0.7.RELEASE\spring-context-support-5.0.7.RELEASE.jar;C:\Leon\repository\io\lettuce\lettuce-core\5.0.4.RELEASE\lettuce-core-5.0.4.RELEASE.jar;C:\Leon\repository\io\projectreactor\reactor-core\3.1.8.RELEASE\reactor-core-3.1.8.RELEASE.jar;C:\Leon\repository\io\netty\netty-common\4.1.25.Final\netty-common-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-transport\4.1.25.Final\netty-transport-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-buffer\4.1.25.Final\netty-buffer-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-resolver\4.1.25.Final\netty-resolver-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-handler\4.1.25.Final\netty-handler-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-codec\4.1.25.Final\netty-codec-4.1.25.Final.jar;C:\Leon\repository\com\bojun\commons_redis\0.0.1-SNAPSHOT\commons_redis-0.0.1-SNAPSHOT.jar;C:\Leon\repository\org\springframework\spring-context\5.0.7.RELEASE\spring-context-5.0.7.RELEASE.jar;C:\Leon\repository\com\fasterxml\jackson\core\jackson-annotations\2.9.0\jackson-annotations-2.9.0.jar;C:\Leon\repository\com\fasterxml\jackson\core\jackson-databind\2.9.6\jackson-databind-2.9.6.jar;C:\Leon\repository\com\bojun\commons_sms\0.0.1-SNAPSHOT\commons_sms-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-core\4.5.3\aliyun-java-sdk-core-4.5.3.jar;C:\Leon\repository\javax\xml\bind\jaxb-api\2.3.0\jaxb-api-2.3.0.jar;C:\Leon\repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;C:\Leon\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Leon\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;C:\Leon\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;C:\Leon\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;C:\Leon\repository\com\bojun\system-entity\0.0.1-SNAPSHOT\system-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\org\projectlombok\lombok\1.16.22\lombok-1.16.22.jar;C:\Leon\repository\com\baomidou\mybatis-plus-generator\3.3.2\mybatis-plus-generator-3.3.2.jar;C:\Leon\repository\com\bojun\sphygmometer-entity\0.0.1-SNAPSHOT\sphygmometer-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\io\swagger\swagger-annotations\1.5.24\swagger-annotations-1.5.24.jar;C:\Leon\repository\com\bojun\sphygmometer-service-api\0.0.1-SNAPSHOT\sphygmometer-service-api-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\github\xiaoymin\swagger-bootstrap-ui\1.9.6\swagger-bootstrap-ui-1.9.6.jar;C:\Leon\repository\org\javassist\javassist\3.25.0-GA\javassist-3.25.0-GA.jar;C:\Leon\repository\com\bojun\organization-entity\0.0.1-SNAPSHOT\organization-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\bojun\employee-entity\0.0.1-SNAPSHOT\employee-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\baomidou\mybatis-plus-annotation\3.3.2\mybatis-plus-annotation-3.3.2.jar;C:\Leon\repository\cmcciot\onenet\nbapi\sdk\1.0-SNAPSHOT\sdk-1.0-SNAPSHOT.jar;C:\Leon\repository\org\json\json\20180130\json-20180130.jar;C:\Leon\repository\commons-collections\commons-collections\3.2.1\commons-collections-3.2.1.jar;C:\Leon\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Leon\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Leon\repository\com\squareup\okhttp3\okhttp\3.8.1\okhttp-3.8.1.jar;C:\Leon\repository\com\squareup\okio\okio\1.13.0\okio-1.13.0.jar;C:\Leon\repository\com\github\binarywang\weixin-java-mp\4.0.0\weixin-java-mp-4.0.0.jar;C:\Leon\repository\com\github\binarywang\weixin-java-common\4.0.0\weixin-java-common-4.0.0.jar;C:\Leon\repository\org\apache\httpcomponents\httpmime\4.5.5\httpmime-4.5.5.jar;C:\Leon\repository\org\slf4j\jcl-over-slf4j\1.7.25\jcl-over-slf4j-1.7.25.jar;C:\Leon\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Leon\repository\com\github\binarywang\weixin-java-miniapp\4.0.0\weixin-java-miniapp-4.0.0.jar;C:\Leon\repository\org\bouncycastle\bcpkix-jdk15on\1.65\bcpkix-jdk15on-1.65.jar;C:\Leon\repository\com\gexin\platform\gexin-rp-sdk-base\4.0.0.38\gexin-rp-sdk-base-4.0.0.38.jar;C:\Leon\repository\com\google\protobuf\protobuf-java\2.5.0\protobuf-java-2.5.0.jar;C:\Leon\repository\com\gexin\platform\gexin-rp-fastjson\1.0.0.7\gexin-rp-fastjson-1.0.0.7.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="32"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value=""/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Leon\java\jdk\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire3467455081710181610\surefirebooter8600775023125812102.jar C:\Users\<USER>\AppData\Local\Temp\surefire3467455081710181610 2021-12-07T15-01-28_267-jvmRun1 surefire4041699281782181979tmp surefire_05949661738990858998tmp"/>
    <property name="surefire.test.class.path" value="C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\test-classes;C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes;C:\Leon\repository\org\springframework\boot\spring-boot-starter-web\2.0.3.RELEASE\spring-boot-starter-web-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter\2.0.3.RELEASE\spring-boot-starter-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot\2.0.3.RELEASE\spring-boot-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-logging\2.0.3.RELEASE\spring-boot-starter-logging-2.0.3.RELEASE.jar;C:\Leon\repository\org\apache\logging\log4j\log4j-to-slf4j\2.10.0\log4j-to-slf4j-2.10.0.jar;C:\Leon\repository\org\apache\logging\log4j\log4j-api\2.10.0\log4j-api-2.10.0.jar;C:\Leon\repository\org\slf4j\jul-to-slf4j\1.7.25\jul-to-slf4j-1.7.25.jar;C:\Leon\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Leon\repository\org\yaml\snakeyaml\1.19\snakeyaml-1.19.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-json\2.0.3.RELEASE\spring-boot-starter-json-2.0.3.RELEASE.jar;C:\Leon\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.9.6\jackson-datatype-jdk8-2.9.6.jar;C:\Leon\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.9.6\jackson-datatype-jsr310-2.9.6.jar;C:\Leon\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.9.6\jackson-module-parameter-names-2.9.6.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-tomcat\2.0.3.RELEASE\spring-boot-starter-tomcat-2.0.3.RELEASE.jar;C:\Leon\repository\org\apache\tomcat\embed\tomcat-embed-core\8.5.31\tomcat-embed-core-8.5.31.jar;C:\Leon\repository\org\apache\tomcat\embed\tomcat-embed-el\8.5.31\tomcat-embed-el-8.5.31.jar;C:\Leon\repository\org\apache\tomcat\embed\tomcat-embed-websocket\8.5.31\tomcat-embed-websocket-8.5.31.jar;C:\Leon\repository\org\hibernate\validator\hibernate-validator\6.0.10.Final\hibernate-validator-6.0.10.Final.jar;C:\Leon\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Leon\repository\org\jboss\logging\jboss-logging\3.3.2.Final\jboss-logging-3.3.2.Final.jar;C:\Leon\repository\com\fasterxml\classmate\1.3.4\classmate-1.3.4.jar;C:\Leon\repository\org\springframework\spring-web\5.0.7.RELEASE\spring-web-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-webmvc\5.0.7.RELEASE\spring-webmvc-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-aop\5.0.7.RELEASE\spring-aop-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-expression\5.0.7.RELEASE\spring-expression-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\2.0.0.RELEASE\spring-cloud-starter-netflix-eureka-client-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter\2.0.0.RELEASE\spring-cloud-starter-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-context\2.0.0.RELEASE\spring-cloud-context-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\security\spring-security-rsa\1.0.5.RELEASE\spring-security-rsa-1.0.5.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-core\2.0.0.RELEASE\spring-cloud-netflix-core-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-aop\2.0.3.RELEASE\spring-boot-starter-aop-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-eureka-client\2.0.0.RELEASE\spring-cloud-netflix-eureka-client-2.0.0.RELEASE.jar;C:\Leon\repository\com\netflix\eureka\eureka-client\1.9.2\eureka-client-1.9.2.jar;C:\Leon\repository\org\codehaus\jettison\jettison\1.3.7\jettison-1.3.7.jar;C:\Leon\repository\stax\stax-api\1.0.1\stax-api-1.0.1.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;C:\Leon\repository\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;C:\Leon\repository\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;C:\Leon\repository\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;C:\Leon\repository\antlr\antlr\2.7.7\antlr-2.7.7.jar;C:\Leon\repository\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;C:\Leon\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Leon\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Leon\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Leon\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Leon\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Leon\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Leon\repository\com\google\inject\guice\4.1.0\guice-4.1.0.jar;C:\Leon\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Leon\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\Leon\repository\com\github\vlsi\compactmap\compactmap\1.2.1\compactmap-1.2.1.jar;C:\Leon\repository\com\github\andrewoma\dexx\dexx-collections\0.2\dexx-collections-0.2.jar;C:\Leon\repository\com\fasterxml\jackson\core\jackson-core\2.9.6\jackson-core-2.9.6.jar;C:\Leon\repository\com\netflix\eureka\eureka-core\1.9.2\eureka-core-1.9.2.jar;C:\Leon\repository\org\codehaus\woodstox\woodstox-core-asl\4.4.1\woodstox-core-asl-4.4.1.jar;C:\Leon\repository\javax\xml\stream\stax-api\1.0-2\stax-api-1.0-2.jar;C:\Leon\repository\org\codehaus\woodstox\stax2-api\3.1.4\stax2-api-3.1.4.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.0.0.RELEASE\spring-cloud-starter-netflix-archaius-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.0.0.RELEASE\spring-cloud-netflix-archaius-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.0.0.RELEASE\spring-cloud-starter-netflix-ribbon-2.0.0.RELEASE.jar;C:\Leon\repository\com\netflix\ribbon\ribbon\2.2.5\ribbon-2.2.5.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-transport\2.2.5\ribbon-transport-2.2.5.jar;C:\Leon\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Leon\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Leon\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Leon\repository\io\netty\netty-codec-http\4.1.25.Final\netty-codec-http-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-transport-native-epoll\4.1.25.Final\netty-transport-native-epoll-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-transport-native-unix-common\4.1.25.Final\netty-transport-native-unix-common-4.1.25.Final.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-core\2.2.5\ribbon-core-2.2.5.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-httpclient\2.2.5\ribbon-httpclient-2.2.5.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-loadbalancer\2.2.5\ribbon-loadbalancer-2.2.5.jar;C:\Leon\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Leon\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Leon\repository\com\netflix\ribbon\ribbon-eureka\2.2.5\ribbon-eureka-2.2.5.jar;C:\Leon\repository\com\thoughtworks\xstream\xstream\1.4.10\xstream-1.4.10.jar;C:\Leon\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;C:\Leon\repository\xpp3\xpp3_min\1.1.4c\xpp3_min-1.1.4c.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-test\2.0.3.RELEASE\spring-boot-starter-test-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-test\2.0.3.RELEASE\spring-boot-test-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.0.3.RELEASE\spring-boot-test-autoconfigure-2.0.3.RELEASE.jar;C:\Leon\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Leon\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Leon\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Leon\repository\junit\junit\4.12\junit-4.12.jar;C:\Leon\repository\org\assertj\assertj-core\3.9.1\assertj-core-3.9.1.jar;C:\Leon\repository\org\mockito\mockito-core\2.15.0\mockito-core-2.15.0.jar;C:\Leon\repository\net\bytebuddy\byte-buddy\1.7.11\byte-buddy-1.7.11.jar;C:\Leon\repository\net\bytebuddy\byte-buddy-agent\1.7.11\byte-buddy-agent-1.7.11.jar;C:\Leon\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Leon\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Leon\repository\org\hamcrest\hamcrest-library\1.3\hamcrest-library-1.3.jar;C:\Leon\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Leon\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Leon\repository\org\springframework\spring-core\5.0.7.RELEASE\spring-core-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-jcl\5.0.7.RELEASE\spring-jcl-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-test\5.0.7.RELEASE\spring-test-5.0.7.RELEASE.jar;C:\Leon\repository\org\xmlunit\xmlunit-core\2.5.1\xmlunit-core-2.5.1.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.0.0.RELEASE\spring-cloud-starter-openfeign-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.0.0.RELEASE\spring-cloud-openfeign-core-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-commons\2.0.0.RELEASE\spring-cloud-commons-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\security\spring-security-crypto\5.0.6.RELEASE\spring-security-crypto-5.0.6.RELEASE.jar;C:\Leon\repository\io\github\openfeign\feign-core\9.5.1\feign-core-9.5.1.jar;C:\Leon\repository\io\github\openfeign\feign-slf4j\9.5.1\feign-slf4j-9.5.1.jar;C:\Leon\repository\io\github\openfeign\feign-hystrix\9.5.1\feign-hystrix-9.5.1.jar;C:\Leon\repository\io\github\openfeign\feign-java8\9.5.1\feign-java8-9.5.1.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-starter-netflix-hystrix\2.0.0.RELEASE\spring-cloud-starter-netflix-hystrix-2.0.0.RELEASE.jar;C:\Leon\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.0.0.RELEASE\spring-cloud-netflix-ribbon-2.0.0.RELEASE.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-core\1.5.12\hystrix-core-1.5.12.jar;C:\Leon\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-serialization\1.5.12\hystrix-serialization-1.5.12.jar;C:\Leon\repository\com\fasterxml\jackson\module\jackson-module-afterburner\2.9.6\jackson-module-afterburner-2.9.6.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-metrics-event-stream\1.5.12\hystrix-metrics-event-stream-1.5.12.jar;C:\Leon\repository\com\netflix\hystrix\hystrix-javanica\1.5.12\hystrix-javanica-1.5.12.jar;C:\Leon\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Leon\repository\org\aspectj\aspectjweaver\1.8.13\aspectjweaver-1.8.13.jar;C:\Leon\repository\com\google\guava\guava\15.0\guava-15.0.jar;C:\Leon\repository\io\reactivex\rxjava-reactive-streams\1.2.1\rxjava-reactive-streams-1.2.1.jar;C:\Leon\repository\org\reactivestreams\reactive-streams\1.0.2\reactive-streams-1.0.2.jar;C:\Leon\repository\com\zaxxer\HikariCP\2.7.9\HikariCP-2.7.9.jar;C:\Leon\repository\org\slf4j\slf4j-api\1.7.25\slf4j-api-1.7.25.jar;C:\Leon\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.3\mybatis-spring-boot-starter-2.1.3.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-jdbc\2.0.3.RELEASE\spring-boot-starter-jdbc-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\spring-jdbc\5.0.7.RELEASE\spring-jdbc-5.0.7.RELEASE.jar;C:\Leon\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.3\mybatis-spring-boot-autoconfigure-2.1.3.jar;C:\Leon\repository\org\mybatis\mybatis\3.5.5\mybatis-3.5.5.jar;C:\Leon\repository\org\mybatis\mybatis-spring\2.0.5\mybatis-spring-2.0.5.jar;C:\Leon\repository\com\baomidou\mybatis-plus-boot-starter\3.3.2\mybatis-plus-boot-starter-3.3.2.jar;C:\Leon\repository\com\baomidou\mybatis-plus\3.3.2\mybatis-plus-3.3.2.jar;C:\Leon\repository\com\baomidou\mybatis-plus-extension\3.3.2\mybatis-plus-extension-3.3.2.jar;C:\Leon\repository\com\baomidou\mybatis-plus-core\3.3.2\mybatis-plus-core-3.3.2.jar;C:\Leon\repository\org\springframework\boot\spring-boot-autoconfigure\2.0.3.RELEASE\spring-boot-autoconfigure-2.0.3.RELEASE.jar;C:\Leon\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.2.3\pagehelper-spring-boot-starter-1.2.3.jar;C:\Leon\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.2.3\pagehelper-spring-boot-autoconfigure-1.2.3.jar;C:\Leon\repository\com\github\pagehelper\pagehelper\5.1.2\pagehelper-5.1.2.jar;C:\Leon\repository\com\github\jsqlparser\jsqlparser\1.0\jsqlparser-1.0.jar;C:\Leon\repository\mysql\mysql-connector-java\5.1.46\mysql-connector-java-5.1.46.jar;C:\Leon\repository\joda-time\joda-time\2.9.9\joda-time-2.9.9.jar;C:\Leon\repository\com\bojun\commons\commons_utils\0.0.1-SNAPSHOT\commons_utils-0.0.1-SNAPSHOT.jar;C:\Leon\repository\org\apache\commons\commons-lang3\3.7\commons-lang3-3.7.jar;C:\Leon\repository\commons-lang\commons-lang\2.4\commons-lang-2.4.jar;C:\Leon\repository\commons-configuration\commons-configuration\1.7\commons-configuration-1.7.jar;C:\Leon\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;C:\Leon\repository\commons-digester\commons-digester\1.8.1\commons-digester-1.8.1.jar;C:\Leon\repository\commons-beanutils\commons-beanutils\1.8.3\commons-beanutils-1.8.3.jar;C:\Leon\repository\com\google\zxing\core\3.3.2\core-3.3.2.jar;C:\Leon\repository\org\apache\poi\poi\3.9\poi-3.9.jar;C:\Leon\repository\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Leon\repository\org\apache\poi\poi-ooxml\3.9\poi-ooxml-3.9.jar;C:\Leon\repository\org\apache\poi\poi-ooxml-schemas\3.9\poi-ooxml-schemas-3.9.jar;C:\Leon\repository\org\apache\xmlbeans\xmlbeans\2.3.0\xmlbeans-2.3.0.jar;C:\Leon\repository\dom4j\dom4j\1.6.1\dom4j-1.6.1.jar;C:\Leon\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\Leon\repository\commons-io\commons-io\2.1\commons-io-2.1.jar;C:\Leon\repository\com\alibaba\fastjson\1.2.47\fastjson-1.2.47.jar;C:\Leon\repository\org\springframework\spring-beans\5.0.7.RELEASE\spring-beans-5.0.7.RELEASE.jar;C:\Leon\repository\com\belerweb\pinyin4j\2.5.0\pinyin4j-2.5.0.jar;C:\Leon\repository\com\artofsolving\jodconverter\2.2.1\jodconverter-2.2.1.jar;C:\Leon\repository\org\openoffice\ridl\4.1.2\ridl-4.1.2.jar;C:\Leon\repository\org\openoffice\jurt\3.2.1\jurt-3.2.1.jar;C:\Leon\repository\com\hynnet\jacob\1.18\jacob-1.18.jar;C:\Leon\repository\org\openoffice\juh\3.1.0\juh-3.1.0.jar;C:\Leon\repository\org\openoffice\unoil\3.0.0\unoil-3.0.0.jar;C:\Leon\repository\org\apache\httpcomponents\httpclient\4.5.5\httpclient-4.5.5.jar;C:\Leon\repository\org\apache\httpcomponents\httpcore\4.4.9\httpcore-4.4.9.jar;C:\Leon\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;C:\Leon\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Leon\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;C:\Leon\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;C:\Leon\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Leon\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Leon\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;C:\Leon\repository\org\jdom\jdom\2.0.2\jdom-2.0.2.jar;C:\Leon\repository\com\google\code\gson\gson\2.8.5\gson-2.8.5.jar;C:\Leon\repository\cn\jpush\api\jpush-client\3.4.7\jpush-client-3.4.7.jar;C:\Leon\repository\cn\jpush\api\jiguang-common\1.1.9\jiguang-common-1.1.9.jar;C:\Leon\repository\io\netty\netty-all\4.1.25.Final\netty-all-4.1.25.Final.jar;C:\Leon\repository\org\bouncycastle\bcprov-jdk15on\1.60\bcprov-jdk15on-1.60.jar;C:\Leon\repository\com\aliyun\oss\aliyun-sdk-oss\3.9.1\aliyun-sdk-oss-3.9.1.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-ram\3.0.0\aliyun-java-sdk-ram-3.0.0.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-sts\3.0.0\aliyun-java-sdk-sts-3.0.0.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-ecs\4.2.0\aliyun-java-sdk-ecs-4.2.0.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-kms\2.7.0\aliyun-java-sdk-kms-2.7.0.jar;C:\Leon\repository\commons-net\commons-net\3.3\commons-net-3.3.jar;C:\Leon\repository\com\google\zxing\javase\3.3.3\javase-3.3.3.jar;C:\Leon\repository\com\beust\jcommander\1.72\jcommander-1.72.jar;C:\Leon\repository\com\github\jai-imageio\jai-imageio-core\1.4.0\jai-imageio-core-1.4.0.jar;C:\Leon\repository\com\getui\push\restful-sdk\1.0.0.3\restful-sdk-1.0.0.3.jar;C:\Leon\repository\org\apache\pdfbox\pdfbox\2.0.6\pdfbox-2.0.6.jar;C:\Leon\repository\org\apache\pdfbox\fontbox\2.0.6\fontbox-2.0.6.jar;C:\Leon\repository\org\apache\pdfbox\pdfbox-tools\2.0.6\pdfbox-tools-2.0.6.jar;C:\Leon\repository\org\apache\pdfbox\pdfbox-debugger\2.0.6\pdfbox-debugger-2.0.6.jar;C:\Leon\repository\com\aspose\aspose-words\14.9.0\aspose-words-14.9.0.jar;C:\Leon\repository\com\aspose\aspose-cells\8.5.2\aspose-cells-8.5.2.jar;C:\Leon\repository\com\aspose\aspose-slides\15.9.0\aspose-slides-15.9.0.jar;C:\Leon\repository\org\springframework\boot\spring-boot-starter-data-redis\2.0.3.RELEASE\spring-boot-starter-data-redis-2.0.3.RELEASE.jar;C:\Leon\repository\org\springframework\data\spring-data-redis\2.0.8.RELEASE\spring-data-redis-2.0.8.RELEASE.jar;C:\Leon\repository\org\springframework\data\spring-data-keyvalue\2.0.8.RELEASE\spring-data-keyvalue-2.0.8.RELEASE.jar;C:\Leon\repository\org\springframework\data\spring-data-commons\2.0.8.RELEASE\spring-data-commons-2.0.8.RELEASE.jar;C:\Leon\repository\org\springframework\spring-tx\5.0.7.RELEASE\spring-tx-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-oxm\5.0.7.RELEASE\spring-oxm-5.0.7.RELEASE.jar;C:\Leon\repository\org\springframework\spring-context-support\5.0.7.RELEASE\spring-context-support-5.0.7.RELEASE.jar;C:\Leon\repository\io\lettuce\lettuce-core\5.0.4.RELEASE\lettuce-core-5.0.4.RELEASE.jar;C:\Leon\repository\io\projectreactor\reactor-core\3.1.8.RELEASE\reactor-core-3.1.8.RELEASE.jar;C:\Leon\repository\io\netty\netty-common\4.1.25.Final\netty-common-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-transport\4.1.25.Final\netty-transport-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-buffer\4.1.25.Final\netty-buffer-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-resolver\4.1.25.Final\netty-resolver-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-handler\4.1.25.Final\netty-handler-4.1.25.Final.jar;C:\Leon\repository\io\netty\netty-codec\4.1.25.Final\netty-codec-4.1.25.Final.jar;C:\Leon\repository\com\bojun\commons_redis\0.0.1-SNAPSHOT\commons_redis-0.0.1-SNAPSHOT.jar;C:\Leon\repository\org\springframework\spring-context\5.0.7.RELEASE\spring-context-5.0.7.RELEASE.jar;C:\Leon\repository\com\fasterxml\jackson\core\jackson-annotations\2.9.0\jackson-annotations-2.9.0.jar;C:\Leon\repository\com\fasterxml\jackson\core\jackson-databind\2.9.6\jackson-databind-2.9.6.jar;C:\Leon\repository\com\bojun\commons_sms\0.0.1-SNAPSHOT\commons_sms-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\aliyun\aliyun-java-sdk-core\4.5.3\aliyun-java-sdk-core-4.5.3.jar;C:\Leon\repository\javax\xml\bind\jaxb-api\2.3.0\jaxb-api-2.3.0.jar;C:\Leon\repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;C:\Leon\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Leon\repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;C:\Leon\repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;C:\Leon\repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;C:\Leon\repository\com\bojun\system-entity\0.0.1-SNAPSHOT\system-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\org\projectlombok\lombok\1.16.22\lombok-1.16.22.jar;C:\Leon\repository\com\baomidou\mybatis-plus-generator\3.3.2\mybatis-plus-generator-3.3.2.jar;C:\Leon\repository\com\bojun\sphygmometer-entity\0.0.1-SNAPSHOT\sphygmometer-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\io\swagger\swagger-annotations\1.5.24\swagger-annotations-1.5.24.jar;C:\Leon\repository\com\bojun\sphygmometer-service-api\0.0.1-SNAPSHOT\sphygmometer-service-api-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\github\xiaoymin\swagger-bootstrap-ui\1.9.6\swagger-bootstrap-ui-1.9.6.jar;C:\Leon\repository\org\javassist\javassist\3.25.0-GA\javassist-3.25.0-GA.jar;C:\Leon\repository\com\bojun\organization-entity\0.0.1-SNAPSHOT\organization-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\bojun\employee-entity\0.0.1-SNAPSHOT\employee-entity-0.0.1-SNAPSHOT.jar;C:\Leon\repository\com\baomidou\mybatis-plus-annotation\3.3.2\mybatis-plus-annotation-3.3.2.jar;C:\Leon\repository\cmcciot\onenet\nbapi\sdk\1.0-SNAPSHOT\sdk-1.0-SNAPSHOT.jar;C:\Leon\repository\org\json\json\20180130\json-20180130.jar;C:\Leon\repository\commons-collections\commons-collections\3.2.1\commons-collections-3.2.1.jar;C:\Leon\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;C:\Leon\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;C:\Leon\repository\com\squareup\okhttp3\okhttp\3.8.1\okhttp-3.8.1.jar;C:\Leon\repository\com\squareup\okio\okio\1.13.0\okio-1.13.0.jar;C:\Leon\repository\com\github\binarywang\weixin-java-mp\4.0.0\weixin-java-mp-4.0.0.jar;C:\Leon\repository\com\github\binarywang\weixin-java-common\4.0.0\weixin-java-common-4.0.0.jar;C:\Leon\repository\org\apache\httpcomponents\httpmime\4.5.5\httpmime-4.5.5.jar;C:\Leon\repository\org\slf4j\jcl-over-slf4j\1.7.25\jcl-over-slf4j-1.7.25.jar;C:\Leon\repository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;C:\Leon\repository\com\github\binarywang\weixin-java-miniapp\4.0.0\weixin-java-miniapp-4.0.0.jar;C:\Leon\repository\org\bouncycastle\bcpkix-jdk15on\1.65\bcpkix-jdk15on-1.65.jar;C:\Leon\repository\com\gexin\platform\gexin-rp-sdk-base\4.0.0.38\gexin-rp-sdk-base-4.0.0.38.jar;C:\Leon\repository\com\google\protobuf\protobuf-java\2.5.0\protobuf-java-2.5.0.jar;C:\Leon\repository\com\gexin\platform\gexin-rp-fastjson\1.0.0.7\gexin-rp-fastjson-1.0.0.7.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Leon\java\jdk\jre"/>
    <property name="basedir" value="C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire3467455081710181610\surefirebooter8600775023125812102.jar"/>
    <property name="sun.boot.class.path" value="C:\Leon\java\jdk\jre\lib\resources.jar;C:\Leon\java\jdk\jre\lib\rt.jar;C:\Leon\java\jdk\jre\lib\sunrsasign.jar;C:\Leon\java\jdk\jre\lib\jsse.jar;C:\Leon\java\jdk\jre\lib\jce.jar;C:\Leon\java\jdk\jre\lib\charsets.jar;C:\Leon\java\jdk\jre\lib\jfr.jar;C:\Leon\java\jdk\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot Client Compiler"/>
    <property name="java.runtime.version" value="1.8.0_144-b01"/>
    <property name="user.name" value="LIUJUN"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Leon\java\jdk\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) Client VM"/>
    <property name="localRepository" value="C:\Leon\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2020.3"/>
    <property name="java.version" value="1.8.0_144"/>
    <property name="user.dir" value="C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service"/>
    <property name="os.arch" value="x86"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Leon\java\jdk\jre\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\app\LIUJUN\product\11.2.0\client_1\bin;C:\Leon\software\OracleClient;C:\Leon\software\Oracle\product\11.2.0\dbhome_1\bin;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\iCLS\;C:\Program Files\Intel\Intel(R) Management Engine Components\iCLS\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files\Intel\Intel(R) Management Engine Components\DAL;C:\Program Files (x86)\Intel\Intel(R) Management Engine Components\IPT;C:\Program Files\Intel\Intel(R) Management Engine Components\IPT;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\TortoiseSVN\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\pb\Sybase\SQL Anywhere 8\win32;C:\pb\Sybase\Shared\win32;C:\pb\Sybase\Shared\Sybase Central 4.1;C:\pb\Sybase\PowerDynamo\win32;C:\Program Files (x86)\Sybase\PowerDynamo\win32;C:\Program Files (x86)\szzfcg\tbqw\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\Redis\;C:\Leon\software\apache-maven-3.6.3\bin;C:\Leon\java\jdk\bin;C:\Leon\java\jdk\jre\bin;C:\Leon\software\erl-23.2\bin;C:\Leon\sqlite3;C:\Program Files\Git\cmd;C:\Program Files\TortoiseGit\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;;C:\Leon\JetBrains\IntelliJ IDEA\IntelliJ IDEA 2020.3\bin;;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.144-b01"/>
    <property name="java.ext.dirs" value="C:\Leon\java\jdk\jre\lib\ext;C:\WINDOWS\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="maven.repo.local" value="C:\Leon\repository"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="processUserTestPlanRecord" classname="com.bojun.sphygmometer.listener.KeyExpiredListenerTest" time="1.223"/>
</testsuite>