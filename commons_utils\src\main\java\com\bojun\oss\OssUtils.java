package com.bojun.oss;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.net.URL;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.Bucket;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.bojun.utils.PropertiesUtils;

/**
*Model：模块名称
*Description：OSS文件操作工具类
*Author: 肖泽权
*created：2021年1月26日
*/
public class OssUtils implements Serializable{
	
	static Log log = LogFactory.getLog(OssUtils.class);

	private static final long serialVersionUID = -1487312899544754588L;
	/**
	  * 应用KEY
	 */
	private final static String END_POINT = PropertiesUtils.getProperty("config.properties", "oss.end.point");
	
	/**
	  * 应用KEY
	 */
	private final static String BASE_URL = PropertiesUtils.getProperty("config.properties", "oss.base.url");
	
	/**
	  * 应用KEY
	 */
	private final static String BUCKET_NAME = PropertiesUtils.getProperty("config.properties", "oss.bucket.name");
	/**
     * 通过文件流上传文件
     * 
     * @param fileName
     * @param InputStream
     * @return
     */
    public static String uploadFileInputStream(String fileName, InputStream is) {
        OSSClient ossClient = getOssClient();
        String filePath = null;
        try {

            ObjectMetadata metadata = new ObjectMetadata();
            // 上传的文件的长度
            metadata.setContentLength(is.available());
            // 指定该Object被下载时的网页的缓存行为
            metadata.setCacheControl("no-cache");
            // 指定该Object下设置Header
            metadata.setHeader("Pragma", "no-cache");
            // 指定该Object被下载时的内容编码格式
            metadata.setContentEncoding("UTF-8");
            // 上传文件 (上传文件流的形式)
            @SuppressWarnings("unused")
            PutObjectResult putObject = ossClient.putObject(AliOssConfig.BUCKET_NAME, fileName, is,
                metadata);
            filePath = BASE_URL.concat(fileName);
            System.out.println(filePath);
            // log.info("filePath:{},PutObjectResult:{}", filePath, JSONObject.toJSONString(putObject));
        } catch (OSSException oe) {
            oe.printStackTrace();
            // log.warn("Error Message: " + oe.getErrorCode());
            // log.warn("Error Code: " + oe.getErrorCode());
            // log.warn("Request ID: " + oe.getRequestId());
            // log.warn("Host ID: " + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Error Message: " + ce.getMessage());
        } catch (Throwable e) {
            e.printStackTrace();
        } finally {
            ossClient.shutdown();
        }
        return filePath;
    }

    public static String uploadFileWithString(String fileName, String content) {
        return uploadFileInputStream(fileName, new ByteArrayInputStream(content.getBytes()));
    }

    public static void main(String[] args) {
//        String fileKey = "AkXk4uaRdRAkXcdJRTKDb8_Sga8GIUfVa73s9ry2m8IYA_D2450_1611572989813.flv";
//        System.out.println(AliOssFileUtils.key("txt"));
        // new AliOssUploadService()
        //downloadFile("PCFD7yOOe39ykEE1nf41z7_d899940e-7999-4628-b16e-888acc0e7541_H100015_1611746844697.aac","D:/rongDownload/rong/vedio","PCFD7yOOe39ykEE1nf41z7_d899940e-7999-4628-b16e-888acc0e7541_H100015_1611746844697.aac");
//        new OssUtils().getSignUrl(fileKey);
    	//System.out.println(BASE_URL);
    }

    /**
     * 通过文件KEY下载加密路径
     * 
     * @param fileKey
     * @return
     */
    public static String getSignUrl(String fileKey) {
        OSSClient ossClient = getOssClient();
        String signUrl = null;
        // 24小时有效期
        long expire = System.currentTimeMillis() + (1000 * 60 * 60 * 24);
        URL urlObj = ossClient.generatePresignedUrl(AliOssConfig.BUCKET_NAME, fileKey, new Date(expire));
        // 获取加密的下载路径
        signUrl = urlObj == null ? "" : urlObj.toString();
        if(!BASE_URL.contains("qbj-rong-file")) {
        	signUrl=signUrl.replaceFirst("http://192.168.8.101:9002/qbj-rong-file/", "http://qbj-rong-file.oss-cn-shenzhen.aliyuncs.com/");
        }
        System.out.println("urlObj"+urlObj.toString());
        System.out.println("signUrl"+signUrl.toString());
        return signUrl;
    }
    

    /**
     * 通过文件KEY下载文件
     * 
     * @param fileKey
     * @return
     */
    public static boolean downloadFile(String fileKey, String filePath, String fileName) {
        OSSClient ossClient = getOssClient();
        String fileUrl = null;
        try {
            // 设置文件下载的头部元素
            ObjectMetadata metadata = new ObjectMetadata();
            // 上传的文件的长度
            // metadata.setContentLength(is.available());
            // 指定该Object被下载时的网页的缓存行为
            metadata.setCacheControl("no-cache");
            // 指定该Object下设置Header
            metadata.setHeader("Pragma", "no-cache");
            // 指定该Object被下载时的内容编码格式
            metadata.setContentEncoding("UTF-8");
            // 下载文件 (上传文件流的形式)
            OSSObject ossObj = ossClient.getObject(BUCKET_NAME, fileKey);
            // 写入文件到本地
            wirteFile(fileKey, ossObj.getObjectContent(), filePath, fileName);
            // ossObj.getObjectContent()
            // ObjectAcl objectAcl = ossClient.getObjectAcl(AliOssConfig.BUCKET_NAME, fileKey);

            log.info(ossObj.getResponse().getUri());
            // System.out.println(objectAcl.getResponse().getUri());
            // log.info("filePath:{},PutObjectResult:{}", filePath, JSONObject.toJSONString(putObject));
        } catch (OSSException oe) {
            oe.printStackTrace();
            return false;
        } catch (ClientException ce) {
            System.out.println("Error Message: " + ce.getMessage());
            return false;
        } catch (Throwable e) {
            e.printStackTrace();
            return false;
        } finally {
            ossClient.shutdown();
        }
        return true;
    }

    /**
     * 文件写入到指定路径 
     * @Description
     * <AUTHOR>
     * @param fileKey
     * @param is
     * @throws FileNotFoundException
     * @throws IOException
     * @return void
     * created：2021年1月25日
     */
    private static void wirteFile(String fileKey, InputStream is, String filePath, String fileName) throws FileNotFoundException,
			IOException {
		BufferedInputStream input = null;
		FileOutputStream fos = null;
		try {
			input = new BufferedInputStream(is);
			File fileIn = new File(filePath);
			if(!fileIn.exists()){
				fileIn.mkdirs();
			}
			File fileOut = new File(filePath + File.separator + fileName);
			fos = new FileOutputStream(fileOut);
			byte[] buffBytes = new byte[1024];
			int read = 0;
			while ((read = input.read(buffBytes)) != -1) {
				fos.write(buffBytes, 0, read);
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (input != null) {
				input.close();
			}
			if (fos != null) {
				fos.flush();
				fos.close();
			}
		}
	}

    /**
     * 创建OSS客户端连接对象
     * 
     * @return
     */
    private static OSSClient getOssClient() {
        OSSClient ossClient = new OSSClient(END_POINT, AliOssConfig.accessKeyId,
            AliOssConfig.accessKeySecret);
        return ossClient;
    }

    /**
     * 创建存储空间
     * @param ossClient      OSS连接
     * @param bucketName 存储空间
     * @return
     */
    public static String createBucketName(String bucketName) {
        // 存储空间
        OSSClient ossClient = getOssClient();
        final String bucketNames = bucketName;
        if (!ossClient.doesBucketExist(bucketName)) {
            // 创建存储空间
            Bucket bucket = ossClient.createBucket(bucketName);
            // log.info("创建存储空间成功");
            return bucket.getName();
        }
        ossClient.shutdown();
        return bucketNames;
    }

    /**  
     * 根据key删除OSS服务器上的文件  
     * 
     * @param bucketName  存储空间 
     * @param key Bucket下的文件的路径名+文件名 如："upload/cake.jpg"
     */
    public static void deleteFile(String bucketName, String key) {
        OSSClient ossClient = getOssClient();
        ossClient.deleteObject(bucketName, key);
        // log.info("删除" + bucketName + "下的文件" + key + "成功");
        ossClient.shutdown();
    }

    /*
     * public String uploadFile(String uploadFile, String fileName) {
     * UUIDUtils.getID();
     * OSSClient ossClient = getOssClient();
     * String filePath = null;
     * try {
     * UploadFileRequest uploadFileRequest = new UploadFileRequest(AliOssConfig.bucketName,
     * fileName);
     * uploadFileRequest.setUploadFile(uploadFile);
     * uploadFileRequest.setTaskNum(5);
     * // Sets the part size to 1MB.
     * uploadFileRequest.setPartSize(1024 * 1024 * 1);
     * // Enables the checkpoint file. By default it's off.
     * // uploadFileRequest.setEnableCheckpoint(true);
     * 
     * UploadFileResult uploadResult = ossClient.uploadFile(uploadFileRequest);
     * 
     * CompleteMultipartUploadResult multipartUploadResult = uploadResult
     * .getMultipartUploadResult();
     * filePath = multipartUploadResult.getLocation();
     * System.out.println(filePath);
     * 
     * } catch (Exception e) {
     * e.printStackTrace();
     * } catch (Throwable e) {
     * e.printStackTrace();
     * } finally {
     * ossClient.shutdown();
     * }
     * return filePath;
     * }
     */

}

