package com.bojun.system.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 15:56
 */
public class QuartzJobRecordDTO implements Serializable {
	
	private static final long serialVersionUID = -6433867229851289743L;

	private String TASKID;//定时任务子任务id
    
    private Date TRIGGERDATE;//执行日期
    
    private Integer STATUS;//执行状态（1成功2失败）
    
    private String REMARK;//备注
    
    private String TASKPARENTID;//定时任务id
    
    private String TRIGGESTARTRDATE;//执行开始日期
    
    private String TRIGGEENDRDATE;//执行结束日期
    
    private Integer everyPage;//页数
    
    private Integer pageNum;//页码
    
    private String JOBNAME;//任务名称
    
    private String JOBGROUP;//任务分组
    
    private Integer TPLTYPE;//执行模板类型1数据质量探查2医嘱闭环
    
    private String TPLID;//执行模板id

	public String getTASKID() {
		return TASKID;
	}

	public void setTASKID(String tASKID) {
		TASKID = tASKID;
	}

	public Date getTRIGGERDATE() {
		return TRIGGERDATE;
	}

	public void setTRIGGERDATE(Date tRIGGERDATE) {
		TRIGGERDATE = tRIGGERDATE;
	}

	public Integer getSTATUS() {
		return STATUS;
	}

	public void setSTATUS(Integer sTATUS) {
		STATUS = sTATUS;
	}

	public String getREMARK() {
		return REMARK;
	}

	public void setREMARK(String rEMARK) {
		REMARK = rEMARK;
	}

	public String getTASKPARENTID() {
		return TASKPARENTID;
	}

	public void setTASKPARENTID(String tASKPARENTID) {
		TASKPARENTID = tASKPARENTID;
	}

	public String getTRIGGESTARTRDATE() {
		return TRIGGESTARTRDATE;
	}

	public void setTRIGGESTARTRDATE(String tRIGGESTARTRDATE) {
		TRIGGESTARTRDATE = tRIGGESTARTRDATE;
	}

	public String getTRIGGEENDRDATE() {
		return TRIGGEENDRDATE;
	}

	public void setTRIGGEENDRDATE(String tRIGGEENDRDATE) {
		TRIGGEENDRDATE = tRIGGEENDRDATE;
	}

	public Integer getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(Integer everyPage) {
		this.everyPage = everyPage;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public String getJOBNAME() {
		return JOBNAME;
	}

	public void setJOBNAME(String jOBNAME) {
		JOBNAME = jOBNAME;
	}

	public String getJOBGROUP() {
		return JOBGROUP;
	}

	public void setJOBGROUP(String jOBGROUP) {
		JOBGROUP = jOBGROUP;
	}

	public Integer getTPLTYPE() {
		return TPLTYPE;
	}

	public void setTPLTYPE(Integer tPLTYPE) {
		TPLTYPE = tPLTYPE;
	}

	public String getTPLID() {
		return TPLID;
	}

	public void setTPLID(String tPLID) {
		TPLID = tPLID;
	}

    
    

}
