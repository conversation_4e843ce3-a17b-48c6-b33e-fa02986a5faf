/**
 * 
 */
package com.bojun.base.manage.controller.login.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：系统用户信息
*Description：系统用户登录成功返回信息
*Author:段德鹏
*created：2020年4月23日
**/
@ApiModel(value = "管理员用户信息", description = "管理员用户登录成功返回信息")
public class ManageUserLoginVO implements Serializable {

	private static final long serialVersionUID = -5171151162339639466L;
	
	@ApiModelProperty(value="账号")
	private String accountNo;
	
	@ApiModelProperty(value="真实姓名")
	private String realName;
	
	@ApiModelProperty(value="工号")
	private String workNumber;
	
	@ApiModelProperty(value="机构Id")
	private Integer organizationId;
	
	@ApiModelProperty(value="机构名称")
	private String organizationName;
	
	@ApiModelProperty(value="权限类型（0：超级管理员，1：普通管理员） ")
	private Integer authType;
	
	@ApiModelProperty(value="用户类型  1：医疗机构人员   2：养老机构人员  3：监管人员  4：其他 ")
	private Integer userType;
	
	@ApiModelProperty(value="角色ID ")
	private String roleId;
	
	@ApiModelProperty(value="角色名称 ")
	private String roleName;
	
	@ApiModelProperty(value="登陆身份识别令牌")
	private String token;

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public String getRealName() {
		return realName;
	}

	public void setRealName(String realName) {
		this.realName = realName;
	}

	

	public Integer getAuthType() {
		return authType;
	}

	public void setAuthType(Integer authType) {
		this.authType = authType;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getWorkNumber() {
		return workNumber;
	}

	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	

}
