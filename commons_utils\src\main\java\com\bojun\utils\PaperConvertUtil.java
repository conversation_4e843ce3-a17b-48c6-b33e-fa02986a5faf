package com.bojun.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ConnectException;

import com.artofsolving.jodconverter.DocumentConverter;
import com.artofsolving.jodconverter.openoffice.connection.OpenOfficeConnection;
import com.artofsolving.jodconverter.openoffice.connection.SocketOpenOfficeConnection;
import com.artofsolving.jodconverter.openoffice.converter.OpenOfficeDocumentConverter;

/**
 * Model： 将Office文档转换为PDF
 * Description：将Office文档转换为PDF
 * Author：赖水秀
 * created： 2019年2月28日
 */
public class PaperConvertUtil implements Runnable {

	private String sourcePath; //源文件
	private String fileName; //文件名
	private String directory; //文件夹
	private String fileType; //文件类型
	
	
	
	public PaperConvertUtil(String sourcePath, String fileName, String directory, String fileType) {
		super();
		this.sourcePath = sourcePath;
		this.fileName = fileName;
		this.directory = directory;
		this.fileType = fileType;
	}

	public void run() {
		// TODO Auto-generated method stub
		executeConvert();
	}
	
	public void executeConvert() {
		int i = -1;
		if(this.fileType.equals(".doc") || this.fileType.equals(".docx")) {
			System.out.println(sourcePath);
			System.out.println(directory+"/"+fileName+".pdf");
			i = office2PDF(sourcePath, directory+"/"+fileName+".pdf");
		}
		System.out.println("i:"+i);
		/*
		if(i!=-1) {
			int j = ConvertPdfToSwf(directory+"/"+fileName+".pdf", directory);
			System.out.println("j:"+j);
		}*/
	}
	
	/**
	 * 将Office文档转换为PDF. 运行该函数需要用到OpenOffice, OpenOffice下载地址为
	 * http://www.openoffice.org/
	 * @param sourceFile
	 *            源文件, 绝对路径. 可以是Office2003-2007全部格式的文档, Office2010的没测试. 包括.doc,
	 *            .docx, .xls, .xlsx, .ppt, .pptx等. 示例: F:\\office\\source.doc
	 * @param destFile
	 *            目标文件. 绝对路径. 示例: F:\\pdf\\dest.pdf
	 * @return 操作成功与否的提示信息. 如果返回 -1, 表示找不到源文件, 或url.properties配置错误; 如果返回 0,
	 *         则表示操作成功; 返回1, 则表示转换失败
	 */
	public int office2PDF(String sourceFile, String destFile) {
		try {
			File inputFile = new File(sourceFile);
			if (!inputFile.exists()) {
				return -1;// 找不到源文件, 则返回-1
			}

			// 如果目标路径不存在, 则新建该路径
			File outputFile = new File(destFile);
			if (!outputFile.getParentFile().exists()) {
				outputFile.getParentFile().mkdirs();
			}

			String OpenOffice_HOME = "C:\\Program Files (x86)\\OpenOffice 4";
			// 如果从文件中读取的URL地址最后一个字符不是 '\'，则添加'\'
			if (OpenOffice_HOME.charAt(OpenOffice_HOME.length() - 1) != '\\') {
				OpenOffice_HOME += "\\";
			}
			// 启动OpenOffice的服务
			String command = OpenOffice_HOME
					+ "program\\soffice.exe -headless -accept=\"socket,host=127.0.0.1,port=8100;urp;\"";
			System.out.println(command);
			Process pro = Runtime.getRuntime().exec(command);
			// connect to an OpenOffice.org instance running on port 8100
			//OpenOfficeConnection connection = new SocketOpenOfficeConnection("127.0.0.1", 8100);
			OpenOfficeConnection connection = new SocketOpenOfficeConnection(8100);
			connection.connect();

			// convert
			DocumentConverter converter = new OpenOfficeDocumentConverter(connection);  
			converter.convert(inputFile, outputFile);

			// close the connection
			connection.disconnect();
			// 关闭OpenOffice服务的进程
			pro.destroy();

			return 0;
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			return -1;
		} catch (ConnectException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}

		return 1;
	}
	
	/**
	 * PDF文件转SWF
	 * @param fileName 如"C:\A.PDF"
	 * @param destPath "C:\"
	 * @return
	 */
	public int ConvertPdfToSwf(String fileName,String destPath){   
		String destName = "",fileExt = "";   
		StringBuffer command = new StringBuffer();   
		fileExt = fileName.split("\\.")[fileName.split("\\.").length-1].toLowerCase();   
		try{   
			File file = new File(fileName);   
			if(!file.exists()){//判断源文件是否存在   
				return -1;   
			}else if(!fileExt.equals("pdf")){//判断文件是否是pdf格式的文件   
				return -2;   
			}   
			else{   
				//D:\SWFTools
				String swftoolsPath = "D:\\tools\\SWFTools\\";//获取pdf转swf工具的路径   
				if(!swftoolsPath.substring(swftoolsPath.length()-1, swftoolsPath.length()).equals("\\")){   
					swftoolsPath = swftoolsPath+"/";    //在目录后加 "\"   
				}   
				if(!destPath.substring(destPath.length()-1, destPath.length()).equals("\\")){   
					destPath = destPath+"/";    //在目录后加 "\"   
				}   
				File destFile = new File(destPath);   
				if(!destFile.exists()){//目标文件路径如果不存在，则创建目录   
					destFile.mkdirs();   
				}   
				destName = file.getName().substring(0, file.getName().length()-4)+".swf";//目标文件名称   
				command.append(swftoolsPath).append("pdf2swf.exe ").append(fileName).append(" -o ").append(destPath.replace("\\", "/")).append(destName).append(" -f -T 9 -G -s poly2bitmap");   
				System.out.println(command);
				Process pro = Runtime.getRuntime().exec(command.toString());   
				BufferedReader buffer = new BufferedReader(new InputStreamReader(pro.getInputStream()));   
				while(buffer.readLine()!=null);   
				int i =0;
				i=pro.exitValue();
				System.out.println(i);
				return i;   
			}   
		}catch (Exception e){   
			e.printStackTrace();   
			return -3;   
		}   
	}  

	public static void main(String[] args) {
		PaperConvertUtil pcu = new PaperConvertUtil("F:/LAI/1212.docx", "test2", "F:/LAI/", ".docx");
		pcu.executeConvert();
	}
	
}
