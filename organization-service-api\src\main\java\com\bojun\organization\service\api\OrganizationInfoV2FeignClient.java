package com.bojun.organization.service.api;

import com.bojun.organization.dto.GetAuthOrganizationV2DTO;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;
import com.bojun.organization.service.api.hystrix.OrganizationInfoV2FeignHystrix;
import com.bojun.page.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * OrganizationInfoFeignClient
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@FeignClient(name="organization-service", fallback = OrganizationInfoV2FeignHystrix.class)
public interface OrganizationInfoV2FeignClient
{
	
    String PREFIX = "OrganizationInfo";

//	/**
//     * 查询机构信息表分页列表
//     */
//    @PostMapping(PREFIX + "/page")
//    public Results<PageData<OrganizationInfoV2DTO>> page(@RequestBody OrganizationInfoV2DTO organizationInfoDTO);

    /**
     * 查询机构信息表列表
     */

    @PostMapping(PREFIX + "/list")
    Results<List<OrganizationInfoV2DTO>> list(@RequestBody OrganizationInfoV2DTO organizationInfoDTO);



    /**
     * 获取机构信息表详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<OrganizationInfoV2DTO> getInfo(@RequestParam("organizationId") Integer organizationId);

    /**
     * 根据机构名字获取机构信息表详细信息
     */
    @GetMapping(PREFIX + "/getInfoByName")
    public Results<OrganizationInfoV2> getInfoByName(@RequestParam("organizationName") String organizationName);
//
//    /**
//     * 新增机构信息表DTO
//     */
//    @PostMapping(PREFIX + "/addDTO")
//    public Results addDTO(@RequestBody OrganizationInfoV2DTO organizationInfoDTO);
//
//    /**
//     * 修改机构信息表DTO
//     */
//    @PostMapping(PREFIX + "/editDTO")
//    public Results editDTO(@RequestBody OrganizationInfoV2DTO organizationInfoDTO);
//
//    /**
//     * 新增机构信息表
//     */
//    @PostMapping(PREFIX + "/add")
//    public Results add(@RequestBody OrganizationInfoV2 organizationInfo);
//
//    /**
//     * 修改机构信息表
//     */
//    @PostMapping(PREFIX + "/edit")
//    public Results edit(@RequestBody OrganizationInfoV2 organizationInfo);
//
//    /**
//     * 删除机构信息表，多个以逗号分隔
//     */
//    @GetMapping(PREFIX + "/removeByIds")
//    public Results removeByIds(@RequestParam("ids") String ids);

    /**
     * 获取有权限(不含半选)的机构列表, 非树形, 主要供后端使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    @PostMapping(PREFIX + "/getFullAuthOrgList")
    Results<List<OrganizationInfoV2DTO>> getFullAuthOrgList(@RequestBody GetAuthOrganizationV2DTO getAuthOrganizationV2DTO);

    /**
     * 获取有权限(含半选)的机构树形列表, 供前端树形列表使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    @PostMapping(PREFIX + "/getAuthOrgTreeList")
    Results<List<OrganizationInfoV2DTO>> getAuthOrgTreeList(@RequestBody GetAuthOrganizationV2DTO getAuthOrganizationV2DTO);
}
