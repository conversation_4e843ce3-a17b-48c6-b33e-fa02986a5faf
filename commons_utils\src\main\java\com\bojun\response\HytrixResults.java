/**
 * 
 */
package com.bojun.response;

import com.alibaba.fastjson.JSON;
import com.bojun.enums.ResponseCodeEnum;

/**
*Model：熔断返回信息
*Description：熔断返回信息
*Author:段德鹏
*created：2020年6月5日
**/
public class HytrixResults {
	
	/**
	 * @Description  请求失败返回
	 * <AUTHOR>
	 * @return
	 * @return String
	 * created：2020年6月5日
	 */
	@SuppressWarnings("rawtypes")
	public static String failBack() {
		Results results = new Results<>(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode(), 
				ResponseCodeEnum.UNREASONABLE_REQUEST.getErrorDescr(), null);
		return JSON.toJSONString(results);
	}
	
	/**
	 * @Description  请求失败返回
	 * <AUTHOR>
	 * @return
	 * @return String
	 * created：2020年6月5日
	 */
	@SuppressWarnings("rawtypes")
	public static String failBack(String msg) {
		Results results = new Results<>(ResponseCodeEnum.UNREASONABLE_REQUEST.getCode(), 
				msg, null);
		return JSON.toJSONString(results);
	}


}
