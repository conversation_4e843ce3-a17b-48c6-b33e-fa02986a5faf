<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.NewsPushRecordMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.NewsPushRecordDTO" id="NewsPushRecordDTOResult">
        <result property="id"    column="id"    />
        <result property="newsId"    column="news_id"    />
        <result property="pushType"    column="push_type"    />
        <result property="pushTime"    column="push_time"    />
        <result property="status"    column="status"    />
        <result property="personTagCode"    column="person_tag_code"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectNewsPushRecord">
    	select
	        id,
	        news_id,
	        push_type,
	        push_time,
	        status,
	        person_tag_code,
	        create_time
		from 
        	t_news_push_record
    </sql>

    <select id="selectNewsPushRecordById" parameterType="int" resultMap="NewsPushRecordDTOResult">
		<include refid="selectNewsPushRecord"/>
		where status != 1 and
		news_id = #{newsId}
    </select>

    <select id="selectNewsPushRecordList" parameterType="com.bojun.sphygmometer.dto.NewsPushRecordDTO" resultMap="NewsPushRecordDTOResult">
        <include refid="selectNewsPushRecord"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="newsId != null "> and news_id = #{newsId}</if>
		<if test="pushType != null "> and push_type = #{pushType}</if>
		<if test="pushTime != null "> and push_time = #{pushTime}</if>
		<if test="status != null "> and status = #{status}</if>
		<if test="personTagCode != null  and personTagCode != ''"> and person_tag_code = #{personTagCode}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>


	<select id="selectNewsInfoByNewsId" parameterType="int" resultType="Map">
		select title, content, cover_image coverImage
		from health_promotion.t_news_info
		where news_id = #{newsId}
	</select>

</mapper>