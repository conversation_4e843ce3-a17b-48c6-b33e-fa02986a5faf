package com.bojun.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.Semaphore;

/**
 * Model：对象工具类
 * Description：对象工具类
 * Author: 潘文泽
 * created：2021年3月11日
 */
public class ObjectUtil {
    static Logger logger = LoggerFactory.getLogger(ObjectUtil.class);

    private ObjectUtil() {
    }

    public static boolean equal(Object obj1, Object obj2) {
        return obj1 != null ? obj1.equals(obj2) : obj2 == null;
    }

    public static boolean notEqual(Object obj1, Object obj2) {
        return !equal(obj1, obj2);
    }

    public static int length(Object obj) {
        if (obj == null) {
            return 0;
        } else if (obj instanceof CharSequence) {
            return ((CharSequence) obj).length();
        } else if (obj instanceof Collection) {
            return ((Collection) obj).size();
        } else if (obj instanceof Map) {
            return ((Map) obj).size();
        } else {
            int count;
            if (obj instanceof Iterator) {
                Iterator<?> iter = (Iterator) obj;
                count = 0;

                while (iter.hasNext()) {
                    ++count;
                    iter.next();
                }
                
                return count;
            } else if (!(obj instanceof Enumeration)) {
                return obj.getClass().isArray() ? Array.getLength(obj) : -1;
            } else {
                Enumeration<?> enumeration = (Enumeration) obj;
                count = 0;

                while (enumeration.hasMoreElements()) {
                    ++count;
                    enumeration.nextElement();
                }

                return count;
            }
        }
    }


    public static boolean isNull(Object obj) {
        return null == obj;
    }

    public static boolean isNotNull(Object obj) {
        return null != obj;
    }

    public static <T> T defaultIfNull(T object, T defaultValue) {
        return null != object ? object : defaultValue;
    }


    public static boolean isValidIfNumber(Object obj) {
        if (obj != null && obj instanceof Number) {
            if (obj instanceof Double) {
                if (((Double) obj).isInfinite() || ((Double) obj).isNaN()) {
                    return false;
                }
            } else if (obj instanceof Float && (((Float) obj).isInfinite() || ((Float) obj).isNaN())) {
                return false;
            }
        }

        return true;
    }

    public static <T extends Comparable<? super T>> int compare(T c1, T c2) {
        return compare(c1, c2, false);
    }

    public static <T extends Comparable<? super T>> int compare(T c1, T c2, boolean nullGreater) {
        if (c1 == c2) {
            return 0;
        } else if (c1 == null) {
            return nullGreater ? 1 : -1;
        } else if (c2 == null) {
            return nullGreater ? -1 : 1;
        } else {
            return c1.compareTo(c2);
        }
    }


    public static boolean isEmpty(Object o) {
        if (o == null) {
            return true;
        } else if (o instanceof Collection) {
            return ((Collection) o).isEmpty();
        } else if (o instanceof Map) {
            return ((Map) o).isEmpty();
        } else if (o instanceof Object[]) {
            return ((Object[]) ((Object[]) o)).length == 0;
        } else {
            return o.toString().length() == 0;
        }
    }

}
