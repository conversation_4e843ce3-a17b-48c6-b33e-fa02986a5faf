package com.bojun.health.promotion.common.dto;

import com.bojun.health.promotion.common.entity.NewsInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 资讯信息表对象 t_news_info
 * 
 * <AUTHOR>
 * @date 2021-06-01 16:08:00
 */
@ApiModel(value = "TNewsInfoDTO对象")
@Data
public class NewsInfoDTO extends NewsInfo
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;
    @ApiModelProperty(value = "栏目集合", example = "")
    private List<UpdateNewsDTO> topicIdList;
    @ApiModelProperty(value = "创建人名称", example = "")
    private String createUserName;
    @ApiModelProperty(value = "人员标签", example = "")
    private String personTag;
    @ApiModelProperty(value = "精准推送 0 ：否 、1 ：是", example = "")
    private Integer accuratePush;
    @ApiModelProperty(value = "推送状态 ：0：立即推送 1：定时推送", example = "")
    private Integer pushType;
    @ApiModelProperty(value = "推送时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date pushTime;
    @ApiModelProperty(value = "发布人", example = "")
    private String publishUserName;
    @ApiModelProperty(value = "发布id", example = "")
    private Integer publishUserId;
    @ApiModelProperty(value = "是否置顶： 1：否 0：是", example = "")
    private Integer isTop;
    @ApiModelProperty(value = "文章在栏目的显示下标", example = "")
    private Integer newsShowIndex;
    @ApiModelProperty(value = "收藏数量", example = "")
    private Integer favoriteNumber;
    @ApiModelProperty(value = "机构名称", example = "")
    private String organizationName;
    @ApiModelProperty(value = "科室名称", example = "")
    private String deptName;
    @ApiModelProperty(value = "用户id", example = "")
    private Integer userId;
    @ApiModelProperty(value = "是否收藏 0：否 1： 是", example = "")
    private Integer isFavorite;
    @ApiModelProperty(value = "收藏 数量", example = "")
    private Integer favoriteNum;
    @ApiModelProperty(value = "栏目名称", example = "")
    private String topicName;
}
