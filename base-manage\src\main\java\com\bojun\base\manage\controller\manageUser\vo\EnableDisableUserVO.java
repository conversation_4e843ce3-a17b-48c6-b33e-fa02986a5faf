/**
 * 
 */
package com.bojun.base.manage.controller.manageUser.vo;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：启用禁用用户状态
 * Description：启用禁用用户状态
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "启用禁用用户状态信息", description = "启用禁用用户状态传入参数")
public class EnableDisableUserVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;


	@NotNull(message = "用户Id不能为空")
	@ApiModelProperty(value = "用户Id", required = true, example = "1")
	private Integer userId;


	@NotNull(message = "状态类型不能为空")
	@ApiModelProperty(value = "状态0:停用 1:启用", required = true, example = "1")
	private Integer status;
	
	@ApiModelProperty(value = "登录身份令牌（token）", required = false, example = "1")
	private String token; 


	
	
	
	public String getToken() {
		return token;
	}


	public void setToken(String token) {
		this.token = token;
	}


	public Integer getUserId() {
		return userId;
	}


	public void setUserId(Integer userId) {
		this.userId = userId;
	}


	public Integer getStatus() {
		return status;
	}


	public void setStatus(Integer status) {
		this.status = status;
	}


	

}
