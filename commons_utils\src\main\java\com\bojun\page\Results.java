package com.bojun.page;

import com.bojun.enums.ResponseCodeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Collection;

/**
 * 统一API响应结果封装
 *
 * <AUTHOR>
 */
@Data
@ToString
@ApiModel(description = "返回信息")
public class Results<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "状态码", required = true)
    private int code;
    @ApiModelProperty(value = "承载数据")
    private T data;
    @ApiModelProperty(value = "返回消息", required = true)
    private String msg;

    public Results() {}

    private Results(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    /**
     * 返回R
     *
     * @param data 数据
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> data(T data) {
        return data(data, ResponseCodeEnum.SUCCESS_REQUEST.getErrorDescr());
    }

    /**
     * 返回R
     *
     * @param data 数据
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> list(T data) {
        if (null == data) {
            return new Results<>(ResponseCodeEnum.NO_DATA.getCode(), null, ResponseCodeEnum.NO_DATA.getErrorDescr());
        }
        return data(data, ResponseCodeEnum.SUCCESS_REQUEST.getErrorDescr());
    }

    /**
     * 返回R
     *
     * @param data 数据
     * @param msg  消息
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> data(T data, String msg) {
        return data(ResponseCodeEnum.SUCCESS_REQUEST.getCode(), data, msg);
    }

    /**
     * 返回R
     *
     * @param code 状态码
     * @param data 数据
     * @param msg  消息
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> data(int code, T data, String msg) {
        if (null != data) {
            if (data instanceof PageData) {
                PageData pageData = (PageData) data;
                if (pageData.getTotalCount() == 0L || pageData.getDataList() == null || (pageData.getDataList() != null && pageData.getDataList().size() == 0)) {
                    return new Results<>(ResponseCodeEnum.NO_DATA.getCode(), data, ResponseCodeEnum.NO_DATA.getErrorDescr());
                }
            } else if (data instanceof Collection) {
                Collection collection = (Collection) data;
                if (collection.size() == 0) return new Results<>(ResponseCodeEnum.NO_DATA.getCode(), data, ResponseCodeEnum.NO_DATA.getErrorDescr());
            }
        }
        return new Results<>(code, data, msg);
    }

    /**
     * 返回R
     *
     * @param <T> T 泛型标记
     * @return Results
     */
    public static <T> Results<T> success() {
        return success(ResponseCodeEnum.SUCCESS_REQUEST.getErrorDescr());
    }

    /**
     * 返回R
     *
     * @param msg 消息
     * @param <T> T 泛型标记
     * @return Results
     */
    public static <T> Results<T> success(String msg) {
        return new Results<>(ResponseCodeEnum.SUCCESS_REQUEST.getCode(), null, msg);
    }

    /**
     * 返回R
     *
     * @param resultCode 业务代码
     * @param msg        消息
     * @param <T>        T 泛型标记
     * @return Results
     */
    public static <T> Results<T> success(int resultCode, String msg) {
        return new Results<>(resultCode, null, msg);
    }

    /**
     * 返回R
     *
     * @param <T> T 泛型标记
     * @return Results
     */
    public static <T> Results<T> fail() {
        return fail(ResponseCodeEnum.FAIL_REQUEST.getErrorDescr());
    }

    /**
     * 返回R
     *
     * @param msg 消息
     * @param <T> T 泛型标记
     * @return Results
     */
    public static <T> Results<T> fail(String msg) {
        return new Results<>(ResponseCodeEnum.FAIL_REQUEST.getCode(), null, msg);
    }


    /**
     * 返回R
     *
     * @param code 状态码
     * @param msg  消息
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> fail(int code, String msg) {
        return new Results<>(code, null, msg);
    }

    /**
     * 返回R
     *
     * @param opNum 增删改的操作值
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> opResult(int opNum) {
        if (opNum > 0) return success();
        return fail();
    }

    /**
     * 返回R
     *
     * @param result 增删改的操作值
     * @param <T>  T 泛型标记
     * @return Results
     */
    public static <T> Results<T> opResult(boolean result) {
        if (result) return success();
        return fail();
    }
}
