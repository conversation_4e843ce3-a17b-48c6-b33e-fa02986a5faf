package com.bojun.push;

import com.bojun.utils.PropertiesUtils;
import com.bojun.utils.StringUtil;
import com.gexin.fastjson.JSONObject;
import com.gexin.rp.sdk.base.IBatch;
import com.gexin.rp.sdk.base.IPushResult;
import com.gexin.rp.sdk.base.impl.AppMessage;
import com.gexin.rp.sdk.base.impl.SingleMessage;
import com.gexin.rp.sdk.base.impl.Target;
import com.gexin.rp.sdk.exceptions.PushAppException;
import com.gexin.rp.sdk.exceptions.RequestException;
import com.gexin.rp.sdk.http.IGtPush;
import com.gexin.rp.sdk.template.AbstractTemplate;
import com.gexin.rp.sdk.template.LinkTemplate;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description // 极光推送工具类
 * @Date 2020/11/13 15:34 
 * @Param 
 * @return 
**/
public class GeTuiPushUtils {

    public static String appID = PropertiesUtils.getProperty("config.properties", "getui.AppID");

    public static String appKey = PropertiesUtils.getProperty("config.properties", "getui.AppKey");

    public static String AppSecret = PropertiesUtils.getProperty("config.properties", "getui.AppSecret");

    public static String masterSecret = PropertiesUtils.getProperty("config.properties", "getui.MasterSecret");

/*    public static String appID = "kHhEbftn4S9KfI9Ok5RhV9";

    public static String appKey = "fI1vrFlRJQA9OD6gKdam35";

    public static String AppSecret = "qJSTcyaKle8O3hZkjkS2g5";

    public static String masterSecret = "RwZotoiGJP7nypQ88FjMYA";*/

    public static IGtPush push = new IGtPush(appKey, masterSecret);


    public static void main(String[] args) throws ParseException {
//        pushToSingle("94f06aef8cb820433113ce8f92a4a536","这是title","这是text","{'noticeId':'1','msgType':'1'}");

        pushToSingleTransmissionTemplate("94f06aef8cb820433113ce8f92a4a536","{'noticeId':'1','msgType':'0'}");
    }

    public static IPushResult pushToSingleTransmissionTemplate(String cid,String param){
        //推送透传消息
        AbstractTemplate template = PushTemplate.getTransmissionTemplate(param);
        return setPush(template,cid);
    }

    /**
     * 根据Cid单推消息
     * @param cid
     * @param title
     * @param content
     * @param param
     */
    public static IPushResult pushToSingle(String cid,String title,String content,String param) {
        if(null != param &&!param.equals("")){
            //推送透传消息
            JSONObject jsonObject = JSONObject.parseObject(param);
            if(jsonObject.containsKey("noticeId")){
                jsonObject.remove("noticeId");
            }
            AbstractTemplate template = PushTemplate.getTransmissionTemplate(jsonObject.toString());
            setPush(template,cid);
        }
        AbstractTemplate template = PushTemplate.getNotificationTemplate(title,content,param); //通知模板(点击后续行为: 支持打开应用、发送透传内容、打开应用同时接收到透传 这三种行为)
        // 单推消息类型
        IPushResult ret = setPush(template,cid);
        return ret;
    }

    private static IPushResult setPush(AbstractTemplate template,String cid){
        // 单推消息类型
        SingleMessage message =  getSingleMessage(template);
        Target target = new Target();
        target.setAppId(appID);
        target.setClientId(cid);
        IPushResult ret = null;
        try {
            ret  = push.pushMessageToSingle(message, target);
        }catch (RequestException e){
            e.printStackTrace();
            ret = push.pushMessageToSingle(message, target, e.getRequestId());
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
        return ret;
    }


    /**
     * 批量单推
     * <p>
     * 当单推任务较多时，推荐使用该接口，可以减少与服务端的交互次数。
     */
    public static IPushResult pushToSingleBatch(String cids,String title,String content,String param) {
        IBatch batch = push.getBatch();
        IPushResult ret = null;
        try {
            String [] ids = cids.split(",");
            for(int i=0;i< ids.length;i++){
                constructClientTransMsg(ids[i],batch,title,content,param);
            }
            ret = batch.submit();
        } catch (Exception e) {
            e.printStackTrace();
            try {
                ret = batch.retry();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
        } else {
            System.out.println("服务器响应异常");
        }
        return ret;
    }


    private static SingleMessage getSingleMessage(AbstractTemplate template) {
        SingleMessage message = new SingleMessage();
        message.setData(template);
        // 设置消息离线，并设置离线时间
        message.setOffline(true);
        // 离线有效时间，单位为毫秒，可选
        message.setOfflineExpireTime(24 * 3600 * 1000);
        // 判断客户端是否wifi环境下推送。1为仅在wifi环境下推送，0为不限制网络环境，默认不限
        message.setPushNetWorkType(0);
        // 厂商下发策略；1: 个推通道优先，在线经个推通道下发，离线经厂商下发(默认);2: 在离线只经厂商下发;3: 在离线只经个推通道下发;4: 优先经厂商下发，失败后经个推通道下发;
        message.setStrategyJson("{\"default\":4,\"ios\":4,\"st\":4}");
        return message;
    }


    private static void constructClientTransMsg(String cid, IBatch batch,String title,String content,String param) throws Exception {
        AbstractTemplate template = PushTemplate.getNotificationTemplate(title,content,param);
        SingleMessage message = getSingleMessage(template);

        // 设置推送目标，填入appid和clientId
        Target target = new Target();
        target.setAppId(appID);
        target.setClientId(cid);
        batch.add(message, target);
    }


    /**
     * 对指定应用群推消息
     * @return
     */
    public static String pushMessageToApp(String cids,String title,String content,String param,String pushTime) throws ParseException {
        AbstractTemplate template = PushTemplate.getNotificationTemplate(title,content,param);
        AppMessage message = new AppMessage();
        message.setData(template);
        message.setOffline(true);
        message.setOfflineExpireTime(24 * 1000 * 3600);  //离线有效时间，单位为毫秒，可选
        // 厂商下发策略；1: 个推通道优先，在线经个推通道下发，离线经厂商下发(默认);2: 在离线只经厂商下发;3: 在离线只经个推通道下发;4: 优先经厂商下发，失败后经个推通道下发;
        message.setStrategyJson("{\"default\":4,\"ios\":4,\"st\":4}");
        if(!StringUtil.isEmpty(pushTime)){
            message.setPushTime(pushTime); //限制条件参见官网（http://docs.getui.com/getui/server/java/push/）定时推送有关说明
        }
        List<String> appIdList = new ArrayList<>();
        appIdList.add(appID);
        message.setAppIdList(appIdList);
        IPushResult ret = null;
        try {
            ret = push.pushMessageToApp(message);
        } catch (PushAppException e) {
            e.printStackTrace();
        }
        if (ret != null) {
            System.out.println(ret.getResponse().toString());
            return null;
        } else {
            System.out.println("服务器响应异常");
            return null;
        }
    }

}

