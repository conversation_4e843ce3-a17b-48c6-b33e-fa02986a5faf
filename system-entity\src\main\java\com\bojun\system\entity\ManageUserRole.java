 package com.bojun.system.entity;

import java.io.Serializable;

/**
 * 
*Model：菜单表
*Description：菜单表实体类
*Author:刘修纬
*created：2020年3月3日
 */
public class ManageUserRole implements Serializable {

   private static final long serialVersionUID = -1344026328907379635L;
      private Integer id; // 
      private Integer userId; // 用户id
      private String roleId; // 角色id
      private String systemId; // 角色关联的系统id
    
      
    
    public Integer getId()
    {
        return id;
    }
    
    public void setId(Integer id)
    {
        this.id = id;
    }   	
      
    
    public Integer getUserId()
    {
        return userId;
    }
    
    public void setUserId(Integer userId)
    {
        this.userId = userId;
    }   	
      
    
    public String getRoleId()
    {
        return roleId;
    }
    
    public void setRoleId(String roleId)
    {
        this.roleId = roleId;
    }   	
      
    
    public String getSystemId()
    {
        return systemId;
    }
    
    public void setSystemId(String systemId)
    {
        this.systemId = systemId;
    }   	
    

}
