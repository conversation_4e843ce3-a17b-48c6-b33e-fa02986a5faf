package com.bojun.organization.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.contants.Contants;
import com.bojun.organization.dto.GetAuthOrganizationV2DTO;
import com.bojun.organization.dto.OrganizationInfoV2DTO;
import com.bojun.organization.entity.OrganizationInfoV2;
import com.bojun.organization.mapper.OrganizationInfoV2Mapper;
import com.bojun.organization.service.OrganizationInfoV2Service;
import com.bojun.utils.Convert;
import com.bojun.utils.DateUtils;
import com.bojun.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * OrganizationInfoService业务层处理
 * 
 * <AUTHOR>
 * @date 2021-05-07 11:14:18
 */
@Slf4j
@Service
public class OrganizationInfoV2ServiceImpl extends ServiceImpl<OrganizationInfoV2Mapper, OrganizationInfoV2> implements OrganizationInfoV2Service
{

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 查询机构信息表
     * 
     * @param organizationId 机构信息表ID
     * @return 机构信息表
     */
    @Override
    public OrganizationInfoV2DTO selectOrganizationInfoById(Integer organizationId)
    {
        return this.baseMapper.selectOrganizationInfoById(organizationId);
    }

    /**
     * 查询机构信息表列表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 机构信息表集合
     */
    @Override
    public List<OrganizationInfoV2DTO> selectOrganizationInfoList(OrganizationInfoV2DTO organizationInfoDTO)
    {
        return this.baseMapper.selectOrganizationInfoList(organizationInfoDTO);
    }

    /**
     * 新增机构信息表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 结果
     */
    @Override
    public int insertOrganizationInfo(OrganizationInfoV2DTO organizationInfoDTO)
    {
        organizationInfoDTO.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(organizationInfoDTO);
    }

    /**
     * 修改机构信息表
     * 
     * @param organizationInfoDTO 机构信息表
     * @return 结果
     */
    @Override
    public int updateOrganizationInfo(OrganizationInfoV2DTO organizationInfoDTO)
    {
        return this.baseMapper.updateById(organizationInfoDTO);
    }
    
    /**
     * 新增机构信息表
     * 
     * @param organizationInfo 机构信息表
     * @return 结果
     */
    @Override
    public int insertOrganizationInfo(OrganizationInfoV2 organizationInfo)
    {
        organizationInfo.setCreateTime(DateUtils.getNowDate());
        return this.baseMapper.insert(organizationInfo);
    }

    /**
     * 修改机构信息表
     * 
     * @param organizationInfo 机构信息表
     * @return 结果
     */
    @Override
    public int updateOrganizationInfo(OrganizationInfoV2 organizationInfo)
    {
        return this.baseMapper.updateById(organizationInfo);
    }

    /**
     * 删除机构信息表对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteOrganizationInfoByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除机构信息表信息
     * 
     * @param organizationId 机构信息表ID
     * @return 结果
     */
    @Override
    public int deleteOrganizationInfoById(Integer organizationId)
    {
        return this.removeById(organizationId) ? 1 : 0;
    }

    /**
     * 获取下级机构列表
     * @param organizationInfoDTO
     * @return
     */
    public List<OrganizationInfoV2DTO> getChildrenOrgListByRoleId(OrganizationInfoV2DTO organizationInfoDTO) {
        return this.baseMapper.getChildrenOrgListByRoleId(organizationInfoDTO);
    }

    /**
     * 1. 传入roleId, 将获得相关角色的机构数据
     * 2. 什么都不传, 查全部机构
     * 3. organizationId/parentIds有值,则会查其下级机构数据
     * @param organizationInfoDTO
     * @return 返回平级机构列表
     */
    public List<OrganizationInfoV2DTO> getAuthOrganizationList(OrganizationInfoV2DTO organizationInfoDTO) {
        //id为空查全部
        if (organizationInfoDTO != null && organizationInfoDTO.getOrganizationId() == null) {
            return this.getChildrenOrgListByRoleId(organizationInfoDTO);
        }
        List<Integer> parentIdList = new ArrayList<Integer>();
        parentIdList.add(organizationInfoDTO.getOrganizationId());
        List<OrganizationInfoV2DTO> allOrgList = new ArrayList();
        allOrgList.add(organizationInfoDTO);
        getAuthOrganizationChildren(allOrgList, parentIdList, organizationInfoDTO.getRoleId());
        return allOrgList;

    }

    public void getAuthOrganizationChildren(List<OrganizationInfoV2DTO> allOrgList, List<Integer> parentIdList, String roleId) {
        OrganizationInfoV2DTO oid = new OrganizationInfoV2DTO();
        oid.setParentIds(parentIdList);
        oid.setRoleId(roleId);
        List<OrganizationInfoV2DTO> list = this.getChildrenOrgListByRoleId(oid);
        if (null != list && !list.isEmpty()) {
            allOrgList.addAll(list);
            parentIdList.clear();
            list.forEach(organizationDTO -> {
                parentIdList.add(organizationDTO.getOrganizationId());
            });
            getAuthOrganizationChildren(allOrgList, parentIdList, roleId);
        }
    }

    /**
     * 获取有权限(不含半选)的机构列表, 非树形, 主要供后端使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    @Override
    public List<OrganizationInfoV2DTO> getFullAuthOrgList(GetAuthOrganizationV2DTO getAuthOrganizationV2DTO) {
        Integer authType = getAuthOrganizationV2DTO.getAuthType();
        Integer dataPermissions = getAuthOrganizationV2DTO.getDataPermissions();
        String organizationClassCode = getAuthOrganizationV2DTO.getOrganizationClassCode();
        Integer orgId = getAuthOrganizationV2DTO.getParentId();
        Integer userOrganizationId = getAuthOrganizationV2DTO.getOrganizationId();
        String roleId = getAuthOrganizationV2DTO.getRoleId();
        List<OrganizationInfoV2DTO> allAuthList = new ArrayList<>();
        List<OrganizationInfoV2DTO> authList = new ArrayList<>();
        String redisKey = StringUtils.EMPTY;
        if (authType == null) {
            return new ArrayList<>();
        }
        if (authType == 0) {//超管权限
            redisKey = Contants.RK_SUPERADMIN_ORG_LIST;
            if (redisUtil.hasKey(redisKey)) {
                allAuthList = (List<OrganizationInfoV2DTO>) redisUtil.get(redisKey);
            } else {
                //传Null查全部
                allAuthList = this.getAuthOrganizationList(new OrganizationInfoV2DTO());
                //超管默认有所有机构的权限
                for (OrganizationInfoV2DTO organizationInfoV2DTO : allAuthList) {
                    organizationInfoV2DTO.setHasAuth(1);
                }
                redisUtil.set(redisKey, allAuthList, 24*3600);
            }
            //按机构分类筛选出机构
            List<OrganizationInfoV2DTO> occList = getOrgListByClassCode(organizationClassCode, allAuthList);
            //超管不存在所谓全选半选
            if (orgId != null) {
                this.getPartOrgList(authList, occList, orgId);
            } else {
                authList = occList;
            }
        } else if (authType == 1) {//非超管权限
            if (dataPermissions == null) {
                return new ArrayList<>();
            }
            if (dataPermissions == 1) {//角色权限
                redisKey = MessageFormat.format(Contants.RK_ROLE_ORG_LIST, roleId);
                if (redisUtil.hasKey(redisKey)) {
                    //有缓存取缓存
                    allAuthList = (List<OrganizationInfoV2DTO>) redisUtil.get(redisKey);
                } else {
                    //无缓存取库, 再设置至缓存
                    OrganizationInfoV2DTO params = new OrganizationInfoV2DTO();
                    params.setRoleId(roleId);
                    allAuthList = this.getAuthOrganizationList(params);
                    redisUtil.set(redisKey, allAuthList, 24*3600);
                }
                //按机构分类筛选出机构
                List<OrganizationInfoV2DTO> occList = getOrgListByClassCode(organizationClassCode, allAuthList);
                //把全选/半选的权限机构都查出来
                if (orgId != null) {
                    this.getPartOrgList(authList, occList, orgId);
                } else {
                    authList = occList;
                }
                //过滤出全选的权限机构
                List<OrganizationInfoV2DTO> fullAuthList = new ArrayList<>();
                for (OrganizationInfoV2DTO organizationInfoV2DTO : authList) {
                    if (organizationInfoV2DTO.getHasAuth()!= null &&
                            organizationInfoV2DTO.getHasAuth().equals(1)) {
                        fullAuthList.add(organizationInfoV2DTO);
                    }
                }
                authList = fullAuthList;
            } else if (dataPermissions == 2) {//机构权限, 只有自身的机构权限
                if (orgId == null || userOrganizationId.intValue() == orgId.intValue()) {
                    OrganizationInfoV2DTO org = this.selectOrganizationInfoById(userOrganizationId);
                    authList.add(org);
                }
            }
        }
        if (authList == null) {
            return new ArrayList<>();
        }
        return authList;
    }

    private List<OrganizationInfoV2DTO> getOrgListByClassCode(String organizationClassCode, List<OrganizationInfoV2DTO> allAuthList) {
        List<OrganizationInfoV2DTO> occList = new ArrayList<>();
        if (StringUtils.isNotBlank(organizationClassCode)) {
            for (OrganizationInfoV2DTO organizationInfoV2DTO : allAuthList) {
                if (organizationInfoV2DTO.getOrganizationClassCode().equals(organizationClassCode)) {
                    occList.add(organizationInfoV2DTO);
                }
            }
        } else {
            occList = allAuthList;
        }
        return occList;
    }

    /**
     * 获取有权限(含半选)的机构树形列表, 供前端树形列表使用
     * @param getAuthOrganizationV2DTO
     * @return
     */
    @Override
    public List<OrganizationInfoV2DTO> getAuthOrgTreeList(GetAuthOrganizationV2DTO getAuthOrganizationV2DTO) {
        Integer authType = getAuthOrganizationV2DTO.getAuthType();
        Integer dataPermissions = getAuthOrganizationV2DTO.getDataPermissions();
        String organizationClassCode = getAuthOrganizationV2DTO.getOrganizationClassCode();
        Integer orgId = getAuthOrganizationV2DTO.getParentId();
        Integer userOrganizationId = getAuthOrganizationV2DTO.getOrganizationId();
        String roleId = getAuthOrganizationV2DTO.getRoleId();
        List<OrganizationInfoV2DTO> allAuthList = new ArrayList<>();
        List<OrganizationInfoV2DTO> authList = new ArrayList<>();
        String redisListKey = StringUtils.EMPTY;
        String redisTreeKey = StringUtils.EMPTY;
        String redisClassCodeTreeKey = StringUtils.EMPTY;
        if (authType == null) {
            return new ArrayList<>();
        }
        if (authType == 0) {//超管权限
            redisTreeKey = Contants.RK_SUPERADMIN_ORG_TREE_LIST;
            //如果有机构分类的话，则返回机构分类的机构集合缓存
            if (StringUtils.isNotBlank(organizationClassCode)) {
                redisClassCodeTreeKey = MessageFormat.format(Contants.RK_SUPERADMIN_CLASSCODE_ORG_TREE_LIST, organizationClassCode);
                if (this.redisUtil.hasKey(redisClassCodeTreeKey)) {
                    authList = (List<OrganizationInfoV2DTO>) this.redisUtil.get(redisClassCodeTreeKey);
                    return authList;
                }
            } else {
                //先判断有无树缓存
                if (this.redisUtil.hasKey(redisTreeKey)) {
                    authList = (List<OrganizationInfoV2DTO>) this.redisUtil.get(redisTreeKey);
                    return authList;
                }
            }
            redisListKey = Contants.RK_SUPERADMIN_ORG_LIST;
            //无则看Redis有无列表缓存(非树)
            if (redisUtil.hasKey(redisListKey)) {
                allAuthList = (List<OrganizationInfoV2DTO>) redisUtil.get(redisListKey);
            } else {
                //传Null查全部
                allAuthList = this.getAuthOrganizationList(new OrganizationInfoV2DTO());
                //超管默认有所有机构的权限
                for (OrganizationInfoV2DTO organizationInfoV2DTO : allAuthList) {
                    organizationInfoV2DTO.setHasAuth(1);
                }
                redisUtil.set(redisListKey, allAuthList, 24*3600);
            }
            //按机构分类筛选出机构
            List<OrganizationInfoV2DTO> occList = getOrgListByClassCode(organizationClassCode, allAuthList);
            //构建成树
            authList = this.buildOrgTree(occList, null, 1);
            if (StringUtils.isBlank(organizationClassCode)) {
                //将超管的树形权限数据放入redis，不分机构类别
                this.redisUtil.set(redisTreeKey, authList, 24*3600);
            } else {
                //将超管分机构类别的机构集合放入redis
                this.redisUtil.set(redisClassCodeTreeKey, authList, 24*3600);
            }
        } else if (authType == 1) {//非超管权限
            if (dataPermissions == null) {
                return new ArrayList<>();
            }
            if (dataPermissions == 1) {//角色权限
                redisTreeKey = MessageFormat.format(Contants.RK_ROLE_ORG_TREE_LIST, roleId);
                //如果有机构分类的话，则返回机构分类的机构集合缓存
                if (StringUtils.isNotBlank(organizationClassCode)) {
                    redisClassCodeTreeKey = MessageFormat.format(Contants.RK_ROLE_CLASSCODE_ORG_TREE_LIST, roleId, organizationClassCode);
                    if (this.redisUtil.hasKey(redisClassCodeTreeKey)) {
                        authList = (List<OrganizationInfoV2DTO>) this.redisUtil.get(redisClassCodeTreeKey);
                        return authList;
                    }
                } else {
                    //先判断有无树缓存
                    if (this.redisUtil.hasKey(redisTreeKey)) {
                        authList = (List<OrganizationInfoV2DTO>) this.redisUtil.get(redisTreeKey);
                        return authList;
                    }
                }
                redisListKey = MessageFormat.format(Contants.RK_ROLE_ORG_LIST, roleId);
                //无则看Redis有无列表缓存(非树)
                if (redisUtil.hasKey(redisListKey)) {
                    allAuthList = (List<OrganizationInfoV2DTO>) redisUtil.get(redisListKey);
                } else {
                    OrganizationInfoV2DTO params = new OrganizationInfoV2DTO();
                    params.setRoleId(roleId);
                    allAuthList = this.getAuthOrganizationList(params);
                    redisUtil.set(redisListKey, allAuthList, 24*3600);
                }
                //按机构分类筛选出机构
                List<OrganizationInfoV2DTO> occList = getOrgListByClassCode(organizationClassCode, allAuthList);
                //层级从1开始
                authList = this.buildOrgTree(occList, null, 1);
                if (StringUtils.isBlank(organizationClassCode)) {
                    //将该角色的树形权限数据放入redis，不分机构类别
                    this.redisUtil.set(redisTreeKey, authList, 24*3600);
                } else {
                    //将该角色分机构类别的机构集合放入redis
                    this.redisUtil.set(redisClassCodeTreeKey, authList, 24*3600);
                }
            } else if (dataPermissions == 2) {//机构权限, 只有自身的机构权限
                if (orgId == null || userOrganizationId.intValue() == orgId.intValue()) {
                    OrganizationInfoV2DTO org = this.selectOrganizationInfoById(userOrganizationId);
                    authList.add(org);
                }
            }
        }
        return authList;
    }

    /**
     * 通过递归方式取得部分机构
     * @return
     */
    private void getPartOrgList(List<OrganizationInfoV2DTO> partAuthOrgList, List<OrganizationInfoV2DTO> orgList, Integer orgId) {
        //添加本身机构
        orgList.forEach(org -> {
            if (org.getOrganizationId().equals(orgId)) {
                partAuthOrgList.add(org);
            }
        });
        //获取其子机构
        orgList.forEach(org -> {
            if ( org.getParentId() != null && org.getParentId().equals(orgId) ) {
                partAuthOrgList.add(org);
                this.getPartOrgList(partAuthOrgList, orgList, org.getOrganizationId());
            }
        });
    }

    /**
     * 通过递归方式将机构集合组成树
     * @param orgList
     * @param parentId
     * @return
     */
    private List<OrganizationInfoV2DTO> buildOrgTree(List<OrganizationInfoV2DTO> orgList, Integer parentId, Integer hierarchy) {
        List<OrganizationInfoV2DTO> treeList = new ArrayList<>();
        for (OrganizationInfoV2DTO org : orgList) {
            // 先找出最大机构，在找对应机构的下级机构
            if ((org.getParentId() == null && parentId == null)
                    || (org.getParentId() != null && org.getParentId().equals(parentId))) {
                org.setHierarchy(hierarchy);
                org.setChildren(this.buildOrgTree(orgList, org.getOrganizationId(), hierarchy+1));
                treeList.add(org);
            }
        }
        return treeList;
    }
}
