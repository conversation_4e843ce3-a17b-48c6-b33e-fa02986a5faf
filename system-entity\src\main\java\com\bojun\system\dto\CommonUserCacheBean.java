package com.bojun.system.dto;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/**
*Model：模块名称
*Description：通用用户信息缓存对象
*Author: 肖泽权
*created：2020年9月23日
*/
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class CommonUserCacheBean implements Serializable{

	private static final long serialVersionUID = -3246612560138129713L;

	private Integer userId; //用户Id
	private String accountNo; // 登录账号
	private Integer organizationId; // 机构id
	private String organizationName; // 机构名称
	private String roleId;// 角色ID
	private String workNumber; // 工号
	private Integer deptId;// 部门id
	private String deptName;// 部门名称
	private String deptCode;// 部门code
	private String realName; // 姓名
	private String idNo; // 身份证号
	private Integer authType; // 权限类型（0：超级管理员，1：普通管理员）
	private Integer userType; // 用户类型 1：医疗机构人员 2：养老机构人员 3：监管人员 4：其他
	private String passwords; // 密码
	private String salt; // 密码盐
	private Integer status; // 0:停用 1:启用
	private String token; // 登录身份令牌（token）
	private String mobile; // 手机号
	private Integer dataPermissions;// 数据权限 1: 角色机构 2：账号机构
//	private String rongToken;
//	private String doctorId;
//	private Integer appUserId;
	//机器编码-一体机
	private String machineId;

	private Date createTime;
	private String  headPortrait;//头像
}

