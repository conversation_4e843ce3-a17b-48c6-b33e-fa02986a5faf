package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.MessageNotificationSystemV2Mapper;
import com.bojun.base.system.service.MessageNotificationSystemV2Service;
import com.bojun.system.dto.MessageNotificationSystemV2DTO;
import com.bojun.system.entity.MessageNotificationSystemV2;
import com.bojun.utils.Convert;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * MessageNotificationSystemV2Service业务层处理
 * 
 * <AUTHOR>
 * @date 2021-07-22 16:11:35
 */
@Service
public class MessageNotificationSystemV2ServiceImpl extends ServiceImpl<MessageNotificationSystemV2Mapper, MessageNotificationSystemV2> implements MessageNotificationSystemV2Service {

    /**
     * 查询消息通知关联产品表
     * 
     * @param id 消息通知关联产品表ID
     * @return 消息通知关联产品表
     */
    @Override
    public MessageNotificationSystemV2DTO selectMessageNotificationSystemV2ById(Integer id)
    {
        return this.baseMapper.selectMessageNotificationSystemV2ById(id);
    }

    /**
     * 查询消息通知关联产品表列表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 消息通知关联产品表集合
     */
    @Override
    public List<MessageNotificationSystemV2DTO> selectMessageNotificationSystemV2List(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO)
    {
        return this.baseMapper.selectMessageNotificationSystemV2List(messageNotificationSystemV2DTO);
    }

    /**
     * 新增消息通知关联产品表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 结果
     */
    @Override
    public int insertMessageNotificationSystemV2(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO)
    {
        return this.baseMapper.insert(messageNotificationSystemV2DTO);
    }

    /**
     * 修改消息通知关联产品表
     * 
     * @param messageNotificationSystemV2DTO 消息通知关联产品表
     * @return 结果
     */
    @Override
    public int updateMessageNotificationSystemV2(MessageNotificationSystemV2DTO messageNotificationSystemV2DTO)
    {
        return this.baseMapper.updateById(messageNotificationSystemV2DTO);
    }
    
    /**
     * 新增消息通知关联产品表
     * 
     * @param messageNotificationSystemV2 消息通知关联产品表
     * @return 结果
     */
    @Override
    public int insertMessageNotificationSystemV2(MessageNotificationSystemV2 messageNotificationSystemV2)
    {
        return this.baseMapper.insert(messageNotificationSystemV2);
    }

    /**
     * 修改消息通知关联产品表
     * 
     * @param messageNotificationSystemV2 消息通知关联产品表
     * @return 结果
     */
    @Override
    public int updateMessageNotificationSystemV2(MessageNotificationSystemV2 messageNotificationSystemV2)
    {
        return this.baseMapper.updateById(messageNotificationSystemV2);
    }

    /**
     * 删除消息通知关联产品表对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationSystemV2ByIds(String ids)
    {
        return this.removeByIds(Arrays.asList(Convert.toStrArray(ids))) ? 1 : 0;
    }

    /**
     * 删除消息通知关联产品表信息
     * 
     * @param id 消息通知关联产品表ID
     * @return 结果
     */
    @Override
    public int deleteMessageNotificationSystemV2ById(Integer id)
    {
        return this.removeById(id) ? 1 : 0;
    }
}
