package com.bojun.encrypt.comms;

/**
 * CreateToken
 * <AUTHOR>
 */

public class CreateToken {
	
	/**
	 * token生成.
	 * @param id 用户id
	 * @param name 用户名
	 * @param machine 机器信息
	 * @param key 指定的键值
	 */
	public static String getEncryptionToken(String id, String name, String machine, String key) {
		String token = MD5Util.MD5Encrypt(id + name + machine + MD5Util.MD5Encrypt(key));
		return token;
	}
}
