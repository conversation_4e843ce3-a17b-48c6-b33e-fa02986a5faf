package com.bojun.base.manage.controller.log.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 *Model：
 *Description：操作日志
 *Author: 黄卫平
 *created：2020年4月23日
 */

@ApiModel
public class OperationLogParamVO {

    @ApiModelProperty(value = "查询关键字（IP、用户名、操作内容）")
    private String searchKey;

    @ApiModelProperty(value = "操作类型（ 1：新增  2：修改  3：删除 ，4：登录、5：登出）")
    private Integer operationType;

    @ApiModelProperty(value = "日志类型（1、登录/登出日志 2、操作日志 3、异常日志）", required =  true)
    private Integer type;

    @ApiModelProperty(value = "每页条数")
    private Integer everyPage;

    @ApiModelProperty(value = "页码")
    private Integer pageNum;

    @ApiModelProperty(value = "日志开始时间（2020-01-01）")
    private String beginTime;

    @ApiModelProperty(value = "日志结束时间（2020-01-01）")
    private String endTime;

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getEveryPage() {
        return everyPage;
    }

    public void setEveryPage(Integer everyPage) {
        this.everyPage = everyPage;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }
}
