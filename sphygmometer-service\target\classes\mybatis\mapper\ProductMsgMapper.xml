<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.ProductMsgMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.ProductMsgDTO" id="ProductMsgDTOResult">
        <result property="id"    column="id"    />
        <result property="msgCode"    column="msg_code"    />
        <result property="msgType"    column="msg_type"    />
        <result property="pushProduct"    column="push_product"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectProductMsg">
    	select
	        id,
	        msg_code,
	        msg_type,
	        push_product,
	        title,
	        content,
	        send_time,
	        create_time,
	        create_user_id,
	        create_user_name,
	        update_time,
	        update_user_id,
	        update_user_name,
	        is_delete
		from 
        	t_product_msg
    </sql>

    <select id="selectProductMsgById" parameterType="int" resultMap="ProductMsgDTOResult">
		<include refid="selectProductMsg"/>
		where 
        	id = #{id}
    </select>

    <select id="selectProductMsgList" parameterType="com.bojun.sphygmometer.dto.ProductMsgDTO" resultMap="ProductMsgDTOResult">
        <include refid="selectProductMsg"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="msgType != null "> and msg_type = #{msgType}</if>
		<if test="pushProduct != null "> and push_product = #{pushProduct}</if>
		<if test="pushProductStr != null and pushProductStr != '' ">
			<foreach item="item" collection="pushProductStrList" separator="OR" open="(" close=")" index="">
				find_in_set(#{item},push_product)
			</foreach>
		</if>
		<if test="title != null  and title != ''"> and title LIKE CONCAT('%',#{title},'%')</if>
		<if test="startDate != null">
			AND create_time &gt;= #{startDate}
		</if>
		<if test="endDate != null">
			AND create_time &lt;= #{endDate}
		</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		 and is_delete = 0
        </where>
        order by create_time desc
    </select>

	<insert id="insertMessageNotificationSystem" parameterType="com.bojun.system.entity.MessageNotificationSystem">
		INSERT INTO `system`.`t_message_notification_system`(`notice_id`, `system_id`) VALUES (#{noticeId}, #{systemId})
	</insert>

</mapper>