/**
 * 
 */
package com.bojun.base.system.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.ManageRoleMapper;
import com.bojun.base.system.service.IManageRoleService;
import com.bojun.system.dto.ManageRoleDTO;
import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.entity.ManageRole;
import com.bojun.system.entity.ManageRoleDept;
import com.bojun.system.entity.ManageRoleMenu;
import com.bojun.system.entity.ManageRoleOrgan;
import com.bojun.system.entity.ManageRoleSystem;
import com.github.pagehelper.Page;

/**
 * Model：角色管理 Description： Author:刘俊 created：2020年4月24日
 **/
@Service
public class ManageRoleServiceImpl implements IManageRoleService {

	@Autowired
	private ManageRoleMapper manageRoleMapper;

	/**
	 * @Description 新增角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int addManageRole(ManageRole manageRole) {
		return manageRoleMapper.addManageRole(manageRole);
	}

	/**
	 * @Description 修改角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int updateManageRole(ManageRole manageRole) {
		return manageRoleMapper.updateManageRole(manageRole);
	}

	/**
	 * @Description 查询角色列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
//	public Page<ManageRoleDTO> getManageRoleList(ManageRoleDTO manageRoleDTO) {
//		Page<ManageRoleDTO> roleList = manageRoleMapper.getManageRoleList(manageRoleDTO);
//		StringBuffer organizationName = new StringBuffer();// 机构名称
//		StringBuffer deptName = new StringBuffer();// 部门名称
//		StringBuffer wardName = new StringBuffer();// 病区名称
//		for (ManageRoleDTO dto : roleList.getResult()) {
//			organizationName.reverse();
//			deptName.reverse();
//			wardName.reverse();
//			// 查询机构
//			List<ManageRoleOrganDTO> roleOrganList = dto.getRoleOrganList();
//			if (null != roleOrganList && !roleOrganList.isEmpty()) {
//				for (ManageRoleOrganDTO organList : roleOrganList) {
//					// 拼接机构名称
//					organizationName.append(organList.getOrganizationName() + ",");
//					ManageRoleDept manageRoleDept = new ManageRoleDept();
//					manageRoleDept.setRoId(organList.getRoId());
//					organList.setRoleDeptList(manageRoleMapper.getRoleDeptDet(manageRoleDept));
//					// 查询部门
//					List<ManageRoleDept> roleDeptList = manageRoleMapper.getRoleDeptDet(manageRoleDept);
//					if (null != roleDeptList && !roleDeptList.isEmpty()) {
//						for (ManageRoleDept roleDept : roleDeptList) {
//							// 拼接部门名称
//							if (StringUtils.isNotBlank(roleDept.getDeptName())) {
//								if (deptName.indexOf(roleDept.getDeptName()) < 0) {
//									deptName.append(roleDept.getDeptName() + ",");
//								}
//							}
//							// 拼接病区名称
//							if (StringUtils.isNotBlank(roleDept.getWardName())) {
//								if (wardName.indexOf(roleDept.getWardName()) < 0) {
//									wardName.append(roleDept.getWardName() + ",");
//								}
//							}
//						}
//					}
//				}
//				dto.setOrganizationName(organizationName.toString());
//				dto.setDeptName(deptName.toString());
//				dto.setWardName(wardName.toString());
//			}
//		}
//
//		return roleList;
//	}
	
	
	/**
	 * @Description 
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年09月16日
	 */
	public Page<ManageRoleDTO> getManageRoleList(ManageRoleDTO manageRoleDTO) {
		Page<ManageRoleDTO> roleList = manageRoleMapper.getManageRoleList(manageRoleDTO);
		return roleList;
	}

	/**
	 * @Description 新增角色产品关联
	 * <AUTHOR>
	 * @param ManageRoleSystem
	 * @return int 2020年4月27日
	 */
	public int addManageRoleSystem(ManageRoleSystem manageRole) {
		return manageRoleMapper.addManageRoleSystem(manageRole);
	}

	/**
	 * @Description 新增角色菜单关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int addManageRoleMenu(ManageRoleMenu manageRoleMenu) {
		return manageRoleMapper.addManageRoleMenu(manageRoleMenu);
	}

	/**
	 * @Description 新增角色机构关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int addManageRoleOrgan(ManageRoleOrgan manageRoleOrgan) {
		return manageRoleMapper.addManageRoleOrgan(manageRoleOrgan);
	}

	/**
	 * @Description 新增角色部门关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int addManageRoleDept(ManageRoleDept manageRoleDept) {
		return manageRoleMapper.addManageRoleDept(manageRoleDept);
	}

	/**
	 * @Description 删除角色产品关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int deleteManageRoleSystem(ManageRole manageRole) {
		return manageRoleMapper.deleteManageRoleSystem(manageRole);
	}

	/**
	 * @Description 删除角色菜单关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int deleteManageRoleMenu(ManageRole manageRole) {
		return manageRoleMapper.deleteManageRoleMenu(manageRole);
	}

	/**
	 * @Description 删除角色机构关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int deleteManageRoleOrgan(ManageRole manageRole) {
		return manageRoleMapper.deleteManageRoleOrgan(manageRole);
	}

	/**
	 * @Description 删除角色部门关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return int 2020年4月27日
	 */
	public int deleteManageRoleDept(ManageRole manageRole) {
		return manageRoleMapper.deleteManageRoleDept(manageRole);
	}

	/**
	 * @Description 查询角色
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
	public ManageRoleDTO getManageRoleOne(ManageRoleDTO manageRoleDTO) {
		return manageRoleMapper.getManageRoleOne(manageRoleDTO);
	}

	/**
	 * @Description 查询角色部门底层id
	 * <AUTHOR>
	 * @param
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
	public List<Map<String, Object>> roleOrganTreeList(Map<String, Object> map) {
		return manageRoleMapper.roleOrganTreeList(map);
	}

	/**
	 * @Description 查询角色机构列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
	public List<ManageRoleOrganDTO> getRoleOrganDet(Map<String, Object> map) {
		return manageRoleMapper.getRoleOrganDet(map);
	}

	/**
	 * @Description 查询角色部门列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
	public List<ManageRoleDept> getAllRoleDeptByRoleId(String roleId) {
		return manageRoleMapper.getAllRoleDeptByRoleId(roleId);
	}

	/**
	 * @Description 查询角色部门列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
	public List<ManageRoleOrganDTO> getRoleOrganByOrganId(Map<String, Object> map) {
		return manageRoleMapper.getRoleOrganByOrganId(map);
	}

	/**
	 * @Description 查询角色部门列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return List<ManageRoleDTO> 2020年4月27日
	 */
	public List<ManageRoleDept> getRoleDeptByDeptId(ManageRoleDept manageRoleDept) {
		return manageRoleMapper.getRoleDeptByDeptId(manageRoleDept);
	}

}
