<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/base-manage/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_encrypt/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_encrypt/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_ip/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_ip/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_redis/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_redis/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_sms/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_sms/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_utils/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/commons_utils/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/eureka-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/health-promotion-entity/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/health-promotion-entity/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/health-promotion-service-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/health-promotion-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/health-promotion-service/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/organization-entity/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/organization-entity/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/organization-service-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/organization-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-app/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-entity/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-entity/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-manage/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-mp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-service-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-service/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-socket/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/sphygmometer-wxapp/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/system-entity/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/system-entity/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/system-service-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/system-service/src/main/java" charset="UTF-8" />
  </component>
</project>