package com.bojun.base.manage.controller.notice.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/24 17:24
 */
@ApiModel(value = "通知通告参数", description = "通知通告")
public class GetNoticeVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "推送内容")
    private String noticeContent;
    @ApiModelProperty(value = "通知类型")
    private String noticeTypeId;
    @ApiModelProperty(value = "开始时间")
    private String startTime;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "systemId", example = "1,2,3")
    private String synchronizationPlatform;
    @ApiModelProperty(value = "页数")
    private int pageNum;
    @ApiModelProperty(value = "每页条数")
    private int everyPage;
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;
    @ApiModelProperty(value = "病区ID")
    private Integer wardId;
    @ApiModelProperty(value = "科室ID")
    private Integer deptId;
    @ApiModelProperty(value = "机构ID")
    private Integer organizationId;

    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getNoticeTypeId() {
        return noticeTypeId;
    }

    public void setNoticeTypeId(String noticeTypeId) {
        this.noticeTypeId = noticeTypeId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSynchronizationPlatform() {
        return synchronizationPlatform;
    }

    public void setSynchronizationPlatform(String synchronizationPlatform) {
        this.synchronizationPlatform = synchronizationPlatform;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getEveryPage() {
        return everyPage;
    }

    public void setEveryPage(int everyPage) {
        this.everyPage = everyPage;
    }
}
