package com.bojun.system.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：呼叫记录
 * Description：呼叫记录实体类
 * Author:刘修纬
 * created：2020年5月11日
 */
public class SatisfactionQuestionnaireResult implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;


    private Integer resultId; // 结果id
    private Integer questionnaireId; // 问卷id
    private String answerUserId; // 答题人用户id
    private Date startAnswerTime; // 开始答题时间
    private Date endAnswerTime; // 结束答题时间
    private Integer totalScore; // 总分


    public Integer getResultId() {
        return resultId;
    }

    public void setResultId(Integer resultId) {
        this.resultId = resultId;
    }

    public Integer getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(Integer questionnaireId) {
        this.questionnaireId = questionnaireId;
    }

    public String getAnswerUserId() {
        return answerUserId;
    }

    public void setAnswerUserId(String answerUserId) {
        this.answerUserId = answerUserId;
    }

    public Date getStartAnswerTime() {
        return startAnswerTime;
    }

    public void setStartAnswerTime(Date startAnswerTime) {
        this.startAnswerTime = startAnswerTime;
    }

    public Date getEndAnswerTime() {
        return endAnswerTime;
    }

    public void setEndAnswerTime(Date endAnswerTime) {
        this.endAnswerTime = endAnswerTime;
    }

    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }
}
