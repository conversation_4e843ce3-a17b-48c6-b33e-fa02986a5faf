/**
 * 
 */
package com.bojun;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
*Model：机构微服务入口
*Description：机构微服务入口
*Author: 段德鹏
*created：2020年3月4日
*/
@EnableFeignClients
@EnableCircuitBreaker
@EnableEurekaClient
@EnableTransactionManagement
@SpringBootApplication
@EnableAsync
@EnableScheduling
@MapperScan("com.bojun.organization.mapper")
public class OrganizationServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(OrganizationServiceApplication.class, args);
	}

}
