package com.bojun.health.promotion.service.api;

import com.bojun.health.promotion.common.dto.NewsInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoParamDTO;
import com.bojun.health.promotion.service.api.hystrix.NewsInfoFeignClientHystrix;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 13:23
 */
@FeignClient(name = "health-promotion-service", fallback = NewsInfoFeignClientHystrix.class)
public interface NewsInfoFeignClient {

    @PostMapping(value = "/getNewsInfoList")
    Results<PageData<NewsInfoDTO>> getNewsInfoList(TopicInfoParamDTO topicInfo);


    @PostMapping(value = "/addNewsInfo")
    Integer addNewsInfo(NewsInfoDTO newsInfo) throws Exception;


    @PostMapping(value = "/updateNewsInfo")
    Results updateNewsInfo(NewsInfoDTO newsInfo) throws Exception;

    @PostMapping(value = "/newsPutonOrOffShelf")
    Results newsPutonOrOffShelf(NewsInfoDTO newsInfo) throws Exception;

    @PostMapping(value = "/deleteNewsInfo")
    Integer deleteNewsInfo(NewsInfoDTO newsInfo) throws Exception;

    @PostMapping(value = "/getNewsInfo")
    NewsInfoDTO getNewsInfo(NewsInfoDTO newsInfo, @RequestParam("isLookOver") boolean isLookOver);
}
