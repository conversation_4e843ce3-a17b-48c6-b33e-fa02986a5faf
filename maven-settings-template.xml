<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

  <!-- 本地仓库路径 -->
  <localRepository>${user.home}/.m2/repository</localRepository>

  <!-- 是否与用户交互 -->
  <interactiveMode>true</interactiveMode>

  <!-- 是否离线模式 -->
  <offline>false</offline>

  <!-- 插件组 -->
  <pluginGroups>
    <pluginGroup>org.apache.maven.plugins</pluginGroup>
    <pluginGroup>org.codehaus.mojo</pluginGroup>
  </pluginGroups>

  <!-- 服务器认证信息 -->
  <servers>
    <!-- 如果需要认证的私有仓库，在这里配置 -->
  </servers>

  <!-- 镜像配置 -->
  <mirrors>
    <!-- 阿里云镜像（可选，用于加速国内访问） -->
    <!--
    <mirror>
      <id>aliyun-maven</id>
      <mirrorOf>central</mirrorOf>
      <name>Aliyun Maven Repository</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </mirror>
    -->
    
    <!-- 使用官方中央仓库（推荐） -->
    <mirror>
      <id>central-mirror</id>
      <mirrorOf>central</mirrorOf>
      <name>Maven Central Repository</name>
      <url>https://repo1.maven.org/maven2/</url>
    </mirror>
  </mirrors>

  <!-- 配置文件 -->
  <profiles>
    <profile>
      <id>official-repos</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      
      <repositories>
        <!-- Maven中央仓库 -->
        <repository>
          <id>central</id>
          <name>Maven Central Repository</name>
          <url>https://repo1.maven.org/maven2/</url>
          <releases>
            <enabled>true</enabled>
            <updatePolicy>daily</updatePolicy>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- Spring官方仓库 -->
        <repository>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://repo.spring.io/libs-release</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </repository>
        
        <!-- Apache快照仓库 -->
        <repository>
          <id>apache-snapshots</id>
          <name>Apache Snapshots</name>
          <url>https://repository.apache.org/snapshots/</url>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
        </repository>
      </repositories>
      
      <pluginRepositories>
        <!-- Maven中央插件仓库 -->
        <pluginRepository>
          <id>central</id>
          <name>Maven Central Plugin Repository</name>
          <url>https://repo1.maven.org/maven2/</url>
          <releases>
            <enabled>true</enabled>
          </releases>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
        </pluginRepository>
      </pluginRepositories>
    </profile>
  </profiles>

  <!-- 激活的配置文件 -->
  <activeProfiles>
    <activeProfile>official-repos</activeProfile>
  </activeProfiles>

</settings>
