package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色表对象 t_v2_role
 *
 * <AUTHOR>
 * @date 2021-06-15 11:01:35
 */
@ApiModel(value = "Role对象", description = "角色表")
@Data
@TableName("t_role")
public class Role implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 角色ID（uuid）
     */
    @ApiModelProperty(value = "主键ID", example = "")
    @TableId(value = "role_id", type = IdType.ASSIGN_UUID)
    private String roleId;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称", example = "")
    @TableField("role_name")
    private String roleName;

    /**
     * 角色类型  1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维
     */
    @ApiModelProperty(value = "角色类型  1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维", example = "")
    @TableField("role_type")
    private Integer roleType;

    /**
     * 启用状态： 0：否  1：是
     */
    @ApiModelProperty(value = "启用状态： 0：否  1：是", example = "")
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 角色说明
     */
    @ApiModelProperty(value = "角色说明", example = "")
    @TableField("role_describe")
    private String roleDescribe;

    /**
     * 数据权限  1: 角色机构  2：账号机构
     */
    @ApiModelProperty(value = "数据权限  1: 角色机构  2：账号机构", example = "")
    @TableField("data_permissions")
    private Integer dataPermissions;

    /**
     * 添加角色的用户ID
     */
    @ApiModelProperty(value = "添加角色的用户ID", example = "")
    @TableField("create_user_id")
    private Integer createUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("create_time")
    private Date createTime;
}
