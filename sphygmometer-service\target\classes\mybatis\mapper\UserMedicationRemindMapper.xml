<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserMedicationRemindMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.UserMedicationRemindDTO" id="UserMedicationRemindDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="name"    column="name"    />
        <result property="testFrequency"    column="test_frequency"    />
        <result property="alarmClockRemind"    column="alarm_clock_remind"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectUserMedicationRemind">
    	select
	        id,
	        user_id,
	        name,
	        test_frequency,
	        alarm_clock_remind,
	        create_time,
	        update_time,
	        is_delete
		from 
        	t_user_medication_remind
    </sql>

    <select id="selectUserMedicationRemindById" parameterType="int" resultMap="UserMedicationRemindDTOResult">
		<include refid="selectUserMedicationRemind"/>
		where 
        	id = #{id} and is_delete = 0
    </select>

    <select id="selectUserMedicationRemindList" parameterType="com.bojun.sphygmometer.dto.UserMedicationRemindDTO" resultMap="UserMedicationRemindDTOResult">
        <include refid="selectUserMedicationRemind"/>
        <where>
			and is_delete = 0
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="name != null  and name != ''"> and name = #{name}</if>
		<if test="testFrequency != null  and testFrequency != ''"> and test_frequency = #{testFrequency}</if>
		<if test="alarmClockRemind != null "> and alarm_clock_remind = #{alarmClockRemind}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>

</mapper>