/**
 * 
 */
package com.bojun.enums;

/**
 * <AUTHOR>
 * 
 */
public enum ResponseCodeEnum {

	SUCCESS_REQUEST(200, "操作成功"),
	SUCCESS_OTHER(201, "请另一个护士核对"),
	FAIL_REQUEST(300, "操作失败"),
	BAD_REQUEST(400, "参数错误"),
	UNLOGIN_REQUEST(401, "登录已失效，请重新登录"),
	LOGINSSO_INVALID(402, "您当前账号在另一台设备登录，如非本人操作，请您尽量联系管理员或客服。"),
	UNAUTHORITY_REQUEST(403, "暂无访问权限"),
	NO_DATA(404, "暂无数据"),
	IILEGAL_REQUEST(405, "非法请求"),
	SEN_WORDS_REQUEST(406, "包含敏感词信息请求"),
	INTERNET_HOSPITAL_CLOSED(407, "医生没有问诊、咨询权限"),
	EXCEPTION_REQUEST(500, "系统繁忙, 请稍后再试"),
	NO_ORGANIZATIONID(503, "未绑定机构,请选择机构"),
	UNREASONABLE_REQUEST(501, "服务已断开, 请稍后再试"),
	ALREADY_LOGIN_REQUEST(600, "当前账号正在使用，是否登录，登录后该账号会进行强制下线"),
	ORDER_STATUS_CHANGE(601, "用户订单已经申请退款，无法操作"),
	DEVICE_IS_EXIST(602, "重复编码，请检查编码是否已使用"),
	NO_AUTHENTICATION(700, "未实名认证"),
	YES_BINGDING(605,"已绑定"),;
	
	
	private Integer code;
	private String errorDescr;


	private ResponseCodeEnum(Integer code, String errorDescr) {
		this.code = code;
		this.errorDescr = errorDescr;
	}

	public static String getErrorCodeDescr(Integer code) {
		String descr = "";
		for (ResponseCodeEnum responseCodeEnum : ResponseCodeEnum.values()) {
			if (code.equals(responseCodeEnum.getCode())) {
				descr = responseCodeEnum.getErrorDescr();
				break;
			}
		}
		return descr;
	}

	public Integer getCode() {
		return code;
	}

	public String getErrorDescr() {
		return errorDescr;
	}

	

}
