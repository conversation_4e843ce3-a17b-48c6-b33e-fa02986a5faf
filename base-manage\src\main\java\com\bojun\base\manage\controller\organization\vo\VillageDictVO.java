/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model： 村居委会信息
 * Description：村居委会信息
 * Author：赖水秀
 * created： 2020年7月2日
 */
@ApiModel(value = "村居委会信息", description = "查询村居委会返回信息")
public class VillageDictVO implements Serializable {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = -2392360913295507404L;

	@ApiModelProperty(value="乡镇code")
	private String townCode;

	@ApiModelProperty(value="村code")
	private String villageCode;
	
	@ApiModelProperty(value="村名称")
	private String villageName;

	public String getTownCode() {
		return townCode;
	}

	public void setTownCode(String townCode) {
		this.townCode = townCode;
	}

	public String getVillageCode() {
		return villageCode;
	}

	public void setVillageCode(String villageCode) {
		this.villageCode = villageCode;
	}

	public String getVillageName() {
		return villageName;
	}

	public void setVillageName(String villageName) {
		this.villageName = villageName;
	}
	
}
