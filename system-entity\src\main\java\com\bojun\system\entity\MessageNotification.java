package com.bojun.system.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：消息通知（站内）
 * Description：消息通知（站内）实体类
 * Author:李欣颖
 * created：2020年1月7日
 */
public class MessageNotification implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;
    private String noticeId; // 消息通知id(uuid)
    private Integer organizationId; // 是否立即发送
    private String title; // 消息标题
    private String noticeContent; // 消息内容
    private String noticeTypeId; // 消息通知类型id
    private String synchronizationPlatform; // 同步平台  1：全部  2：OA系统  3：账务系统  4：邮件系统  5：微信公众号 6：网站
    private String fileName; // 附近文件名
    private String receiveDeptId;
    private String receiveUserId;
    private Integer isImmediately; // 是否立即发送
    private Integer isStatistics; // 是否统计阅读量
    private Date timingTime; // 定时发布时间
    private Integer status; // 状态：1：已发布 2：待发布  3：已撤回   4：重发
    private Integer isDelete; // 是否删除
    private Date deleteTime; // 删除时间
    private Date publishTime; // 发布时间
    private Integer createUserId; // 创建人用户id
    private Date createTime; // 创建时间


    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getNoticeTypeId() {
        return noticeTypeId;
    }

    public void setNoticeTypeId(String noticeTypeId) {
        this.noticeTypeId = noticeTypeId;
    }

    public String getSynchronizationPlatform() {
        return synchronizationPlatform;
    }

    public void setSynchronizationPlatform(String synchronizationPlatform) {
        this.synchronizationPlatform = synchronizationPlatform;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getReceiveDeptId() {
        return receiveDeptId;
    }

    public void setReceiveDeptId(String receiveDeptId) {
        this.receiveDeptId = receiveDeptId;
    }

    public String getReceiveUserId() {
        return receiveUserId;
    }

    public void setReceiveUserId(String receiveUserId) {
        this.receiveUserId = receiveUserId;
    }

    public Integer getIsImmediately() {
        return isImmediately;
    }

    public void setIsImmediately(Integer isImmediately) {
        this.isImmediately = isImmediately;
    }

    public Integer getIsStatistics() {
        return isStatistics;
    }

    public void setIsStatistics(Integer isStatistics) {
        this.isStatistics = isStatistics;
    }

    public Date getTimingTime() {
        return timingTime;
    }

    public void setTimingTime(Date timingTime) {
        this.timingTime = timingTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
