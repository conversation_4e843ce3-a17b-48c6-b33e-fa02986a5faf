package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知（站内）对象 t_message_notification
 * 
 * <AUTHOR>
 * @date 2021-05-14 19:19:04
 */
@ApiModel(value = "MessageNotification对象" , description = "消息通知（站内）")
@Data
@TableName("system.t_message_notification")
public class MessageNotificationV2 implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 消息通知id(uuid) */
    @ApiModelProperty(value = "主键ID", example = "")
    @TableId(value = "notice_id", type = IdType.ASSIGN_UUID)
    private String noticeId;

    /** 消息标题 */
    @ApiModelProperty(value = "消息标题", example = "")
	@TableField("title")
    private String title;

    /** 消息内容 */
    @ApiModelProperty(value = "消息内容", example = "")
	@TableField("notice_content")
    private String noticeContent;

    /** 消息通知类型id */
    @ApiModelProperty(value = "消息通知类型id", example = "")
	@TableField("notice_type_id")
    private String noticeTypeId;

    /** 同步平台（多个逗号隔开） */
    @ApiModelProperty(value = "同步平台（多个逗号隔开）", example = "")
	@TableField("synchronization_platform")
    private String synchronizationPlatform;

    /** 附近文件名 */
    @ApiModelProperty(value = "附近文件名", example = "")
	@TableField("file_name")
    private String fileName;

    /** 接收部门（多个逗号隔开） */
    @ApiModelProperty(value = "接收部门（多个逗号隔开）", example = "")
	@TableField("receive_dept_id")
    private String receiveDeptId;

    /** 接收人用户id(多个逗号隔开) */
    @ApiModelProperty(value = "接收人用户id(多个逗号隔开)", example = "")
	@TableField("receive_user_id")
    private String receiveUserId;

    /** 是否立即发送 1:是 0:否 */
    @ApiModelProperty(value = "是否立即发送 1:是 0:否", example = "")
	@TableField("is_immediately")
    private Integer isImmediately;

    /** 是否统计阅读量 1:是 0:否 */
    @ApiModelProperty(value = "是否统计阅读量 1:是 0:否", example = "")
	@TableField("is_statistics")
    private Integer isStatistics;

    /** 定时发布时间 */
    @ApiModelProperty(value = "定时发布时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("timing_time")
    private Date timingTime;

    /** 状态：1：已发布 2：待发布  3：已撤回   4：重发 */
    @ApiModelProperty(value = "状态：1：已发布 2：待发布  3：已撤回   4：重发", example = "")
	@TableField("status")
    private Integer status;

    /** 是否删除 1:是 0:否 */
    @ApiModelProperty(value = "是否删除 1:是 0:否", example = "")
	@TableField("is_delete")
    private Integer isDelete;

    /** 删除时间 */
    @ApiModelProperty(value = "删除时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("delete_time")
    private Date deleteTime;

    /** 发布时间 */
    @ApiModelProperty(value = "发布时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("publish_time")
    private Date publishTime;

    /** 创建（发布）人用户id */
    @ApiModelProperty(value = "创建（发布）人用户id", example = "")
	@TableField("create_user_id")
    private Integer createUserId;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	@TableField("create_time")
    private Date createTime;

    /** 是否已读 1:是 0:否 */
    @ApiModelProperty(value = "是否已读 1:是 0:否", example = "")
	@TableField("is_read")
    private String isRead;
}
