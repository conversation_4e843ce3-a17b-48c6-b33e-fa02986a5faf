/**
 * 
 */
package com.bojun.base.manage.controller.manageRole.vo;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import com.bojun.vo.BaseQueryInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：角色管理 Description：查询角色列表信息 Author：lj created： 2020年4月27日
 */
@ApiModel(value = "查询角色列表信息", description = "查询角色列表传入参数")
public class GetManageRoleVO   extends BaseQueryInfoVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;



//	@NotEmpty(message = "角色名称不能为空")
	@ApiModelProperty(value = "角色名称", required = false, example = "医生")
	private String roleName;

//	 @NotNull(message = "启用状态不能为空")
	@ApiModelProperty(value = "启用状态： 0：否  1：是", required = false, example = "医生")
	private Integer isEnabled; //
	
	@ApiModelProperty(value = "机构id", required = false, example = "1")
	private Integer organizationId;
	
	@NotNull(message = "角色类型不能为空")
	@ApiModelProperty(value = "1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维", required = true, example = "1")
	private Integer roleType;

	
	
	
	
	
	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}
	

	

}
