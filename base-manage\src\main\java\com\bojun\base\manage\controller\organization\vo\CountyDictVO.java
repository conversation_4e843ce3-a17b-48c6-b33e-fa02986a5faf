/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：县区信息
 * Author：赖水秀
 * created： 2020年5月7日
 */
@ApiModel(value = "县区信息", description = "查询县区返回信息")
public class CountyDictVO implements Serializable {
	

	/**
	 * 
	 */
	private static final long serialVersionUID = -2392360913295507404L;

	@ApiModelProperty(value="县区code")
	private String countyCode;

	@ApiModelProperty(value="县区名称")
	private String countyName;

	@ApiModelProperty(value="市区code")
	private String cityCode;

	public String getCountyCode() {
		return countyCode;
	}

	public void setCountyCode(String countyCode) {
		this.countyCode = countyCode;
	}

	public String getCountyName() {
		return countyName;
	}

	public void setCountyName(String countyName) {
		this.countyName = countyName;
	}

	public String getCityCode() {
		return cityCode;
	}

	public void setCityCode(String cityCode) {
		this.cityCode = cityCode;
	}
	
}
