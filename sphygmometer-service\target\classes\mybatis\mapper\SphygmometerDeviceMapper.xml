<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.SphygmometerDeviceMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO" id="SphygmometerDeviceDTOResult">
        <result property="deviceId"    column="device_id"    />
        <result property="deviceNo"    column="device_no"    />
		<result property="relationOrgId"    column="relation_org_id"    />
        <result property="organizationId"    column="organization_id"    />
        <result property="deviceName"    column="device_name"    />
        <result property="deviceType"    column="device_type"    />
        <result property="deviceModel"    column="device_model"    />
        <result property="isOnline"    column="is_online"    />
        <result property="isEnabled"    column="is_enabled"    />
        <result property="lastestIpAddress"    column="lastest_ip_address"    />
        <result property="lastestLoginTime"    column="lastest_login_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="addUserId"    column="add_user_id"    />
        <result property="addUserName"    column="add_user_name"    />
        <result property="addTime"    column="add_time"    />
		<result property="deviceImei"    column="device_imei"    />
		<result property="remark"  column ="remark"/>
    </resultMap>

    <sql id="selectSphygmometerDevice">
    	select
	        device_id,
	        device_no,
	        relation_org_id,
	        organization_id,
	        device_name,
	        device_type,
	        device_model,
	        is_online,
	        is_enabled,
	        lastest_ip_address,
	        lastest_login_time,
	        update_user_id,
	        update_user_name,
	        update_time,
	        add_user_id,
	        add_user_name,
	        add_time,
	        location,
	        device_imei,
	        heartbeat,
			remark,
			(select organization_name  from organization.t_organization_info where tsd.organization_id = organization_id) organization_name,
	        (select organization_name  from organization.t_organization_info where tsd.relation_org_id = organization_id) relation_org_name,
			offline_reminder,
			offline_reminder_start_time,
			offline_reminder_end_time,
			offline_reminder_time
		from
        	t_sphygmometer_device tsd
    </sql>

    <select id="selectSphygmometerDeviceById" parameterType="int" resultType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO">
		<include refid="selectSphygmometerDevice"/>
		where 
        	device_id = #{deviceId}
    </select>

    <select id="selectSphygmometerDeviceList" parameterType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO" resultType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO">
		select
			temp.*,
			temp.user_id as bind_user_id,
			temp.nick_name as bind_user_name,
			temp.bind_time as activation_time,
			DATE_FORMAT(temp.measure_time, '%Y-%m-%d %H:%i:%s' ) measure_time_str,
			REPLACE(temp.mobile, SUBSTR(temp.mobile, 4, 4), '****') as user_mobile,
			REPLACE(temp.second_mobile, SUBSTR(temp.second_mobile, 4, 4), '****') as second_user_mobile
		from (
			select
				tsd.device_id,
				tsd.device_no,
				tsd.relation_org_id,
				tsd.organization_id,
				tsd.device_name,
				tsd.device_type,
				tsd.device_model,
				tsd.is_online,
				tsd.is_enabled,
				tsd.lastest_ip_address,
				tsd.lastest_login_time,
				tsd.update_user_id,
				tsd.update_user_name,
				tsd.update_time,
				tsd.add_user_id,
				tsd.add_user_name,
				tsd.add_time,
				tsd.location,
				tsd.device_imei,
				tsd.heartbeat,
				tsd.remark,
				tsu.mobile,
				tsu.bind_time,
				tsu.user_id,
				tsu.nick_name,
				trbi.real_name,
				tsu2.mobile as second_mobile,
				tsu2.bind_time as second_bind_time,
				tsu2.user_id as second_user_id,
				tsu2.nick_name as second_nick_name,
				trbi2.real_name as second_real_name,
				(
					select
						tsr1.measure_time
					from
						t_sphygmometer_record tsr1
					where
						tsd.device_id = tsr1.device_id
					<if test="organizationIds != null and organizationIds != '' ">
						and tsr1.organization_id in (${organizationIds})
					</if>
					ORDER BY
						tsr1.measure_time DESC
					LIMIT 0,1
				) measure_time,
				(
					select
						count(1)
					from
						t_sphygmometer_record tsr2
					where
						tsd.device_id = tsr2.device_id
					<if test="organizationIds != null and organizationIds != '' ">
						and tsr2.organization_id in (${organizationIds})
					</if>
				) measure_count,
				(select organization_name  from organization.t_organization_info where tsd.organization_id = organization_id) organization_name,
				(select organization_name  from organization.t_organization_info where tsd.relation_org_id = organization_id) relation_org_name,
				(
					CASE WHEN tsu.user_id is not null THEN 2
					WHEN tsu2.user_id is not null THEN 2
					ELSE 1 END) AS activation_state,
				(select count(1) from t_device_bind_record where device_id = tsd.device_id and bind_status = 1) AS device_bind_count,
				tsd.offline_reminder,
				tsd.offline_reminder_start_time,
				tsd.offline_reminder_end_time,
				tsd.offline_reminder_time
			from
				t_sphygmometer_device tsd
			left join t_sphygmometer_user tsu on tsu.bind_device_id = tsd.device_id and tsu.bind_device_user_type = 1
			left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
			left join t_sphygmometer_user tsu2 on tsu2.bind_device_id = tsd.device_id and tsu2.bind_device_user_type = 2
			left join t_resident_basic_info trbi2 on trbi2.id = tsu2.resident_id
		) temp
        <where>
		<if test="deviceId != null "> and temp.device_id = #{deviceId}</if>
		<if test="deviceNo != null  and deviceNo != ''"> and temp.device_no = #{deviceNo}</if>
		<if test="deviceImei != null  and deviceImei != ''"> and temp.device_imei = #{deviceImei}</if>
		<if test="deviceName != null  and deviceName != ''"> and temp.device_name = #{deviceName}</if>
		<if test="deviceType != null "> and temp.device_type = #{deviceType}</if>
		<if test="deviceModel != null  and deviceModel != ''"> and temp.device_model = #{deviceModel}</if>
		<if test="isOnline != null "> and temp.is_online = #{isOnline}</if>
		<if test="isEnabled != null "> and temp.is_enabled = #{isEnabled}</if>
		<if test="organizationIds != null and organizationIds != '' ">
			and temp.organization_id in (${organizationIds})
		</if>
		<if test="keyWords != null and keyWords != '' and deviceType == 1">
		 	and (
				temp.device_no LIKE concat('%',#{keyWords},'%') or
				temp.device_model LIKE concat('%',#{keyWords},'%')
		 	)
		</if>
		<if test="keyWords != null and keyWords != '' and deviceType == 2">
			and (
				temp.device_no LIKE concat( '%', #{keyWords}, '%' ) or
				temp.device_model LIKE concat( '%', #{keyWords}, '%' ) or
				temp.mobile LIKE concat( '%', #{keyWords}, '%' )
			)
		</if>
		<if test="startDateStr != null and startDateStr != '' and endDateStr != null and endDateStr != '' and timeType == 2">
			AND DATE_FORMAT( temp.measure_time, '%Y-%m-%d' ) &lt;= #{endDateStr}
			AND DATE_FORMAT( temp.measure_time, '%Y-%m-%d' ) &gt;= #{startDateStr}
		</if>
		<if test="startDateStr != null and startDateStr != '' and endDateStr != null and endDateStr != '' and timeType == 1">
			AND DATE_FORMAT(temp.add_time, '%Y-%m-%d')  &lt;= #{endDateStr}
			AND DATE_FORMAT(temp.add_time, '%Y-%m-%d') &gt;= #{startDateStr}
		</if>
		<if test="activationState != null and activationState != '' and activationState == 1">
			AND temp.user_id is null
		</if>
		<if test="activationState != null and activationState != '' and activationState == 2">
			AND temp.user_id is not null
		</if>
        </where>
		order by temp.add_time desc
       <!-- <if test="deviceType == 1">
			order by is_online desc,add_time desc
		</if>
		<if test="deviceType ==2">
			order by activationState desc,add_time desc
		</if>-->
    </select>


	<select id="countDevice" parameterType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO" resultType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO">
	SELECT
	( SELECT count( 1 ) FROM t_sphygmometer_device WHERE device_type = #{deviceType} <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS deviceCount,
	( select count(DISTINCT location) from t_sphygmometer_device WHERE device_type = #{deviceType} <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS locationCount,
	( select count(DISTINCT organization_id) from t_sphygmometer_device WHERE device_type = #{deviceType} <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS orgCount,
	( SELECT count( 1 ) FROM t_sphygmometer_device WHERE device_type = #{deviceType} AND is_online = 1 <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS onlineDeviceCount,
	( SELECT count( 1 ) FROM t_sphygmometer_device WHERE device_type = #{deviceType} AND is_online = 0 <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS offlineDeviceCount,
	( select count(DISTINCT user_id) from t_sphygmometer_record where device_type = #{deviceType} <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS testingResidentCount,
	( SELECT count( 1 ) FROM t_sphygmometer_record WHERE device_type = #{deviceType}  <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS testingReportCount,
	( select count(DISTINCT bind_device_id) from t_sphygmometer_user where bind_device_id is not null <if test="null != organizationIds and ''  != organizationIds "> AND manage_organization_id in (${organizationIds}) </if>) activationCount,
	( SELECT count(DISTINCT user_id) FROM t_sphygmometer_record WHERE device_type =#{deviceType} and (pressure_result = 2 OR pressure_result = 3) <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS abnormalResidentCount,
	( SELECT count( 1 ) FROM t_sphygmometer_record WHERE device_type = #{deviceType} and (pressure_result = 2 OR pressure_result = 3) <if test="null != organizationIds and ''  != organizationIds "> AND organization_id in (${organizationIds}) </if> ) AS abnormalReportCount;
	</select>

	<select id="selectSphygmometerDeviceByDeviceNo" parameterType="java.lang.String" resultType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO">
		<include refid="selectSphygmometerDevice"/>
		where
			device_no = #{deviceNo}
	</select>


	<select id="selectSphygmometerDeviceByWxQrCodeUrl" parameterType="java.lang.String" resultType="com.bojun.sphygmometer.dto.SphygmometerDeviceDTO">
		<include refid="selectSphygmometerDevice"/>
		where
			wx_qr_code_url = #{wxQrCodeUrl}
	</select>

	<update id="updateHeartbeatByDeviceNo">
		update
			t_sphygmometer_device
		set
			heartbeat = now()
		where
			device_no = #{deviceNo}
	</update>

</mapper>