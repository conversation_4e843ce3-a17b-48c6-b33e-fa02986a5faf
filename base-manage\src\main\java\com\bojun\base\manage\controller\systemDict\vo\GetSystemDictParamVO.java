/**
 * 
 */
package com.bojun.base.manage.controller.systemDict.vo;

import java.io.Serializable;

import com.bojun.vo.BaseQueryInfoVO;
import com.bojun.vo.Page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：产品管理
 * Description：查询产品信息
 * Author：赖水秀
 * created： 2020年4月27日
 */
@ApiModel(value = "查询产品条件", description = "查询产品列表传入的查询条件")
public class GetSystemDictParamVO extends BaseQueryInfoVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;
	
	
	@ApiModelProperty(value="系统类型id")
	private Integer systemTypeId;	
	
	@ApiModelProperty(value="查询关键字")
	private String searchKey;
	
	@ApiModelProperty(value="终端类型(0:移动端，1：web端)")
	private Integer isMobile;

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public String getSearchKey() {
		return searchKey;
	}

	public void setSearchKey(String searchKey) {
		this.searchKey = searchKey;
	}

	public Integer getIsMobile() {
		return isMobile;
	}

	public void setIsMobile(Integer isMobile) {
		this.isMobile = isMobile;
	}
}
