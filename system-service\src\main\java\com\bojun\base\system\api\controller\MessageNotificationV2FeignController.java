package com.bojun.base.system.api.controller;


import com.bojun.base.system.api.MessageNotificationV2FeignClient;
import com.bojun.base.system.service.MessageNotificationObjectService;
import com.bojun.base.system.service.MessageNotificationSystemV2Service;
import com.bojun.base.system.service.MessageNotificationV2Service;
import com.bojun.common.controller.BaseFeignController;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import com.bojun.system.dto.MessageNotificationV2DTO;
import com.bojun.system.entity.MessageNotificationObject;
import com.bojun.system.entity.MessageNotificationSystemV2;
import com.bojun.system.entity.MessageNotificationV2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 消息通知（站内）FeignController
 *
 * <AUTHOR>
 * @date 2021-05-14 19:19:04
 */
@RestController
public class MessageNotificationV2FeignController extends BaseFeignController implements MessageNotificationV2FeignClient
{
    @Autowired
    private MessageNotificationV2Service messageNotificationService;
    @Autowired
    private MessageNotificationObjectService messageNotificationObjectService;
    @Autowired
    private MessageNotificationSystemV2Service messageNotificationSystemV2Service;


	/**
     * 查询消息通知（站内）分页列表
     */
    @PostMapping(PREFIX + "/page")
    @Override
    public Results<PageData<MessageNotificationV2DTO>> page(@RequestBody MessageNotificationV2DTO MessageNotificationV2DTO){
        startPage(MessageNotificationV2DTO.getPageNum(), MessageNotificationV2DTO.getEveryPage());
        List<MessageNotificationV2DTO> list = messageNotificationService.selectMessageNotificationList(MessageNotificationV2DTO);
        return Results.list(getPageData(list));
    }

    /**
     * 查询消息通知（站内）列表
     */
    @PostMapping(PREFIX + "/list")
    public Results<List<MessageNotificationV2DTO>> list(@RequestBody MessageNotificationV2DTO MessageNotificationV2DTO){
        List<MessageNotificationV2DTO> list = messageNotificationService.selectMessageNotificationList(MessageNotificationV2DTO);
        return Results.list(list);
    }

    /**
     * 获取消息通知（站内）详细信息
     */
    @GetMapping(PREFIX + "/getInfo")
    public Results<MessageNotificationV2DTO> getInfo(@RequestParam("noticeId") String noticeId){
        MessageNotificationV2DTO MessageNotificationV2DTO = messageNotificationService.selectMessageNotificationById(noticeId);
        return Results.data(MessageNotificationV2DTO);
    }

    /**
     * 新增消息通知（站内）DTO
     */
    @Override
    @PostMapping(PREFIX + "/addDTO")
    public Results<MessageNotificationV2DTO> addDTO(@RequestBody MessageNotificationV2DTO messageNotificationV2DTO) {
        int save = messageNotificationService.insertMessageNotification(messageNotificationV2DTO);
        if (save > 0) {
            MessageNotificationObject messageNotificationObject = new MessageNotificationObject();
            messageNotificationObject.setNoticeId(messageNotificationV2DTO.getNoticeId());
            messageNotificationObject.setOrganizationId(messageNotificationV2DTO.getOrganizationId());
            messageNotificationObjectService.save(messageNotificationObject);
            MessageNotificationSystemV2 messageNotificationSystem = new MessageNotificationSystemV2();
            messageNotificationSystem.setSystemId(messageNotificationV2DTO.getSystemId());
            messageNotificationSystem.setNoticeId(messageNotificationV2DTO.getNoticeId());
            messageNotificationSystemV2Service.insertMessageNotificationSystemV2(messageNotificationSystem);
        } else {
            Results.fail("新增失败!");
        }
        return Results.data(messageNotificationV2DTO);
    }

    /**
     * 修改消息通知（站内）DTO
     */
    @PostMapping(PREFIX + "/editDTO")
    public Results editDTO(@RequestBody MessageNotificationV2DTO MessageNotificationV2DTO){
        Integer num = messageNotificationService.updateMessageNotification(MessageNotificationV2DTO);
        return Results.opResult(num);
    }

    /**
     * 新增消息通知（站内）
     */
    @PostMapping(PREFIX + "/add")
    public Results add(@RequestBody MessageNotificationV2 messageNotification){
        Integer num = messageNotificationService.insertMessageNotification(messageNotification);
        return Results.opResult(num);
    }

    /**
     * 修改消息通知（站内）
     */
    @PostMapping(PREFIX + "/edit")
    public Results edit(@RequestBody MessageNotificationV2 messageNotification){
        Integer num = messageNotificationService.updateMessageNotification((MessageNotificationV2DTO) messageNotification);
        return Results.opResult(num);
    }

    /**
     * 删除消息通知（站内），多个以逗号分隔
     */
    @GetMapping(PREFIX + "/removeByIds")
    public Results removeByIds(@RequestParam("ids") String ids) {
        Integer num = messageNotificationService.deleteMessageNotificationByIds(ids);
        return Results.opResult(num);
    }
}
