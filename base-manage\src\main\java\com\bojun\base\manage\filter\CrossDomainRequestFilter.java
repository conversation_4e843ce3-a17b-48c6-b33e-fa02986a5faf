/**
 * 
 */
package com.bojun.base.manage.filter;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Component;

/**
*Model：跨域请求过滤器
*Description：跨域请求过滤器
*Author: 段德鹏
*created：2019年12月24日
*/
@Component
public class CrossDomainRequestFilter implements Filter {
	
	//初始化调用的方法
    //当服务器 被启动的时候，调用
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		
	}
	
	//拦截调用的方法
	@Override
	public void doFilter(ServletRequest req, ServletResponse res, Filter<PERSON>hain chain)
			throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;  
		response.setHeader("Access-Control-Allow-Origin", "*");  
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");  
        response.setHeader("Access-Control-Allow-Headers", "x-requested-with,content-type,token"); 
        response.setContentType("text/html;charset=UTF-8");
        chain.doFilter(req, res); 
	}
	
	//销毁时候调用的方法
	@Override
	public void destroy() {
		
	}

}
