package com.bojun.base.system.controller;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.ISequenceService;
import com.bojun.enums.ResponseCodeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class SequenceController extends BoJunBaseController {
    private static Logger logger = LoggerFactory.getLogger(SequenceController.class);
    @Autowired
    private ISequenceService sequenceService;
    @GetMapping("/getNextVal")
    public void getNextVal(@RequestParam(value = "seqName") String seqName) {
        try{
           Integer val = sequenceService.getNextVal(seqName);
            outJson(successInfo(val));
        }catch (Exception e) {
            logger.error("/getNextVal:", e);
            outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
        }
    }
}
