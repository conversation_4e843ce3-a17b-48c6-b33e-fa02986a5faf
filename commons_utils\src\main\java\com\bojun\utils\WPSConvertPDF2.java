package com.bojun.utils;

import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import com.aspose.slides.Presentation;
import com.aspose.words.Document;
import com.aspose.words.SaveFormat;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * Model： word,excel,ppt转pdf工具类，基于asposes
 * Description： word,excel,ppt转pdf工具类
 * Author：潘文泽
 * created： 2021年8月16日
 */
public class WPSConvertPDF2 {

    private static final String lincense = "<License>\n" +
            "    <Data>\n" +
            "        <Products>\n" +
            "            <Product>Aspose.Total for Java</Product>\n" +
            "            <Product>Aspose.Words for Java</Product>\n" +
            "        </Products>\n" +
            "        <EditionType>Enterprise</EditionType>\n" +
            "        <SubscriptionExpiry>20991231</SubscriptionExpiry>\n" +
            "        <LicenseExpiry>20991231</LicenseExpiry>\n" +
            "        <SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber>\n" +
            "    </Data>\n" +
            "    <Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature>\n" +
            "</License>";

    /***
     * 判断需要转化文件的类型（Excel、Word、ppt）
     *
     * @param inputFile
     * @param pdfFile
     */
    public static int convert2PDF(String inputFile, String pdfFile) {
        String kind = getFileSufix(inputFile);
        if (kind.equals("pdf")) {
            return -3;//原文件就是PDF文件
        }
        if (kind.equals("doc") || kind.equals("docx") || kind.equals("txt")) {
            return WPSConvertPDF2.word2PDF(inputFile, pdfFile);
        } else if (kind.equals("ppt") || kind.equals("pptx")) {
            return WPSConvertPDF2.ppt2PDF(inputFile, pdfFile);
        } else if (kind.equals("xls") || kind.equals("xlsx")) {
            return WPSConvertPDF2.Ex2PDF(inputFile, pdfFile);
        } else {
            return -4;
        }
    }

    /***
     * 判断文件类型
     *
     * @param fileName
     * @return
     */
    public static String getFileSufix(String fileName) {
        int splitIndex = fileName.lastIndexOf(".");
        return fileName.substring(splitIndex + 1);
    }

    /**
     * word转PDF
     *
     * @param inPath
     * @param outPath
     * @return
     */
    public static int word2PDF(String inPath, String outPath) {
        try {
            InputStream is = getLincense();
            com.aspose.words.License license = new com.aspose.words.License();
            license.setLicense(is);
            long old = System.currentTimeMillis();
            File file = new File(outPath); // 新建一个pdf文档
            FileOutputStream os = new FileOutputStream(file);
            Document doc = new Document(inPath); // 验证License 是将要被转化的word文档
            doc.save(os, SaveFormat.PDF);// 全面支持DOC, DOCX,
            // OOXML, RTF HTML,
            // OpenDocument,
            // PDF, EPUB, XPS,
            // SWF 相互转换
            long now = System.currentTimeMillis();
            os.close();
            int time = (int) ((now - old) / 1000);
            return time;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * ppt转PDF
     *
     * @param inPath
     * @param outPath
     * @return
     */
    public static int ppt2PDF(String inPath, String outPath) {
        try {
            InputStream is = getLincense();
            com.aspose.slides.License license = new com.aspose.slides.License();
            license.setLicense(is);
            long old = System.currentTimeMillis();
            File pdfFile = new File(outPath);// 输出路径
            FileOutputStream os = new FileOutputStream(pdfFile);
            //原始路径
            Presentation pres = new Presentation(inPath);
            pres.save(os, com.aspose.slides.SaveFormat.Pdf);
            os.close();
            long now = System.currentTimeMillis();
            int time = (int) ((now - old) / 1000);
            return time;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }

    }

    /**
     * excel转PDF
     *
     * @param inPath
     * @param outPath
     * @return
     */
    public static int Ex2PDF(String inPath, String outPath) {
        try {
            InputStream is = getLincense();
            com.aspose.cells.License license = new com.aspose.cells.License();
            license.setLicense(is);
            long old = System.currentTimeMillis();
            File pdfFile = new File(outPath);// 输出路径
            Workbook wb = new Workbook(inPath);// 原始excel路径
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(true);
            pdfSaveOptions.setAllColumnsInOnePagePerSheet(true);
            FileOutputStream fileOS = new FileOutputStream(pdfFile);
            //wb.save(fileOS, SaveFormat.PDF);
            wb.save(fileOS, pdfSaveOptions);
            fileOS.close();
            long now = System.currentTimeMillis();
            int time = (int) ((now - old) / 1000);
            return time;
        } catch (Exception e) {
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * 去除水印
     */
    private static InputStream getLincense() {
        InputStream is = new ByteArrayInputStream(lincense.getBytes());
        return is;
    }

    public static void main(String[] args) {
        convert2PDF("/Users/<USER>/something/112.ppt", "/Users/<USER>/something/123.pdf");
    }
}
