package com.bojun.sphygmometer.app.controller.healthPromotion;

import com.bojun.author.AuthAnnotation;
import com.bojun.common.controller.BaseController;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.health.promotion.common.dto.TopicInfoDTO;
import com.bojun.health.promotion.service.api.TopicInfoFeignClient;
import com.bojun.page.Results;
import com.bojun.sphygmometer.app.controller.healthPromotion.vo.TopicInfoVo;
import com.bojun.sphygmometer.app.controller.healthPromotion.vo.params.TopicInfoParamVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiSort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 10:09
 */
@Api(tags = {"健康栏目"})
@RestController
@ApiSort(value = 0)
@RequestMapping(TopicInfoController.BASE_URL)
public class TopicInfoController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(TopicInfoController.class);

    public static final String BASE_URL = "/healthPromotion";

    @Autowired
    private TopicInfoFeignClient topicInfoService;

    @Autowired
    private RedisUtil redisUtil;

    @ApiOperation("查询栏目列表")
    @PostMapping("/getNewsTopicList")
    @AuthAnnotation(action = TopicInfoController.BASE_URL + "/getNewsTopicList")
    @ApiOperationSupport(order = 1)
    public Results<List<TopicInfoVo>> getNewsTopicList(@RequestBody TopicInfoParamVo tTopicInfoVo) {
        try {
            TopicInfoDTO topicInfoDTO = new TopicInfoDTO();
            BeanUtils.copyProperties(tTopicInfoVo, topicInfoDTO);
            Results<List<TopicInfoDTO>> newsTopicList = topicInfoService.getNewsTopicList(topicInfoDTO);
            if (null == newsTopicList) {
                return Results.fail(ResponseCodeEnum.NO_DATA.getCode(), ResponseCodeEnum.NO_DATA.getErrorDescr());
            }
            return Results.list(getListVo(newsTopicList.getData(), TopicInfoVo.class));
        } catch (BeansException e) {
            logger.error("/addNewsTopic:", e);
            return Results.fail("查询栏目列表失败!");
        }
    }

}
