package com.bojun.sphygmometer.mq.handle;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bojun.sphygmometer.mq.common.Constants;
import com.bojun.sphygmometer.mq.common.MeasureQueue;
import com.bojun.sphygmometer.mq.common.utils.MeasureQueueUtils;
import com.bojun.sphygmometer.mq.common.utils.SocketUtils;
import com.bojun.sphygmometer.mq.common.utils.WXMessageUtils;
import com.bojun.sphygmometer.mq.dto.MeasuringUserDTO;
import com.bojun.sphygmometer.mq.dto.PreQueueDTO;
import com.bojun.sphygmometer.mq.dto.SphygmometerUserRelativeDTO;
import com.bojun.sphygmometer.mq.dto.TestTipTplMsgDTO;
import com.bojun.sphygmometer.mq.entity.*;
import com.bojun.sphygmometer.mq.service.ManageUserMpRelativeService;
import com.bojun.sphygmometer.mq.service.SphygmometerDeviceService;
import com.bojun.sphygmometer.mq.service.SphygmometerUserRelativeService;
import com.bojun.sphygmometer.mq.service.SphygmometerUserService;
import com.bojun.utils.HttpUtil;
import com.bojun.utils.StringUtils;
import com.gexin.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
*Model：公众号消息业务处理类
*Description：公众号消息业务处理类
*Author:段德鹏
*created：2021年3月27日
**/
@Slf4j
@Component
public class WXMessageHandle {

	@Autowired
	private MeasureQueue measureQueue;
	@Autowired
	private MeasureQueueUtils measureQueueUtils;
	@Autowired
	private SocketUtils socketUtils;
	@Autowired
	private WXMessageUtils wxMessageUtils;

	@Autowired
	private SphygmometerUserRelativeService sphygmometerUserRelativeService;

	@Autowired
	private SphygmometerUserService sphygmometerUserService;

	@Autowired
	private SphygmometerDeviceService sphygmometerDeviceService;

	@Autowired
	private ManageUserMpRelativeService manageUserMpRelativeService;

	@Autowired
	private WxMpService wxMpService;

	@Value("${wx.open.ma.appId}")
	private String wMaAppId ;
	@Value("${wxmp.screen.url}")
	private String wxmpScreenUrl;

	@Autowired
	private RestTemplate restTemplate;

	/**
	 * @Description  文本消息处理业务（只能通过菜单点击）
	 * <AUTHOR>
	 * @param wxMessage 公众号推送消息内容
	 * @return String 返回内容
	 * created：2021年3月27日
	 */
	public String textMessageHandle(WXMessage wxMessage) {
		try {
			//校验文本消息业务菜单ID,无关文本消息返回空
			if (!Constants.MENU_MEASURE_START.equals(wxMessage.getBizmsgmenuid())) {
				return Constants.SUCCESS;
			}
			//用户openId
			String openId = wxMessage.getFromUserName();
			String content = wxMessage.getContent();
			//设备编号
			String deviceNo = StringUtils.EMPTY;
			//如果点击的是开始排队消息, 则进入这个逻辑
			if (StringUtils.isNotBlank(content) && content.startsWith(Constants.START_QUEUE_PREFIX)) {
				deviceNo = content.replace(Constants.START_QUEUE_PREFIX, "");
				//加入预备队列
				MeasureQueue.preQueue.offer(new PreQueueDTO(deviceNo, openId));
				return Constants.SUCCESS;
			}
			//获取消息内容参数
			String[] params = wxMessage.getContent().split("\\|");
			//校验内容参数合法性
			if (params.length < 2){
				this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, "未知", Constants.TIP_QUEUE_FAILURE));
				return Constants.SUCCESS;
			}
			//设备编号
			deviceNo = params[0];
			SphygmometerDevice sphygmometerDevice = this.sphygmometerDeviceService.getSphygmometerDeviceByDeviceNo(deviceNo);
			String orgName = sphygmometerDevice.getOrganizationName();
			//超时时间戳
			long expireTime = Long.parseLong(params[1]);
			MeasuringUserDTO measuringUserDTO = this.measureQueue.getMeasuringUser(deviceNo);
			if (measuringUserDTO != null && expireTime == measuringUserDTO.getStartTestExpireTime()) {//时间相等才是正确的解锁指令
				String measuringUserOpenId = measuringUserDTO.getOpenId();
				if (!openId.equals(measuringUserOpenId)) {//不相等，但超时时间过了，提示超时
					if (System.currentTimeMillis() > expireTime) {
						this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_QUEUE_FAILURE));
					} else if(this.measureQueue.containsMeasureUser(deviceNo, openId)) {//不相等，且在队列，提示请耐心等待
						this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_PLEASE_WAIT));
					} else {//其他情况，提示超时
						this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_QUEUE_FAILURE));
					}
				} else {
					if (System.currentTimeMillis() < expireTime) {//相等，且未超时，重复解锁
						//向设备发送解锁指令
						socketUtils.sendUnlockCmdToDevice(deviceNo, openId);
						this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_TEST_START));
					} else if (StringUtils.isNotBlank(this.measureQueue.getDeviceStartupSuccess(deviceNo)) &&
							System.currentTimeMillis() > expireTime) {//相等，且按了开始检测，但超时，提示正在测量中
						this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_MEASURING));
					} else {
						this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_QUEUE_FAILURE));
					}
				}
			} else {
				//key不存在, 也代表超时
				this.wxMessageUtils.sendTestTipTplMsg(new TestTipTplMsgDTO(openId, orgName, Constants.TIP_QUEUE_FAILURE));
			}
			return Constants.SUCCESS;
		} catch (Exception e) {
			log.error("公众号文本消息处理异常", e);
            return Constants.FAILED;
		}
    }

	/**
	 * @Description 事件消息处理业务，订阅（关注）事件，扫码事件（"subscribe", "SCAN"）
	 * <AUTHOR>
	 * @param wxMessage 公众号推送消息内容
	 * @return String 返回内容
	 * created：2021年3月27日
	 */
	public String eventMessageHandle(WXMessage wxMessage,String messageXML) {
		try {
			String openId = null;
			//设备编号（事件key就是设备编号）
			String eventKey = wxMessage.getEventKey();
			if(StringUtils.isBlank(eventKey)){
				return  Constants.SUCCESS;
			}
			openId = wxMessage.getFromUserName();
			//不在捕捉范围内的事件，返回失败
			log.info("eventKey:{},openId:{}，event:{}",eventKey,openId,wxMessage.getEvent());
			if (!"subscribe".equals(wxMessage.getEvent()) && !"SCAN".equals(wxMessage.getEvent()) && !"unsubscribe".equals(wxMessage.getEvent())) {
				return Constants.FAILED;
			}
			//取消关注则删除绑定关系
			if("unsubscribe".equals(wxMessage.getEvent())){
				sphygmometerUserRelativeService.remove(new QueryWrapper<SphygmometerUserRelative>().lambda().eq(SphygmometerUserRelative::getWxOpenId,openId));
				return Constants.SUCCESS;
			}
			//如果包含UserId
			if(eventKey.contains("appUserId_")){
				//绑定高血压用户用户和家属
				bindSphygmometerUserRelative(wxMessage,eventKey,openId);
				return Constants.SUCCESS;
			} else if(eventKey.contains("manageUser_")){
				//运维人员绑定微信公众号
				bindManageUserMqRelative(eventKey,openId);
				return Constants.SUCCESS;
			}else{
				//发送客服消息
				sendCustomerServiceMessage(wxMessage, openId,messageXML);
			}
			return Constants.SUCCESS;
		} catch (Exception e) {
			e.printStackTrace();
			log.error("公众号事件消息处理异常", e);
			return Constants.FAILED;
		}
	}

	/**
	 * 发送客服消息
	 * @param wxMessage
	 * @param openId
	 */
	private void sendCustomerServiceMessage(WXMessage wxMessage, String openId,String messageXML){
		if("SCAN".equals(wxMessage.getEvent()) || "subscribe".equals(wxMessage.getEvent())){
			if(wxMessage.getEventKey().contains("wxapp")){
				String deviceNo = wxMessage.getEventKey();
				if(wxMessage.getEventKey().contains("qrscene")){
					deviceNo = deviceNo.replace("qrscene","");
				}
				if(wxMessage.getEventKey().contains("wxapp")){
					deviceNo = deviceNo.replace("wxapp","");
				}
				if(wxMessage.getEventKey().contains("_")){
					deviceNo = deviceNo.replace("_","");
				}
				SphygmometerDevice sphygmometerDevice = sphygmometerDeviceService.getSphygmometerDeviceByDeviceNo(deviceNo);
				if (sphygmometerDevice == null) {
					log.info(deviceNo + "设备不存在");
					return;
				}
				if(null == sphygmometerDevice.getIsEnabled() || sphygmometerDevice.getIsEnabled().intValue() == 0){ //判断设备是否禁用
					wxMessageUtils.sendMsg(openId, "设备已被禁用，请更换设备再试");
					log.info(deviceNo + "设备已禁用，请更换设备再试");
					return;
				}
				wxMessageUtils.sendMsg(openId, "欢迎您使用网格化高血压筛查\n\n\n" +
						"-----<a href='https://www.qq.com' data-miniprogram-appid='"+wMaAppId+"'data-miniprogram-path='pages/index/Init?deviceNo="+ deviceNo +"&sendTime="+System.currentTimeMillis()+"'>线上测量</a>-----\n" +
						"自主筛查，实时查看结果\n\n\n"+
						"当前的设备已获得FDA 、CFDA、 CE认证，请您安心测量！");
				return;
			}
		/*	try {
				log.info("----wxmpScreenUrl-----:"+wxmpScreenUrl+",messageXML:"+messageXML);
				String result = HttpUtil.doPostWithText(wxmpScreenUrl,messageXML);
				log.info("请求结果"+result);
			}catch (Exception  ex){
				ex.printStackTrace();
			}*/
		}
	}

	/**
	 * 绑定高血压用户用户和家属
	 * @param wxMessage
	 * @param eventKey
	 * @param openId
	 * @throws WxErrorException
	 */
	private void bindSphygmometerUserRelative(WXMessage wxMessage,String eventKey,String openId) throws WxErrorException {
		if("subscribe".equals(wxMessage.getEvent()) || "SCAN".equals(wxMessage.getEvent())){
			//绑定高血压用户用户和家属
			String str =  eventKey;
			if(eventKey.contains("qrscene")){
				str = str.replace("qrscene","");
			}
			if(eventKey.contains("appUserId")){
				str = str.replace("appUserId","");
			}
			if(eventKey.contains("_")){
				str = str.replace("_","");
			}
			Integer userId = Integer.valueOf(str);
			SphygmometerUser sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery().eq(SphygmometerUser::getWxOpenId,openId));
			if(userId == sphygmometerUser.getUserId().intValue()){
				//绑定则提示家属用户已绑定
				wxMessageUtils.sendMsg(openId, Constants.BIND_ONESELF);
				return;
			}
			SphygmometerUserRelativeDTO sphygmometerUserRelativeDTO = new SphygmometerUserRelativeDTO();
			sphygmometerUserRelativeDTO.setUserId(userId);
			//查询用户是否绑定家属
			List<SphygmometerUserRelativeDTO> results = sphygmometerUserRelativeService.selectSphygmometerUserRelativeList(sphygmometerUserRelativeDTO);
			if(!CollectionUtils.isEmpty(results)){
				//绑定则提示家属用户已绑定
				wxMessageUtils.sendMsg(openId, Constants.USER_BOUND);
				return;
			}
			//发送欢迎消息
			WxMpUser wxMpUser = wxMpService.getUserService().userInfo(openId);
			Integer relativeUserId = saveUser(openId,wxMpUser);
			log.info("--------relativeUserId------"+relativeUserId);
			String wxUserName = wxMpUser.getNickname();
			sphygmometerUserRelativeDTO.setWxOpenId(openId);
			sphygmometerUserRelativeDTO.setRelativeUserId(relativeUserId);
			sphygmometerUserRelativeService.insertSphygmometerUserRelative(sphygmometerUserRelativeDTO);
			SphygmometerUser sphygmometerUserDTO = sphygmometerUserService.getUserById(userId);
			String appUserName = sphygmometerUserDTO.getNickName()==null?"":sphygmometerUserDTO.getNickName();
			String content = String.format(Constants.WELCOME, wxUserName,appUserName);
			wxMessageUtils.sendMsg(openId, content);
		}
	}


	/**
	 * 运维人员绑定微信公众号
	 * @param eventKey
	 * @param openId
	 */
	private void bindManageUserMqRelative(String eventKey,String openId){
		//运维人员绑定微信公众号
		String str =  eventKey;
		if(eventKey.contains("qrscene")){
			str = str.replace("qrscene","");
		}
		if(eventKey.contains("manageUser")){
			str = str.replace("manageUser","");
		}
		if(eventKey.contains("_")){
			str = str.replace("_","");
		}
		ManageUserMpRelative manageUserMpRelative = manageUserMpRelativeService.getOne(Wrappers.<ManageUserMpRelative>lambdaQuery().eq(ManageUserMpRelative::getUserId,str));
		//判断关联关系是否存在
		if(!Optional.ofNullable(manageUserMpRelative).isPresent()){
			//新增运维人员和公众号关联信息
			manageUserMpRelative = new ManageUserMpRelative();
			manageUserMpRelative.setUserId(Integer.valueOf(str));
			manageUserMpRelative.setOpenId(openId);
			manageUserMpRelative.setCreateTime(new Date());
			manageUserMpRelativeService.save(manageUserMpRelative);
		}
	}


	private Integer saveUser(String openId,WxMpUser wxMpUser){
		log.info("-----------wxMpUser---------" + JSON.toJSONString(wxMpUser));
		SphygmometerUser sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery()
				.eq(SphygmometerUser::getUnionId, wxMpUser.getUnionId()));
		if (sphygmometerUser == null) {
			sphygmometerUser = sphygmometerUserService.getOne(Wrappers.<SphygmometerUser>lambdaQuery()
					.eq(SphygmometerUser::getWxOpenId, wxMpUser.getOpenId()));
		}
		if(sphygmometerUser == null){
			sphygmometerUser = new SphygmometerUser();
			sphygmometerUser.setWxOpenId(openId);
			sphygmometerUser.setUnionId(wxMpUser.getUnionId());
			sphygmometerUser.setNickName(wxMpUser.getNickname());
			sphygmometerUser.setGender(wxMpUser.getSex());
			sphygmometerUser.setHeadPortrait(wxMpUser.getHeadImgUrl());
			sphygmometerUser.setCountry(wxMpUser.getCountry());
			sphygmometerUser.setProvince(wxMpUser.getProvince());
			sphygmometerUser.setCity(wxMpUser.getCity());
			sphygmometerUser.setRegisterSource(1);
			sphygmometerUser.setRegisterTime(new Date());
			sphygmometerUserService.save(sphygmometerUser);
		}else{
			sphygmometerUser.setWxOpenId(openId);
			sphygmometerUserService.updateById(sphygmometerUser);
		}
		return sphygmometerUser.getUserId();
	}

}
