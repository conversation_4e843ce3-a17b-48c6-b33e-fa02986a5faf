package com.bojun.base.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.RoleDTO;
import com.bojun.system.entity.Role;

import java.util.List;

/**
 * RoleService接口
 *
 * <AUTHOR>
 * @date 2021-06-15 11:01:35
 */
public interface RoleService extends IService<Role> {
    /**
     * 查询角色表
     *
     * @param roleId 角色表ID
     * @return 角色表
     */
    public RoleDTO selectRoleById(String roleId);

    /**
     * 查询角色表列表
     *
     * @param roleDTO 角色表
     * @return 角色表集合
     */
    public List<RoleDTO> selectRoleList(RoleDTO roleDTO);

    /**
     * 新增角色表
     *
     * @param roleDTO 角色表
     * @return 结果
     */
    public int insertRole(RoleDTO roleDTO);

    /**
     * 修改角色表
     *
     * @param roleDTO 角色表
     * @return 结果
     */
    public int updateRole(RoleDTO roleDTO);

    /**
     * 新增角色表
     *
     * @param role 角色表
     * @return 结果
     */
    public int insertRole(Role role);

    /**
     * 修改角色表
     *
     * @param role 角色表
     * @return 结果
     */
    public int updateRole(Role role);

    /**
     * 批量删除角色表
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteRoleByIds(String ids);

    /**
     * 删除角色表信息
     *
     * @param roleId 角色表ID
     * @return 结果
     */
    public int deleteRoleById(String roleId);
}
