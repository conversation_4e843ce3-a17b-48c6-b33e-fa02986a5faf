
package com.bojun.base.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.MenuDTO;
import com.github.pagehelper.Page;


/**
 * Model： 菜单管理
 * Description： 菜单管理DAO
 * Author：赖水秀
 * created： 2020年4月27日
 */
@Mapper
public interface MenuMapper {
		
	/**
	 * @Description 保存菜单信息
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * int
	 * 2020年4月28日
	 */
	int saveMenu(MenuDTO menuDTO);
	
	/**
	 * @Description 修改菜单信息
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * int
	 * 2020年4月28日
	 */
	int updateMenu(MenuDTO menuDTO);
	
	/**
	 * @Description 根据id查询单个菜单信息
	 * <AUTHOR>
	 * @param menuId
	 * @return
	 * MenuDTO
	 * 2020年4月28日
	 */
	MenuDTO getMenuById(String menuId);

	/**
	 * @Description 分页查询菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	Page<MenuDTO> getMenuPageList(MenuDTO menuDTO);
	
	/**
	 * @Description 查询菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	List<MenuDTO> getMenuList(MenuDTO menuDTO);
	
	/**
	 * @Description 根据id查询子菜单列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月29日
	 */
	List<MenuDTO> getSubMenuList(String menuId);
	
	/**
	 * @Description 根据菜单id删除数据
	 * <AUTHOR>
	 * @param ids
	 * @return
	 * int
	 * 2020年4月28日
	 */
	int deleteMenuById(String menuId);
	
	/**
	 * @Description 查询角色菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	List<MenuDTO> getMenuRoleList(MenuDTO menuDTO);
		
	/**
	 * @Description 批量添加按钮
	 * <AUTHOR>
	 * @param list
	 * @return
	 * int
	 * 2020年5月12日
	 */
	int batchAddButton(List<MenuDTO> list);
	
	/**
	 * @Description  删除菜单下的按钮
	 * <AUTHOR>
	 * @param menuId
	 * @return
	 * int
	 * 2020年5月12日
	 */
	int deleteButtonByMenuId(String menuId);	
	
	
	/**
	 * @Description 获取菜单下的所有按钮
	 * <AUTHOR>
	 * @param menuId
	 * @return
	 * List<MenuDTO>
	 * 2020年5月12日
	 */
	List<MenuDTO> getButtonListByMenu(String menuId);
	

	/**
	 * 
	 * @Description 获取医疗服务订单入口
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * MenuDTO
	 * 2020年5月25日
	 */
	public MenuDTO getMedicalServiceEntranceTwo(MenuDTO menuDTO);
	
	/**
	 * @Description 查询菜单/按钮列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	List<MenuDTO> getMenuButtonList(MenuDTO menuDTO);
}
