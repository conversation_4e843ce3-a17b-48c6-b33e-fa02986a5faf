package com.bojun.base.system.controller;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.bojun.base.controller.BoJunBaseController;
import com.bojun.base.system.service.IFormQuestionAnswerService;
import com.bojun.base.system.service.IFormQuestionItemService;
import com.bojun.base.system.service.IFormQuestionOptionService;
import com.bojun.base.system.service.IFormQuestionService;
import com.bojun.base.system.service.ISatisfactionQuestionnaireResultService;
import com.bojun.base.system.service.ISatisfactionQuestionnaireService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.FormQuestionDTO;
import com.bojun.system.dto.FormQuestionItemDTO;
import com.bojun.system.dto.FormQuestionOptionDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireDTO;
import com.bojun.system.dto.SatisfactionQuestionnaireResultDTO;
import com.bojun.utils.accuracyUtils;
import com.github.pagehelper.Page;
/**
 * 
*Model：满意度问卷答题结果信息表
*Description：满意度问卷答题结果信息表
*Author:李欣颖
*created：2020年5月7日
 */
@RestController
//@RequestMapping("satisfactionQuestionnaireResult")
public class SatisfactionQuestionnaireResultController extends BoJunBaseController {

	private static Log log = LogFactory.getLog(SatisfactionQuestionnaireResultController.class);

	@Autowired
	ISatisfactionQuestionnaireResultService satisfactionQuestionnaireResultService;
	@Autowired
	ISatisfactionQuestionnaireService satisfactionQuestionnaireService;
	@Autowired
	IFormQuestionService formQuestionService;
	@Autowired
	IFormQuestionOptionService formQuestionOptionService;
	@Autowired
	IFormQuestionAnswerService formQuestionAnswerService;
	@Autowired
	IFormQuestionItemService formQuestionItemService;
	
	/**
	 * 
	 * @Description 查询满意度问卷答题结果信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/getSatisfactionQuestionnaireResult", method = RequestMethod.POST)
	public void getSatisfactionQuestionnaireResult(@RequestBody SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO) {
		try {
//			int pageNum = (null == satisfactionQuestionnaireResultDTO.getPageNum() ? 1:satisfactionQuestionnaireResultDTO.getPageNum());
//			int everyPage = (null == satisfactionQuestionnaireResultDTO.getEveryPage() ? 10:satisfactionQuestionnaireResultDTO.getEveryPage());
//			PageHelper.startPage(pageNum, everyPage);
			Page<List<SatisfactionQuestionnaireResultDTO>> page = satisfactionQuestionnaireResultService.getSatisfactionQuestionnaireResult(satisfactionQuestionnaireResultDTO);
			if (page == null || page.getTotal() == 0) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			
			outJson(successPageInfo(page.getResult(), page.getTotal()));
		} catch (Exception e) {

			log.error("getSatisfactionQuestionnaireResult:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	
	

	/**
	 * 
	 * @Description 新增满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/addSatisfactionQuestionnaireResult", method = RequestMethod.POST)
	public void addSatisfactionQuestionnaireResult(@RequestBody SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO ) {
		try {
				if (null==satisfactionQuestionnaireResultDTO) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
//			paramsMap.put("id",UuidGenerator.getUuidGenerator());
			Integer nNum = satisfactionQuestionnaireResultService.addSatisfactionQuestionnaireResult(satisfactionQuestionnaireResultDTO);
			if (null == nNum || nNum.intValue() != 1) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());

		} catch (Exception e) {
			log.error("addSatisfactionQuestionnaireResult:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 删除满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/deleteSatisfactionQuestionnaireResult", method = RequestMethod.POST)
	public void deleteSatisfactionQuestionnaireResult(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap || paramsMap.size() == 0 || null == paramsMap.get("ids")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			Integer deleteNum = satisfactionQuestionnaireResultService.deleteSatisfactionQuestionnaireResult(paramsMap);

			outJson(successInfo("成功删除" + deleteNum + "记录"));

		} catch (Exception e) {

			log.error("deleteSatisfactionQuestionnaireResult:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}

	/**
	 * 
	 * @Description 查询单个满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/getSatisfactionQuestionnaireResultById", method = RequestMethod.POST)
	public void getSatisfactionQuestionnaireResultById(@RequestBody Map<String, Object> paramsMap) {
		try {
			if (null == paramsMap || paramsMap.isEmpty() || null == paramsMap.get("id")) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			SatisfactionQuestionnaireResultDTO resSatisfactionQuestionnaireResultDTO = satisfactionQuestionnaireResultService.getSatisfactionQuestionnaireResultById(paramsMap);
			if (null == resSatisfactionQuestionnaireResultDTO) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}

			outJson(successInfo(resSatisfactionQuestionnaireResultDTO));
		} catch (Exception e) {
			log.error("getSatisfactionQuestionnaireResultById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}


	/**
	 * 
	 * @Description 修改满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/updateSatisfactionQuestionnaireResult", method = RequestMethod.POST)
	public void updateSatisfactionQuestionnaireResult(@RequestBody SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO) {
		try {
			if (null == satisfactionQuestionnaireResultDTO) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			Integer nNum=satisfactionQuestionnaireResultService.updateSatisfactionQuestionnaireResult(satisfactionQuestionnaireResultDTO);
			if (null == nNum || nNum.intValue() <= 0) {
				outJson(info(ResponseCodeEnum.BAD_REQUEST.getCode(), "操作失败"));
				return;
			}
			outJson(successInfo());
		} catch (Exception e) {
			log.error("updateSatisfactionQuestionnaireResult:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
	/**
	 * 
	 * @Description 查询单个满意度问卷表信息统计
	 * <AUTHOR>
	 * @param paramsMap
	 * @return void
	 * created：2020年5月7日
	 */
	@RequestMapping(value = "/getSatisfactionQuestionnaireResultByIdStatistics", method = RequestMethod.POST)
	public void getSatisfactionQuestionnaireResultByIdStatistics(@RequestBody SatisfactionQuestionnaireDTO satisfactionQuestionnaireDTO) {
		try {
			if (null == satisfactionQuestionnaireDTO) {
				outJson(errorInfo(ResponseCodeEnum.BAD_REQUEST.getCode()));
				return;
			}
			SatisfactionQuestionnaireDTO resSatisfactionQuestionnaireDTO = satisfactionQuestionnaireService.getSatisfactionQuestionnaireById(satisfactionQuestionnaireDTO.getQuestionnaireId());
			if (null == resSatisfactionQuestionnaireDTO) {
				outJson(errorInfo(ResponseCodeEnum.NO_DATA.getCode()));
				return;
			}
			//获取问卷总分
			Integer totalScore = resSatisfactionQuestionnaireDTO.getTotalScore();
			 Map<String, Object> paramsMap = new HashMap<String, Object>();
			paramsMap.put("questionnaireId", satisfactionQuestionnaireDTO.getQuestionnaireId());
			paramsMap.put("totalScore", totalScore);
			//获取总人数
			Integer resultCount = satisfactionQuestionnaireResultService.querySatisfactionQuestionnaireResultCount(paramsMap);
			//获取全院总分
			Integer totalScores = satisfactionQuestionnaireResultService.queryTotalScore(paramsMap);
			//各区人数统计
			 List<Map<String, Object>>  numberMapList=satisfactionQuestionnaireResultService.getNumberStatistics(paramsMap);
			if (null != numberMapList && numberMapList.size() > 0) {
				int[] arr = new int[numberMapList.size()];
				for (int i = 0; i < numberMapList.size(); i++) {
					Map<String, Object> map = numberMapList.get(i);
					arr[i] = Integer.valueOf(map.get("fractionCount").toString());
				}
				for (int i = 0; i < numberMapList.size(); i++) {
					Map<String, Object> map = numberMapList.get(i);
					map.put("ratio", accuracyUtils.getPercentValue(arr, resultCount, i, 2));
				}
				}
			//统计详情
			//问题数组
			List<FormQuestionDTO> formQuestionList = formQuestionService.getFormQuestion(paramsMap);
            if (null!=formQuestionList&&formQuestionList.size()>0) {
				for (FormQuestionDTO formQuestionDTO : formQuestionList) {
					paramsMap.clear();
					paramsMap.put("questionId", formQuestionDTO.getQuestionId());
					//获取该题答题得总人数
					Integer questionCount = formQuestionAnswerService.queryFormQuestionAnswerCount(paramsMap);
//					List<FormQuestionOptionDTO> formQuestionOptionDTOList =null;
					if(3==formQuestionDTO.getQuestionType() || 4==formQuestionDTO.getQuestionType())
					{
						
						//获取子项目
						List<FormQuestionItemDTO> formQuestionItemDTOLsit = formQuestionItemService.getFormQuestionItem(paramsMap);
						if (null!=formQuestionItemDTOLsit&&formQuestionItemDTOLsit.size()>0) {
							formQuestionDTO.setFormQuestionItemDTOList(formQuestionItemDTOLsit);
							for (FormQuestionItemDTO formQuestionItemDTO : formQuestionItemDTOLsit) {
								
								paramsMap.put("questionItemId", formQuestionItemDTO.getQuestionItemId());
								Integer questionItemCount = formQuestionAnswerService.queryFormQuestionAnswerCount(paramsMap);
								List<FormQuestionOptionDTO> formQuestionOptionDTOList=formQuestionOptionService.getFormQuestionItemOption(paramsMap);
								if (null!=formQuestionOptionDTOList&&formQuestionOptionDTOList.size()>0) {
									if (null != formQuestionOptionDTOList && formQuestionOptionDTOList.size() > 0&&null!=questionCount&&questionItemCount>0) {
										int[] arr = new int[formQuestionOptionDTOList.size()];
										for (int i = 0; i < formQuestionOptionDTOList.size(); i++) {
											FormQuestionOptionDTO formQuestionOptionDTO = formQuestionOptionDTOList.get(i);
											arr[i] = Integer.valueOf(formQuestionOptionDTO.getNumber().toString());
										}
										for (int i = 0; i < formQuestionOptionDTOList.size(); i++) {
											FormQuestionOptionDTO formQuestionOptionDTO = formQuestionOptionDTOList.get(i);
											formQuestionOptionDTO.setRatio(accuracyUtils.getPercentValue(arr, questionItemCount, i, 2));
										}
										}	
									formQuestionItemDTO.setFormQuestionOptionDTOList(formQuestionOptionDTOList);
							}
							}
						   
						
						}
						
						
					}
					else {
						//获取答案题目总数
						List<FormQuestionOptionDTO> formQuestionOptionDTOList = formQuestionOptionService.getFormQuestionOption(paramsMap);
							if (null!=formQuestionOptionDTOList&&formQuestionOptionDTOList.size()>0) {
									if (null != formQuestionOptionDTOList && formQuestionOptionDTOList.size() > 0&&null!=questionCount&&questionCount>0) {
										int[] arr = new int[formQuestionOptionDTOList.size()];
										for (int i = 0; i < formQuestionOptionDTOList.size(); i++) {
											FormQuestionOptionDTO formQuestionOptionDTO = formQuestionOptionDTOList.get(i);
											arr[i] = Integer.valueOf(formQuestionOptionDTO.getNumber().toString());
										}
										for (int i = 0; i < formQuestionOptionDTOList.size(); i++) {
											FormQuestionOptionDTO formQuestionOptionDTO = formQuestionOptionDTOList.get(i);
											formQuestionOptionDTO.setRatio(accuracyUtils.getPercentValue(arr, questionCount, i, 2));
										}
										}	
								formQuestionDTO.setFormQuestionOptionDTOList(formQuestionOptionDTOList);
							}
					}
				
				}
			}
            //问题详情
            resSatisfactionQuestionnaireDTO.setFormQuestionDTOList(formQuestionList);
            //全院总分
            resSatisfactionQuestionnaireDTO.setTotalScores(totalScores);
            //总人数
            resSatisfactionQuestionnaireDTO.setResultCount(resultCount);
            //饼图数据
            resSatisfactionQuestionnaireDTO.setNumberMapList(numberMapList);
			outJson(successInfo(resSatisfactionQuestionnaireDTO));
		} catch (Exception e) {
			log.error("getSatisfactionQuestionnaireById:", e);
			outJson(errorInfo(ResponseCodeEnum.EXCEPTION_REQUEST.getCode()));
		}
	}
}
