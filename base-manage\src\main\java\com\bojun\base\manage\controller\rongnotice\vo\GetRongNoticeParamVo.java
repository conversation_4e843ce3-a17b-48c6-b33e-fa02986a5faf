package com.bojun.base.manage.controller.rongnotice.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知vo类
 * @Description: 基础控台融云消息通知vo类
 * @since 2020-12-04
 */
@Data
@TableName("t_rong_notice")
@ApiModel(value = "添加RongNoticeVo对象", description = "基础控台融云消息通知")
public class GetRongNoticeParamVo implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "关键字", example = " ")
    private String keyWord; //消息标题

    @ApiModelProperty(value = "消息通知类型id", example = " ")
    private String noticeTypeId; //消息通知类型id

    @ApiModelProperty(value = "推送产品 1.全部 2.患者移动端，3医生移动端", example = " ")
    private Integer pushProducts; //推送产品 1.全部 2.患者移动端，3医生移动端


    @ApiModelProperty(value = "开始时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private java.sql.Date startTime; //定时发布时间

    @ApiModelProperty(value = "结束时间", example = " ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private java.sql.Date endTime; //定时发布时间

    @ApiModelProperty(value = "机构ID")
    private Integer organizationId;
    @ApiModelProperty(value = "页数")
    private Integer pageNum;
    @ApiModelProperty(value = "条数")
    private Integer everyPage;

}
