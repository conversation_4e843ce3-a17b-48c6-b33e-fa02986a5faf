package com.bojun.base.manage.controller.notice.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/30 17:32
 */
@ApiModel(value = "推送对象VO", description = "对象VO")
public class ObjectVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "科室Id")
    private Integer deptId;
    @ApiModelProperty(value = "病区Id")
    private Integer wardId;
    @ApiModelProperty(value = "机构Id")
    private Integer organizationId;
    @ApiModelProperty(value = "科室名称")
    private String deptName;
    @ApiModelProperty(value = "病区名称")
    private String wardName;
    @ApiModelProperty(value = "角色Id")
    private String roleId;
    @ApiModelProperty(value = "lastId")
    private Integer lastId;
    @ApiModelProperty(value = "机构类型")
    private String organizationTypeCode;

    public String getOrganizationTypeCode() {
        return organizationTypeCode;
    }

    public void setOrganizationTypeCode(String organizationTypeCode) {
        this.organizationTypeCode = organizationTypeCode;
    }

    public Integer getLastId() {
        return lastId;
    }

    public void setLastId(Integer lastId) {
        this.lastId = lastId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getDeptName() {
        return deptName;
    }

    private String organizationName;

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getWardName() {
        return wardName;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }
}
