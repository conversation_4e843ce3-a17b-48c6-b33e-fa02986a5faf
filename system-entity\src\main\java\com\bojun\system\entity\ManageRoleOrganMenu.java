package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：角色机构关联
 * Description：角色机构关联实体类
 * Author:刘俊
 * created：2020年4月30日
 */
public class ManageRoleOrganMenu implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;

    private Integer id;
    private String type;

    private Integer deptId; // 部门（科室）id

    private Integer wardId; // 区域（病区）id

    private Integer organizationId; // 机构id

	private Integer hasAuth;//是否有权限(全选半选之分)0无(半选)1有(全选)

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

	public Integer getHasAuth() {
		return hasAuth;
	}

	public void setHasAuth(Integer hasAuth) {
		this.hasAuth = hasAuth;
	}
}
