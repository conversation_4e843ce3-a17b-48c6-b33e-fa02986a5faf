<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserBindDeviceMapper">
    
 
    
    <select id="getUserBindDeviceList" resultType="com.bojun.sphygmometer.dto.UserBindDeviceDTO">
    	select distinct  tsd.device_no,tdbr.user_id, tsd.device_id,tsd.device_name, tsd.device_type, tsd.device_model, tsd.is_enabled, tsd.device_imei,
    	(select measure_time from t_sphygmometer_record tsr where tsr.device_id = tsd.device_id and tsr.user_id = #{userId} order by measure_time desc limit 1) lastMeasureTime,
    	(select count(id) from t_sphygmometer_record tsr where tsr.user_id = #{userId} and tsr.device_id = tsd.device_id) measureNumber
        from t_device_bind_record tdbr left join t_sphygmometer_device tsd on tdbr.device_id = tsd.device_id 
        where tdbr.user_id = #{userId} order by tdbr.create_date desc
    </select>
    
    <select id="getUserDeviceMeasureRecordList" resultType="com.bojun.sphygmometer.dto.SphygmometerRecordDTO">
        select
            tsr.id,
            tsr.user_id,
            tsr.device_id,
            tsr.device_type,
            tsr.systolic_pressure,
            tsr.diastolic_pressure,
            tsr.heartbeat,
            tsr.pressure_result,
            tsr.heartbeat_result,
            tsr.measure_place,
            tsr.measure_time,
            tsr.organization_id,
            tsd.device_no,
            tsr.record_type,
            (select organization_name from organization.t_organization_info toi where toi.organization_id = tsr.organization_id) organization_name
		from
		    t_sphygmometer_record tsr
		left join t_sphygmometer_user tsu on tsu.user_id = tsr.user_id and tsu.bind_device_user_type = tsr.record_type
	    left join t_sphygmometer_device tsd on tsd.device_id = tsr.device_id
		where 1=1 
		<if test="userId != null ">
            AND tsr.device_type = 2 and tsr.user_id = #{userId}
        </if>
        <if test="deviceId != null ">
            AND tsr.device_id =  #{deviceId}
        </if>
        order by tsr.measure_time desc
    </select>

    <select id="getDeviceBindUserList" resultType="com.bojun.sphygmometer.dto.DeviceBindUserListDTO">
        select
            tsu.user_id,
            trbi.real_name,
            tsu.bind_device_id,
            tsu.bind_time,
            tsu.bind_device_user_type,
            1 as bind_status
        from
            t_sphygmometer_user tsu
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsu.bind_device_id = #{deviceId}
        order by tsu.bind_device_user_type asc
    </select>

    <select id="getDeviceUnBindUserList" resultType="com.bojun.sphygmometer.dto.DeviceBindUserListDTO">
        select
            tsu.user_id,
            trbi.real_name,
            temp.bind_device_id,
            temp.bind_device_user_type,
            2 as bind_status
        from
            t_sphygmometer_user tsu
        inner join (
            select distinct
                tdbr.user_id,
                tdbr.device_id as bind_device_id,
                tdbr.bind_device_user_type
            from
                t_device_bind_record tdbr
            where
                tdbr.device_id = #{deviceId}
                and tdbr.bind_status = 2
                <if test="deviceBindUserIdList != null  and deviceBindUserIdList.size() > 0">
                    and tdbr.user_id not in
                    <foreach item="item" index="index"
                             collection="deviceBindUserIdList" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
        ) temp on temp.user_id = tsu.user_id
        left join t_resident_basic_info trbi on trbi.id = tsu.resident_id
        where
            tsu.bind_device_id = #{deviceId}
    </select>

</mapper>