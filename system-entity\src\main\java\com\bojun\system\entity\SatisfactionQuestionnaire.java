package com.bojun.system.entity;

import java.io.Serializable;

/**
 * Model：呼叫记录
 * Description：呼叫记录实体类
 * Author:刘修纬
 * created：2020年5月11日
 */
public class SatisfactionQuestionnaire implements Serializable {

    private static final long serialVersionUID = -1344026328907379635L;


    private Integer questionnaireId; // 问卷id
    private Integer organizationId; // 机构id
    private String systemId; // 问卷标题ID
    private String title; // 问卷标题
    private String coverImg; // 封面图片
    private String decribe; // 问卷说明
    private Integer isRelaDept; // 是否关联科室  0:否 1：是
    private String deptIds; // 关联科室
    private java.sql.Date validBeginDate; // 有效开始日期
    private java.sql.Date validEndDate; // 有效结束日期
    private Integer status; // 状态  0：未开始  1：已结束 2：进行中  3：已暂停
    private Integer totalScore; // 总分
    private Integer isDelete; // 删除标记  0：否 1：是
    private Integer updateUserId; // 更新人用户id
    private java.sql.Date updateTime; // 更新时间
    private Integer deleteUserId; // 删除人用户id
    private java.sql.Date deleteTime; // 删除时间
    private Integer createUserId; // 创建人用户id
    private java.sql.Date createTime; // 创建时间


    public Integer getQuestionnaireId() {
        return questionnaireId;
    }

    public void setQuestionnaireId(Integer questionnaireId) {
        this.questionnaireId = questionnaireId;
    }


    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }


    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }


    public String getCoverImg() {
        return coverImg;
    }

    public void setCoverImg(String coverImg) {
        this.coverImg = coverImg;
    }


    public String getDecribe() {
        return decribe;
    }

    public void setDecribe(String decribe) {
        this.decribe = decribe;
    }


    public Integer getIsRelaDept() {
        return isRelaDept;
    }

    public void setIsRelaDept(Integer isRelaDept) {
        this.isRelaDept = isRelaDept;
    }


    public String getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(String deptIds) {
        this.deptIds = deptIds;
    }


    public java.sql.Date getValidBeginDate() {
        return validBeginDate;
    }

    public void setValidBeginDate(java.sql.Date validBeginDate) {
        this.validBeginDate = validBeginDate;
    }


    public java.sql.Date getValidEndDate() {
        return validEndDate;
    }

    public void setValidEndDate(java.sql.Date validEndDate) {
        this.validEndDate = validEndDate;
    }


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }


    public Integer getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }


    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }


    public Integer getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }


    public java.sql.Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(java.sql.Date updateTime) {
        this.updateTime = updateTime;
    }


    public Integer getDeleteUserId() {
        return deleteUserId;
    }

    public void setDeleteUserId(Integer deleteUserId) {
        this.deleteUserId = deleteUserId;
    }


    public java.sql.Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(java.sql.Date deleteTime) {
        this.deleteTime = deleteTime;
    }


    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }


    public java.sql.Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(java.sql.Date createTime) {
        this.createTime = createTime;
    }


}
