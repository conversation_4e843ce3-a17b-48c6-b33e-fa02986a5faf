<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.bojun</groupId>
  <artifactId>organization-entity</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <name>organization-entity</name>
  <url>http://maven.apache.org</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
      <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
      <dependency>
          <groupId>org.projectlombok</groupId>
          <artifactId>lombok</artifactId>
          <version>1.18.12</version>
          <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>io.springfox</groupId>
          <artifactId>springfox-swagger2</artifactId>
          <version>2.9.2</version>
      </dependency>
      <!-- https://mvnrepository.com/artifact/com.baomidou/mybatis-plus-generator -->
      <dependency>
          <groupId>com.baomidou</groupId>
          <artifactId>mybatis-plus-generator</artifactId>
          <version>3.3.2</version>
      </dependency>
      <dependency>
          <groupId>com.bojun</groupId>
          <artifactId>employee-entity</artifactId>
          <version>0.0.1-SNAPSHOT</version>
      </dependency>
  </dependencies>
  
  <distributionManagement>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>maven-snapshots</name>
            <url>http://bj-ihealthcare.com:56787/repository/maven-snapshots/</url>
        </snapshotRepository>
  </distributionManagement>
  
  <build>
  	<finalName>organization-entity</finalName>
  	<plugins>
        <!-- java编译插件 -->
        <plugin>
        	<groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
            	<source>1.8</source>
                <target>1.8</target>
                <encoding>UTF-8</encoding>
           	</configuration>
        </plugin>
    </plugins>
  </build>
</project>
