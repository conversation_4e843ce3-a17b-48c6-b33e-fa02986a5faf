/**
 * 
 */
package com.bojun.base.manage.controller.systemDict.vo;

import java.io.Serializable;
import java.util.List;

import com.bojun.system.dto.SystemDictDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：产品管理 
 * Description：查询产品列表返回信息
 * Author：赖水秀 
 * created： 2020年4月27日
 */
@ApiModel(value = "产品树形列表", description = "查询产品树形列表返回信息")
public class SystemDictTreeVO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3833437130055960315L;

	@ApiModelProperty(value = "系统id")
	private String systemId;

	@ApiModelProperty(value = "系统名称")
	private String systemName;

	@ApiModelProperty(value = "是否是移动端(0:移动端，1：web端)")
	private Integer isMobile;
	
	@ApiModelProperty(value = "树形键")
	private String parentId;	//父id
	
	/**
	 * 子级菜单
	 */
	private List<SystemDictTreeVO> children;

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public Integer getIsMobile() {
		return isMobile;
	}

	public void setIsMobile(Integer isMobile) {
		this.isMobile = isMobile;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public List<SystemDictTreeVO> getChildren() {
		return children;
	}

	public void setChildren(List<SystemDictTreeVO> children) {
		this.children = children;
	}
	
	
	
}
