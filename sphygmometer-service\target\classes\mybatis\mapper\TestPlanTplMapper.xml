<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.TestPlanTplMapper">

    <resultMap type="com.bojun.sphygmometer.dto.TestPlanTplDTO" id="TestPlanTplDTOResult">
        <result property="id" column="id"/>
        <result property="tplName" column="tpl_name"/>
        <result property="testFrequency" column="test_frequency"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="createUserName" column="create_user_name"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateUserId" column="update_user_id"/>
        <result property="updateUserName" column="update_user_name"/>
        <result property="isDelete" column="is_delete"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="tagCode" column="tag_code"/>
    </resultMap>

    <sql id="selectTestPlanTpl">
        select id,
               tpl_name,
               test_frequency,
               remark,
               create_time,
               create_user_id,
               create_user_name,
               update_time,
               update_user_id,
               update_user_name,
               is_delete,
               enable_status,
               tag_code
        from t_test_plan_tpl
    </sql>

    <select id="selectTestPlanTplById" parameterType="int" resultMap="TestPlanTplDTOResult">
        <include refid="selectTestPlanTpl"/>
        where
        id = #{id}
    </select>

    <select id="selectTestPlanTplList" parameterType="com.bojun.sphygmometer.dto.TestPlanTplDTO"
            resultMap="TestPlanTplDTOResult">
        <include refid="selectTestPlanTpl"/>
        where is_delete = 0
        <if test="id != null ">and id = #{id}</if>
        <if test="tagCode != null ">and tag_code = #{tagCode}</if>
        <if test="tplName != null  and tplName != ''">and tpl_name LIKE CONCAT('%',#{tplName},'%')</if>
        <if test="testFrequency != null  and testFrequency != ''">and test_frequency = #{testFrequency}</if>
        <if test="remark != null  and remark != ''">and remark = #{remark}</if>
        <if test="createTime != null ">and create_time = #{createTime}</if>
        <if test="enableStatus != null ">and enable_status = #{enableStatus}</if>
        <if test="createUserId != null ">and create_user_id = #{createUserId}</if>
        <if test="createUserName != null  and createUserName != ''">and create_user_name = #{createUserName}</if>
        <if test="updateTime != null ">and update_time = #{updateTime}</if>
        <if test="updateUserId != null ">and update_user_id = #{updateUserId}</if>
        <if test="updateUserName != null  and updateUserName != ''">and update_user_name = #{updateUserName}</if>
        <if test="startTime != null and endTime != null ">and update_time between #{startTime} and
            #{endTime}
        </if>
        order by update_time desc
    </select>

</mapper>