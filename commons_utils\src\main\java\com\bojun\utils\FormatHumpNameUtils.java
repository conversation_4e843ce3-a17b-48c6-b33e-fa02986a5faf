/**
 * 
 */
package com.bojun.utils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Model： 将Map中的key由下划线转换为驼峰
 * Description： 将Map中的key由下划线转换为驼峰
 * Author:李欣颖 
 * created：2020年3月19日
 **/
public class FormatHumpNameUtils {

	/**
	 * 将Map中的key由下划线转换为驼峰
	 *
	 * @param map
	 * @return
	 */
	public static Map<String, Object> formatHumpName(Map<String, Object> map) {
		Map<String, Object> newMap = new HashMap<String, Object>();
		Iterator<Map.Entry<String, Object>> it = map.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry<String, Object> entry = it.next();
			String key = entry.getKey();
			String newKey = toFormatCol(key);
			newMap.put(newKey, entry.getValue());
		}
		return newMap;
	}

	public static String toFormatCol(String colName) {
		StringBuilder sb = new StringBuilder();
		String[] str = colName.toLowerCase().split("_");
		int i = 0;
		for (String s : str) {
			if (s.length() == 1) {
				s = s.toUpperCase();
			}
			i++;
			if (i == 1) {
				sb.append(s);
				continue;
			}
			if (s.length() > 0) {
				sb.append(s.substring(0, 1).toUpperCase());
				sb.append(s.substring(1));
			}
		}
		return sb.toString();
	}

}
