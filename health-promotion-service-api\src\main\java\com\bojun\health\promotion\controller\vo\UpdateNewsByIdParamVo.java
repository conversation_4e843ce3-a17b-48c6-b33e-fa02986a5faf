package com.bojun.health.promotion.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/7 14:42
 */
@ApiModel(value = "修改资讯参数", description = "修改单查资讯参数")
public class UpdateNewsByIdParamVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "资讯Id")
    private int newsId;
    @ApiModelProperty(value = "机构Id")
    private int organizationId;
    @ApiModelProperty(value = "科室Id")
    private int deptId;
    @ApiModelProperty(value = "标题")
    private String title;
    @ApiModelProperty(value = "文章封面")
    private String coverImage;
    @ApiModelProperty(value = "文件")
    private String fileName;
    @ApiModelProperty(value = "分类排序")
    private int showIndex;
    @ApiModelProperty(value = "是否在首页")
    private int isHome;
    @ApiModelProperty(value = "首页排序")
    private int homeIndex;
    @ApiModelProperty(value = "文章")
    private String content;
    @ApiModelProperty(value = "发布状态  1:审核通过后立即发布   2:手动发布")
    private int publishType;
    @ApiModelProperty(value = "状态  1:已发布  2:待发布  3：已下架")
    private int status;
    @ApiModelProperty(value = "审核状态  1：审核通过  2：待审核  3：审核未通过")
    private int authStatus;
    @ApiModelProperty(value = "话题ID", example = "话题1，话题2")
    private String topicId;
    @ApiModelProperty(value = "重新发送", example = "resubmit")
    private String type;
    @ApiModelProperty(value = "资讯类型1文章2视频3其他", example = "1")
    private Integer newsType;
    @ApiModelProperty(value = "资讯应用平台1患者app2医生app3全部", example = "1")
    private Integer showType;
    @ApiModelProperty(value = "资讯来源", example = "百度百科")
    private String newsSource;
    @ApiModelProperty(value = "1健康资讯2医学资讯")
    private Integer sort;
    public String getTopicId() {
        return topicId;
    }

    public void setTopicId(String topicId) {
        this.topicId = topicId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(int authStatus) {
        this.authStatus = authStatus;
    }


    public int getNewsId() {
        return newsId;
    }

    public void setNewsId(int newsId) {
        this.newsId = newsId;
    }

    public int getDeptId() {
        return deptId;
    }

    public void setDeptId(int deptId) {
        this.deptId = deptId;
    }

    public int getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(int organizationId) {
        this.organizationId = organizationId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCoverImage() {
        return coverImage;
    }

    public void setCoverImage(String coverImage) {
        this.coverImage = coverImage;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getShowIndex() {
        return showIndex;
    }

    public void setShowIndex(int showIndex) {
        this.showIndex = showIndex;
    }

    public int getIsHome() {
        return isHome;
    }

    public void setIsHome(int isHome) {
        this.isHome = isHome;
    }

    public int getHomeIndex() {
        return homeIndex;
    }

    public void setHomeIndex(int homeIndex) {
        this.homeIndex = homeIndex;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getPublishType() {
        return publishType;
    }

    public void setPublishType(int publishType) {
        this.publishType = publishType;
    }

	public Integer getNewsType() {
		return newsType;
	}

	public void setNewsType(Integer newsType) {
		this.newsType = newsType;
	}

	public Integer getShowType() {
		return showType;
	}

	public void setShowType(Integer showType) {
		this.showType = showType;
	}

	public String getNewsSource() {
		return newsSource;
	}

	public void setNewsSource(String newsSource) {
		this.newsSource = newsSource;
	}

	public Integer getSort() {
		return sort;
	}

	public void setSort(Integer sort) {
		this.sort = sort;
	}
    
    
}
