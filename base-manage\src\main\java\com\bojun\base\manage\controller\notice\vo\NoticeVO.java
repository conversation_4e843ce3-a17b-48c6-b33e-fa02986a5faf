package com.bojun.base.manage.controller.notice.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/24 17:24
 */
@ApiModel(value = "通知通告", description = "通知通告")
public class NoticeVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;

    @ApiModelProperty(value="消息Id")
    private String noticeId;
    @ApiModelProperty(value="推送内容")
    private String noticeContent;
    @ApiModelProperty(value="通知类型")
    private String noticeTypeId;
    @ApiModelProperty(value="开始时间")
    private Date startTime;
    @ApiModelProperty(value="结束时间")
    private Date endTime;
    @ApiModelProperty(value = "推送产品", example = "基础配置，医生app")
    private String synchronizationPlatform;
    @ApiModelProperty(value="页数")
    private int pageNum;
    @ApiModelProperty(value="每页条数")
    private int everyPage;
    @ApiModelProperty(value="总记录数")
    private Integer totalCount;
    @ApiModelProperty(value="通知类型名称")
    private String noticeTypeName;
    @ApiModelProperty(value="科室名称")
    private String deptName;
    @ApiModelProperty(value="状态")
    private int status;
    @ApiModelProperty(value = "是否删除，删除时传1")
    private int isDelete;
    @ApiModelProperty(value = "科室Id")
    private String deptId;
    @ApiModelProperty(value = "病区Id")
    private String wardId;
    @ApiModelProperty(value = "病区名称")
    private String wardName;
    @ApiModelProperty(value = "推送对象")
    private List<ObjectVO> objectVO;
    @ApiModelProperty(value = "是否立即发送 1是  2否")
    private Integer isImmediately; // 是否立即发送
    @ApiModelProperty(value = "机构名称")
    private String organizationName;
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;
    @ApiModelProperty(value = "推送对象")
    private String publishObject;
    @ApiModelProperty(value = "待发布定时时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date timingTime;
    @ApiModelProperty(value = "序列号")
    private Integer number; // 序列号
    @ApiModelProperty(value = "标题")
    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public void setIsImmediately(Integer isImmediately) {
        this.isImmediately = isImmediately;
    }

    public Date getTimingTime() {
        return timingTime;
    }

    public void setTimingTime(Date timingTime) {
        this.timingTime = timingTime;
    }

    public String getPublishObject() {
        return publishObject;
    }

    public void setPublishObject(String publishObject) {
        this.publishObject = publishObject;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public int getIsImmediately() {
        return isImmediately;
    }

    public void setIsImmediately(int isImmediately) {
        this.isImmediately = isImmediately;
    }

    public List<ObjectVO> getObjectVO() {
        return objectVO;
    }

    public void setObjectVO(List<ObjectVO> objectVO) {
        this.objectVO = objectVO;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getWardId() {
        return wardId;
    }

    public void setWardId(String wardId) {
        this.wardId = wardId;
    }

    public String getWardName() {
        return wardName;
    }

    public void setWardName(String wardName) {
        this.wardName = wardName;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getNoticeTypeName() {
        return noticeTypeName;
    }

    public void setNoticeTypeName(String noticeTypeName) {
        this.noticeTypeName = noticeTypeName;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(String noticeId) {
        this.noticeId = noticeId;
    }

    public String getNoticeContent() {
        return noticeContent;
    }

    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    public String getNoticeTypeId() {
        return noticeTypeId;
    }

    public void setNoticeTypeId(String noticeTypeId) {
        this.noticeTypeId = noticeTypeId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getSynchronizationPlatform() {
        return synchronizationPlatform;
    }

    public void setSynchronizationPlatform(String synchronizationPlatform) {
        this.synchronizationPlatform = synchronizationPlatform;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getEveryPage() {
        return everyPage;
    }

    public void setEveryPage(int everyPage) {
        this.everyPage = everyPage;
    }
}
