/**
 * 
 */
package com.bojun;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.circuitbreaker.EnableCircuitBreaker;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

/**
*Model：基础配置管理微服务入口
*Description：基础配置管理微服务入口
*Author:段德鹏
*created：2020年4月23日
**/
@EnableFeignClients
@EnableCircuitBreaker
@EnableEurekaClient
@EnableWebMvc
@SpringBootApplication
public class BaseManageApplication {
	
	public static void main(String[] args) {
		SpringApplication.run(BaseManageApplication.class, args);
	}

}
