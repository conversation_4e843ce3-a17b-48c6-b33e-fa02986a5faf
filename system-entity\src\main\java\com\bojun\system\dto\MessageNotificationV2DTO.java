package com.bojun.system.dto;

import com.bojun.system.entity.MessageNotificationV2;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 消息通知（站内）对象 t_message_notification
 * 
 * <AUTHOR>
 * @date 2021-05-14 19:19:04
 */
@ApiModel(value = "MessageNotificationDTO对象")
@Data
public class MessageNotificationV2DTO extends MessageNotificationV2
{
    @ApiModelProperty(value = "当前页码", example = "")
    private Integer pageNum;
    @ApiModelProperty(value = "当前页显示数量", example = "")
    private Integer everyPage;

    @ApiModelProperty(value = "系统ID", example = "")
    private String systemId;

    @ApiModelProperty(value = "起期", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "止期", example = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    @ApiModelProperty(value = "机构集合", example = "")
    private List<Integer> organizationIdList;

    @ApiModelProperty(value = "机构id", example = "")
    private Integer organizationId;
}
