
package com.bojun.system.dto;

import com.bojun.system.entity.ManageRoleDept;
import com.bojun.system.entity.ManageRoleOrgan;

import java.util.List;
import java.util.Map;

/**
 * 
 * Model：角色机构关联管理 Description：角色机构关联DTO Author:刘俊 created：2020年4月30日
 */
public class ManageRoleOrganDTO extends ManageRoleOrgan {

	private static final long serialVersionUID = -1344026328907379635L;

	// 角色关联部门
	private List<ManageRoleDept> roleDeptList;
	private String organizationName = "";
	private List<Map> getRoleDeptDet;
	private Integer organizationIds; // 机构id
	private String townCode; // 乡镇代码
	private String villageCode; // 村居委会名称
	private String townName; // 乡镇名称
	private String villageName; // 村居委会名称
	private Integer parentId;

	private Integer hasAuth;//是否有权限(全选半选之分)0无(半选)1有(全选)

	@Override
	public Integer getHasAuth() {
		return hasAuth;
	}

	@Override
	public void setHasAuth(Integer hasAuth) {
		this.hasAuth = hasAuth;
	}

	public String getTownCode() {
		return townCode;
	}

	public void setTownCode(String townCode) {
		this.townCode = townCode;
	}

	public String getVillageCode() {
		return villageCode;
	}

	public void setVillageCode(String villageCode) {
		this.villageCode = villageCode;
	}

	public String getTownName() {
		return townName;
	}

	public void setTownName(String townName) {
		this.townName = townName;
	}

	public String getVillageName() {
		return villageName;
	}

	public void setVillageName(String villageName) {
		this.villageName = villageName;
	}

	public Integer getOrganizationIds() {
		return organizationIds;
	}

	public void setOrganizationIds(Integer organizationIds) {
		this.organizationIds = organizationIds;
	}

	public List<Map> getGetRoleDeptDet() {
		return getRoleDeptDet;
	}

	public void setGetRoleDeptDet(List<Map> getRoleDeptDet) {
		this.getRoleDeptDet = getRoleDeptDet;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public List<ManageRoleDept> getRoleDeptList() {
		return roleDeptList;
	}

	public void setRoleDeptList(List<ManageRoleDept> roleDeptList) {
		this.roleDeptList = roleDeptList;
	}

	public Integer getParentId() {
		return parentId;
	}

	public void setParentId(Integer parentId) {
		this.parentId = parentId;
	}
}
