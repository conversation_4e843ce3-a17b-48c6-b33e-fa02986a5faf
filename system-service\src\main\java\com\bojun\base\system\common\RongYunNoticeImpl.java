package com.bojun.base.system.common;

import com.bojun.base.system.mapper.RongNoticeMapper;
import com.bojun.commons.rong.RongUtils;
import com.bojun.system.dto.RongYunDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Model： 融云推送
 * @Description: 融云推送
 * @since 2020-12-04
 */
@Service
public class RongYunNoticeImpl {
    @Autowired
    private RongNoticeMapper rongNoticeMapper;


    /**
     * @description: 推送医生端
     * @author: 赖允翔
     * @date: 2020/12/4
     * @Param:
     * @return:
     */
    public Boolean doctorPushNotice(RongYunDTO rongYunDTO) {
        List<String> doctorIds = rongNoticeMapper.getDoctorAppUserIds();
        String[] doctorId = doctorIds.toArray(new String[doctorIds.size()]);
        return RongUtils.pushSystemMessage(rongYunDTO.getContent(), doctorId, rongYunDTO.getExtras());
    }

    /**
     * @description: 推送患者端
     * @author: 赖允翔
     * @date: 2020/12/4
     * @Param:
     * @return:
     */
    public Boolean patientPushNotice(RongYunDTO rongYunDTO) {
        List<String> patientIds = rongNoticeMapper.patientPushNotice();
        String[] patientId = patientIds.toArray(new String[patientIds.size()]);
        return RongUtils.pushSystemMessage(rongYunDTO.getContent(), patientId, rongYunDTO.getExtras());
    }
}
