/**
 * 
 */
package com.bojun.base.manage.aspect;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.bojun.base.manage.api.system.ISystemOperationService;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.ip.IPUtils;
import com.bojun.log.SystemLog;
import com.bojun.response.Results;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.entity.OperationLog;
import com.bojun.system.enums.SystemDictEnums;

/**
*Model：系统日志切面
*Description：系统日志切面
*Author:段德鹏
*created：2020年4月24日
**/
@Aspect
@Component
@SuppressWarnings("all")
public class SystemLogAspect {
	
	private static Logger logger = LoggerFactory.getLogger(SystemLogAspect.class);
	
	private static final String PLATFORM_NAME = "配置管理系统";
	
	@Autowired
    private ISystemOperationService systemOperationService;
	
	//Controller层切点
    @Pointcut("@annotation(com.bojun.log.SystemLog)")
    public void controllerAspect(){
    	
    }
    
    /**
     * @Description 前置通知  用于拦截Controller层记录用户的操作
     * <AUTHOR>
     * @param joinPoint
     * @return void
     * created：2020年4月24日
     */
    @Before("controllerAspect()")
    public void doBefore(JoinPoint joinPoint){
        try {
        	HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            //读取request中的用户信息
            ManageUserDTO manageUser = (ManageUserDTO) request.getAttribute("manageUser");
            String ip = IPUtils.getClientIP(request);
            //获取方法日志注解
            MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
            Method method = methodSignature.getMethod();
            SystemLog systemLog = method.getAnnotation(SystemLog.class);
            //封装操作日志参数
            OperationLog operationLog = new OperationLog();
            operationLog.setUserId(manageUser.getUserId());
            operationLog.setSystemId(SystemDictEnums.BASE_CONFIG_MANAGE.getSystemId());
            operationLog.setOperationType(systemLog.operationType());
            operationLog.setRequestAction(systemLog.action());
            operationLog.setIsException(0);
            operationLog.setOperationContent(systemLog.description());
            operationLog.setIpAddress(ip);
            systemOperationService.addSystemLog(operationLog);
        } catch (Exception e) {
            logger.error("doBefore", e);
        }
    }
    
    /**
     * @Description 返回通知  用于拦截Controller层返回结果
     * <AUTHOR>
     * @param joinPoint
     * @return void
     * created：2020年06月05日
     */
    @AfterReturning(returning = "results", pointcut = "controllerAspect()")
	public void doAfter(JoinPoint joinPoint, Results results) throws Throwable {
    	try {
    		if (ResponseCodeEnum.UNREASONABLE_REQUEST.getCode().intValue() == results.getCode().intValue() 
    				|| ResponseCodeEnum.EXCEPTION_REQUEST.getCode().intValue() == results.getCode().intValue()) {
    			HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
                //读取request中的用户信息
                ManageUserDTO manageUser = (ManageUserDTO) request.getAttribute("manageUser");
                String ip = IPUtils.getClientIP(request);
            	//获取方法日志注解
                MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
                Method method = methodSignature.getMethod();
                SystemLog systemLog = method.getAnnotation(SystemLog.class);
                //封装操作日志参数
                OperationLog operationLog = new OperationLog();
                operationLog.setUserId(manageUser.getUserId());
                operationLog.setSystemId(SystemDictEnums.BASE_CONFIG_MANAGE.getSystemId());
                operationLog.setOperationType(systemLog.operationType());
                operationLog.setRequestAction(systemLog.action());
                operationLog.setIsException(1);
                operationLog.setExceptionMsg(results.getMsg());
                operationLog.setOperationContent(systemLog.description());
                operationLog.setIpAddress(ip);
                systemOperationService.addSystemLog(operationLog);
    		}
    	} catch (Exception e) {
    		logger.error("doAfter", e);
    	}
	}

}
