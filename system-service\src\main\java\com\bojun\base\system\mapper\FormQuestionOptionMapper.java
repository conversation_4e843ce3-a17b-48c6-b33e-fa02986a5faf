
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.FormQuestionOptionDTO;



/**
 * 
*Model：题目选项信息表
*Description：题目选项信息表
*Author:李欣颖
*created：2020年5月7日
 */
@Mapper
public interface FormQuestionOptionMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	int queryFormQuestionOptionCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询题目选项信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionOptionDTO>
     * created：2020年5月7日
	 */
	List<FormQuestionOptionDTO> getFormQuestionOption(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer addFormQuestionOption(FormQuestionOptionDTO formQuestionOptionDTO);

	/**
	 * 
	 * @Description 删除题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer deleteFormQuestionOption(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer updateFormQuestionOption(FormQuestionOptionDTO formQuestionOptionDTO);

	/**
	 * 
	 * @Description 查询单个题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionOptionDTO
	 * created：2020年5月7日
	 */
	FormQuestionOptionDTO getFormQuestionOptionById(Map<String, Object> mapPara);

	List<FormQuestionOptionDTO> getFormQuestionItemOption(Map<String, Object> paramsMap);


	
}
