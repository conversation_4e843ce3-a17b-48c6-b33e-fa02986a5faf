package com.bojun.export;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

public class TxtUtil {
	private static File file;
	private static FileWriter fw;
	private static BufferedWriter bw;
	
	public static void createFile(String fullFileName) throws IOException{
		file = new File(fullFileName);
		if(file.exists()){
			file.createNewFile();
		}
		fw = new FileWriter(file);
		bw = new BufferedWriter(fw);
	}
	
	public static void writeToFile(String text) throws IOException{
		bw.write(text);
	}
	
	public static void writeToFileLn(String text) throws IOException{
		bw.write(text);
		bw.newLine();
	}
	
	public static void closeFile() throws IOException{
		bw.flush();
		bw.close();
	}
}
