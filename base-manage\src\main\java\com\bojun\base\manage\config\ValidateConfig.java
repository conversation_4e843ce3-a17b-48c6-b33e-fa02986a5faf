/**
 * 
 */
package com.bojun.base.manage.config;

import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;

import org.hibernate.validator.HibernateValidator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
*Model：校验器配置
*Description：校验器配置
*Author:段德鹏
*created：2020年4月15日
**/
@Configuration
public class ValidateConfig {
	
	@Bean
	public Validator validator(){
		ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
				.configure()
				//开启快速校验--默认校验所有参数，false校验全部
				.addProperty( "hibernate.validator.fail_fast", "true" )
	            .buildValidatorFactory();
	    Validator validator = validatorFactory.getValidator();
	    return validator;
	 }

}
