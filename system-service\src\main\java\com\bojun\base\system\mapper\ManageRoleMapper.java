
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.ManageRoleDTO;
import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.entity.ManageRole;
import com.bojun.system.entity.ManageRoleDept;
import com.bojun.system.entity.ManageRoleMenu;
import com.bojun.system.entity.ManageRoleOrgan;
import com.bojun.system.entity.ManageRoleSystem;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Param;

/**
*Model：角色信息DAO
*Description：角色信息DAO
*Author:刘俊
*created：2020年4月24日
 */
@Mapper
public interface ManageRoleMapper {

	
	/**
	 * @Description 新增角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int addManageRole(ManageRole manageRole);
	
	/**
	 * @Description 修改角色
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int updateManageRole(ManageRole manageRole);
	/**
	 * @Description 查询角色列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	Page<ManageRoleDTO> getManageRoleList(ManageRoleDTO manageRoleDTO);
	
	/**
	 * 
	 * @Description 获取角色涉及机构名称拼接
	 * <AUTHOR>
	 * @param map
	 * @return
	 * List<ManageRoleOrganDTO>
	 * 2020年9月16日
	 */
	String getRoleOrgNameConcat(Map<String,Object> map);
	
	/**
	 * 
	 * @Description 获取角色涉及部门、病区名称拼接
	 * <AUTHOR>
	 * @param map
	 * @return
	 * List<ManageRoleOrganDTO>
	 * 2020年9月16日
	 */
	ManageRoleDept getRoleDeptWardConcat(Map<String,Object> map);
	
	/**
	 * @Description 查询角色部门列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	List<ManageRoleOrganDTO> getRoleOrganDet(Map<String,Object> map);
	
	/**
	 * @Description 查询角色部门列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	List<ManageRoleOrganDTO> getRoleOrganByOrganId(Map<String,Object> map);
	
	/**
	 * @Description 查询角色部门列表ByDeptId
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	List<ManageRoleDept> getRoleDeptByDeptId(ManageRoleDept manageRoleDept);
	/**
	 * @Description 查询角色部门列表
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	List<ManageRoleDept> getAllRoleDeptByRoleId(@Param("roleId") String roleId);

	/**
	 * @Description 新增角色产品关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int addManageRoleSystem(ManageRoleSystem manageRole);
	/**
	 * @Description 新增角色菜单关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int addManageRoleMenu(ManageRoleMenu manageRoleMenu);
	/**
	 * @Description 新增角色机构关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int addManageRoleOrgan(ManageRoleOrgan manageRoleOrgan);
	/**
	 * @Description 新增角色部门关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int addManageRoleDept(ManageRoleDept manageRoleDept);
	/**
	 * @Description 删除角色产品关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int deleteManageRoleSystem(ManageRole manageRole);
	/**
	 * @Description 删除角色菜单关联 
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int deleteManageRoleMenu(ManageRole manageRole);
	/**
	 * @Description 删除角色机构关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int deleteManageRoleOrgan(ManageRole manageRole);
	/**
	 * @Description 删除角色部门关联
	 * <AUTHOR>
	 * @param ManageRole
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int deleteManageRoleDept(ManageRole manageRole);
	
	/**
	 * @Description 查询角色
	 * <AUTHOR>
	 * @param ManageRoleDTO
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	ManageRoleDTO getManageRoleOne(ManageRoleDTO manageRoleDTO);
	
	/**
	 * @Description 查询角色部门底层id
	 * <AUTHOR>
	 * @param 
	 * @return
	 * List<ManageRoleDTO>
	 * 2020年4月27日
	 */
	 List<Map<String, Object>> roleOrganTreeList (Map<String, Object> map);
	
}
