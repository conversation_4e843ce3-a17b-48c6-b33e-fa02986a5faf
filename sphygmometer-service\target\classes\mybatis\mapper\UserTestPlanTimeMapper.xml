<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.UserTestPlanTimeMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.UserTestPlanTimeDTO" id="UserTestPlanTimeDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userTestPlanId"    column="user_test_plan_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="createUserId"    column="create_user_id"    />
        <result property="createUserName"    column="create_user_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateUserId"    column="update_user_id"    />
        <result property="updateUserName"    column="update_user_name"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <sql id="selectUserTestPlanTime">
    	select
	        id,
	        user_id,
	        user_test_plan_id,
	        start_time,
	        end_time,
	        create_time,
	        create_user_id,
	        create_user_name,
	        update_time,
	        update_user_id,
	        update_user_name,
	        is_delete
		from 
        	t_user_test_plan_time
    </sql>

    <select id="selectUserTestPlanTimeById" parameterType="int" resultMap="UserTestPlanTimeDTOResult">
		<include refid="selectUserTestPlanTime"/>
		where 
        	id = #{id}
    </select>

    <select id="selectUserTestPlanTimeList" parameterType="com.bojun.sphygmometer.dto.UserTestPlanTimeDTO" resultMap="UserTestPlanTimeDTOResult">
        <include refid="selectUserTestPlanTime"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="userTestPlanId != null "> and user_test_plan_id = #{userTestPlanId}</if>
		<if test="startTime != null  and startTime != ''"> and start_time = #{startTime}</if>
		<if test="endTime != null  and endTime != ''"> and end_time = #{endTime}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
		<if test="createUserId != null "> and create_user_id = #{createUserId}</if>
		<if test="createUserName != null  and createUserName != ''"> and create_user_name = #{createUserName}</if>
		<if test="updateTime != null "> and update_time = #{updateTime}</if>
		<if test="updateUserId != null "> and update_user_id = #{updateUserId}</if>
		<if test="updateUserName != null  and updateUserName != ''"> and update_user_name = #{updateUserName}</if>
		<if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>

</mapper>