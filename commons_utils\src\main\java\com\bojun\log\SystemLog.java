package com.bojun.log;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 
*Model：定义系统日志注解
*Description：定义系统日志注解
*Author:段德鹏
*created：2020年4月23日
 */
//方法声明
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SystemLog {
	
	/**
     * 请求action
     *
     */
	String action();
	
	/**
     * 接口类型1：新增  2：修改  3：删除   4：登陆登出
     */
	int operationType();
	
	/**
     * 接口描述
     */
	String description() default "";

}
