package com.bojun.system.enums;
/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年11月26日
*/
public enum SystemTypeEnums {
	
	config(900001, "配置管理"),
    defend_supervise(900005, "运维监管"),
	expand_server(900008, "拓展服务");
	
	private Integer systemTypeId;//系统类型id
	
	private String  systemTypeName;

	private SystemTypeEnums(Integer systemTypeId, String systemTypeName) {
		this.systemTypeId = systemTypeId;
		this.systemTypeName = systemTypeName;
	}

	public Integer getSystemTypeId() {
		return systemTypeId;
	}

	public void setSystemTypeId(Integer systemTypeId) {
		this.systemTypeId = systemTypeId;
	}

	public String getSystemTypeName() {
		return systemTypeName;
	}

	public void setSystemTypeName(String systemTypeName) {
		this.systemTypeName = systemTypeName;
	}
	
}

