package com.bojun.utils;


import java.security.MessageDigest;  
  
/** 
 * MD5加密工具类 
 */  
public abstract class MD5Util {
    
    // 16进制的字符数组 
  private final static String[] strDigits ={"0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F"};

  private static String byteToArrayString(byte bByte){
      int iRet=bByte;
      if(iRet<0)
      iRet+=256;
      int iD1=iRet/16;
      int iD2=iRet%16;
      return strDigits[iD1]+strDigits[iD2];
  }
 
    //转换字符数组为16进制字符串
    private static String byteToString(byte[] bByte){
        StringBuffer sBuffer=new StringBuffer();
        for (int i = 0; i < bByte.length; i++) 
            sBuffer.append(byteToArrayString(bByte[i]));
        return sBuffer.toString();
    }
    
    public static String getMD5code(String strObj){
        String resultString =null;
        try {
            resultString=new String(strObj);
            //信息摘要是安全的单向哈希函数，它接收任意大小的数据，并输出固定长度的哈希值。  
            MessageDigest md= MessageDigest.getInstance("MD5");
            //使用平台的默认字符集将此 String 编码为 byte序列，并将结果存储到一个新的 byte数组中  
            resultString=byteToString(md.digest(strObj.getBytes()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return resultString;
    }
    public static void main(String[] args) {
        System.out.println(getMD5code("123456"));
    }

    	   private static String byteArrayToHexString(byte b[]) {
    	      StringBuffer resultSb = new StringBuffer();
    	      for (int i = 0; i < b.length; i++)
    	         resultSb.append(byteToHexString(b[i]));

    	      return resultSb.toString();
    	   }

    	   private static String byteToHexString(byte b) {
    	      int n = b;
    	      if (n < 0)
    	         n += 256;
    	      int d1 = n / 16;
    	      int d2 = n % 16;
    	      return hexDigits[d1] + hexDigits[d2];
    	   }

    	   public static String MD5Encode(String origin, String charsetname) {
    	      String resultString = null;
    	      try {
    	         resultString = new String(origin);
    	         MessageDigest md = MessageDigest.getInstance("MD5");
    	         if (charsetname == null || "".equals(charsetname))
    	            resultString = byteArrayToHexString(md.digest(resultString
    	                  .getBytes()));
    	         else
    	            resultString = byteArrayToHexString(md.digest(resultString
    	                  .getBytes(charsetname)));
    	      } catch (Exception exception) {
    	      }
    	      return resultString;
    	   }

    	   private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5",
    	         "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };

}
