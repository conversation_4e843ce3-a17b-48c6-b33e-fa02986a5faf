package com.bojun.sphygmometer.wxapp.controller.user;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bojun.author.AuthAnnotation;
import com.bojun.common.controller.BaseController;
import com.bojun.common.util.LoginUserThreadLocal;
import com.bojun.commons.redis.utils.RedisUtil;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.page.Results;
import com.bojun.sphygmometer.api.WxMiniAppUserLoginFeignClient;
import com.bojun.sphygmometer.dto.SphygmometerUserDTO;
import com.bojun.sphygmometer.wxapp.common.Constants;
import com.bojun.sphygmometer.wxapp.controller.user.vo.CommonAttrVO;
import com.bojun.sphygmometer.wxapp.controller.user.vo.MiniAppLoginResultVO;
import com.bojun.sphygmometer.wxapp.controller.user.vo.SphygmometerUserLoginVO;
import com.bojun.sphygmometer.wxapp.controller.user.vo.param.MiniAppLoginParamVO;
import com.bojun.sphygmometer.wxapp.controller.user.vo.param.MiniAppSilentLoginParamVO;
import com.bojun.utils.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;
import java.util.Optional;

/**
 * @ClassName LoginController
 * <AUTHOR>
 * @Date 2021/5/28 19:01
 * @Description LoginController
 * @Version 1.0
 */
@Slf4j
@Api(tags = {"微信小程序登录相关接口(严峡华)"})
@RestController
@RequestMapping(LoginController.BASE_URL)
public class LoginController extends BaseController {

    public static final String BASE_URL = "/auth";

    @Autowired
    private WxMiniAppUserLoginFeignClient wxMiniAppUserLoginFeignClient;

    @Autowired
    private WxMaService wxService;

    @Autowired
    private RedisUtil redisUtil;
    @Value("${file.prefix.url}")
    private String filePrefixUrl;

    @Value("${news.is.enabled}")
    private String newsIsEnabled;

    //超时生效（单位：秒）
    public final static int EXPIRE_SECOND_60 = 60;
    //超时生效（单位：秒）
    public final static int EXPIRE_SECOND_300 = 600;//10分钟

    /**
     * 获取公共的属性
     */
    @ApiOperation("获取公共的属性")
    @GetMapping(value = "/getCommonAttr")
    @AuthAnnotation(action = LoginController.BASE_URL + "/getCommonAttr")
    public Results<CommonAttrVO> getCommonAttr(){
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setFilePrefixUrl(filePrefixUrl);
        return Results.data(commonAttrVO);
    }


    /**
     * 授权登录
     * @param miniAppLoginVo
     * @return
     * @throws WxErrorException
     */
    @PostMapping("/loginByAuth")
    @ApiOperation("授权登录(刘文)")
    public Results miniAppLoginByAuth(@RequestBody MiniAppLoginParamVO miniAppLoginVo) throws Exception {
        if (StringUtil.isEmpty(miniAppLoginVo.getAppId())) {
            return Results.fail("appid为空");
        }
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(miniAppLoginVo, SphygmometerUserDTO.class);
        Results<SphygmometerUserDTO> result = wxMiniAppUserLoginFeignClient.loginByAuth(sphygmometerUserDTO);
        log.info("--------result--------:"+ JSONObject.toJSONString(result));
        if(null != result && result.getCode() != 200){
            return result;
        }
        MiniAppLoginResultVO miniAppLoginResultVO = BeanUtil.deepCopyProperties(result.getData(), MiniAppLoginResultVO.class);
        ssoLogin(null, miniAppLoginResultVO);
        redisUtil.set(Constants.WX_OPENID_KEY + miniAppLoginResultVO.getWxappOpenId(), JSON.toJSONString(miniAppLoginResultVO));
        return Results.data(miniAppLoginResultVO);
    }


    /**
     * 静默授权
     *
     * @param miniAppSilentLoginVo
     * @return
     * @throws WxErrorException
     */
    @ApiOperation("静默授权(刘文)")
    @PostMapping("/loginBySilent")
    public Results<MiniAppLoginResultVO> miniAppLoginBySilent(@RequestBody MiniAppSilentLoginParamVO miniAppSilentLoginVo){
        if (StringUtil.isEmpty(miniAppSilentLoginVo.getAppId())) {
            return Results.fail("appid为空");
        }
        SphygmometerUserDTO sphygmometerUserDTO = BeanUtil.deepCopyProperties(miniAppSilentLoginVo, SphygmometerUserDTO.class);
        SphygmometerUserDTO result = wxMiniAppUserLoginFeignClient.loginBySilent(sphygmometerUserDTO);
        if(null == result){
            return Results.success("用户未注册");
        }
        MiniAppLoginResultVO miniAppLoginResultVO = BeanUtil.deepCopyProperties(result, MiniAppLoginResultVO.class);
        ssoLogin(null, miniAppLoginResultVO);
        redisUtil.set(Constants.WX_OPENID_KEY + miniAppLoginResultVO.getWxappOpenId(), JSON.toJSONString(miniAppLoginResultVO));
        return Results.data(miniAppLoginResultVO);
    }

    /**
     * 获取微信手机号
     */
    @ApiOperation(value = "获取微信手机号（严峡华）", notes = "获取微信手机号（严峡华）")
    @PostMapping("/getEncryptPhone")
    public Results<String> getEncryptPhone(@RequestParam String encryptedData, @RequestParam String iv) {
        MiniAppLoginResultVO loginUser = LoginUserThreadLocal.getUserInfo();
        String sessionKey = (String) redisUtil.get(Constants.WX_SESSION_KEY + loginUser.getAppOpenId());
        WxMaPhoneNumberInfo wxMaPhoneNumberInfo = wxService.getUserService().getPhoneNoInfo(sessionKey, encryptedData, iv);
        return Results.data(wxMaPhoneNumberInfo.getPhoneNumber());
    }


    /**
     * 生成token
     * @param mobile
     * @param userId
     * @return
     */
    private String getToken(String mobile, Integer userId) {
        return MD5Util.getMD5code(mobile + userId + System.currentTimeMillis());
    }


    /**
     * 单点登录操作
     * @param token，可传null，传null代表新设备登录
     * @param user 用户信息
     * @return
     */
    private MiniAppLoginResultVO ssoLogin(String token, MiniAppLoginResultVO user) {
        if (StringUtils.isBlank(token)) {
            token = this.getToken(user.getMobile(), user.getUserId());
        }
        String appLoginSSOKey = MessageFormat.format(Contants.RK_APPLOGINSSO, user.getUserId());
        if (this.redisUtil.hasKey(appLoginSSOKey)) {
            String oldToken = (String) this.redisUtil.get(appLoginSSOKey);
            //如果不相等，覆盖掉之前的token，并删除之
            if (!token.equals(oldToken)) {
                this.redisUtil.set(appLoginSSOKey, token);
                String appLoginSSOInvalidKey = MessageFormat.format(Contants.RK_APPLOGINSSO_INVALID, oldToken);
                this.redisUtil.set(appLoginSSOInvalidKey, redisUtil.get(oldToken));
                this.redisUtil.del(oldToken);
            }
        } else {
            this.redisUtil.set(appLoginSSOKey, token);
        }
        user.setToken(token);
        user.setFilePrefixUrl(filePrefixUrl);
        redisUtil.set(token, JSON.toJSONString(user));
        return user;
    }

    /**
     * 获取微信手机号
     */
    @ApiOperation(value = "获取小程序知识是否显示", notes = "获取小程序知识是否显示（1：显示，2：不显示）（刘文）")
    @GetMapping("/getNewsIsEnabled")
    public Results<String> getNewsIsEnabled() {
        return Results.data(newsIsEnabled);
    }


}
