package com.bojun.base.system.service.impl;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.FormQuestionMapper;
import com.bojun.base.system.service.IFormQuestionService;
import com.bojun.system.dto.FormQuestionDTO;
import com.github.pagehelper.PageHelper;

/**
 * 
*Model：满意度问卷题目信息表
*Description：满意度问卷题目信息表service
*Author:李欣颖
*created：2020年5月7日
 */
@Service
public class FormQuestionServiceImpl implements IFormQuestionService{

	@Autowired
	FormQuestionMapper formQuestionMapper;
	
	
	/**
	 * 
	 * @Description 查询满意度问卷题目信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionDTO>
	 * created：2020年5月7日
	 */
	public List<FormQuestionDTO> getFormQuestion( Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<FormQuestionDTO> resList = formQuestionMapper.getFormQuestion(mapPara);
		return resList;
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer queryFormQuestionCount( Map<String, Object> paramsMap) {
		return (Integer) formQuestionMapper.queryFormQuestionCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer addFormQuestion(FormQuestionDTO formQuestionDTO) {
		return formQuestionMapper.addFormQuestion(formQuestionDTO);
	}
	/**
	 * 
	 * @Description 删除满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer deleteFormQuestion(Map<String, Object> paramsMap) {

		return formQuestionMapper.deleteFormQuestion(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer updateFormQuestion(FormQuestionDTO formQuestionDTO) {

		return formQuestionMapper.updateFormQuestion(formQuestionDTO);
	}
	/**
	 * 
	 * @Description 查询单个满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionDTO
	 * created：2020年5月7日
	 */
	public FormQuestionDTO getFormQuestionById(Map<String, Object> paramsMap) {

		return formQuestionMapper.getFormQuestionById(paramsMap);
	}

	
	

}