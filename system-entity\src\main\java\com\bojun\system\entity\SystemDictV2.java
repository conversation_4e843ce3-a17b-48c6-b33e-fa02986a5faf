package com.bojun.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 系统（项目）字典信息表对象 t_system_dict_v2
 * 
 * <AUTHOR>
 * @date 2021-06-21 11:37:26
 */
@ApiModel(value = "SystemDictV2对象" , description = "系统（项目）字典信息表")
@Data
@TableName("t_system_dict_v2")
public class SystemDictV2 implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 系统id（编码） */
    @ApiModelProperty(value = "主键ID", example = "")
    @TableId(value = "system_id", type = IdType.ASSIGN_UUID)
    private String systemId;

    /** 系统名称 */
    @ApiModelProperty(value = "系统名称", example = "")
	@TableField("system_name")
    private String systemName;

    /** 系统类型id */
    @ApiModelProperty(value = "系统类型id", example = "")
	@TableField("system_type_id")
    private Integer systemTypeId;

    /** 是否是移动端(0:移动端，1：web端) */
    @ApiModelProperty(value = "是否是移动端(0:移动端，1：web端)", example = "")
	@TableField("is_mobile")
    private Integer isMobile;

    /** 主页URL */
    @ApiModelProperty(value = "主页URL", example = "")
	@TableField("home_url")
    private String homeUrl;

    /** 排序下标 */
    @ApiModelProperty(value = "排序下标", example = "")
	@TableField("show_index")
    private Integer showIndex;

    /** 是否启用  0：否  1:是 */
    @ApiModelProperty(value = "是否启用  0：否  1:是", example = "")
	@TableField("is_enabled")
    private Integer isEnabled;

    /** 图标样式 */
    @ApiModelProperty(value = "图标样式", example = "")
	@TableField("icon_class")
    private String iconClass;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "")
	@TableField("remark")
    private String remark;

    /** 内网URL前缀 */
    @ApiModelProperty(value = "内网URL前缀", example = "")
	@TableField("inner_url_prefix")
    private String innerUrlPrefix;

    /** 外网URL前缀 */
    @ApiModelProperty(value = "外网URL前缀", example = "")
	@TableField("outer_url_prefix")
    private String outerUrlPrefix;
}
