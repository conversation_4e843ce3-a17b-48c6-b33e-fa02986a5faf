package com.bojun.base.system.service.impl;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.FormQuestionAnswerMapper;
import com.bojun.base.system.service.IFormQuestionAnswerService;
import com.bojun.system.dto.FormQuestionAnswerDTO;
import com.github.pagehelper.PageHelper;

/**
 * 
*Model：满意度问卷答题信息表
*Description：满意度问卷答题信息表service
*Author:李欣颖
*created：2020年5月7日
 */
@Service
public class FormQuestionAnswerServiceImpl implements IFormQuestionAnswerService{

	@Autowired
	FormQuestionAnswerMapper formQuestionAnswerMapper;
	
	
	/**
	 * 
	 * @Description 查询满意度问卷答题信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionAnswerDTO>
	 * created：2020年5月7日
	 */
	public List<FormQuestionAnswerDTO> getFormQuestionAnswer( Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<FormQuestionAnswerDTO> resList = formQuestionAnswerMapper.getFormQuestionAnswer(mapPara);
		return resList;
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer queryFormQuestionAnswerCount( Map<String, Object> paramsMap) {
		return (Integer) formQuestionAnswerMapper.queryFormQuestionAnswerCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer addFormQuestionAnswer(FormQuestionAnswerDTO formQuestionAnswerDTO) {
		return formQuestionAnswerMapper.addFormQuestionAnswer(formQuestionAnswerDTO);
	}
	/**
	 * 
	 * @Description 删除满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer deleteFormQuestionAnswer(Map<String, Object> paramsMap) {

		return formQuestionAnswerMapper.deleteFormQuestionAnswer(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer updateFormQuestionAnswer(FormQuestionAnswerDTO formQuestionAnswerDTO) {

		return formQuestionAnswerMapper.updateFormQuestionAnswer(formQuestionAnswerDTO);
	}
	/**
	 * 
	 * @Description 查询单个满意度问卷答题信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionAnswerDTO
	 * created：2020年5月7日
	 */
	public FormQuestionAnswerDTO getFormQuestionAnswerById(Map<String, Object> paramsMap) {

		return formQuestionAnswerMapper.getFormQuestionAnswerById(paramsMap);
	}

	
	

}