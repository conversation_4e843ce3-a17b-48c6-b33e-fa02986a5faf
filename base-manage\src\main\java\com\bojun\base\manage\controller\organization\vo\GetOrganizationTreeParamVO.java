/**
 * 
 */
package com.bojun.base.manage.controller.organization.vo;

import java.io.Serializable;

import javax.validation.constraints.NotEmpty;

import com.bojun.vo.BaseQueryInfoVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：机构管理
 * Description：查询机构树形列表
 * Author：赖水秀
 * created： 2020年5月6日
 */
@ApiModel(value = "查询机构树形列表条件", description = "查询机构树形列表传入的查询条件")
public class GetOrganizationTreeParamVO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 8619066983100128309L;
		
	
	@ApiModelProperty(value="机构分类,不传时请传空串", required = true, example = "11")
	private String organizationClassCode;


	public String getOrganizationClassCode() {
		return organizationClassCode;
	}


	public void setOrganizationClassCode(String organizationClassCode) {
		this.organizationClassCode = organizationClassCode;
	}

	
}
