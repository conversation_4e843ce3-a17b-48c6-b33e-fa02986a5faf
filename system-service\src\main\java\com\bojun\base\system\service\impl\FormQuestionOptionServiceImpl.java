package com.bojun.base.system.service.impl;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.FormQuestionOptionMapper;
import com.bojun.base.system.service.IFormQuestionOptionService;
import com.bojun.system.dto.FormQuestionOptionDTO;
import com.github.pagehelper.PageHelper;

/**
 * 
*Model：题目选项信息表
*Description：题目选项信息表service
*Author:李欣颖
*created：2020年5月7日
 */
@Service
public class FormQuestionOptionServiceImpl implements IFormQuestionOptionService {

	@Autowired
	FormQuestionOptionMapper formQuestionOptionMapper;
	
	
	/**
	 * 
	 * @Description 查询题目选项信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionOptionDTO>
	 * created：2020年5月7日
	 */
	public List<FormQuestionOptionDTO> getFormQuestionOption( Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<FormQuestionOptionDTO> resList = formQuestionOptionMapper.getFormQuestionOption(mapPara);
		return resList;
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer queryFormQuestionOptionCount( Map<String, Object> paramsMap) {
		return (Integer) formQuestionOptionMapper.queryFormQuestionOptionCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer addFormQuestionOption(FormQuestionOptionDTO formQuestionOptionDTO) {
		return formQuestionOptionMapper.addFormQuestionOption(formQuestionOptionDTO);
	}
	/**
	 * 
	 * @Description 删除题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer deleteFormQuestionOption(Map<String, Object> paramsMap) {

		return formQuestionOptionMapper.deleteFormQuestionOption(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	public Integer updateFormQuestionOption(FormQuestionOptionDTO formQuestionOptionDTO) {

		return formQuestionOptionMapper.updateFormQuestionOption(formQuestionOptionDTO);
	}
	/**
	 * 
	 * @Description 查询单个题目选项信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionOptionDTO
	 * created：2020年5月7日
	 */
	public FormQuestionOptionDTO getFormQuestionOptionById(Map<String, Object> paramsMap) {

		return formQuestionOptionMapper.getFormQuestionOptionById(paramsMap);
	}
	@Override
	public List<FormQuestionOptionDTO> getFormQuestionItemOption(Map<String, Object> paramsMap) {
		return formQuestionOptionMapper.getFormQuestionItemOption(paramsMap);
	}

	
	

}