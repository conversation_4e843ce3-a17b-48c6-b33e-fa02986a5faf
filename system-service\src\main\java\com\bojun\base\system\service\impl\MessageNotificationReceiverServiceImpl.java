package com.bojun.base.system.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.bojun.base.system.mapper.MessageNotificationReceiverMapper;
import com.bojun.base.system.service.MessageNotificationReceiverService;
import com.bojun.system.dto.MessageNotificationReceiverDTO;
import com.github.pagehelper.PageHelper;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月29日
*/
@Service
public class MessageNotificationReceiverServiceImpl implements MessageNotificationReceiverService{
	
	
	@Autowired
	MessageNotificationReceiverMapper messageNotificationReceiverMapper;

	/**
	 * 
	 * @Description 查询消息通知接收人信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationReceiverDTO>
	 */
	@RequestMapping(value = "/getMessageNotificationReceiver", method = RequestMethod.POST)
	public List<MessageNotificationReceiverDTO> getMessageNotificationReceiver(
			@RequestBody Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<MessageNotificationReceiverDTO> resList = messageNotificationReceiverMapper
				.getMessageNotificationReceiver(mapPara);
		return resList;
	}

	/**
	 * 
	 * @Description 查询消息通知接收人信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationReceiverDTO>
	 */
	@RequestMapping(value = "/getMessageNotificationReceiverHome", method = RequestMethod.POST)
	public List<MessageNotificationReceiverDTO> getMessageNotificationReceiverHome(
			@RequestBody Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<MessageNotificationReceiverDTO> resList = messageNotificationReceiverMapper
				.getMessageNotificationReceiverHome(mapPara);
		return resList;
	}
	
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	@RequestMapping(value = "/queryMessageNotificationReceiverCount", method = RequestMethod.POST)
	public Integer queryMessageNotificationReceiverCount(@RequestBody Map<String, Object> paramsMap) {
		return (int) messageNotificationReceiverMapper.queryMessageNotificationReceiverCount(paramsMap);
	}
	/**
	 * 
	 * @Description 查询未读总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	@RequestMapping(value = "/queryMessageNotificationReceiverUnreadCount", method = RequestMethod.POST)
	public Integer queryMessageNotificationReceiverUnreadCount(@RequestBody Map<String, Object> paramsMap) {
		return (int) messageNotificationReceiverMapper.queryMessageNotificationReceiverUnreadCount(paramsMap);
	}
	
	/**
	 * 
	 * @Description 新增消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	@RequestMapping(value = "/addMessageNotificationReceiver", method = RequestMethod.POST)
	public Integer addMessageNotificationReceiver(
			@RequestBody MessageNotificationReceiverDTO messageNotificationReceiverDTO) {
		return messageNotificationReceiverMapper.addMessageNotificationReceiver(messageNotificationReceiverDTO);
	}
	@RequestMapping(value = "/addMessageNotificationReceiverList", method = RequestMethod.POST)
	public Integer addMessageNotificationReceiverList(@RequestBody List<MessageNotificationReceiverDTO> list) {
		return messageNotificationReceiverMapper.addMessageNotificationReceiverList(list);
	}
	/**
	 * 
	 * @Description 删除消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	@RequestMapping(value = "/deleteMessageNotificationReceiver", method = RequestMethod.POST)
	public int deleteMessageNotificationReceiver(@RequestBody Map<String, Object> paramsMap) {
		return messageNotificationReceiverMapper.deleteMessageNotificationReceiver(paramsMap);
	}

	/**
	 * 
	 * @Description 修改消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	@RequestMapping(value = "/updateMessageNotificationReceiver", method = RequestMethod.POST)
	public int updateMessageNotificationReceiver(
			@RequestBody MessageNotificationReceiverDTO messageNotificationReceiverDTO) {
		return messageNotificationReceiverMapper.updateMessageNotificationReceiver(messageNotificationReceiverDTO);
	}

	/**
	 * 
	 * @Description 查询单个消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return MessageNotificationReceiverDTO
	 */
	@RequestMapping(value = "/getMessageNotificationReceiverById", method = RequestMethod.POST)
	public MessageNotificationReceiverDTO getMessageNotificationReceiverById(
			@RequestBody Map<String, Object> paramsMap) {
		return messageNotificationReceiverMapper.getMessageNotificationReceiverById(paramsMap);
	}
	
	
	@RequestMapping(value = "/getContractMessageById", method = RequestMethod.POST)
	public MessageNotificationReceiverDTO getContractMessageById(
			@RequestBody Map<String, Object> paramsMap) {
		return messageNotificationReceiverMapper.getContractMessageById(paramsMap);
	}
	
	
	@RequestMapping(value = "/getStatisticsEmployees", method = RequestMethod.POST)
	public Map<String, Object> getStatisticsEmployees() {
		return messageNotificationReceiverMapper.getStatisticsEmployees();
	}

	
	
	@RequestMapping(value = "/getStatisticsContract", method = RequestMethod.POST)
	public Map<String, Object> getStatisticsContract() {
		return messageNotificationReceiverMapper.getStatisticsContract();
	}

	
	
	@RequestMapping(value = "/getStatisticsIncompleteData", method = RequestMethod.POST)
	public Map<String, Object> getStatisticsIncompleteData() {
		return messageNotificationReceiverMapper.getStatisticsIncompleteData();
	}


}

