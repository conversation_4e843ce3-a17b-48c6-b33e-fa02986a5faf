package com.bojun.system.dto;

import com.bojun.system.entity.OperationLog;
import com.bojun.utils.DateUtil;

import java.util.List;
import java.util.ArrayList;

/**
 *
 *Model：系统日志
 *Description：系统日志
 *Author:黄卫平
 *created：2020年4月27日
 */
public class OperationLogDTO extends OperationLog {

    //组合查询
    private List<Integer> operationTypes = new ArrayList<Integer>();
    // 查询字符串
    private String searchKey;
    // 每页条数
    private Integer everyPage;
    // 页码
    private Integer pageNum;
    // 总行数
    private Integer totalCount;

    // 日志开始时间（2020-01-01）
    private String beginTime;

    // 日志结束时间（2020-01-01）
    private String endTime;

    private String accountNo; // 登录账号
    private String workNumber; //工号
    private String realName; // 姓名

    private String systemName; //系统名称

    private String operationTimeStr;

    public void setOperationTimeStr(String operationTimeStr) {
        this.operationTimeStr = operationTimeStr;
    }

    public List<Integer> getOperationTypes() {
        return operationTypes;
    }

    public void setOperationTypes(List<Integer> operationTypes) {
        this.operationTypes = operationTypes;
    }

    public String getOperationTimeStr() {
        if(this.getOperationTime() == null) return "";

        return DateUtil.getFormatDate(this.getOperationTime(), "yyyy-MM-dd HH:mm:ss");
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getSearchKey() {
        return searchKey;
    }

    public void setSearchKey(String searchKey) {
        this.searchKey = searchKey;
    }

    public Integer getEveryPage() {
        return everyPage;
    }

    public void setEveryPage(Integer everyPage) {
        this.everyPage = everyPage;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
}