
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.SatisfactionQuestionnaireResultDTO;
import com.github.pagehelper.Page;



/**
 * 
*Model：满意度问卷答题结果信息表
*Description：满意度问卷答题结果信息表
*Author:李欣颖
*created：2020年5月7日
 */
@Mapper
public interface SatisfactionQuestionnaireResultMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	int querySatisfactionQuestionnaireResultCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询满意度问卷答题结果信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<SatisfactionQuestionnaireResultDTO>
     * created：2020年5月7日
	 */
	Page<List<SatisfactionQuestionnaireResultDTO>> getSatisfactionQuestionnaireResult(SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO);

	/**
	 * 
	 * @Description 新增满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer addSatisfactionQuestionnaireResult(SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO);

	/**
	 * 
	 * @Description 删除满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer deleteSatisfactionQuestionnaireResult(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer updateSatisfactionQuestionnaireResult(SatisfactionQuestionnaireResultDTO satisfactionQuestionnaireResultDTO);

	/**
	 * 
	 * @Description 查询单个满意度问卷答题结果信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return SatisfactionQuestionnaireResultDTO
	 * created：2020年5月7日
	 */
	SatisfactionQuestionnaireResultDTO getSatisfactionQuestionnaireResultById(Map<String, Object> mapPara);

	 List<Map<String, Object>> getNumberStatistics(Map<String, Object> paramsMap);

	Integer queryTotalScore(Map<String, Object> paramsMap);


	
}
