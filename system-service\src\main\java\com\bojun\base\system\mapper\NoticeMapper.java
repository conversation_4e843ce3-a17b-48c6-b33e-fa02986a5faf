package com.bojun.base.system.mapper;

import com.bojun.healthcare.dto.UserNoticeDTO;
import com.bojun.system.dto.MessageNotificationDTO;
import com.bojun.system.dto.MessageNotificationObjectDTO;
import com.bojun.system.entity.MessageNotificaionType;
import com.bojun.system.entity.MessageNotificationObject;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/26 9:24
 */
@Mapper
public interface NoticeMapper {
    /**
     * @description: 通知公告查询（全查）
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    Page<MessageNotificationDTO> getNotices(MessageNotificationDTO messageNotificationDTO);

    /**
     * @description: 通知公告查询（单查）
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    MessageNotificationDTO getNoticeById(@Param("noticeId") String noticeId);
    /**
     * @description: 添加通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    int addNotice(MessageNotificationDTO messageNotificationDTO);
    
    /**
     * 
     * @Description 新增app融云通知
     * <AUTHOR>
     * @param notice
     * @return
     * Integer
     * 2021年3月2日
     */
    Integer addRongNotice(UserNoticeDTO notice);

    /**
     * @description: 删除通知
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    int updateNotice(MessageNotificationDTO messageNotificationDTO);

    /**
     * @param
     * @description: 获取通知类型
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    List<MessageNotificaionType> getNoticeType();


    /**
     * @param
     * @description: 添加关联对象推送
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    int addMessageNotificationObject(MessageNotificationObject object);

    /**
     * @param
     * @description: 添加关联对象推送
     * @author: 赖允翔
     * @date: 2020/4/26
     * @Param:
     * @return:
     */
    int addMessageNotificationSystem(@Param("noticeId") String noticeId, @Param("list") List<String> systemIds);

    int deleteMessageNotificationSystem(MessageNotificationDTO messageNotificationDTO);

    int deleteMessageNotificationObject(MessageNotificationDTO messageNotificationDTO);
    
    /**
     * 
     * @Description 获取app端推送用户
     * <AUTHOR>
     * @param messageNotificationDTO
     * @return
     * List<Integer>
     * 2021年1月14日
     */
    List<String> getAllAppUser(MessageNotificationDTO messageNotificationDTO);
    
    /**
     * 
     * @Description 新增app端推送通知记录
     * <AUTHOR>
     * @param messageNotificationDTO
     * @return
     * Integer
     * 2021年1月14日
     */
    Integer addAppNotice(MessageNotificationDTO messageNotificationDTO);

    List<MessageNotificationObjectDTO> getObjectOrg(MessageNotificationDTO notificationDTO);

    List<MessageNotificationObjectDTO> getObjectDept(MessageNotificationObjectDTO notificationObjectDTO);

    List<MessageNotificationObjectDTO> getObjectWard(MessageNotificationDTO messageNotificationDTO);

    Page<MessageNotificationDTO> getMessageNotification(MessageNotificationDTO messageNotification);

    MessageNotificationDTO getMessageNotificationById(String noticeId);
}
