package com.bojun.system.dto;

import java.io.Serializable;
import java.math.BigDecimal;
public class MedicalIncomeDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	// 总收入
	private BigDecimal totalIncome;
	// 医疗收入
	private BigDecimal medicalIncome;
	// 药品收入
	private BigDecimal medicineIncome;
	// 退费金额
	private BigDecimal refundCost;
	// 住院费用
	private BigDecimal inhisCost;
	// 门诊费用
	private BigDecimal outhisCost;
	// 住院患者数
	private Integer inpatientCount;
	// 门诊患者数
	private Integer outpatientCount;
	// 平均住院费用
	private BigDecimal inhisCostAvg;
	// 平均门诊费用
	private BigDecimal outhisCostAvg;

	public BigDecimal getTotalIncome() {
		return totalIncome;
	}

	public void setTotalIncome(BigDecimal totalIncome) {
		this.totalIncome = totalIncome;
	}

	public BigDecimal getMedicalIncome() {
		return medicalIncome;
	}

	public void setMedicalIncome(BigDecimal medicalIncome) {
		this.medicalIncome = medicalIncome;
	}

	public BigDecimal getMedicineIncome() {
		return medicineIncome;
	}

	public void setMedicineIncome(BigDecimal medicineIncome) {
		this.medicineIncome = medicineIncome;
	}

	public BigDecimal getRefundCost() {
		return refundCost;
	}

	public void setRefundCost(BigDecimal refundCost) {
		this.refundCost = refundCost;
	}

	public BigDecimal getInhisCost() {
		return inhisCost;
	}

	public void setInhisCost(BigDecimal inhisCost) {
		this.inhisCost = inhisCost;
	}

	public BigDecimal getOuthisCost() {
		return outhisCost;
	}

	public void setOuthisCost(BigDecimal outhisCost) {
		this.outhisCost = outhisCost;
	}

	public Integer getInpatientCount() {
		return inpatientCount;
	}

	public void setInpatientCount(Integer inpatientCount) {
		this.inpatientCount = inpatientCount;
	}

	public Integer getOutpatientCount() {
		return outpatientCount;
	}

	public void setOutpatientCount(Integer outpatientCount) {
		this.outpatientCount = outpatientCount;
	}

	public BigDecimal getInhisCostAvg() {
		return inhisCostAvg;
	}

	public void setInhisCostAvg(BigDecimal inhisCostAvg) {
		this.inhisCostAvg = inhisCostAvg;
	}

	public BigDecimal getOuthisCostAvg() {
		return outhisCostAvg;
	}

	public void setOuthisCostAvg(BigDecimal outhisCostAvg) {
		this.outhisCostAvg = outhisCostAvg;
	}

}
