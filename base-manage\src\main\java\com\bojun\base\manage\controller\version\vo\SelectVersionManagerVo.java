package com.bojun.base.manage.controller.version.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/30 8:00
 */
@ApiModel(value = "查询版本管理", description = "查询版本管理")
public class SelectVersionManagerVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    private int Id;
    @ApiModelProperty(value = "版本号")
    private String versionNumber;
    @ApiModelProperty(value = "产品名称")
    private String systemId;
    @ApiModelProperty(value = "移动端类型  1：安卓  2：IOS")
    private int mobileType;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
    @ApiModelProperty(value = "大小")
    private String size;
    @ApiModelProperty(value = "升级说明")
    private String updateDescription;
    @ApiModelProperty(value = "是否强制升级 0 否 1是")
    private int isForce;
    @ApiModelProperty(value = "产品名称")
    private String systemName;

    private String startTime;
    private String endTime;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getId() {
        return Id;
    }

    public void setId(int id) {
        Id = id;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getUpdateDescription() {
        return updateDescription;
    }

    public void setUpdateDescription(String updateDescription) {
        this.updateDescription = updateDescription;
    }

    public int getIsForce() {
        return isForce;
    }

    public void setIsForce(int isForce) {
        this.isForce = isForce;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public int getMobileType() {
        return mobileType;
    }

    public void setMobileType(int mobileType) {
        this.mobileType = mobileType;
    }

    public String getSystemName() {
        return systemName;
    }

    public void setSystemName(String systemName) {
        this.systemName = systemName;
    }
}
