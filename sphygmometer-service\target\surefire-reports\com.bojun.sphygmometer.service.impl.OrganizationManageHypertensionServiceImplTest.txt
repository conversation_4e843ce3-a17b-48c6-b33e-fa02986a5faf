-------------------------------------------------------------------------------
Test set: com.bojun.sphygmometer.service.impl.OrganizationManageHypertensionServiceImplTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 0.243 s <<< FAILURE! - in com.bojun.sphygmometer.service.impl.OrganizationManageHypertensionServiceImplTest
testSumByIds(com.bojun.sphygmometer.service.impl.OrganizationManageHypertensionServiceImplTest)  Time elapsed: 0.215 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\OrganizationManageHypertensionMapper.xml]
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: select         sum(manage_hypertension_count) manage_hypertension_count         from         t_organization_manage_hypertension          WHERE  organization_id in                 (                     ?                 )
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
	at com.bojun.sphygmometer.service.impl.OrganizationManageHypertensionServiceImplTest.testSumByIds(OrganizationManageHypertensionServiceImplTest.java:22)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_organization_manage_hypertension' doesn't exist
	at com.bojun.sphygmometer.service.impl.OrganizationManageHypertensionServiceImplTest.testSumByIds(OrganizationManageHypertensionServiceImplTest.java:22)

