package com.bojun.exception;

import com.bojun.enums.ResponseCodeEnum;
import lombok.Data;

/**
 * @ClassName BusinessException
 * <AUTHOR>
 * @Date 2021/7/23 10:31
 * @Description BusinessException
 * @Version 1.0
 */
@Data
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = -2549331797273774349L;
    private Integer errorCode;

    public BusinessException(String message, Integer errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(String message) {
        super(message);
        this.errorCode = ResponseCodeEnum.FAIL_REQUEST.getCode();
    }
}
