package com.bojun.health.promotion.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.health.promotion.common.entity.NewsTopic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface NewsTopicMapper extends BaseMapper<NewsTopic> {
    int deleteByPrimaryKey(Integer id);

    int deleteByNewsId(Integer newsId);

    @Override
    int insert(NewsTopic record);

    int insertSelective(NewsTopic record);

    List<NewsTopic> selectByNewsId(@Param("newsId") Integer newsId);

    int updateByPrimaryKeySelective(NewsTopic record);

    int updateByPrimaryKey(NewsTopic record);

    List<NewsTopic> selectByTopicId(Integer id);
}