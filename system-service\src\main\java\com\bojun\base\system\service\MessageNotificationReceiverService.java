package com.bojun.base.system.service;

import java.util.List;
import java.util.Map;

import org.springframework.web.bind.annotation.RequestBody;

import com.bojun.system.dto.MessageNotificationReceiverDTO;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2020年5月29日
*/
public interface MessageNotificationReceiverService {
	
	
	/**
	 * 
	 * @Description 查询消息通知接收人信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationReceiverDTO>
	 */
	public List<MessageNotificationReceiverDTO> getMessageNotificationReceiver(
			@RequestBody Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询消息通知接收人信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<MessageNotificationReceiverDTO>
	 */
	public List<MessageNotificationReceiverDTO> getMessageNotificationReceiverHome(
			@RequestBody Map<String, Object> mapPara);
	
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer queryMessageNotificationReceiverCount(@RequestBody Map<String, Object> paramsMap);
	
	
	/**
	 * 
	 * @Description 查询未读总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer queryMessageNotificationReceiverUnreadCount(@RequestBody Map<String, Object> paramsMap);
	
	/**
	 * 
	 * @Description 新增消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 */
	public Integer addMessageNotificationReceiver(
			@RequestBody MessageNotificationReceiverDTO messageNotificationReceiverDTO);
	
	
	
	
	Integer addMessageNotificationReceiverList(@RequestBody List<MessageNotificationReceiverDTO> list);
	
	
	/**
	 * 
	 * @Description 删除消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	public int deleteMessageNotificationReceiver(@RequestBody Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return int
	 */
	public int updateMessageNotificationReceiver(
			@RequestBody MessageNotificationReceiverDTO messageNotificationReceiverDTO);

	/**
	 * 
	 * @Description 查询单个消息通知接收人信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return MessageNotificationReceiverDTO
	 */
	public MessageNotificationReceiverDTO getMessageNotificationReceiverById(
			@RequestBody Map<String, Object> paramsMap);
	
	
	
	public MessageNotificationReceiverDTO getContractMessageById(
			@RequestBody Map<String, Object> paramsMap);
	
	
	
	public Map<String, Object> getStatisticsEmployees();

	public Map<String, Object> getStatisticsContract();

	public Map<String, Object> getStatisticsIncompleteData();

}

