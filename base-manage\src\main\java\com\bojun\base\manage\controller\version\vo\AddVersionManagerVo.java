package com.bojun.base.manage.controller.version.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/27 11:25
 */
@ApiModel(value = "添加版本管理", description = "添加版本管理")
public class AddVersionManagerVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "版本号")
    private String versionNumber;
    @ApiModelProperty(value = "产品名称")
    private String systemId;
    @ApiModelProperty(value = "移动端类型  1：安卓  2：IOS")
    private int mobileType;
    @ApiModelProperty(value = "是否强制更新   0：否  1：是")
    private int isForce;
    @ApiModelProperty(value = "文件名")
    private String fileName;
    @ApiModelProperty(value = "更新说明")
    private String updateDescription;
    @ApiModelProperty(value = "文件大小，转成MB,保留后两位数")
    private double appSize;

    public double getAppSize() {
        return appSize;
    }

    public void setAppSize(double appSize) {
        this.appSize = appSize;
    }

    public String getSystemId() {
        return systemId;
    }

    public void setSystemId(String systemId) {
        this.systemId = systemId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
    }

    public int getMobileType() {
        return mobileType;
    }

    public void setMobileType(int mobileType) {
        this.mobileType = mobileType;
    }

    public int getIsForce() {
        return isForce;
    }

    public void setIsForce(int isForce) {
        this.isForce = isForce;
    }

    public String getUpdateDescription() {
        return updateDescription;
    }

    public void setUpdateDescription(String updateDescription) {
        this.updateDescription = updateDescription;
    }


}
