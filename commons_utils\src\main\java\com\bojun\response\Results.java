/**
 * 
 */
package com.bojun.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
*Model：通用接口返回对象
*Description：通用接口返回对象
*Author:段德鹏
*created：2020年4月9日
**/
@ApiModel
public class Results<T> {
	
	@ApiModelProperty(required = true, notes = "结果状态码", example = "200")
	private Integer code;
	
	@ApiModelProperty(required = true, notes = "返回信息", example = "操作成功")
	private String msg;
	
	@ApiModelProperty(required = true, notes = "返回时间(时间戳)", example = "")
	private long timestamp=System.currentTimeMillis()-1000*6;
	
	private T data;
	
	public Results(Integer code, String msg, T data){
        setCode(code);
        setMsg(msg);
        setData(data);
        setTimestamp(timestamp);
    }

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	
}
