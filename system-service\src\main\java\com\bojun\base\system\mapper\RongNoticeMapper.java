package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.QuartzJobRecordDTO;
import com.bojun.system.dto.RongNoticeDTO;
import com.bojun.system.entity.RongNotice;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知接口
 * @Description: 基础控台融云消息通知接口
 * @since 2020-12-04
 */
@Mapper
public interface RongNoticeMapper extends BaseMapper<RongNotice> {

    Page<RongNoticeDTO> getRongNotice(RongNoticeDTO rongnoticeDTO);

    List<String> getDoctorAppUserIds();

    List<String> patientPushNotice();
    
    Page<QuartzJobRecordDTO> getQuartzJobRecordList(QuartzJobRecordDTO quartzJobRecordDTO);
}
