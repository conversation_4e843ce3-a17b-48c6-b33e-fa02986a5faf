package com.bojun.base.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bojun.base.system.mapper.RongNoticeMapper;
import com.bojun.base.system.service.IRongNoticeService;
import com.bojun.system.dto.QuartzJobRecordDTO;
import com.bojun.system.dto.RongNoticeDTO;
import com.bojun.system.entity.RongNotice;
import com.github.pagehelper.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Model： 基础控台融云消息通知服务类
 * @Description: 基础控台融云消息通知服务类
 * @since 2020-12-04
 */
@Service
public class RongNoticeServiceImpl extends ServiceImpl<RongNoticeMapper, RongNotice> implements IRongNoticeService {
    @Autowired
    private RongNoticeMapper rongNoticeMapper;


    @Override
    public Page<RongNoticeDTO> getRongNotice(RongNoticeDTO rongnoticeDTO) {
        return rongNoticeMapper.getRongNotice(rongnoticeDTO);
    }


	@Override
	public Page<QuartzJobRecordDTO> getQuartzJobRecordList(QuartzJobRecordDTO quartzJobRecordDTO) {
		return rongNoticeMapper.getQuartzJobRecordList(quartzJobRecordDTO);
	}
}
