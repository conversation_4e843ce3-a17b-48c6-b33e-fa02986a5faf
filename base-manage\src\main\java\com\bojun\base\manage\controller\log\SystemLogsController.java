package com.bojun.base.manage.controller.log;

import com.bojun.author.AuthAnnotation;
import com.bojun.base.controller.BaseController;
import com.bojun.base.manage.api.system.ISystemOperationService;
import com.bojun.base.manage.controller.log.vo.OperationLogParamVO;
import com.bojun.base.manage.controller.log.vo.OperationLogsVO;
import com.bojun.base.manage.controller.login.LoginController;
import com.bojun.contants.Contants;
import com.bojun.enums.ResponseCodeEnum;
import com.bojun.system.dto.OperationLogDTO;
import com.bojun.response.Results;
import com.bojun.utils.BeanUtil;
import com.bojun.vo.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiSort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 *Model：
 *Description：操作日志
 *Author: 黄卫平
 *created：2020年4月23日
 */

@SuppressWarnings("unchecked")
@RestController
@RequestMapping("log")
@Api(tags = {"系统日志"})
@ApiSort(value = 1)
public class SystemLogsController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(SystemLogsController.class);

    @Autowired
    private ISystemOperationService systemOperationService;

    @AuthAnnotation(action = "getOperationLogsByPage")
    @PostMapping("/getOperationLogsByPage")
    @ApiOperation(value = "分页获取系统日志", notes = "分页获取系统日志，（黄卫平）")
    public Results<Page<OperationLogsVO>> getOperationLogsByPage(@Valid @RequestBody OperationLogParamVO operationLogParamVO) {

        try {
            OperationLogDTO operationLogDTO = new OperationLogDTO();

            BeanUtil.copyPropertiesIgnoreNull(operationLogParamVO, operationLogDTO);

            // 默认不是异常日志
            operationLogDTO.setIsException(0);

            if(operationLogParamVO.getType() == 1) {
                // 登录登出
                if(operationLogParamVO.getOperationType() == null || operationLogParamVO.getOperationType() == 0) {
                    List<Integer> types = new ArrayList<Integer>();
                    types.add(4); // 登录
                    types.add(5); // 登出
                    operationLogDTO.setOperationTypes(types);
                }else {
                    List<Integer> types = new ArrayList<Integer>();
                    types.add(operationLogParamVO.getOperationType());
                    operationLogDTO.setOperationTypes(types);
                }
            }

            if(operationLogParamVO.getType() == 2) {
                // 操作日志
                if(operationLogParamVO.getOperationType() == null || operationLogParamVO.getOperationType() == 0) {
                    List<Integer> types = new ArrayList<>();
                    types.add(1); // 插入
                    types.add(2); // 修改
                    types.add(2); // 删除
                    operationLogDTO.setOperationTypes(types);
                }else {
                    List<Integer> types = new ArrayList<Integer>();
                    types.add(operationLogParamVO.getOperationType());
                    operationLogDTO.setOperationTypes(types);
                }
            }

            if(operationLogParamVO.getType() == 3) {
                // 异常日志
                operationLogDTO.setOperationType(null);
                operationLogDTO.setOperationTypes(null);

                operationLogDTO.setIsException(1);
            }

           String result = systemOperationService.getSystemLogList(operationLogDTO);

            return returnResultsPage(result, OperationLogsVO.class);
        }catch (Exception e) {
            logger.error("getOperationLogsByPage:", e);
            return failResults(ResponseCodeEnum.FAIL_REQUEST.getCode(), e.getMessage());
        }
    }

}
