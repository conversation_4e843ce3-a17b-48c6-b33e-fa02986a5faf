package com.bojun.base.manage.controller.organization.vo.ward;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/4 20:52
 */
@ApiModel(value = "员工信息", description = "员工信息")
public class EmployeeInfoVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "员工Id")
    private String employeeId; // 员工id(E+100000)
    @ApiModelProperty(value = "员工姓名")
    private String realName; // 姓名
    @ApiModelProperty(value = "机构Id")
    private Integer organizationId;
    @ApiModelProperty(value = "科室Id")
    private Integer deptId;

    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public String getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(String employeeId) {
        this.employeeId = employeeId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }
}
