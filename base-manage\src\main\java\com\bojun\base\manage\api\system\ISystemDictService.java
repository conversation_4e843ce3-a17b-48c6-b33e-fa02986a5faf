/**
 * 
 */
package com.bojun.base.manage.api.system;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.bojun.base.manage.api.system.hystrix.SystemDictServiceHystrix;
import com.bojun.system.dto.SystemDictDTO;

/**
 * Model： 产品管理服务
 * Description：产品管理服务
 * Author：赖水秀
 * created： 2020年4月27日
 */
@FeignClient(name="system-service", fallback = SystemDictServiceHystrix.class)
public interface ISystemDictService {
	
	/**
	 * @Description 获取产品分类下拉列表数据
	 * <AUTHOR>
	 * @return
	 * List<SystemDictTypeDTO>
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemDictTypeList", method = RequestMethod.POST)
	String getSystemDictTypeList();
	
	/**
	 * @Description 保存产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/saveSystemDict", method = RequestMethod.POST)
	String saveSystemDict(SystemDictDTO systemDictDto);
	
	
	/**
	 * @Description 修改产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/updateSystemDict", method = RequestMethod.POST)
	String updateSystemDict(SystemDictDTO systemDictDto);
	
	
	/**
	 * @Description 根据单个产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * SystemDictDTO
	 * 2020年4月27日
	 */	
	@RequestMapping(value = "/getSystemDictById", method = RequestMethod.POST)
	String getSystemDictById(@RequestParam(value="systemId") String systemId);

	/**
	 * @Description 查询产品信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月27日
	 */
	@RequestMapping(value = "/getSystemDictList", method = RequestMethod.POST)
	String getSystemDictList(SystemDictDTO systemDictDto);	
	
	/**
	 * @Description 根据角色ID查询产品列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * SystemDictDTO
	 * 2020年4月27日
	 */	
	@RequestMapping(value = "/getSystemDictByRoleId", method = RequestMethod.POST)
	String getSystemDictByRoleId(SystemDictDTO systemDictDto );
	
	
	/**
	 * @Description 查询产品树形列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return	 
	 * 2020年4月30日
	 */
	@RequestMapping(value = "/getSystemDictTreeList", method = RequestMethod.POST)
	String getSystemDictTreeList();	
	
	/**
	 * @Description 根据角色ID查询产品列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * SystemDictDTO
	 * 2020年4月27日
	 */	
	@RequestMapping(value = "/getSystemMenuTreeByRoleId", method = RequestMethod.POST)
	String getSystemMenuTreeByRoleId(SystemDictDTO systemDictDto );

}
