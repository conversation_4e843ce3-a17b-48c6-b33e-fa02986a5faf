
package com.bojun.base.system.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.FormQuestionDTO;



/**
 * 
*Model：满意度问卷题目信息表
*Description：满意度问卷题目信息表
*Author:李欣颖
*created：2020年5月7日
 */
@Mapper
public interface FormQuestionMapper {

	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	int queryFormQuestionCount(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 查询满意度问卷题目信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionDTO>
     * created：2020年5月7日
	 */
	List<FormQuestionDTO> getFormQuestion(Map<String, Object> mapPara);

	/**
	 * 
	 * @Description 新增满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer addFormQuestion(FormQuestionDTO formQuestionDTO);

	/**
	 * 
	 * @Description 删除满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer deleteFormQuestion(Map<String, Object> paramsMap);

	/**
	 * 
	 * @Description 修改满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月7日
	 */
	Integer updateFormQuestion(FormQuestionDTO formQuestionDTO);

	/**
	 * 
	 * @Description 查询单个满意度问卷题目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionDTO
	 * created：2020年5月7日
	 */
	FormQuestionDTO getFormQuestionById(Map<String, Object> mapPara);


	
}
