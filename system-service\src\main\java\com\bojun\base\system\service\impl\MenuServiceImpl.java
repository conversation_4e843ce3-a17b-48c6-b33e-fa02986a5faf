/**
 * 
 */
package com.bojun.base.system.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.bojun.base.system.mapper.MenuMapper;
import com.bojun.base.system.service.IMenuService;
import com.bojun.system.dto.MenuDTO;
import com.github.pagehelper.Page;


/**
 * Model： 菜单管理模块服务实现类
 * Description：菜单管理模块服务实现类
 * Author：赖水秀
 * created： 2020年4月28日
 */
@Service
public class MenuServiceImpl implements IMenuService {
	
	@Autowired
	private MenuMapper menuMapper;
	
	/**
	 * @Description 保存菜单信息
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * int
	 * 2020年4月28日
	 */
	@Override
	public int saveMenu(MenuDTO menuDTO) {		
		return menuMapper.saveMenu(menuDTO);
	}
	
	/**
	 * @Description 修改菜单信息
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * int
	 * 2020年4月28日
	 */
	@Override
	public int updateMenu(MenuDTO menuDTO) {		
		return menuMapper.updateMenu(menuDTO);
	}
	
	/**
	 * @Description 根据id查询单个菜单信息
	 * <AUTHOR>
	 * @param menuId
	 * @return
	 * MenuDTO
	 * 2020年4月28日
	 */
	@Override
	public MenuDTO getMenuById(String menuId) {		
		return menuMapper.getMenuById(menuId);
	}
	
	/**
	 * @Description 按分页查询菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	@Override
	public Page<MenuDTO> getMenuPageList(MenuDTO menuDTO) {		
		return menuMapper.getMenuPageList(menuDTO);
	}
	
	/**
	 * @Description 查询菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	@Override
	public List<MenuDTO> getMenuList(MenuDTO menuDTO) {
		return menuMapper.getMenuList(menuDTO);
	}
	
	/**
	 * @Description 根据父亲id查询子菜单列表
	 * <AUTHOR>
	 * @param parentId
	 * @return
	 * List<MenuDTO>
	 * 2020年4月29日
	 */
	@Override
	public int deleteMenuById(String ids) {
		return menuMapper.deleteMenuById(ids);
	}
	
	/**
	 * @Description 查询子级菜单信息
	 * <AUTHOR>
	 * @param ids
	 * @return
	 * int
	 * 2020年4月29日
	 */
	@Override
	public List<MenuDTO> getSubMenuList(String menuId) {		
		return menuMapper.getSubMenuList(menuId);
	}
	/**
	 * @Description 查询角色菜单信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	public List<MenuDTO> getMenuRoleList(MenuDTO menuDTO){
		return menuMapper.getMenuRoleList(menuDTO);
	}
	
	/**
	 * @Description 获取菜单下的所有按钮
	 * <AUTHOR>
	 * @param menuId
	 * @return
	 * List<MenuDTO>
	 * 2020年5月12日
	 */
	@Override
	public List<MenuDTO> getButtonListByMenu(String menuId) {
		return menuMapper.getButtonListByMenu(menuId);
	}
	
	/**
	 * @Description 保存按钮信息
	 * <AUTHOR>
	 * @param menuDTO
	 * @return 
	 * int
	 * 2020年5月12日
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public int saveButtonInfo(MenuDTO menuDTO) {
		MenuDTO menuObj = menuMapper.getMenuById(menuDTO.getMenuId());		
		menuMapper.deleteButtonByMenuId(menuDTO.getMenuId());	
		List<MenuDTO> children = menuDTO.getChildren();	//传入的按钮信息			
		int updateCount = 0;
		if(null != children && !children.isEmpty()) {
			for (MenuDTO button : children) {
				if(null == button.getMenuId() || "".equals(button.getMenuId())) {
					button.setMenuId(UUID.randomUUID().toString());
					
				} else {					
					button.setMenuId(button.getMenuId());
					
				}
				button.setParentId(menuDTO.getMenuId());
				button.setSystemId(menuObj.getSystemId());
				button.setMenuType(2);
			}
			
			updateCount = menuMapper.batchAddButton(children);
		}			
		return updateCount;
	}

	/**
	 * 
	 * @Description 获取医疗服务订单入口
	 * <AUTHOR>
	 * @param menuDTO
	 * @return
	 * MenuDTO
	 * 2020年5月25日
	 */
	@Override
	public MenuDTO getMedicalServiceEntranceTwo(MenuDTO menuDTO) {
		return menuMapper.getMedicalServiceEntranceTwo(menuDTO);
	}
	
	/**
	 * @Description 查询菜单/按钮列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * Page<List<MenuDTO>>
	 * 2020年4月28日
	 */
	public List<MenuDTO> getMenuButtonList(MenuDTO menuDTO){
		return menuMapper.getMenuButtonList(menuDTO);
	}

}
