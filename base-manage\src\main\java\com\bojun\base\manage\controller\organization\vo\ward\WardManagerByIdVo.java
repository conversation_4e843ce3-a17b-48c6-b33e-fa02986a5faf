package com.bojun.base.manage.controller.organization.vo.ward;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/5/4 19:35
 */
@ApiModel(value = "单查病区", description = "单查病区")
public class WardManagerByIdVo implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    @ApiModelProperty(value = "病区Id")
    private Integer wardId;

    public Integer getWardId() {
        return wardId;
    }

    public void setWardId(Integer wardId) {
        this.wardId = wardId;
    }
}
