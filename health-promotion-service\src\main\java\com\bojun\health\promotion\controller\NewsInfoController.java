package com.bojun.health.promotion.controller;

import com.bojun.health.promotion.common.BaseFeignController;
import com.bojun.health.promotion.common.dto.NewsInfoDTO;
import com.bojun.health.promotion.common.dto.TopicInfoParamDTO;
import com.bojun.health.promotion.service.NewsInfoService;
import com.bojun.health.promotion.service.api.NewsInfoFeignClient;
import com.bojun.health.promotion.service.impl.NewsInfoServiceImpl;
import com.bojun.page.PageData;
import com.bojun.page.Results;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Model：
 * @Description：
 * @author: 林伟
 * @created: 2021/5/28 18:12
 */
@RestController
public class NewsInfoController extends BaseFeignController implements NewsInfoFeignClient {

    private static Logger logger = LoggerFactory.getLogger(NewsInfoController.class);

    @Autowired
    private NewsInfoService newsInfoService;

    @Override
    @PostMapping(value = "/getNewsInfoList")
    public Results<PageData<NewsInfoDTO>> getNewsInfoList(@RequestBody TopicInfoParamDTO topicInfo) {
        List<NewsInfoDTO> newsInfo = newsInfoService.getNewsInfo(topicInfo);
        return Results.list(getPageData(newsInfo));
    }

    @Override
    @PostMapping(value = "/addNewsInfo")
    public Integer addNewsInfo(@RequestBody NewsInfoDTO newsInfo) throws Exception {
        boolean save = newsInfoService.save(newsInfo);
        if (!save) {
            throw new Exception("添加文章失败！");
        }
        return newsInfo.getNewsId();
    }

    @PostMapping(value = "/newsPutonOrOffShelf")
    @Override
    public Results newsPutonOrOffShelf(@RequestBody NewsInfoDTO newsInfo) throws Exception {
        try {
            int result = newsInfoService.newsPutonOrOffShelf(newsInfo);
            if (result <= 0) {
                return Results.fail("更新文章失败！");
            }
            return Results.opResult(result);
        } catch (Exception e) {
            return Results.fail(e.getMessage());
        }
    }

    @Override
    @PostMapping(value = "/updateNewsInfo")
    public Results updateNewsInfo(@RequestBody NewsInfoDTO newsInfo) throws Exception {
        try {
            int result = newsInfoService.updateByPrimaryKeySelective(newsInfo);
            if (result <= 0) {
                throw new Exception("更新文章失败！");
            }
            return Results.opResult(result);
        } catch (Exception e) {
            return Results.fail(e.getMessage());
        }
    }

    @PostMapping(value = "/deleteNewsInfo")
    public Integer deleteNewsInfo(@RequestBody NewsInfoDTO newsInfo) throws Exception {
        int result = newsInfoService.deleteByPrimaryKey(newsInfo);
        if (result <= 0) {
            throw new Exception("删除文章失败！");
        }
        return result;
    }

    @PostMapping(value = "/getNewsInfo")
    public NewsInfoDTO getNewsInfo(@RequestBody NewsInfoDTO newsInfo,@RequestParam("isLookOver") boolean isLookOver) {
        return newsInfoService.selectNewsInfoByNewsId(newsInfo.getNewsId(),isLookOver);
    }
}
