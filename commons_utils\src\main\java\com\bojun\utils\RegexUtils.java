package com.bojun.utils;

import java.util.Optional;
import java.util.regex.Pattern;

/**
 * @program: commons_utils
 * @description
 * <AUTHOR>
 * @create: 2020-07-07
 **/
public class RegexUtils {

    /**
     * 判断是否是手机号 较全
     * @param content
     * @return
     */
    public static boolean isMobilePhone(String content){
        boolean flag = false;
        if(Optional.ofNullable(content).isPresent()) {
            String pattern = "^((13|14|15|16|17|18|19)[0-9])\\d{8}$";
            flag = Pattern.compile(pattern).matcher(content).matches();
        }
        return flag;
    }

    /**
     * 判断是15或18位身份证
     * @param content
     * @return
     */
    public static boolean isIdNum(String content){
        boolean flag = false;
        if(Optional.ofNullable(content).isPresent()) {
            String pattern = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|" +
                    "(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
            flag = Pattern.compile(pattern).matcher(content).matches();
        }
        return flag;
    }

    /**
     * 判断是否是汉字
     * @param content
     * @return
     */
    public static boolean isHanzi(String content){
        boolean flag = false;
        if(Optional.ofNullable(content).isPresent()) {
            String pattern = "[\\u4E00-\\u9FA5]+";
            flag = Pattern.compile(pattern).matcher(content).matches();
        }
        return flag;
    }
}
