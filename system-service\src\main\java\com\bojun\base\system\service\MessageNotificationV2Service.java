package com.bojun.base.system.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bojun.system.dto.MessageNotificationV2DTO;
import com.bojun.system.entity.MessageNotificationV2;

import java.util.List;

/**
 * MessageNotificationService接口
 * 
 * <AUTHOR>
 * @date 2021-05-14 19:19:04
 */
public interface MessageNotificationV2Service extends IService<MessageNotificationV2>
{
    /**
     * 查询消息通知（站内）
     * 
     * @param noticeId 消息通知（站内）ID
     * @return 消息通知（站内）
     */
    public MessageNotificationV2DTO selectMessageNotificationById(String noticeId);

    /**
     * 查询消息通知（站内）列表
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 消息通知（站内）集合
     */
    public List<MessageNotificationV2DTO> selectMessageNotificationList(MessageNotificationV2DTO messageNotificationDTO);

    /**
     * 新增消息通知（站内）
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 结果
     */
    public int insertMessageNotification(MessageNotificationV2 messageNotificationDTO);

    /**
     * 修改消息通知（站内）
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 结果
     */
    public int updateMessageNotification(MessageNotificationV2 messageNotificationDTO);
    
    /**
     * 新增消息通知（站内）
     * 
     * @param messageNotification 消息通知（站内）
     * @return 结果
     */
    public int insertMessageNotification(MessageNotificationV2DTO messageNotification);

    /**
     * 修改消息通知（站内）
     * 
     * @param messageNotification 消息通知（站内）
     * @return 结果
     */
    public int updateMessageNotification(MessageNotificationV2DTO messageNotification);

    /**
     * 批量删除消息通知（站内）
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteMessageNotificationByIds(String ids);

    /**
     * 删除消息通知（站内）信息
     * 
     * @param noticeId 消息通知（站内）ID
     * @return 结果
     */
    public int deleteMessageNotificationById(String noticeId);
}
