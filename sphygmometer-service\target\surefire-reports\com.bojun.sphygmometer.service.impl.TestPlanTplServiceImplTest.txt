-------------------------------------------------------------------------------
Test set: com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest
-------------------------------------------------------------------------------
Tests run: 4, Failures: 0, Errors: 4, Skipped: 0, Time elapsed: 0.495 s <<< FAILURE! - in com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest
testSelectTestPlanTplById(com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest)  Time elapsed: 0.057 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\TestPlanTplMapper.xml]
### The error may involve com.bojun.sphygmometer.mapper.TestPlanTplMapper.selectTestPlanTplById-Inline
### The error occurred while setting parameters
### SQL: select id,                tpl_name,                test_frequency,                remark,                create_time,                create_user_id,                create_user_name,                update_time,                update_user_id,                update_user_name,                is_delete,                enable_status,                tag_code         from t_test_plan_tpl               where         id = ?
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testSelectTestPlanTplById(TestPlanTplServiceImplTest.java:20)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testSelectTestPlanTplById(TestPlanTplServiceImplTest.java:20)

testSelectTestPlanTplList(com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest)  Time elapsed: 0.063 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\TestPlanTplMapper.xml]
### The error may involve com.bojun.sphygmometer.mapper.TestPlanTplMapper.selectTestPlanTplList-Inline
### The error occurred while setting parameters
### SQL: select id,                tpl_name,                test_frequency,                remark,                create_time,                create_user_id,                create_user_name,                update_time,                update_user_id,                update_user_name,                is_delete,                enable_status,                tag_code         from t_test_plan_tpl               where is_delete = 0                              and tpl_name LIKE CONCAT('%',?,'%')                                                                                                              order by update_time desc
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testSelectTestPlanTplList(TestPlanTplServiceImplTest.java:32)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testSelectTestPlanTplList(TestPlanTplServiceImplTest.java:32)

testInsertTestPlanTpl(com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest)  Time elapsed: 0.301 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
### The error may exist in com/bojun/sphygmometer/mapper/TestPlanTplMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT( 1 ) FROM t_test_plan_tpl     WHERE (tag_code = ? AND is_delete = ?)
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testInsertTestPlanTpl(TestPlanTplServiceImplTest.java:59)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testInsertTestPlanTpl(TestPlanTplServiceImplTest.java:59)

testBindUserAndTestPlan(com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest)  Time elapsed: 0.062 s  <<< ERROR!
org.springframework.jdbc.BadSqlGrammarException: 

### Error querying database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
### The error may exist in file [C:\Leon\idea_workspace\sphygmometer-project\sphygmometer-service\target\classes\mybatis\mapper\TestPlanTplMapper.xml]
### The error may involve com.bojun.sphygmometer.mapper.TestPlanTplMapper.selectTestPlanTplList-Inline
### The error occurred while setting parameters
### SQL: select id,                tpl_name,                test_frequency,                remark,                create_time,                create_user_id,                create_user_name,                update_time,                update_user_id,                update_user_name,                is_delete,                enable_status,                tag_code         from t_test_plan_tpl               where is_delete = 0                    and tag_code = ?                                                                                                                        order by update_time desc
### Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
; bad SQL grammar []; nested exception is com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testBindUserAndTestPlan(TestPlanTplServiceImplTest.java:64)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'sphygmometer.t_test_plan_tpl' doesn't exist
	at com.bojun.sphygmometer.service.impl.TestPlanTplServiceImplTest.testBindUserAndTestPlan(TestPlanTplServiceImplTest.java:64)

