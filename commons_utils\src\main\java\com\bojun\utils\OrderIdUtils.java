package com.bojun.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.atomic.AtomicInteger;

public class OrderIdUtils {
	private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
	private static AtomicInteger atomicInteger = new AtomicInteger(0);
	private static String dates = "";
	/**
	 * 生成8+2+6 16位唯一订单号（前四位日期、中两位订单类型和后六位每天重复排）
	 * @param type 数据类型订单：在线问诊订单类型：01 预约挂号订单类型：02 预约就诊订单类型：03
                      处方订单类型：04 体检订单类型：05 智能设备订单类型：06 随访：20 转诊：30 住院充值 40，门诊缴费 50 代领代寄订单  11
	 * @return 同一天内按类型订单连续的编号
	 */
	public static synchronized String getOrderNoByAtomic(String prefix, String type,Integer initValue) {
		String date = simpleDateFormat.format(new Date());
//		if(!dates.equals(date)) {
//			atomicInteger = new AtomicInteger(initValue);
//		}
//		dates=date;
//		atomicInteger.getAndIncrement();
//		int i = atomicInteger.get();
		String sort=String.format("%0" + 6 + "d",  initValue);
		return prefix + date+type+sort;
	}
	
	
	public static void main(String[] args) {
		System.out.println(getOrderNoByAtomic("cf", "04", 18));
	}
}
