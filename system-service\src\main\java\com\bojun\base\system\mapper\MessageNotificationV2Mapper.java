package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.MessageNotificationV2DTO;
import com.bojun.system.entity.MessageNotificationV2;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * MessageNotificationMapper接口
 * 
 * <AUTHOR>
 * @date 2021-05-14 19:19:04
 */
@Mapper
public interface MessageNotificationV2Mapper extends BaseMapper<MessageNotificationV2>
{

    /**
     * 查询消息通知（站内）
     *
     * @param noticeId 消息通知（站内）ID
     * @return 消息通知（站内）
     */
    public MessageNotificationV2DTO selectMessageNotificationById(String noticeId);

    /**
     * 查询消息通知（站内）列表
     * 
     * @param messageNotificationDTO 消息通知（站内）
     * @return 消息通知（站内）集合
     */
    public List<MessageNotificationV2DTO> selectMessageNotificationList(MessageNotificationV2DTO messageNotificationDTO);
}
