<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.health.promotion.mapper.NewsTopicMapper">
    <resultMap id="BaseResultMap" type="com.bojun.health.promotion.common.dto.NewsTopicDTO">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="news_id" jdbcType="INTEGER" property="newsId"/>
        <result column="topic_id" jdbcType="VARCHAR" property="topicId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="INTEGER" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="is_top" jdbcType="INTEGER" property="isTop"/>
        <result column="news_show_index" jdbcType="INTEGER" property="newsShowIndex"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , news_id, topic_id, create_time, create_user_id, create_user_name
    </sql>
    <select id="selectByNewsId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_news_topic
        where news_id = #{newsId,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from t_news_topic
        where topic_id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.bojun.health.promotion.common.dto.NewsTopicDTO">
        insert into t_news_topic (id, news_id, topic_id,
                                  create_time, create_user_id, create_user_name,is_top,news_show_index)
        values (#{id,jdbcType=INTEGER}, #{newsId,jdbcType=INTEGER}, #{topicId,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=INTEGER}, #{createUserName,jdbcType=VARCHAR}
                , #{isTop,jdbcType=INTEGER}, #{newsShowIndex,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.bojun.health.promotion.common.dto.NewsTopicDTO">
        insert into t_news_topic
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="newsId != null">
                news_id,
            </if>
            <if test="topicId != null">
                topic_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="newsId != null">
                #{newsId,jdbcType=INTEGER},
            </if>
            <if test="topicId != null">
                #{topicId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.bojun.health.promotion.common.dto.NewsTopicDTO">
        update t_news_topic
        <set>
            <if test="newsId != null">
                news_id = #{newsId,jdbcType=INTEGER},
            </if>
            <if test="topicId != null">
                topic_id = #{topicId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId,jdbcType=INTEGER},
            </if>
            <if test="createUserName != null">
                create_user_name = #{createUserName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.bojun.health.promotion.common.dto.NewsTopicDTO">
        update t_news_topic
        set news_id          = #{newsId,jdbcType=INTEGER},
            topic_id         = #{topicId,jdbcType=VARCHAR},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            create_user_id   = #{createUserId,jdbcType=INTEGER},
            create_user_name = #{createUserName,jdbcType=VARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <delete id="deleteByNewsId" parameterType="java.lang.Integer">
        delete
        from t_news_topic
        where news_id = #{newsId,jdbcType=INTEGER}
    </delete>

    <select id="selectByTopicId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_news_topic
        where topic_id = #{topicId,jdbcType=INTEGER}
    </select>
</mapper>