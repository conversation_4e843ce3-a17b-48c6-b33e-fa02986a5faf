package com.bojun.oss;

import org.apache.commons.lang.StringUtils;

import com.bojun.utils.UUID;

/**
*Model：模块名称
*Description：文件描述
*Author: 肖泽权
*created：2021年1月26日
*/
public class AliOssFileUtils {
	
	public static String key(String fileSuffix) {
        if (StringUtils.isNotBlank(fileSuffix)) {
            return UUID.randomUUID().toString() + "." + fileSuffix;
        } else {
            // log.warn("fileSuffix error, fileSuffix:{}", fileSuffix);
            return null;
        }
    }

}

