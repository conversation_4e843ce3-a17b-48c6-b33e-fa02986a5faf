package com.bojun.base.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bojun.system.dto.SystemDictV2DTO;
import com.bojun.system.entity.SystemDictV2;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * SystemDictV2Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-06-21 11:37:26
 */
@Mapper
public interface SystemDictV2Mapper extends BaseMapper<SystemDictV2> {

    /**
     * 查询系统（项目）字典信息表
     *
     * @param systemId 系统（项目）字典信息表ID
     * @return 系统（项目）字典信息表
     */
    public SystemDictV2DTO selectSystemDictV2ById(String systemId);

    /**
     * 查询系统（项目）字典信息表列表
     * 
     * @param systemDictV2DTO 系统（项目）字典信息表
     * @return 系统（项目）字典信息表集合
     */
    public List<SystemDictV2DTO> selectSystemDictV2List(SystemDictV2DTO systemDictV2DTO);
}
