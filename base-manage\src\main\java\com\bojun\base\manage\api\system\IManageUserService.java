/**
 * 
 */
package com.bojun.base.manage.api.system;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.bojun.base.manage.api.system.hystrix.ManageUserServiceHystrix;
import com.bojun.system.dto.ManageUserDTO;
import com.bojun.system.entity.ManageUser;

/**
*Model： 管理员信息接口
*Description：管理员信息接口
*Author: 段德鹏
*created：2020年3月3日
*/
@FeignClient(name="system-service", fallback = ManageUserServiceHystrix.class)
public interface IManageUserService {
	
	/**
	 * @Description 管理员密码登录
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * String
	 * created：2020年3月3日
	 */
	@RequestMapping(value = "/userLoginByPwd", method = RequestMethod.POST)
	String userLoginByPwd(@RequestBody ManageUserDTO manageUserDTO);
	
	/**
	 * @Description 新增用户
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * String
	 * created：2020年3月3日
	 */
	@RequestMapping(value = "/addManageUser", method = RequestMethod.POST)
	String addManageUser(@RequestBody ManageUser manageUser);
	/**
	 * @Description 修改用户
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * String
	 * created：2020年3月3日
	 */
	@RequestMapping(value = "/updateManageUser", method = RequestMethod.POST)
	String updateManageUser(@RequestBody ManageUser manageUser);
	
	/**
	 * @Description 修改密码
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * String
	 * created：2020年3月3日
	 */
	@RequestMapping(value = "/updateUserPasswords", method = RequestMethod.POST)
	String updateUserPasswords(@RequestBody ManageUserDTO manageUser);
	
	/**
	 * @Description 查询所有用户
	 * <AUTHOR>
	 * @param ManageUserDTO
	 * @return
	 * String
	 * created：2020年3月3日
	 */
	@RequestMapping(value = "/getManageUserList", method = RequestMethod.POST)
	String getManageUserList(@RequestBody ManageUserDTO manageUser);
	/**
	 * @Description 启用禁用用户状态
	 * <AUTHOR>
	 * @param ManageUser
	 * @return
	 * String
	 * created：2020年3月3日
	 */
	@RequestMapping(value = "/enableDisableUser", method = RequestMethod.POST)
	String enableDisableUser(@RequestBody ManageUser manageUser);

}
