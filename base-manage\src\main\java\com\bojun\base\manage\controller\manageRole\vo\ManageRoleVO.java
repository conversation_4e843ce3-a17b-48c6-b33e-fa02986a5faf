/**
 * 
 */
package com.bojun.base.manage.controller.manageRole.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import com.bojun.system.dto.ManageRoleOrganDTO;
import com.bojun.system.dto.SystemDictDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Model：角色管理 
 * Description：角色信息 列表 
 * Author：lj 
 * created： 2020年4月27日
 */
@ApiModel(value = "角色信息列表", description = "角色列表信息返回参数")
public class ManageRoleVO implements Serializable {

	private static final long serialVersionUID = -3833437130055960315L;

	@ApiModelProperty(value = "角色ID", required = true, example = "1")
	private String roleId;

	@ApiModelProperty(value = "角色名称", required = true, example = "医生")
	private String roleName;

	@ApiModelProperty(value = "启用状态： 0：否  1：是", required = true, example = "医生")
	private Integer isEnabled; //

	@ApiModelProperty(value = "角色说明", required = true, example = "医生")
	private String roleDescribe;

	@ApiModelProperty(value = "添加角色的用户IDId", required = true, example = "1")
	private Integer createUserId;
	@ApiModelProperty(value = "角色下用户总数", required = true, example = "1")
	private Integer roleCount;

	@ApiModelProperty(value = "机构id", required = true, example = "1")
	private Integer organizationId;

	@ApiModelProperty(value = "角色机构关联id", required = true, example = "1")
	private Integer roId;

	@ApiModelProperty(value = "机构名称", required = true, example = "测试")
	private String organizationName;

	@ApiModelProperty(value = "部门id", required = true, example = "1")
	private Integer deptId;

	@ApiModelProperty(value = "部门名称", required = true, example = "1")
	private String deptName;

	@ApiModelProperty(value = "区域（病区）id", required = true, example = "1")
	private Integer wardId;

	@ApiModelProperty(value = "区域（病区）名称", required = true, example = "1")
	private String wardName;
	
	
	@ApiModelProperty(value = "1：医疗机构人员   2：养老机构人员  3：监管人员  4：运维", required = true, example = "1")
	private Integer roleType;
	
	@ApiModelProperty(value = "角色下的产品列表", required = true, example = "1")
	private  List<SystemDictDTO> systemList; 
	
	@ApiModelProperty(value = "角色下的机构列表", required = true, example = "1")
	private  List<Map> getRoleOrganDet;//角色关联机构
	
	private List<ManageRoleOrganDTO> roleOrganList;//角色机构关联
	private List<Map<String, Object>> roleOrganTreeList;//角色机构关联id

	
	@ApiModelProperty(value = "1: 角色机构  2：账号机构", required = true, example = "2")
	private Integer dataPermissions;//数据权限  
	
	
	
	public Integer getDataPermissions() {
		return dataPermissions;
	}

	public void setDataPermissions(Integer dataPermissions) {
		this.dataPermissions = dataPermissions;
	}

	
	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public List<Map<String, Object>> getRoleOrganTreeList() {
		return roleOrganTreeList;
	}

	public void setRoleOrganTreeList(List<Map<String, Object>> roleOrganTreeList) {
		this.roleOrganTreeList = roleOrganTreeList;
	}

	

	public List<SystemDictDTO> getSystemList() {
		return systemList;
	}

	public void setSystemList(List<SystemDictDTO> systemList) {
		this.systemList = systemList;
	}

	public Integer getRoleCount() {
		return roleCount;
	}

	public void setRoleCount(Integer roleCount) {
		this.roleCount = roleCount;
	}

	public Integer getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Integer organizationId) {
		this.organizationId = organizationId;
	}

	public Integer getRoId() {
		return roId;
	}

	public void setRoId(Integer roId) {
		this.roId = roId;
	}

	public String getOrganizationName() {
		return organizationName;
	}

	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}

	public Integer getDeptId() {
		return deptId;
	}

	public void setDeptId(Integer deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public Integer getWardId() {
		return wardId;
	}

	public void setWardId(Integer wardId) {
		this.wardId = wardId;
	}

	public String getWardName() {
		return wardName;
	}

	public void setWardName(String wardName) {
		this.wardName = wardName;
	}

	public String getRoleName() {
		return roleName;
	}

	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}

	public Integer getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(Integer isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getRoleDescribe() {
		return roleDescribe;
	}

	public void setRoleDescribe(String roleDescribe) {
		this.roleDescribe = roleDescribe;
	}

	public Integer getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(Integer createUserId) {
		this.createUserId = createUserId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public List<Map> getGetRoleOrganDet() {
		return getRoleOrganDet;
	}

	public void setGetRoleOrganDet(List<Map> getRoleOrganDet) {
		this.getRoleOrganDet = getRoleOrganDet;
	}

	public List<ManageRoleOrganDTO> getRoleOrganList() {
		return roleOrganList;
	}

	public void setRoleOrganList(List<ManageRoleOrganDTO> roleOrganList) {
		this.roleOrganList = roleOrganList;
	}
	
	

}
