package  com.bojun.system.dto;

import java.util.List;

import com.bojun.system.entity.Menu;

/**
 * Model： 菜单表
 * Description：菜单表DTO
 * Author：赖水秀
 * created： 2020年4月28日
 */
public class MenuDTO extends Menu {

	private static final long serialVersionUID = -1344026328907379635L;

	/**
	 * 子级菜单
	 */
	private List<MenuDTO> children;
	
	/**
	 * 上级菜单名称
	 */
	private String parentName;
		
	private Integer everyPage; 	//每页显示数
	
	private Integer pageNum;   //页面数
	
	private Integer totalCount;  //总记录数
	
	private String roleId;//角色ID 
	
	/**
	 * 菜单下的按钮列表
	 */
	private List<MenuDTO> btnChildren;
	
	private String menuBtnName;		//菜单下的按钮名称
	
	private Integer appUserId;//app用户id
	
	private Integer authType=1;//0：超级管理员，1：普通管理员
	
	
	
	
	public Integer getAuthType() {
		return authType;
	}

	public void setAuthType(Integer authType) {
		this.authType = authType;
	}

	public Integer getAppUserId() {
		return appUserId;
	}

	public void setAppUserId(Integer appUserId) {
		this.appUserId = appUserId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public List<MenuDTO> getChildren() {
		return children;
	}

	public void setChildren(List<MenuDTO> children) {
		this.children = children;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public Integer getEveryPage() {
		return everyPage;
	}

	public void setEveryPage(Integer everyPage) {
		this.everyPage = everyPage;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

	public Integer getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(Integer totalCount) {
		this.totalCount = totalCount;
	}

	public List<MenuDTO> getBtnChildren() {
		return btnChildren;
	}

	public void setBtnChildren(List<MenuDTO> btnChildren) {
		this.btnChildren = btnChildren;
	}

	public String getMenuBtnName() {
		return menuBtnName;
	}

	public void setMenuBtnName(String menuBtnName) {
		this.menuBtnName = menuBtnName;
	}
	
}
