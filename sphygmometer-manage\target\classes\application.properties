spring.application.name=sphygmometer-manage
spring.profiles.active=dev
server.servlet.context-path=/sphygmometerManage
eureka.client.service-url.defaultZone=http://localhost:1115/eureka

feign.okhttp.enabled=true
feign.hystrix.enabled=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=60000

ribbon.ReadTimeout=50000
ribbon.ConnectTimeout=50000

#reids#
spring.redis.database=0
spring.redis.jedis.pool.max-active=200
spring.redis.jedis.pool.max-wait=-1
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=0
spring.redis.timeout=5000

spring.jackson.time-zone=GMT+8

#swagger2 config#
swagger.project.title=\u9AD8\u8840\u538B\u81EA\u4E3B\u7B5B\u67E5\u7BA1\u7406\u7CFB\u7EDF
swagger.project.description=\u9AD8\u8840\u538B\u81EA\u4E3B\u7B5B\u67E5\u7BA1\u7406\u7CFB\u7EDF\u63A5\u53E3\u6587\u6863
swagger.project.groupname=1.0
swagger.project.version=1.0
swagger.project.base.package=com.bojun.sphygmometer.manage.controller
swagger.project.base.url=http\://localhost\:${server.port}/sphygmometerManage