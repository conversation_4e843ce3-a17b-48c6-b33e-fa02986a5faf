<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.MobileModifyRecordMapper">
    
    <resultMap type="com.bojun.sphygmometer.dto.MobileModifyRecordDTO" id="MobileModifyRecordDTOResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="oldMobile"    column="old_mobile"    />
        <result property="newMobile"    column="new_mobile"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectMobileModifyRecord">
    	select
	        id,
	        user_id,
	        old_mobile,
	        new_mobile,
	        create_time
		from 
        	t_mobile_modify_record
    </sql>

    <select id="selectMobileModifyRecordById" parameterType="int" resultMap="MobileModifyRecordDTOResult">
		<include refid="selectMobileModifyRecord"/>
		where 
        	id = #{id}
    </select>

    <select id="selectMobileModifyRecordList" parameterType="com.bojun.sphygmometer.dto.MobileModifyRecordDTO" resultMap="MobileModifyRecordDTOResult">
        <include refid="selectMobileModifyRecord"/>
        <where>  
		<if test="id != null "> and id = #{id}</if>
		<if test="userId != null "> and user_id = #{userId}</if>
		<if test="oldMobile != null  and oldMobile != ''"> and old_mobile = #{oldMobile}</if>
		<if test="newMobile != null  and newMobile != ''"> and new_mobile = #{newMobile}</if>
		<if test="createTime != null "> and create_time = #{createTime}</if>
        </where>
    </select>

</mapper>