<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mapper.GeneralMapper">
    
  	<select id="getChilrenOrgByRoleId"
			resultType="com.bojun.organization.dto.OrganizationInfoDTO">
		SELECT
		<if test="roleId !=null and roleId !=''">
		ro.role_id,
		</if>
		oi.*
		FROM
		organization.t_organization_info oi
		<if test="roleId !=null and roleId !=''">
		LEFT JOIN system.t_role_organization ro ON
		oi.organization_id=ro.organization_id
		</if>
		where is_enabled=1
		<if test="roleId !=null and roleId !=''">
			and ro.role_id=#{roleId} 
		</if>
		<if test="organizationId !=null and organizationId !=''">
			and parent_id =#{organizationId}
		</if>
		<if test="organizationIds != null  and organizationIds.size() > 0">
				and parent_id in
				<foreach item="item" index="index"
					collection="organizationIds" open="(" separator=","
					close=")">
					#{item}
				</foreach>
			</if>
	</select>

	<select id="getOrgById" resultType="com.bojun.organization.dto.OrganizationInfoDTO" parameterType="java.lang.Integer">
      SELECT
		organization_id,
		organization_name,
		social_credit_code,
		organization_property,
		organization_scale,
		organization_class_code,
		organization_type_code,
		organization_director,
		telephone_number,
		province_code,
		weight,
		city_code,
		county_code,
		organization_address,
		longitude,
		latitude,
		organization_introduction,
		organization_history,
		organization_honor,
		front_business_license,
		back_business_license,
		create_user_id,
		parent_id,
		organization_code,
		is_enabled,
		t.town_name,
		t.village_name,
		t.town_code,
		t.village_code
	  FROM organization.t_organization_info t where organization_id = #{organizationId}
    </select>

</mapper>