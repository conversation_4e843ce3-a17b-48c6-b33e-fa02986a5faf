package com.bojun.base.system.service.impl;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.bojun.base.system.mapper.FormQuestionItemMapper;
import com.bojun.base.system.service.IFormQuestionItemService;
import com.bojun.system.dto.FormQuestionItemDTO;
import com.github.pagehelper.PageHelper;

/**
 * 
*Model：满意度问卷题目子项目信息表
*Description：满意度问卷题目子项目信息表service
*Author:李欣颖
*created：2020年5月13日
 */
@Service
public class FormQuestionItemService  implements IFormQuestionItemService{

	@Autowired
	FormQuestionItemMapper formQuestionItemMapper;
	
	
	/**
	 * 
	 * @Description 查询满意度问卷题目子项目信息表信息列表
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return List<FormQuestionItemDTO>
	 * created：2020年5月13日
	 */
	public List<FormQuestionItemDTO> getFormQuestionItem( Map<String, Object> mapPara) {
		if (null != mapPara.get("pageNum") && null != mapPara.get("everyPage")) {
			Integer pageNum = (Integer) mapPara.get("pageNum");
			Integer pageSize = (Integer) mapPara.get("everyPage");
			PageHelper.startPage(pageNum, pageSize);
		}
		List<FormQuestionItemDTO> resList = formQuestionItemMapper.getFormQuestionItem(mapPara);
		return resList;
	}
	/**
	 * 
	 * @Description 查询总记录数
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	public Integer queryFormQuestionItemCount( Map<String, Object> paramsMap) {
		return (Integer) formQuestionItemMapper.queryFormQuestionItemCount(paramsMap);
	}
	/**
	 * 
	 * @Description 新增满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	public Integer addFormQuestionItem(FormQuestionItemDTO formQuestionItemDTO) {
		return formQuestionItemMapper.addFormQuestionItem(formQuestionItemDTO);
	}
	/**
	 * 
	 * @Description 删除满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	public Integer deleteFormQuestionItem(Map<String, Object> paramsMap) {

		return formQuestionItemMapper.deleteFormQuestionItem(paramsMap);
	}
	
	/**
	 * 
	 * @Description 修改满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return Integer
	 * created：2020年5月13日
	 */
	public Integer updateFormQuestionItem(FormQuestionItemDTO formQuestionItemDTO) {

		return formQuestionItemMapper.updateFormQuestionItem(formQuestionItemDTO);
	}
	/**
	 * 
	 * @Description 查询单个满意度问卷题目子项目信息表信息
	 * <AUTHOR>
	 * @param paramsMap
	 * @return
	 * @return FormQuestionItemDTO
	 * created：2020年5月13日
	 */
	public FormQuestionItemDTO getFormQuestionItemById(Map<String, Object> paramsMap) {

		return formQuestionItemMapper.getFormQuestionItemById(paramsMap);
	}

	
	

}