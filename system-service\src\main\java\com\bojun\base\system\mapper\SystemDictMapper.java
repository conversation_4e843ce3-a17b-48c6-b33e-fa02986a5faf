
package com.bojun.base.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.bojun.system.dto.SystemDictDTO;
import com.bojun.system.dto.SystemDictTypeDTO;
import com.github.pagehelper.Page;


/**
 * Model： 产品管理DAO
 * Description：文件描述
 * Author：赖水秀
 * created： 2020年4月27日
 */
@Mapper
public interface SystemDictMapper {
	
	/**
	 * @Description 根据角色ID查询产品列表
	 * <AUTHOR>
	 * @return
	 * List<SystemDictTypeDTO>
	 * 2020年4月28日
	 */
	List<SystemDictDTO> getSystemDictByRoleId(SystemDictDTO systemDictDto);
	
	/**
	 * @Description 查询系统分类列表
	 * <AUTHOR>
	 * @return
	 * List<SystemDictTypeDTO>
	 * 2020年4月27日
	 */
	List<SystemDictTypeDTO> getSystemDictTypeList();
	
	/**
	 * @Description 保存产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int saveSystemDict(SystemDictDTO systemDictDto);
	
	/**
	 * @Description 修改产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * int
	 * 2020年4月27日
	 */
	int updateSystemDict(SystemDictDTO systemDictDto);
	
	/**
	 * @Description 根据单个产品信息
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * SystemDictDTO
	 * 2020年4月27日
	 */
	SystemDictDTO getSystemDictById(String systemId);

	/**
	 * @Description 查询产品信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月27日
	 */
	Page<SystemDictDTO> getSystemDictList(SystemDictDTO systemDictDto);
	
	
	
	/**
	 * @description: 查询产品名称
	 * @author: 赖允翔
	 * @date: 2020/4/27
	 * @Param:1
	 * @return:
	 */
	List<SystemDictDTO> getSystemDicts(SystemDictDTO systemDictDTO);
	

	/**
	 * @Description 查询所有产品信息列表
	 * <AUTHOR>
	 * @param systemDictDto
	 * @return
	 * List<SystemDictDTO>
	 * 2020年4月30日
	 */
	List<SystemDictDTO> getAllSystemDictList();

}
