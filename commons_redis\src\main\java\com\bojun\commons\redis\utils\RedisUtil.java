/**
 * 
 */
package com.bojun.commons.redis.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
*Model：redis工具类
*Description：redis工具类
*Author: 段德鹏
*created：2019年10月31日
*/
@Component
public class RedisUtil {
	
	@Autowired
	private RedisTemplate<String, Object> redisTemplate;
	
	/**
	 * @Description set缓存
	 * <AUTHOR>
	 * @param key  键
	 * @param value 值
	 * @return
	 * boolean  true:成功  false:失败
	 */
	public boolean set(String key, Object value) {
		try {
			redisTemplate.opsForValue().set(key, value);
		    return true;
		} catch (Exception e) {
		    e.printStackTrace();
		    return false;
		}
	}
	
	/**
	 * @Description set缓存加入了时效
	 * <AUTHOR>
	 * @param key  键
	 * @param value 值
	 * @param time 时效（秒） 要大于0 如果time小于等于0 将设置无限期
	 * @return
	 * boolean true:成功  false:失败
	 */
	public boolean set(String key, Object value, long time) {
		try {
			if (time > 0) {
				redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
		    } else {
		        set(key, value);
		    }
		    return true;
		} catch (Exception e) {
		    e.printStackTrace();
		    return false;
		}
	}
	
	/**
	 * @Description 获取缓存
	 * <AUTHOR>
	 * @param key 键
	 * @return
	 * Object  值
	 */
	public Object get(String key) {
		return key == null ? null : redisTemplate.opsForValue().get(key);
	}
	
	/**
	 * @Description 删除缓存
	 * <AUTHOR>
	 * @param key 键（可以传一个值 或多个）
	 * void
	 */
	@SuppressWarnings("unchecked")
	public void del(String... key) {
		if (key != null && key.length > 0) {
			if (key.length == 1) {
				redisTemplate.delete(key[0]);
		    } else {
		        redisTemplate.delete(CollectionUtils.arrayToList(key));
		    }
		}
	}
	
	/**
	 * @Description 判断key是否存在
	 * <AUTHOR>
	 * @param key 键
	 * @return
	 * boolean true:存在  false:不存在
	 */
	public boolean hasKey(String key) {
		try {
			return redisTemplate.hasKey(key);
		} catch (Exception e) {
		    e.printStackTrace();
		    return false;
		}
	}
	
	/**
	 * @Description 指定缓存失效时间
	 * <AUTHOR>
	 * @param key 键
	 * @param time 时效（秒） 要大于0
	 * @return
	 * boolean
	 */
	public boolean expire(String key, long time) {
		try {
			if (time > 0) {
				redisTemplate.expire(key, time, TimeUnit.SECONDS);
		    }
		    return true;
		} catch (Exception e) {
		    e.printStackTrace();
		    return false;
		}
	}

	/**
	 * 递增
	 *
	 * @param key   键
	 * @param delta 要增加几(大于0)
	 * @return
	 */

	public long incr(String key, long delta) {

		if (delta < 0) {

			throw new RuntimeException("递增因子必须大于0");

		}

		return redisTemplate.opsForValue().increment(key, delta);

	}

	/**
	 * 递减
	 *
	 * @param key   键
	 * @param delta 要减少几(小于0)
	 * @return
	 */

	public long decr(String key, long delta) {

		if (delta < 0) {

			throw new RuntimeException("递减因子必须大于0");

		}

		return redisTemplate.opsForValue().increment(key, -delta);

	}

	/**
	 * 模糊匹配批量删除缓存
	 * @param keyPrefix，缓存前缀，例如：cache:user:*，请了解redis通配符后进行使用
	 */
	public void deleteByPrefix(String keyPrefix) {
		Set<String> keys = redisTemplate.keys(keyPrefix);
		//不为空时，删除这些key
		if (!CollectionUtils.isEmpty(keys)) {
			redisTemplate.delete(keys);
		}
	}

}
