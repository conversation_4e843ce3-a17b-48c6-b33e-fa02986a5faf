<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bojun.sphygmometer.mq.mapper.SphygmometerUserMapper">

	<!-- 保存高血压用户信息 -->
	<insert id="addSphygmometerUser" useGeneratedKeys="true" keyProperty="userId" parameterType="com.bojun.sphygmometer.mq.entity.SphygmometerUser">
		INSERT INTO t_sphygmometer_user(wx_open_id, nick_name, gender, head_portrait, union_id, mobile, country, province, city,
		manage_organization_id, register_organization_id, register_source, register_time) 
		VALUES(#{wxOpenId}, #{nickName}, #{gender}, #{headPortrait}, #{unionId}, #{mobile}, #{country}, #{province}, #{city},
		#{manageOrganizationId}, #{registerOrganizationId}, #{registerSource}, now())
	</insert>
	
	<!-- 校验openId是否已注册 -->
	<select id="getSphygmometerUserCountByOpenId" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(user_id) FROM t_sphygmometer_user WHERE wx_open_id = #{openId}
	</select>
    
	<!-- 绑定用户手机号 -->
	<update id="bindMobile">
		UPDATE t_sphygmometer_user SET mobile = #{mobileNum} WHERE wx_open_id = #{openId}
	</update>
	
	<!-- 获取手机绑定用户数量 -->
	<select id="getBindMobileUserCount" parameterType="java.lang.String" resultType="java.lang.Integer">
		SELECT COUNT(user_id) FROM t_sphygmometer_user WHERE mobile = #{mobileNum}
	</select>
	
	<!-- 根据unionId查询用户信息 -->
	<select id="getSphygmometerUserByUnionId" parameterType="java.lang.String" resultType="com.bojun.sphygmometer.mq.entity.SphygmometerUser">
		SELECT
			user_id,
            nick_name,
            gender,
            head_portrait,
            wx_open_id,
            app_open_id,
            wxapp_open_id,
            union_id,
            rong_token,
            mobile,
            country,
            province,
            city,
            bind_device_id,
            bind_time,
            manage_organization_id,
            register_organization_id,
            register_source,
            register_time,
            ge_tui_client_id,
            region_user,
            password,
            salt,
            resident_id,
            bind_device_user_type,
            is_sign,
            sign_time
		FROM
			t_sphygmometer_user
		WHERE
			union_id = #{unionId}
	</select>
	
	<!-- 更新血压计用户信息  -->
	<update id="updateSphygmometerUser" parameterType="com.bojun.sphygmometer.mq.entity.SphygmometerUser">
		update t_sphygmometer_user set wx_open_id = #{wxOpenId}
		<if test="nickName != null and nickName != '' ">
			, nick_name = #{nickName}
		</if>
		<if test="gender != null">
			, gender = #{gender}
		</if>
		<if test="headPortrait != null and headPortrait != '' ">
			, head_portrait = #{headPortrait}
		</if>
		<if test="wxOpenId != null and wxOpenId != '' ">
			, wx_open_id = #{wxOpenId}
		</if>
		<if test="unionId != null and unionId != '' ">
			, union_id = #{unionId}
		</if>
		<if test="mobile != null and mobile != '' ">
			, mobile = #{mobile}
		</if>
		<if test="country != null and country != '' ">
			, country = #{country}
		</if>
		<if test="province != null and province != '' ">
			, province = #{province}
		</if>
		<if test="city != null and city != '' ">
			, city = #{city}
		</if>
		where user_id = #{userId}
	</update>

	<select id="getUserByOpenId" parameterType="java.lang.String" resultType="com.bojun.sphygmometer.mq.entity.SphygmometerUser">
		select
	        tsu.user_id,
            tsu.nick_name,
            tsu.gender,
            tsu.head_portrait,
            tsu.wx_open_id,
            tsu.app_open_id,
            tsu.wxapp_open_id,
            tsu.union_id,
            tsu.rong_token,
            tsu.mobile,
            tsu.country,
            tsu.province,
            tsu.city,
            tsu.bind_device_id,
            tsu.bind_time,
            tsu.manage_organization_id,
            tsu.register_organization_id,
            tsu.register_source,
            tsu.register_time,
            tsu.ge_tui_client_id,
            tsu.region_user,
            tsu.password,
            tsu.salt,
            tsu.resident_id,
            tsu.bind_device_user_type,
            tsu.is_sign,
            tsu.sign_time
		from
        	t_sphygmometer_user tsu
        where
        	tsu.wx_open_id = #{wxOpenId}
        order by
        	tsu.register_time desc
        limit 1
	</select>

	<select id="getUserById" parameterType="java.lang.Integer" resultType="com.bojun.sphygmometer.mq.entity.SphygmometerUser">
		select
			user_id,
            nick_name,
            gender,
            head_portrait,
            wx_open_id,
            app_open_id,
            wxapp_open_id,
            union_id,
            rong_token,
            mobile,
            country,
            province,
            city,
            bind_device_id,
            bind_time,
            manage_organization_id,
            register_organization_id,
            register_source,
            register_time,
            ge_tui_client_id,
            region_user,
            password,
            salt,
            resident_id,
            bind_device_user_type,
            is_sign,
            sign_time
		from
			t_sphygmometer_user
		where
			user_id = #{userId}
	</select>

</mapper>