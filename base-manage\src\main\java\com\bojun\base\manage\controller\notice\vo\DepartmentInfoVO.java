package com.bojun.base.manage.controller.notice.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Model：
 * Description：
 * Author: 赖允翔
 * created：2020/4/30 15:24
 */
public class DepartmentInfoVO implements Serializable {
    private static final long serialVersionUID = -9120830022257472830L;
    private Integer deptId;
    private String deptName;
    private List<DepartmentInfoVO> subDeptList;//下级科室ID
    private String type;
    @ApiModelProperty(value = "启用状态标记  0：未启用  1：启用")
    private Integer isEnabled;

    private Integer organizationId; // 机构id


    public Integer getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(Integer organizationId) {
        this.organizationId = organizationId;
    }

    public Integer getIsEnabled() {
        return isEnabled;
    }

    public void setIsEnabled(Integer isEnabled) {
        this.isEnabled = isEnabled;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<DepartmentInfoVO> getSubDeptList() {
        return subDeptList;
    }

    public void setSubDeptList(List<DepartmentInfoVO> subDeptList) {
        this.subDeptList = subDeptList;
    }



    public Integer getDeptId() {
        return deptId;
    }

    public void setDeptId(Integer deptId) {
        this.deptId = deptId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
}
